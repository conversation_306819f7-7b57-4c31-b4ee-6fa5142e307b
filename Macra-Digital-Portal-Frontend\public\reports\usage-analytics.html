<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Usage Analytics Report - MACRA</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .usage-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metric-card.users { border-left: 4px solid #007bff; }
        .metric-card.sessions { border-left: 4px solid #28a745; }
        .metric-card.pages { border-left: 4px solid #ffc107; }
        .metric-card.downloads { border-left: 4px solid #17a2b8; }
        .metric-card.time { border-left: 4px solid #6f42c1; }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-card.users .metric-value { color: #007bff; }
        .metric-card.sessions .metric-value { color: #28a745; }
        .metric-card.pages .metric-value { color: #ffc107; }
        .metric-card.downloads .metric-value { color: #17a2b8; }
        .metric-card.time .metric-value { color: #6f42c1; }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .usage-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            border-radius: 10px;
        }
        
        .trend-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .trend-up { background: #d4edda; color: #155724; }
        .trend-down { background: #f8d7da; color: #721c24; }
        .trend-stable { background: #e2e3e5; color: #383d41; }
        
        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            min-height: 250px;
        }
        
        .system-performance-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            max-width: 100%;
            overflow: hidden;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Portal Usage Analytics Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> December 15, 2024</div>
                <div><strong>Period:</strong> November 1-30, 2024</div>
                <div><strong>Generated By:</strong> Analytics System</div>
            </div>
        </div>

        <!-- Usage Metrics -->
        <div class="usage-metrics">
            <div class="metric-card users">
                <div class="metric-value">2,847</div>
                <div class="metric-label">Active Users</div>
            </div>
            <div class="metric-card sessions">
                <div class="metric-value">8,924</div>
                <div class="metric-label">Total Sessions</div>
            </div>
            <div class="metric-card pages">
                <div class="metric-value">45,672</div>
                <div class="metric-label">Page Views</div>
            </div>
            <div class="metric-card downloads">
                <div class="metric-value">1,234</div>
                <div class="metric-label">Document Downloads</div>
            </div>
            <div class="metric-card time">
                <div class="metric-value">8m 42s</div>
                <div class="metric-label">Avg. Session Time</div>
            </div>
        </div>

        <!-- Most Popular Pages -->
        <div class="section">
            <h2 class="section-title">Most Popular Pages</h2>
            <div class="chart-container">
                <canvas id="popularPagesChart" style="width: 100%; height: 400px;"></canvas>
            </div>
        </div>

        <!-- User Activity by Department -->
        <div class="section">
            <h2 class="section-title">User Activity by Department</h2>
            <div class="chart-container">
                <canvas id="departmentActivityChart" style="width: 100%; height: 400px;"></canvas>
            </div>
        </div>

        <!-- System Performance Metrics -->
        <div class="section">
            <h2 class="section-title">System Performance Dashboard</h2>
            <div class="chart-container system-performance-container">
                <canvas id="pageLoadChart" style="width: 100%; height: 150px;"></canvas>
                <canvas id="uptimeChart" style="width: 100%; height: 150px;"></canvas>
                <canvas id="errorRateChart" style="width: 100%; height: 150px;"></canvas>
                <canvas id="mobileUsageChart" style="width: 100%; height: 150px;"></canvas>
            </div>
        </div>

        <!-- Monthly Usage Trends -->
        <div class="section">
            <h2 class="section-title">Monthly Usage Trends</h2>
            <div class="chart-container">
                <canvas id="usageTrendsChart" style="width: 100%; height: 300px;"></canvas>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This analytics report is generated automatically and updated monthly.</p>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Helper function to convert time (e.g., "3m 24s") to seconds for charting
        function timeToSeconds(timeStr) {
            const [minutes, seconds] = timeStr.replace('s', '').split('m ').map(Number);
            return minutes * 60 + seconds;
        }

        // 1. Most Popular Pages (Bubble Chart)
        const popularPagesChart = document.getElementById('popularPagesChart').getContext('2d');
        new Chart(popularPagesChart, {
            type: 'bubble',
            data: {
                datasets: [
                    {
                        label: 'Dashboard',
                        data: [{ x: 12456, y: timeToSeconds('3m 24s'), r: 15 }],
                        backgroundColor: '#007bff',
                        borderColor: '#0056b3'
                    },
                    {
                        label: 'My Applications',
                        data: [{ x: 8923, y: timeToSeconds('5m 12s'), r: 12 }],
                        backgroundColor: '#28a745',
                        borderColor: '#1d7b33'
                    },
                    {
                        label: 'Apply for License',
                        data: [{ x: 6789, y: timeToSeconds('12m 45s'), r: 10 }],
                        backgroundColor: '#ffc107',
                        borderColor: '#d39e00'
                    },
                    {
                        label: 'Payment Portal',
                        data: [{ x: 4567, y: timeToSeconds('4m 18s'), r: 8 }],
                        backgroundColor: '#17a2b8',
                        borderColor: '#117a8b'
                    },
                    {
                        label: 'Documents',
                        data: [{ x: 3234, y: timeToSeconds('2m 56s'), r: 6 }],
                        backgroundColor: '#6f42c1',
                        borderColor: '#5a32a3'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    title: { display: true, text: 'Page Views vs Time on Page', font: { size: 16 } }
                },
                scales: {
                    x: {
                        title: { display: true, text: 'Page Views' },
                        beginAtZero: true
                    },
                    y: {
                        title: { display: true, text: 'Average Time on Page (Seconds)' },
                        beginAtZero: true
                    }
                }
            }
        });

        // 2. User Activity by Department (Multi-Series Bar Chart)
        const departmentActivityChart = document.getElementById('departmentActivityChart').getContext('2d');
        new Chart(departmentActivityChart, {
            type: 'bar',
            data: {
                labels: ['Telecommunications', 'Broadcasting', 'Postal Services', 'Standards'],
                datasets: [
                    {
                        label: 'Users',
                        data: [1245, 892, 456, 254],
                        backgroundColor: '#007bff',
                        borderColor: '#0056b3',
                        borderWidth: 1
                    },
                    {
                        label: 'Sessions',
                        data: [3876, 2654, 1234, 1160],
                        backgroundColor: '#28a745',
                        borderColor: '#1d7b33',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    title: { display: true, text: 'User Activity by Department', font: { size: 16 } }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Count' }
                    }
                }
            }
        });

        // 3. System Performance Metrics (Horizontal Bar Charts)
        // Helper function for bar chart
        function createBarChart(ctx, label, value, max, target, color, unit) {
            return new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [label],
                    datasets: [
                        {
                            label: 'Current Value',
                            data: [value],
                            backgroundColor: color,
                            borderWidth: 0
                        },
                        {
                            label: 'Target',
                            data: [target],
                            backgroundColor: '#ffc107',
                            borderWidth: 0
                        }
                    ]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                font: { size: 12 },
                                color: '#333'
                            }
                        },
                        title: { display: true, text: label, font: { size: 14 } },
                        datalabels: {
                            display: true,
                            formatter: (value, context) => `${value}${unit}`,
                            color: '#333',
                            font: { size: 12, weight: 'bold' },
                            anchor: 'end',
                            align: 'start',
                            offset: 4
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: max,
                            title: { display: true, text: unit === 's' ? 'Seconds' : 'Percentage (%)' },
                            grid: { display: false },
                            border: { display: false }
                        },
                        y: {
                            display: false // Hide y-axis completely
                        }
                    },
                    layout: {
                        padding: { right: 10, bottom: 20 }
                    },
                    plugins: ['datalabels']
                }
            });
        }

        createBarChart(
            document.getElementById('pageLoadChart').getContext('2d'),
            'Page Load Time', 1.8, 2.5, 2.0, '#28a745', 's'
        );
        createBarChart(
            document.getElementById('uptimeChart').getContext('2d'),
            'System Uptime', 99.7, 100, 99.5, '#28a745', '%'
        );
        createBarChart(
            document.getElementById('errorRateChart').getContext('2d'),
            'Error Rate', 0.3, 1.5, 1.0, '#28a745', '%'
        );
        createBarChart(
            document.getElementById('mobileUsageChart').getContext('2d'),
            'Mobile Usage', 34.2, 100, 30.0, '#007bff', '%'
        );

        // 4. Monthly Usage Trends (Line Chart)
        const usageTrendsChart = document.getElementById('usageTrendsChart').getContext('2d');
        new Chart(usageTrendsChart, {
            type: 'line',
            data: {
                labels: ['September', 'October', 'November'],
                datasets: [{
                    label: 'Active Users',
                    data: [2456, 2678, 2847],
                    borderColor: '#28a745',
                    backgroundColor: '#28a745',
                    fill: false,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    title: { display: true, text: 'Monthly Usage Trends (Active Users)', font: { size: 16 } }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Active Users' }
                    }
                }
            }
        });
    });
    </script>
</body>
</html>