'use client';

import React, { useState } from 'react';
import { dataBreachService } from '@/services/data-breach';
import { DataBreachReport } from '@/types/data-breach';
import { documentService } from '@/services/documentService';
import { activityNotesService } from '@/services/activityNotesService';
import { useToast } from '@/contexts/ToastContext';

interface DataBreachActionsProps {
  report: DataBreachReport;
  onUpdate?: () => void;
  onRefreshActivity?: () => void;
  className?: string;
}

const DataBreachActions: React.FC<DataBreachActionsProps> = ({
  report,
  onUpdate,
  onRefreshActivity,
  className = ''
}) => {
  const { showSuccess, showError } = useToast();
  const [actionType, setActionType] = useState<'close' | 'request_info' | null>(null);
  const [actionNote, setActionNote] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSubmittingAction, setIsSubmittingAction] = useState(false);

  const handleAction = async (type: 'close' | 'request_info') => {
    if (!report.report_id || !actionNote.trim()) {
      showError('Please enter a note for this action');
      return;
    }

    setIsSubmittingAction(true);
    try {
      // Step 1: Upload attachments if any
      let uploadedAttachments: any[] = [];
      if (attachments.length > 0) {
        for (const file of attachments) {
          try {
            const uploadedDoc = await documentService.uploadDocument(file, {
              entity_type: 'data-breach',
              entity_id: report.report_id,
              document_type: 'supporting_document',
              category: 'data_breach_action'
            });
            uploadedAttachments.push({
              document_id: uploadedDoc.document_id,
              file_name: uploadedDoc.file_name,
              file_size: uploadedDoc.file_size
            });
          } catch (uploadError) {
            console.error('Error uploading attachment:', uploadError);
            // Continue with other attachments even if one fails
          }
        }
      }

      // Step 2: Create activity note with the action
      try {
        const activityNote = await createDataBreachActivityNote(type, actionNote, uploadedAttachments);
        console.log('✅ Activity note created:', activityNote);
      } catch (noteError) {
        console.error('❌ Failed to create activity note:', noteError);
        // Continue with status update even if activity note fails
        showError('Warning: Activity note creation failed, but action will continue');
      }

      // Step 3: Update report status if closing
      if (type === 'close') {
        await dataBreachService.updateStatus(report.report_id, 'closed');
        showSuccess('Report closed successfully and activity note created');
      } else {
        showSuccess('Information request sent successfully and activity note created');
      }
      
      // Reset form
      setActionType(null);
      setActionNote('');
      setAttachments([]);
      
      // Trigger callbacks
      if (onUpdate) onUpdate();
      if (onRefreshActivity) onRefreshActivity();
      
    } catch (err: unknown) {
      console.error('Error performing action:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showError(`Failed to ${type === 'close' ? 'close report' : 'send information request'}: ${errorMessage}`);
    } finally {
      setIsSubmittingAction(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const resetForm = () => {
    setActionType(null);
    setActionNote('');
    setAttachments([]);
  };

  // Helper function to create data breach activity notes
  const createDataBreachActivityNote = async (
    action: 'close' | 'request_info',
    note: string,
    attachments: any[] = []
  ) => {
    return await activityNotesService.createDataBreachAction(
      report.report_id,
      action,
      note.trim(),
      {
        attachments: attachments,
        original_status: report.status,
        severity: report.severity,
        report_title: report.title,
        report_category: report.category
      }
    );
  };

  const renderActionForm = () => {
    if (!actionType) return null;

    return (
      <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {actionType === 'close' ? 'Close Report' : 'Request Information'}
          </h4>
          <button
            onClick={resetForm}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {actionType === 'close' ? 'Closure Note' : 'Information Request'} *
          </label>
          <textarea
            value={actionNote}
            onChange={(e) => setActionNote(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder={actionType === 'close' ? 'Enter closure notes...' : 'Describe what information is needed...'}
            required
          />
        </div>

        {actionType === 'request_info' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Attachments (Optional)
            </label>
            <input
              type="file"
              multiple
              onChange={handleFileUpload}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-gray-100"
            />
            {attachments.length > 0 && (
              <div className="mt-2 space-y-1">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-800 p-2 rounded border">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                    <button
                      onClick={() => removeAttachment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <i className="ri-close-line"></i>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            onClick={resetForm}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={() => handleAction(actionType)}
            disabled={!actionNote.trim() || isSubmittingAction}
            className={`px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed ${
              actionType === 'close' 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isSubmittingAction ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                {actionType === 'close' ? 'Closing...' : 'Sending...'}
              </>
            ) : (
              actionType === 'close' ? 'Close Report' : 'Send Request'
            )}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Actions</h3>
      </div>
      <div className="p-4">
        {!actionType ? (
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setActionType('close')}
              disabled={report.status === 'closed'}
              className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <i className="ri-close-circle-line mr-2"></i>
              {report.status === 'closed' ? 'Already Closed' : 'Close Report'}
            </button>
            <button
              onClick={() => setActionType('request_info')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 flex items-center"
            >
              <i className="ri-question-line mr-2"></i>
              Request Information
            </button>
          </div>
        ) : (
          renderActionForm()
        )}
      </div>
    </div>
  );
};

export default DataBreachActions;
