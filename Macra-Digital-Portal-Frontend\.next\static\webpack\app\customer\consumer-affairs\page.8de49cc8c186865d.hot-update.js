"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/consumer-affairs/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts":
/*!*****************************************************************!*\
  !*** ./src/services/consumer-affairs/consumerAffairsService.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   consumerAffairsService: () => (/* binding */ consumerAffairsService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nconst consumerAffairsService = {\n    // Create new complaint\n    async createComplaint (data) {\n        try {\n            console.log('🔄 Creating consumer affairs complaint:', {\n                title: data.title,\n                category: data.category,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            const formData = new FormData();\n            formData.append('title', data.title);\n            formData.append('description', data.description);\n            formData.append('category', data.category);\n            if (data.priority) {\n                formData.append('priority', data.priority);\n            }\n            // Add attachments if provided\n            if (data.attachments && data.attachments.length > 0) {\n                data.attachments.forEach((file)=>{\n                    formData.append('attachments', file);\n                });\n            }\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/consumer-affairs', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            throw error;\n        }\n    },\n    // Get all complaints with pagination\n    async getComplaints () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID\n    async getComplaint (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID (alias for consistency)\n    async getComplaintById (id) {\n        return this.getComplaint(id);\n    },\n    // Update complaint\n    async updateComplaint (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id), data);\n        // Backend returns { success: true, message: string, data: complaint }\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Delete complaint\n    async deleteComplaint (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/consumer-affairs/\".concat(id));\n    },\n    // Update complaint status (for staff)\n    async updateComplaintStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Update complainee (for staff)\n    async updateComplainee (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/complainee\"), data);\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Assign complaint to staff member (for staff) - Updated to use the general update endpoint\n    async assignComplaint (id, assignedTo) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id), {\n            assigned_to: assignedTo\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Add single attachment to complaint\n    async addAttachment (id, file) {\n        const formData = new FormData();\n        formData.append('files', file);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Add multiple attachments to complaint\n    async addAttachments (id, files) {\n        const formData = new FormData();\n        files.forEach((file)=>{\n            formData.append('files', file);\n        });\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Get all attachments for a complaint\n    async getAttachments (complaintId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments\"));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Remove attachment from complaint\n    async removeAttachment (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result;\n    },\n    // Download attachment from complaint\n    async downloadAttachment (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId, \"/download\"), {\n            responseType: 'blob'\n        });\n        return response.data;\n    },\n    // Get download URL for attachment (returns URL instead of blob for better performance)\n    async getAttachmentDownloadUrl (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId, \"/download-url\"));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Get statistics summary (for staff)\n    async getStatsSummary () {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/consumer-affairs/stats/summary');\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Export complaints to CSV (for staff)\n    async exportToCsv (filters) {\n        const params = new URLSearchParams();\n        if (filters) {\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    params.set(key, value.toString());\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/export/csv?\".concat(params.toString()), {\n            responseType: 'blob'\n        });\n        return response.data;\n    },\n    // File validation utilities\n    validateFile (file) {\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n            'application/vnd.ms-excel',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n            'image/jpeg',\n            'image/png',\n            'image/gif',\n            'text/plain'\n        ];\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                error: \"File size must be less than 10MB. Current size: \".concat((file.size / 1024 / 1024).toFixed(2), \"MB\")\n            };\n        }\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                isValid: false,\n                error: 'Invalid file type. Allowed types: PDF, Word, Excel, Images (JPEG, PNG, GIF), Text files'\n            };\n        }\n        return {\n            isValid: true\n        };\n    },\n    // Validate multiple files\n    validateFiles (files) {\n        const errors = [];\n        if (files.length > 5) {\n            errors.push('Maximum 5 files allowed per upload');\n        }\n        files.forEach((file, index)=>{\n            const validation = this.validateFile(file);\n            if (!validation.isValid) {\n                errors.push(\"File \".concat(index + 1, \" (\").concat(file.name, \"): \").concat(validation.error));\n            }\n        });\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    },\n    // Get file type icon for display\n    getFileTypeIcon (fileType) {\n        const iconMap = {\n            'application/pdf': 'ri-file-pdf-line',\n            'application/msword': 'ri-file-word-line',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'ri-file-word-line',\n            'application/vnd.ms-excel': 'ri-file-excel-line',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'ri-file-excel-line',\n            'image/jpeg': 'ri-image-line',\n            'image/png': 'ri-image-line',\n            'image/gif': 'ri-image-line',\n            'text/plain': 'ri-file-text-line'\n        };\n        return iconMap[fileType] || 'ri-file-line';\n    },\n    // Format file size for display\n    formatFileSize (bytes) {\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // Note: Investigative document methods are not yet implemented in the backend\n    // These will be added when the backend supports investigative document management\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getPriorityColor (priority) {\n        switch(priority === null || priority === void 0 ? void 0 : priority.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'urgent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Billing & Charges',\n                label: 'Billing & Charges'\n            },\n            {\n                value: 'Service Quality',\n                label: 'Service Quality'\n            },\n            {\n                value: 'Network Issues',\n                label: 'Network Issues'\n            },\n            {\n                value: 'Customer Service',\n                label: 'Customer Service'\n            },\n            {\n                value: 'Contract Disputes',\n                label: 'Contract Disputes'\n            },\n            {\n                value: 'Accessibility',\n                label: 'Accessibility'\n            },\n            {\n                value: 'Fraud & Scams',\n                label: 'Fraud & Scams'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts\n"));

/***/ })

});