import { PartialType } from '@nestjs/swagger';
import { CreateCeirCertificationBodyDto } from './create-ceir-certification-body.dto';
import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCeirCertificationBodyDto extends PartialType(CreateCeirCertificationBodyDto) {
  @ApiProperty({
    description: 'User ID who is updating this certification body',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID' })
  updated_by?: string;
}
