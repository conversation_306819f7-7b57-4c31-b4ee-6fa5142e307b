"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachSeverity = exports.DataBreachStatus = exports.DataBreachPriority = exports.DataBreachCategory = void 0;
var DataBreachCategory;
(function (DataBreachCategory) {
    DataBreachCategory["UNAUTHORIZED_ACCESS"] = "Unauthorized Data Access";
    DataBreachCategory["DATA_MISUSE"] = "Data Misuse or Sharing";
    DataBreachCategory["PRIVACY_VIOLATIONS"] = "Privacy Violations";
    DataBreachCategory["IDENTITY_THEFT"] = "Identity Theft";
    DataBreachCategory["PHISHING_ATTEMPTS"] = "Phishing Attempts";
    DataBreachCategory["DATA_LOSS"] = "Data Loss or Theft";
    DataBreachCategory["CONSENT_VIOLATIONS"] = "Consent Violations";
    DataBreachCategory["OTHER"] = "Other";
})(DataBreachCategory || (exports.DataBreachCategory = DataBreachCategory = {}));
var DataBreachPriority;
(function (DataBreachPriority) {
    DataBreachPriority["LOW"] = "low";
    DataBreachPriority["MEDIUM"] = "medium";
    DataBreachPriority["HIGH"] = "high";
    DataBreachPriority["URGENT"] = "urgent";
})(DataBreachPriority || (exports.DataBreachPriority = DataBreachPriority = {}));
var DataBreachStatus;
(function (DataBreachStatus) {
    DataBreachStatus["SUBMITTED"] = "submitted";
    DataBreachStatus["UNDER_REVIEW"] = "under_review";
    DataBreachStatus["INVESTIGATING"] = "investigating";
    DataBreachStatus["RESOLVED"] = "resolved";
    DataBreachStatus["CLOSED"] = "closed";
})(DataBreachStatus || (exports.DataBreachStatus = DataBreachStatus = {}));
var DataBreachSeverity;
(function (DataBreachSeverity) {
    DataBreachSeverity["LOW"] = "low";
    DataBreachSeverity["MEDIUM"] = "medium";
    DataBreachSeverity["HIGH"] = "high";
    DataBreachSeverity["CRITICAL"] = "critical";
})(DataBreachSeverity || (exports.DataBreachSeverity = DataBreachSeverity = {}));
//# sourceMappingURL=data-breachs-constants.js.map