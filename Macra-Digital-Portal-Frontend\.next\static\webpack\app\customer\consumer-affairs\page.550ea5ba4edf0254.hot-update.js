"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/consumer-affairs/page",{

/***/ "(app-pages-browser)/./src/components/customer/CustomerLayout.tsx":
/*!****************************************************!*\
  !*** ./src/components/customer/CustomerLayout.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _LogoutButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../LogoutButton */ \"(app-pages-browser)/./src/components/LogoutButton.tsx\");\n/* harmony import */ var _common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../common/NotificationBell */ \"(app-pages-browser)/./src/components/common/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CustomerLayout = (param)=>{\n    let { children, breadcrumbs } = param;\n    _s();\n    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showLoader } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading)();\n    // Memoize navigation items to prevent unnecessary re-renders\n    const navigationItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[navigationItems]\": ()=>[\n                {\n                    name: 'Dashboard',\n                    href: '/customer',\n                    icon: 'ri-dashboard-line',\n                    current: pathname === '/customer'\n                },\n                {\n                    name: 'My Licenses',\n                    href: '/customer/my-licenses',\n                    icon: 'ri-key-line',\n                    current: pathname === '/customer/my-licenses'\n                },\n                {\n                    name: 'My Applications',\n                    href: '/customer/applications',\n                    icon: 'ri-file-list-3-line',\n                    current: pathname === '/customer/applications'\n                },\n                {\n                    name: 'Invoices & Payments',\n                    href: '/customer/payments',\n                    icon: 'ri-bank-card-line',\n                    current: pathname === '/customer/payments'\n                },\n                {\n                    name: 'Documents',\n                    href: '/customer/documents',\n                    icon: 'ri-file-text-line',\n                    current: pathname === '/customer/documents'\n                },\n                {\n                    name: 'Procurement',\n                    href: '/customer/procurement',\n                    icon: 'ri-auction-line',\n                    current: pathname === '/customer/procurement'\n                },\n                {\n                    name: 'Request Resource',\n                    href: '/customer/resources',\n                    icon: 'ri-hand-heart-line',\n                    current: pathname === '/customer/resources'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[navigationItems]\"], [\n        pathname\n    ]);\n    const supportItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[supportItems]\": ()=>[\n                {\n                    name: 'Consumer Affairs',\n                    href: '/customer/consumer-affairs',\n                    icon: 'ri-customer-service-2-line'\n                },\n                {\n                    name: 'Data Breach',\n                    href: '/customer/data-protection',\n                    icon: 'ri-shield-keyhole-line'\n                },\n                {\n                    name: 'Help Center',\n                    href: '/customer/help',\n                    icon: 'ri-question-line'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[supportItems]\"], []);\n    // Prefetch customer pages on mount for faster navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerLayout.useEffect\": ()=>{\n            const prefetchPages = {\n                \"CustomerLayout.useEffect.prefetchPages\": ()=>{\n                    const customerPages = [\n                        '/customer',\n                        '/customer/applications',\n                        '/customer/applications/standards',\n                        '/customer/payments',\n                        '/customer/my-licenses',\n                        '/customer/procurement',\n                        '/customer/profile',\n                        '/customer/consumer-affairs',\n                        '/customer/data-protection',\n                        '/customer/resources',\n                        '/customer/help'\n                    ];\n                    customerPages.forEach({\n                        \"CustomerLayout.useEffect.prefetchPages\": (page)=>{\n                            router.prefetch(page);\n                        }\n                    }[\"CustomerLayout.useEffect.prefetchPages\"]);\n                }\n            }[\"CustomerLayout.useEffect.prefetchPages\"];\n            // Delay prefetching to not interfere with initial page load\n            const timer = setTimeout(prefetchPages, 1000);\n            return ({\n                \"CustomerLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"CustomerLayout.useEffect\"];\n        }\n    }[\"CustomerLayout.useEffect\"], [\n        router\n    ]);\n    const toggleMobileSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleMobileSidebar]\": ()=>{\n            setIsMobileSidebarOpen(!isMobileSidebarOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleMobileSidebar]\"], [\n        isMobileSidebarOpen\n    ]);\n    const handleNavClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavClick]\": (href, name)=>{\n            const pageMessages = {\n                '/customer': 'Loading Dashboard...',\n                '/customer/my-licenses': 'Loading My Licenses...',\n                '/customer/applications': 'Loading Applications...',\n                '/customer/applications/apply/': 'Loading Standards License Options...',\n                '/customer/payments': 'Loading Payments...',\n                '/customer/documents': 'Loading Documents...',\n                '/customer/procurement': 'Loading Procurement...',\n                '/customer/resources': 'Loading Resources...',\n                '/customer/data-protection': 'Loading Data Breach...',\n                '/customer/help': 'Loading Help Center...',\n                '/customer/profile': 'Loading Profile...',\n                '/customer/settings': 'Loading Settings...'\n            };\n            const message = pageMessages[href] || \"Loading \".concat(name, \"...\");\n            showLoader(message);\n            setIsMobileSidebarOpen(false);\n        }\n    }[\"CustomerLayout.useCallback[handleNavClick]\"], [\n        showLoader\n    ]);\n    const handleNavHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavHover]\": (href)=>{\n            // Prefetch on hover for instant navigation\n            router.prefetch(href);\n        }\n    }[\"CustomerLayout.useCallback[handleNavHover]\"], [\n        router\n    ]);\n    const toggleUserDropdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleUserDropdown]\": ()=>{\n            setIsUserDropdownOpen(!isUserDropdownOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleUserDropdown]\"], [\n        isUserDropdownOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            isMobileSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: ()=>setIsMobileSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\\n        \".concat(isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/images/macra-logo.png\",\n                                    alt: \"MACRA Logo\",\n                                    className: \"max-h-12 w-auto\",\n                                    onLoad: ()=>console.log('MACRA logo loaded successfully in customer portal'),\n                                    onError: (e)=>{\n                                        var _target_parentNode;\n                                        console.error('Error loading MACRA logo in customer portal:', e);\n                                        console.log('Attempting to load logo from:', e.currentTarget.src);\n                                        // Fallback to styled logo if image fails\n                                        const target = e.target;\n                                        target.style.display = 'none';\n                                        const textFallback = document.createElement('div');\n                                        textFallback.innerHTML = 'MACRA';\n                                        textFallback.className = 'text-primary font-bold text-lg bg-red-600 text-white px-3 py-2 rounded-full';\n                                        (_target_parentNode = target.parentNode) === null || _target_parentNode === void 0 ? void 0 : _target_parentNode.appendChild(textFallback);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-6 px-4 side-nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            onClick: ()=>handleNavClick(item.href, item.name),\n                                            onMouseEnter: ()=>handleNavHover(item.href),\n                                            className: \"\\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\\n                    \".concat(item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100', \"\\n                  \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center mr-3 \".concat(item.current ? 'text-red-600 dark:text-red-400' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 space-y-1\",\n                                            children: supportItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    onClick: ()=>handleNavClick(item.href, item.name),\n                                                    onMouseEnter: ()=>handleNavHover(item.href),\n                                                    className: \"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 flex items-center justify-center mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white dark:bg-gray-800 shadow-sm z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleMobileSidebar,\n                                    className: \"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\",\n                                    \"aria-label\": \"Open mobile menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-menu-line ri-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                children: [\n                                                    index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-arrow-right-s-line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: \"hover:text-primary\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-900 dark:text-gray-100\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleUserDropdown,\n                                                    className: \"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Open user menu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            className: \"h-8 w-8 rounded-full object-cover\",\n                                                            src: (user === null || user === void 0 ? void 0 : user.profile_image) || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\",\n                                                            alt: \"Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isUserDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/customer/profile\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                onClick: ()=>{\n                                                                    handleNavClick('/customer/profile', 'Profile');\n                                                                    setIsUserDropdownOpen(false);\n                                                                },\n                                                                children: \"Your Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LogoutButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"text\",\n                                                                size: \"sm\",\n                                                                className: \"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                showConfirmation: true,\n                                                                children: \"Sign out\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerLayout, \"Bvue6xx7qybDlJR/560gagzUyBI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading\n    ];\n});\n_c = CustomerLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerLayout);\nvar _c;\n$RefreshReg$(_c, \"CustomerLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts":
/*!*****************************************************************!*\
  !*** ./src/services/consumer-affairs/consumerAffairsService.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   consumerAffairsService: () => (/* binding */ consumerAffairsService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/documentService */ \"(app-pages-browser)/./src/services/documentService.ts\");\n\n\n\nconst consumerAffairsService = {\n    // Create new complaint\n    async createComplaint (data) {\n        try {\n            console.log('🔄 Creating consumer affairs complaint:', {\n                title: data.title,\n                category: data.category,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            const formData = new FormData();\n            formData.append('title', data.title);\n            formData.append('description', data.description);\n            formData.append('category', data.category);\n            if (data.priority) {\n                formData.append('priority', data.priority);\n            }\n            // Add attachments if provided\n            if (data.attachments && data.attachments.length > 0) {\n                data.attachments.forEach((file)=>{\n                    formData.append('attachments', file);\n                });\n            }\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/consumer-affairs', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            throw error;\n        }\n    },\n    // Get all complaints with pagination\n    async getComplaints () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID\n    async getComplaint (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID (alias for consistency)\n    async getComplaintById (id) {\n        return this.getComplaint(id);\n    },\n    // Update complaint\n    async updateComplaint (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id), data);\n        // Backend returns { success: true, message: string, data: complaint }\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Delete complaint\n    async deleteComplaint (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/consumer-affairs/\".concat(id));\n    },\n    // Update complaint status (for staff)\n    async updateComplaintStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Update complainee (for staff)\n    async updateComplainee (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/complainee\"), data);\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Assign complaint to staff member (for staff) - Updated to use the general update endpoint\n    async assignComplaint (id, assignedTo) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/assign\"), {\n            assigned_to: assignedTo\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Add single attachment to complaint (via consumer-affairs attachments endpoint)\n    async addAttachment (id, file) {\n        const formData = new FormData();\n        formData.append('files', file);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        // Backend returns minimal document info; map to attachment shape\n        const payload = result.data || result;\n        const attachments = (payload || []).map((doc)=>({\n                attachment_id: doc.document_id || doc.attachment_id,\n                complaint_id: id,\n                file_name: doc.file_name,\n                file_type: doc.mime_type || doc.file_type || 'application/octet-stream',\n                file_size: doc.file_size || 0,\n                file_path: doc.file_path || '',\n                uploaded_at: doc.created_at || new Date().toISOString(),\n                uploaded_by: doc.created_by || 'unknown'\n            }));\n        return attachments;\n    },\n    // Add multiple attachments to complaint (via consumer-affairs attachments endpoint)\n    async addAttachments (id, files) {\n        const formData = new FormData();\n        files.forEach((file)=>formData.append('files', file));\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        const payload = result.data || result;\n        const attachments = (payload || []).map((doc)=>({\n                attachment_id: doc.document_id || doc.attachment_id,\n                complaint_id: id,\n                file_name: doc.file_name,\n                file_type: doc.mime_type || doc.file_type || 'application/octet-stream',\n                file_size: doc.file_size || 0,\n                file_path: doc.file_path || '',\n                uploaded_at: doc.created_at || new Date().toISOString(),\n                uploaded_by: doc.created_by || 'unknown'\n            }));\n        return attachments;\n    },\n    // Get all attachments for a complaint (via consumer-affairs attachments endpoint)\n    async getAttachments (complaintId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments\"));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        const payload = result.data || result;\n        // If backend already maps to attachments, return directly; else map fields\n        const attachments = (payload || []).map((att)=>({\n                attachment_id: att.attachment_id || att.document_id,\n                complaint_id: complaintId,\n                file_name: att.file_name,\n                file_type: att.file_type || att.mime_type || 'application/octet-stream',\n                file_size: att.file_size || 0,\n                file_path: att.file_path || '',\n                uploaded_at: att.uploaded_at || att.created_at || new Date().toISOString(),\n                uploaded_by: att.uploaded_by || att.created_by || 'unknown'\n            }));\n        return attachments;\n    },\n    // Remove attachment from complaint (via generic documents API)\n    async removeAttachment (_complaintId, attachmentId) {\n        await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.deleteDocument(attachmentId);\n    },\n    // Download attachment from complaint (via generic documents API)\n    async downloadAttachment (_complaintId, attachmentId) {\n        return await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.downloadDocument(attachmentId);\n    },\n    // Get download URL: not supported in generic API; return a best-effort via blob URL\n    async getAttachmentDownloadUrl (complaintId, attachmentId) {\n        const blob = await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.downloadDocument(attachmentId);\n        const downloadUrl = URL.createObjectURL(blob);\n        // We don't have the Document here; return minimal shape for caller expectations\n        return {\n            downloadUrl,\n            attachment: {\n                attachment_id: attachmentId,\n                complaint_id: complaintId,\n                file_name: 'download',\n                file_type: blob.type || 'application/octet-stream',\n                file_size: blob.size || 0,\n                file_path: '',\n                uploaded_at: new Date().toISOString(),\n                uploaded_by: 'unknown'\n            }\n        };\n    },\n    // Get statistics summary (for staff)\n    async getStatsSummary () {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/consumer-affairs/stats/summary');\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Export complaints to CSV (for staff)\n    async exportToCsv (filters) {\n        const params = new URLSearchParams();\n        if (filters) {\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    params.set(key, value.toString());\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/export/csv?\".concat(params.toString()), {\n            responseType: 'blob'\n        });\n        return response.data;\n    },\n    // File validation utilities\n    validateFile (file) {\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n            'application/vnd.ms-excel',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n            'image/jpeg',\n            'image/png',\n            'image/gif',\n            'text/plain'\n        ];\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                error: \"File size must be less than 10MB. Current size: \".concat((file.size / 1024 / 1024).toFixed(2), \"MB\")\n            };\n        }\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                isValid: false,\n                error: 'Invalid file type. Allowed types: PDF, Word, Excel, Images (JPEG, PNG, GIF), Text files'\n            };\n        }\n        return {\n            isValid: true\n        };\n    },\n    // Validate multiple files\n    validateFiles (files) {\n        const errors = [];\n        if (files.length > 5) {\n            errors.push('Maximum 5 files allowed per upload');\n        }\n        files.forEach((file, index)=>{\n            const validation = this.validateFile(file);\n            if (!validation.isValid) {\n                errors.push(\"File \".concat(index + 1, \" (\").concat(file.name, \"): \").concat(validation.error));\n            }\n        });\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    },\n    // Get file type icon for display\n    getFileTypeIcon (fileType) {\n        const iconMap = {\n            'application/pdf': 'ri-file-pdf-line',\n            'application/msword': 'ri-file-word-line',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'ri-file-word-line',\n            'application/vnd.ms-excel': 'ri-file-excel-line',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'ri-file-excel-line',\n            'image/jpeg': 'ri-image-line',\n            'image/png': 'ri-image-line',\n            'image/gif': 'ri-image-line',\n            'text/plain': 'ri-file-text-line'\n        };\n        return iconMap[fileType] || 'ri-file-line';\n    },\n    // Format file size for display\n    formatFileSize (bytes) {\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // Note: Investigative document methods are not yet implemented in the backend\n    // These will be added when the backend supports investigative document management\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getPriorityColor (priority) {\n        switch(priority === null || priority === void 0 ? void 0 : priority.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'urgent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Billing & Charges',\n                label: 'Billing & Charges'\n            },\n            {\n                value: 'Service Quality',\n                label: 'Service Quality'\n            },\n            {\n                value: 'Network Issues',\n                label: 'Network Issues'\n            },\n            {\n                value: 'Customer Service',\n                label: 'Customer Service'\n            },\n            {\n                value: 'Contract Disputes',\n                label: 'Contract Disputes'\n            },\n            {\n                value: 'Accessibility',\n                label: 'Accessibility'\n            },\n            {\n                value: 'Fraud & Scams',\n                label: 'Fraud & Scams'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/documentService.ts":
/*!*****************************************!*\
  !*** ./src/services/documentService.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   documentService: () => (/* binding */ documentService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n\n\n// Cache for preventing duplicate requests\nconst requestCache = new Map();\nconst documentService = {\n    // Get all documents with pagination and filtering\n    async getDocuments (params) {\n        try {\n            const queryParams = new URLSearchParams();\n            if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());\n            if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());\n            if (params === null || params === void 0 ? void 0 : params.search) queryParams.append('search', params.search);\n            if (params === null || params === void 0 ? void 0 : params.sortBy) queryParams.append('sortBy', params.sortBy);\n            const cacheKey = \"/documents?\".concat(queryParams.toString());\n            // Check if we already have a pending request for this exact query\n            if (requestCache.has(cacheKey)) {\n                console.log('Returning cached request for:', cacheKey);\n                return await requestCache.get(cacheKey);\n            }\n            // Create new request and cache it\n            const requestPromise = _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(cacheKey).then((response)=>{\n                // Remove from cache after completion\n                requestCache.delete(cacheKey);\n                return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            }).catch((error)=>{\n                // Remove from cache on error too\n                requestCache.delete(cacheKey);\n                throw error;\n            });\n            requestCache.set(cacheKey, requestPromise);\n            return await requestPromise;\n        } catch (error) {\n            console.error('DocumentService.getDocuments error:', error);\n            throw error;\n        }\n    },\n    // Get documents by entity (polymorphic relationship)\n    async getDocumentsByEntity (entityType, entityId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/documents/by-entity/\".concat(entityType, \"/\").concat(entityId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.getDocumentsByEntity error:', error);\n            throw error;\n        }\n    },\n    // Get documents by application\n    async getDocumentsByApplication (applicationId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/documents/by-application/\".concat(applicationId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            throw error;\n        }\n    },\n    // Get required documents for license category\n    async getRequiredDocumentsForLicenseCategory (licenseCategoryId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/license-category-documents/category/\".concat(licenseCategoryId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.getRequiredDocumentsForLicenseCategory error:', error);\n            throw error;\n        }\n    },\n    // Upload document (using the dedicated upload endpoint)\n    async uploadDocument (file, documentData) {\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('document_type', documentData.document_type);\n            formData.append('entity_type', documentData.entity_type);\n            formData.append('entity_id', documentData.entity_id);\n            formData.append('is_required', (documentData.is_required || false).toString());\n            formData.append('file_name', file.name);\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/upload', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            return {\n                document: result.data,\n                message: result.message || 'Document uploaded successfully'\n            };\n        } catch (error) {\n            console.error('DocumentService.uploadDocument error:', error);\n            throw error;\n        }\n    },\n    // Create document record (without file upload)\n    async createDocument (documentData) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents', documentData);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.createDocument error:', error);\n            throw error;\n        }\n    },\n    // Update document\n    async updateDocument (documentId, updateData) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/documents/\".concat(documentId), updateData);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.updateDocument error:', error);\n            throw error;\n        }\n    },\n    // Delete document\n    async deleteDocument (documentId) {\n        try {\n            await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete(\"/documents/\".concat(documentId));\n        } catch (error) {\n            console.error('DocumentService.deleteDocument error:', error);\n            throw error;\n        }\n    },\n    // Approve document\n    async approveDocument (documentId, comment) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.patch(\"/documents/\".concat(documentId, \"/approve\"), {\n                comment: comment || undefined\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.approveDocument error:', error);\n            throw error;\n        }\n    },\n    // Reject document\n    async rejectDocument (documentId, comment) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.patch(\"/documents/\".concat(documentId, \"/reject\"), {\n                comment: comment || undefined\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.rejectDocument error:', error);\n            throw error;\n        }\n    },\n    // Get document by ID\n    async getDocument (documentId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/documents/\".concat(documentId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('DocumentService.getDocument error:', error);\n            throw error;\n        }\n    },\n    // Download document\n    async downloadDocument (documentId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/documents/\".concat(documentId, \"/download\"), {\n                responseType: 'blob'\n            });\n            return response.data; // Return blob directly, not processed\n        } catch (error) {\n            console.error('DocumentService.downloadDocument error:', error);\n            throw error;\n        }\n    },\n    // Preview document\n    async previewDocument (documentId) {\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/documents/\".concat(documentId, \"/preview\"), {\n                responseType: 'blob'\n            });\n            return response.data; // Return blob directly for preview\n        } catch (error) {\n            console.error('DocumentService.previewDocument error:', error);\n            throw error;\n        }\n    },\n    // Check if document type is previewable\n    isPreviewable () {\n        let mimeType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n        if (!mimeType) return false;\n        const previewableMimeTypes = [\n            'application/pdf',\n            'image/jpeg',\n            'image/jpg',\n            'image/png',\n            'image/gif',\n            'image/webp',\n            'text/plain',\n            'text/html',\n            'text/css',\n            'text/javascript',\n            'application/json'\n        ];\n        return previewableMimeTypes.includes(mimeType.toLowerCase());\n    },\n    // Check if all required documents are uploaded for an application\n    async checkRequiredDocuments (applicationId, licenseCategoryId) {\n        try {\n            // Get required documents for license category\n            const requiredDocs = await this.getRequiredDocumentsForLicenseCategory(licenseCategoryId);\n            // Get uploaded documents for application\n            const data = await this.getDocumentsByApplication(applicationId);\n            const uploadedDocs = data.data;\n            // Check which required documents are missing\n            const uploadedTypes = uploadedDocs.map((doc)=>doc.document_type);\n            const missing = requiredDocs.filter((reqDoc)=>reqDoc.is_required && !uploadedTypes.includes(reqDoc.name.toLowerCase().replace(/\\s+/g, '_')));\n            return {\n                allUploaded: missing.length === 0,\n                missing,\n                uploaded: uploadedDocs\n            };\n        } catch (error) {\n            console.error('DocumentService.checkRequiredDocuments error:', error);\n            throw error;\n        }\n    },\n    // Get document types enum\n    getDocumentTypes () {\n        return [\n            'certificate_incorporation',\n            'memorandum_association',\n            'shareholding_structure',\n            'business_plan',\n            'financial_statements',\n            'technical_proposal',\n            'coverage_plan',\n            'network_diagram',\n            'equipment_specifications',\n            'insurance_certificate',\n            'tax_clearance',\n            'audited_accounts',\n            'bank_statement',\n            'cv_document',\n            'proof_of_payment',\n            'other'\n        ];\n    },\n    // Format document type for display\n    formatDocumentType (type) {\n        return type.split('_').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n    },\n    // Map document name to DocumentType enum value\n    mapDocumentNameToType (documentName) {\n        const nameToTypeMap = {\n            'Certificate of Incorporation': 'certificate_incorporation',\n            'Memorandum of Association': 'memorandum_association',\n            'Shareholding Structure': 'shareholding_structure',\n            'Business Plan': 'business_plan',\n            'Financial Statements': 'financial_statements',\n            'Technical Proposal': 'technical_proposal',\n            'Coverage Plan': 'coverage_plan',\n            'Network Diagram': 'network_diagram',\n            'Equipment Specifications': 'equipment_specifications',\n            'Insurance Certificate': 'insurance_certificate',\n            'Tax Clearance Certificate': 'tax_clearance',\n            'Tax Clearance': 'tax_clearance',\n            'Audited Accounts': 'audited_accounts',\n            'Bank Statement': 'bank_statement',\n            'CV Document': 'cv_document',\n            'Other': 'other'\n        };\n        // Try exact match first\n        if (nameToTypeMap[documentName]) {\n            return nameToTypeMap[documentName];\n        }\n        // Try case-insensitive match\n        const lowerName = documentName.toLowerCase();\n        for (const [name, type] of Object.entries(nameToTypeMap)){\n            if (name.toLowerCase() === lowerName) {\n                return type;\n            }\n        }\n        // Fallback: convert name to snake_case\n        return documentName.toLowerCase().replace(/\\s+/g, '_');\n    },\n    // Validate file type and size\n    validateFile (file) {\n        let maxSizeMB = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, allowedTypes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n        // Check file size (convert MB to bytes)\n        const maxSizeBytes = maxSizeMB * 1024 * 1024;\n        if (file.size > maxSizeBytes) {\n            return {\n                isValid: false,\n                error: \"File size must be less than \".concat(maxSizeMB, \"MB\")\n            };\n        }\n        // Check file type if specified\n        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {\n            return {\n                isValid: false,\n                error: \"File type not allowed. Allowed types: \".concat(allowedTypes.join(', '))\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/documentService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ })

});