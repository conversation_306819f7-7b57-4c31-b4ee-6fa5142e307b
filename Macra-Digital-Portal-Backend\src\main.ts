import 'reflect-metadata';
// Register tsconfig paths before any other imports
import 'tsconfig-paths/register';

import { NestFactory } from '@nestjs/core';
import { BadRequestException, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { ValidationError } from 'class-validator';

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule);

    // Enable CORS with enhanced configuration
    app.enableCors({
      origin: [
        process.env.FRONTEND_URL || 'http://localhost:3000',
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type', 
        'Authorization', 
        'Accept',
        'Origin',
        'X-Requested-With'
      ],
      preflightContinue: false,
      optionsSuccessStatus: 204
    });

    // Enable global exception handling
    app.useGlobalFilters(new HttpExceptionFilter());

    // Enable global validation
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      forbidUnknownValues: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = errors.flatMap(err => {
          if (err.constraints) return Object.values(err.constraints);
          return [`Validation failed on property ${err.property}`];
        });

        console.error('Validation failed:', messages);

        return new BadRequestException(messages.join(', '));
      },
  }));

    // Setup Swagger documentation
    const config = new DocumentBuilder()
      .setTitle('MACRA Digital Portal API')
      .setDescription('API documentation for MACRA Digital Portal - Authentication and User Management System')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      deepScanRoutes: true,
      extraModels: [], // Add any extra models that might not be automatically detected
    });
    
    SwaggerModule.setup('api/docs', app, document, {
      customSiteTitle: 'MACRA API Documentation',
      customfavIcon: '/favicon.ico',
      customCss: '.swagger-ui .topbar { display: none }',
      swaggerOptions: {
        persistAuthorization: true,
        docExpansion: 'none',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        deepLinking: true,
      },
    });

    const port = process.env.PORT || 3001;
    await app.listen(port);
    console.log(`🚀 Application is running on: http://localhost:${port}`);
    console.log(`📚 API Documentation available at: http://localhost:${port}/api/docs`);
    console.log('🔐 Authentication: ENABLED - Login required for API access');
    console.log('✅ Fixed req.user null reference errors with safety checks');
  } catch (error) {
    console.error('❌ Error starting the application:', error);
    process.exit(1);
  }
}
bootstrap();
