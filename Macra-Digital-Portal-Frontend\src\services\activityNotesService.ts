import { apiClient } from "@/lib/apiClient";
import { processApiResponse } from "@/lib/authUtils";
import { User } from "./auth.service";

export interface ActivityNote {
  id: string;
  entity_type: string;
  entity_id: string;
  note: string;
  note_type: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note';
  status: 'active' | 'archived' | 'deleted';
  category?: string;
  step?: string;
  metadata?: Record<string, any>;
  priority: string;
  is_visible: boolean;
  is_internal: boolean;
  created_by: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  archived_at?: string;
  deleted_at?: string;
  creator?: User;
  updater?: User;
}

export interface CreateActivityNoteDto {
  entity_type: string;
  entity_id: string;
  note: string;
  note_type?: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note' | 'information_request' | 'data_breach_action';
  category?: string;
  step?: string;
  metadata?: Record<string, any>;
  priority?: string;
  is_visible?: boolean;
  is_internal?: boolean;
}

export interface UpdateActivityNoteDto {
  note?: string;
  note_type?: 'evaluation_comment' | 'status_update' | 'general_note' | 'system_log' | 'review_note' | 'approval_note' | 'rejection_note' | 'information_request' | 'data_breach_action';
  status?: 'active' | 'archived' | 'deleted';
  category?: string;
  step?: string;
  metadata?: Record<string, any>;
  priority?: string;
  is_visible?: boolean;
  is_internal?: boolean;
}

export interface ActivityNoteQueryDto {
  entity_type?: string;
  entity_id?: string;
  note_type?: string;
  status?: string;
  category?: string;
  step?: string;
  priority?: string;
  is_internal?: boolean;
  created_by?: string;
}

class ActivityNotesService {
  private baseUrl = '/activity-notes';

  async create(data: CreateActivityNoteDto): Promise<ActivityNote> {
    const response = await apiClient.post(this.baseUrl, data);
    return processApiResponse(response);
  }

  async findAll(query?: ActivityNoteQueryDto): Promise<ActivityNote[]> {
    const response = await apiClient.get(this.baseUrl, { params: query });
    return processApiResponse(response);
  }

  async findByEntity(entityType: string, entityId: string): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}`);
    return processApiResponse(response);
  }

  async findByEntityAndStep(entityType: string, entityId: string, step: string): Promise<ActivityNote[]> {
    const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}/step/${step}`);
    return processApiResponse(response);
  }

  async findOne(id: string): Promise<ActivityNote> {
    const response = await apiClient.get(`${this.baseUrl}/${id}`);
    return processApiResponse(response);
  }

  async update(id: string, data: UpdateActivityNoteDto): Promise<ActivityNote> {
    const response = await apiClient.put(`${this.baseUrl}/${id}`, data);
    return processApiResponse(response);
  }

  async archive(id: string): Promise<ActivityNote> {
    const response = await apiClient.put(`${this.baseUrl}/${id}/archive`);
    return processApiResponse(response);
  }

  async softDelete(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/soft`);
  }

  async hardDelete(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}/hard`);
  }

  // Specialized methods for evaluation workflow
  async createEvaluationComment(
    applicationId: string,
    step: string,
    comment: string,
    metadata?: Record<string, any>
  ): Promise<ActivityNote> {
    const response = await apiClient.post(`${this.baseUrl}/evaluation-comment`, {
      applicationId,
      step,
      comment,
      metadata,
    });
    return processApiResponse(response);
  }

  async createStatusUpdate(
    applicationId: string,
    statusChange: string,
    metadata?: Record<string, any>
  ): Promise<ActivityNote> {
    const response = await apiClient.post(`${this.baseUrl}/status-update`, {
      applicationId,
      statusChange,
      metadata,
    });
    return processApiResponse(response);
  }

  // Helper methods for common use cases
  async getEvaluationComments(applicationId: string, step?: string): Promise<ActivityNote[]> {
    const query: ActivityNoteQueryDto = {
      entity_type: 'application',
      entity_id: applicationId,
      note_type: 'evaluation_comment',
      status: 'active',
    };

    if (step) {
      query.step = step;
    }

    return this.findAll(query);
  }

  async getApplicationNotes(applicationId: string): Promise<any> {
    return this.findByEntity('application', applicationId);
  }

  async getApplicationStatusUpdates(applicationId: string): Promise<ActivityNote[]> {
    return this.findAll({
      entity_type: 'application',
      entity_id: applicationId,
      note_type: 'status_update',
      status: 'active',
    });
  }

  // Data breach specific methods
  async createDataBreachAction(
    reportId: string,
    action: 'close' | 'request_info' | 'investigate' | 'resolve',
    note: string,
    metadata?: Record<string, any>
  ): Promise<ActivityNote> {
    return this.create({
      entity_type: 'data-breach',
      entity_id: reportId,
      note: note,
      note_type: action === 'close' ? 'status_update' : 'data_breach_action',
      category: 'data_breach_management',
      metadata: {
        action,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      priority: action === 'close' ? 'high' : 'normal',
      is_internal: false,
    });
  }

  async getDataBreachNotes(reportId: string): Promise<any> {
    return this.findByEntity('data-breach', reportId);
  }
}

export const activityNotesService = new ActivityNotesService();
