"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const data_breachs_controller_1 = require("./data-breachs.controller");
const data_breachs_service_1 = require("./data-breachs.service");
const data_breachs_entity_1 = require("../entities/data-breachs.entity");
const user_entity_1 = require("../entities/user.entity");
const notifications_module_1 = require("../notifications/notifications.module");
const tasks_module_1 = require("../tasks/tasks.module");
let DataBreachModule = class DataBreachModule {
};
exports.DataBreachModule = DataBreachModule;
exports.DataBreachModule = DataBreachModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                data_breachs_entity_1.DataBreachReport,
                user_entity_1.User,
            ]),
            notifications_module_1.NotificationsModule,
            (0, common_1.forwardRef)(() => tasks_module_1.TasksModule),
        ],
        controllers: [data_breachs_controller_1.DataBreachReportController],
        providers: [data_breachs_service_1.DataBreachReportService],
        exports: [data_breachs_service_1.DataBreachReportService],
    })
], DataBreachModule);
//# sourceMappingURL=data-breachs.module.js.map