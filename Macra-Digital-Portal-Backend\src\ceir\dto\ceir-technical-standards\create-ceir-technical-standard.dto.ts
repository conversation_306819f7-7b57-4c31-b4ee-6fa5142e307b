import {
  IsS<PERSON>,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
  MaxLength,
  IsArray,
  IsIn,
  IsDateString,
  IsUrl,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { 
  STANDARD_TYPES, 
  STANDARD_STATUSES, 
  ISSUING_ORGANIZATIONS 
} from '../../entities/ceir-technical-standards.entity';

export class CreateCeirTechnicalStandardDto {
  @ApiProperty({
    description: 'Standard reference number',
    example: '3GPP TS 51.010-1',
    maxLength: 100
  })
  @IsString({ message: 'Standard reference must be a string' })
  @MaxLength(100, { message: 'Standard reference must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Standard reference is required' })
  standard_reference: string;

  @ApiProperty({
    description: 'Standard title',
    example: 'Mobile Station (MS) conformance specification',
    maxLength: 255
  })
  @IsString({ message: 'Standard title must be a string' })
  @MaxLength(255, { message: 'Standard title must not exceed 255 characters' })
  @IsNotEmpty({ message: 'Standard title is required' })
  standard_title: string;

  @ApiProperty({
    description: 'Detailed description of the standard',
    example: 'Conformance specification for mobile stations operating in GSM networks',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @ApiProperty({
    description: 'Type of standard',
    enum: STANDARD_TYPES,
    example: 'technical'
  })
  @IsString({ message: 'Standard type must be a string' })
  @IsIn(STANDARD_TYPES, { message: 'Standard type must be one of the valid standard types' })
  @IsNotEmpty({ message: 'Standard type is required' })
  standard_type: string;

  @ApiProperty({
    description: 'Organization that issued the standard',
    enum: ISSUING_ORGANIZATIONS,
    example: '3gpp'
  })
  @IsString({ message: 'Issuing organization must be a string' })
  @IsIn(ISSUING_ORGANIZATIONS, { message: 'Issuing organization must be one of the valid organizations' })
  @IsNotEmpty({ message: 'Issuing organization is required' })
  issuing_organization: string;

  @ApiProperty({
    description: 'Version of the standard',
    example: 'V16.0.0',
    maxLength: 50
  })
  @IsString({ message: 'Version must be a string' })
  @MaxLength(50, { message: 'Version must not exceed 50 characters' })
  @IsNotEmpty({ message: 'Version is required' })
  version: string;

  @ApiProperty({
    description: 'Date when the standard was published',
    example: '2020-07-01'
  })
  @IsDateString({}, { message: 'Publication date must be a valid ISO 8601 date string' })
  @IsNotEmpty({ message: 'Publication date is required' })
  publication_date: string;

  @ApiProperty({
    description: 'Date when the standard becomes effective',
    example: '2021-01-01',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Effective date must be a valid ISO 8601 date string' })
  effective_date?: string;

  @ApiProperty({
    description: 'Date when the standard expires or is superseded',
    example: '2025-12-31',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Expiry date must be a valid ISO 8601 date string' })
  expiry_date?: string;

  @ApiProperty({
    description: 'Current status of the standard',
    enum: STANDARD_STATUSES,
    example: 'active',
    default: 'active'
  })
  @IsString({ message: 'Status must be a string' })
  @IsIn(STANDARD_STATUSES, { message: 'Status must be one of the valid standard statuses' })
  status: string = 'active';

  @ApiProperty({
    description: 'Applicable frequency bands',
    type: 'array',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Applicable frequency bands must be an array' })
  @IsString({ each: true, message: 'Each frequency band must be a string' })
  applicable_frequency_bands?: string[];

  @ApiProperty({
    description: 'Equipment categories this standard applies to',
    type: 'array',
    example: ['mobile_phone', 'smartphone', 'modem'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Applicable equipment categories must be an array' })
  @IsString({ each: true, message: 'Each equipment category must be a string' })
  applicable_equipment_categories?: string[];

  @ApiProperty({
    description: 'Test methods defined in this standard',
    type: 'array',
    example: ['Conducted spurious emissions', 'Radiated spurious emissions'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Test methods must be an array' })
  @IsString({ each: true, message: 'Each test method must be a string' })
  test_methods?: string[];

  @ApiProperty({
    description: 'Compliance requirements',
    type: 'array',
    example: ['Maximum power spectral density', 'Frequency stability'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Compliance requirements must be an array' })
  @IsString({ each: true, message: 'Each compliance requirement must be a string' })
  compliance_requirements?: string[];

  @ApiProperty({
    description: 'Reference to superseding standard',
    example: '3GPP TS 51.010-2',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Superseded by must be a string' })
  @MaxLength(100, { message: 'Superseded by must not exceed 100 characters' })
  superseded_by?: string;

  @ApiProperty({
    description: 'Reference to superseded standard',
    example: '3GPP TS 51.010-0',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Supersedes must be a string' })
  @MaxLength(100, { message: 'Supersedes must not exceed 100 characters' })
  supersedes?: string;

  @ApiProperty({
    description: 'URL to the standard document',
    example: 'https://www.3gpp.org/ftp/Specs/archive/51_series/51.010-1/',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsUrl({}, { message: 'Document URL must be a valid URL' })
  @MaxLength(500, { message: 'Document URL must not exceed 500 characters' })
  document_url?: string;

  @ApiProperty({
    description: 'Whether this standard is mandatory for type approval',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is mandatory must be a boolean' })
  is_mandatory: boolean = true;

  @ApiProperty({
    description: 'Whether this standard is currently active',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  is_active: boolean = true;

  @ApiProperty({
    description: 'User ID who is creating this standard (optional, can be extracted from JWT)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided' })
  created_by?: string;
}
