import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';

export interface UploadResult {
  fileName: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  etag: string;
}

export interface UploadOptions {
  folder?: string;
  preserveOriginalName?: boolean;
  allowedMimeTypes?: string[];
  maxSize?: number;
}

@Injectable()
export class MinioService {
  private readonly logger = new Logger(MinioService.name);
  private readonly minioClient: Minio.Client;
  private readonly bucketName: string;

  constructor(private readonly configService: ConfigService) {
    const endpoint = this.configService.get<string>('MINIO_ENDPOINT');
    const port = this.configService.get<number>('MINIO_PORT');
    const accessKey = this.configService.get<string>('MINIO_ACCESS_KEY');
    const secretKey = this.configService.get<string>('MINIO_SECRET_KEY');
    const useSSL = this.configService.get<string>('MINIO_USE_SSL') === 'true';
    this.bucketName = this.configService.get<string>('MINIO_BUCKET_NAME') || 'macra-documents';

    if (!endpoint || !accessKey || !secretKey || !this.bucketName) {
      throw new Error('MinIO configuration is incomplete. Please check environment variables.');
    }

    this.minioClient = new Minio.Client({
      endPoint: endpoint,
      port: port || 9000,
      useSSL: useSSL,
      accessKey: accessKey,
      secretKey: secretKey,
    });

    this.initializeBucket();
  }

  private async initializeBucket(): Promise<void> {
    try {
      const bucketExists = await this.minioClient.bucketExists(this.bucketName);
      if (!bucketExists) {
        await this.minioClient.makeBucket(this.bucketName, 'us-east-1');
        this.logger.log(`Bucket '${this.bucketName}' created successfully`);

        // Set bucket policy for public read access
        const policy = {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: { AWS: ['*'] },
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${this.bucketName}/*`],
            },
          ],
        };

        await this.minioClient.setBucketPolicy(this.bucketName, JSON.stringify(policy));
        this.logger.log(`Bucket policy set for '${this.bucketName}'`);
      } else {
        this.logger.log(`Bucket '${this.bucketName}' already exists`);
      }
    } catch (error) {
      this.logger.error(`Failed to initialize bucket: ${error.message}`, error.stack);
    }
  }

  async uploadFile(file: Express.Multer.File, options: UploadOptions = {}): Promise<UploadResult> {
    try {
      this.validateFile(file, options);
      
      const fileName = this.generateFileName(file, options);
      const metadata = {
        'Content-Type': file.mimetype,
        'Original-Name': file.originalname,
        'Upload-Date': new Date().toISOString(),
      };

      const uploadResult = await this.minioClient.putObject(
        this.bucketName,
        fileName,
        file.buffer,
        file.size,
        metadata
      );

      const url = await this.getFileUrl(fileName);

      const result: UploadResult = {
        fileName,
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        url,
        etag: uploadResult.etag,
      };

      this.logger.log(`File uploaded successfully: ${fileName}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to upload file to storage');
    }
  }

  async getFile(fileName: string): Promise<NodeJS.ReadableStream> {
    try {
      return await this.minioClient.getObject(this.bucketName, fileName);
    } catch (error) {
      this.logger.error(`Failed to get file: ${fileName}`, error.stack);
      throw new BadRequestException('File not found');
    }
  }

  async getFileUrl(fileName: string, expiry: number = 24 * 60 * 60): Promise<string> {
    try {
      return await this.minioClient.presignedGetObject(this.bucketName, fileName, expiry);
    } catch (error) {
      this.logger.error(`Failed to generate file URL: ${fileName}`, error.stack);
      throw new InternalServerErrorException('Failed to generate file URL');
    }
  }

  async deleteFile(fileName: string): Promise<void> {
    try {
      await this.minioClient.removeObject(this.bucketName, fileName);
      this.logger.log(`File deleted successfully: ${fileName}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${fileName}`, error.stack);
      throw new InternalServerErrorException('Failed to delete file');
    }
  }

  async fileExists(fileName: string): Promise<boolean> {
    try {
      await this.minioClient.statObject(this.bucketName, fileName);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getFileInfo(fileName: string): Promise<Minio.BucketItemStat> {
    try {
      return await this.minioClient.statObject(this.bucketName, fileName);
    } catch (error) {
      this.logger.error(`Failed to get file info: ${fileName}`, error.stack);
      throw new BadRequestException('File not found');
    }
  }

  async listFiles(prefix?: string, maxKeys: number = 1000): Promise<Minio.BucketItem[]> {
    try {
      const files: Minio.BucketItem[] = [];
      const stream = this.minioClient.listObjects(this.bucketName, prefix, true);

      return new Promise((resolve, reject) => {
        stream.on('data', (obj: any) => {
          if (obj.name && obj.size !== undefined && obj.etag && obj.lastModified) {
            const bucketItem: Minio.BucketItem = {
              name: obj.name,
              size: obj.size,
              etag: obj.etag,
              lastModified: obj.lastModified
            };
            files.push(bucketItem);
            if (files.length >= maxKeys) {
              stream.destroy();
              resolve(files);
            }
          }
        });

        stream.on('end', () => resolve(files));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error('Failed to list files', error.stack);
      throw new InternalServerErrorException('Failed to list files');
    }
  }

  private validateFile(file: Express.Multer.File, options: UploadOptions): void {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB default
    if (file.size > maxSize) {
      throw new BadRequestException(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
    }

    if (options.allowedMimeTypes && options.allowedMimeTypes.length > 0) {
      if (!options.allowedMimeTypes.includes(file.mimetype)) {
        throw new BadRequestException(
          `File type not allowed. Allowed types: ${options.allowedMimeTypes.join(', ')}`
        );
      }
    }
  }

  private generateFileName(file: Express.Multer.File, options: UploadOptions): string {
    const folder = options.folder || 'documents';
    const ext = path.extname(file.originalname);

    if (options.preserveOriginalName) {
      const baseName = path.basename(file.originalname, ext);
      const timestamp = Date.now();
      return `${folder}/${baseName}-${timestamp}${ext}`;
    }

    const uniqueId = uuidv4();
    return `${folder}/${uniqueId}${ext}`;
  }

  async getBucketStats(): Promise<any> {
    try {
      const files = await this.listFiles();
      const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);

      return {
        totalFiles: files.length,
        totalSize,
        bucketName: this.bucketName,
      };
    } catch (error) {
      this.logger.error('Failed to get bucket stats', error.stack);
      throw new InternalServerErrorException('Failed to get bucket statistics');
    }
  }
}
