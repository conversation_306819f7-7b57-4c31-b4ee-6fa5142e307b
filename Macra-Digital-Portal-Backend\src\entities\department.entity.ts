import { Before<PERSON><PERSON>rt, Column, CreateDateColumn, DeleteDateColumn, Entity, Index, JoinColumn, ManyToOne, UpdateDateColumn } from "typeorm";
import { User } from "./user.entity";
import { v4 as uuidv4 } from "uuid";

@Entity('departments')
export class Department {
  findAll(arg0: { relations: string[]; }): Department[] | PromiseLike<Department[]> {
    throw new Error('Method not implemented.');
  }
  findOne(id: string, arg1: { relations: string[]; }): Department | PromiseLike<Department> {
    throw new Error('Method not implemented.');
  }
  softDelete(id: string): void | PromiseLike<void> {
    throw new Error('Method not implemented.');
  }
  restore(id: string): Department | PromiseLike<Department> {
    throw new Error('Method not implemented.');
  }
  findDeleted(arg0: { relations: string[]; }): Department[] | PromiseLike<Department[]> {
    throw new Error('Method not implemented.');
  }
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  department_id: string;

  @Column({ type: 'varchar', length: 5, unique: true })
  code: string; // SHort abbreviation for department

  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  email: string;

  @Column({ type: 'uuid', nullable: true })
  manager_id ?: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'manager_id' })
  department_manager?: User;

  @BeforeInsert()
  generateId() {
    if (!this.department_id) {
      this.department_id = uuidv4();
    }
  }
}