import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ContactPersonsService } from './contact-persons.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateContactPersonDto } from '../dto/contact-person/create-contact-person.dto';
import { UpdateContactPersonDto } from '../dto/contact-person/update-contact-person.dto';
import { ContactPersons } from '../entities/contact-persons.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('contact-persons')
@Controller('contact-persons')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ContactPersonsController {
  constructor(private readonly contactPersonsService: ContactPersonsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new contact person' })
  @ApiResponse({
    status: 201,
    description: 'Contact person created successfully',
    type: ContactPersons,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Created new contact person',
  })
  async create(
    @Body() createContactPersonDto: CreateContactPersonDto,
    @Request() req: any,
  ): Promise<ContactPersons> {
    return this.contactPersonsService.create(createContactPersonDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all contact persons with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Contact persons retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Viewed contact persons list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<ContactPersons>> {
    const result = await this.contactPersonsService.findAll(query);
    return PaginationTransformer.transform<ContactPersons>(result);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search contact persons' })
  @ApiQuery({ name: 'q', description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
    type: [ContactPersons],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Searched contact persons',
  })
  async search(@Query('q') searchTerm: string): Promise<ContactPersons[]> {
    return this.contactPersonsService.search(searchTerm);
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get contact persons by application ID' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact persons retrieved successfully',
    type: [ContactPersons],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Viewed contact persons by application',
  })
  async findByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<ContactPersons[]> {
    return this.contactPersonsService.findByApplicationId(applicationId);
  }

  @Get('application/:applicationId/grouped')
  @ApiOperation({ summary: 'Get contact persons by application ID grouped by type' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact persons retrieved and grouped successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Viewed grouped contact persons by application',
  })
  async findByApplicationGrouped(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<{
    primary: ContactPersons | null;
    secondary: ContactPersons[];
    emergency: ContactPersons[];
  }> {
    const result = await this.contactPersonsService.getContactPersonsByApplicant(applicationId);
    return {
      ...result,
      emergency: [] // Add empty emergency array for now
    };
  }

  @Get('application/:applicationId/primary')
  @ApiOperation({ summary: 'Get primary contact person by application ID' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Primary contact person retrieved successfully',
    type: ContactPersons,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Viewed primary contact person by application',
  })
  async findPrimaryByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<ContactPersons | null> {
    return this.contactPersonsService.findPrimaryByApplicationId(applicationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get contact person by ID' })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact person retrieved successfully',
    type: ContactPersons,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Viewed contact person details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ContactPersons> {
    return this.contactPersonsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update contact person' })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact person updated successfully',
    type: ContactPersons,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Updated contact person',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateContactPersonDto: UpdateContactPersonDto,
    @Request() req: any,
  ): Promise<ContactPersons> {
    return this.contactPersonsService.update(id, updateContactPersonDto, req.user.userId);
  }

  @Put(':id/set-primary')
  @ApiOperation({ summary: 'Set contact person as primary for application' })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: 200,
    description: 'Primary contact person set successfully',
    type: ContactPersons,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Set primary contact person',
  })
  async setPrimary(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { application_id: string },
    @Request() req: any,
  ): Promise<ContactPersons> {
    return this.contactPersonsService.setPrimaryContact(body.application_id, id, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete contact person' })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact person deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Deleted contact person',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.contactPersonsService.remove(id);
    return { message: 'Contact person deleted successfully' };
  }
}
