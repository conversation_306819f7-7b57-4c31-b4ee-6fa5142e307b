import { Repository } from 'typeorm';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { DataBreachReport } from 'src/entities/data-breachs.entity';
import { CreateDataBreachReportDto, UpdateDataBreachReportDto, UpdateDataBreachReportStatusDto } from 'src/dto/data-breach/data-breach-report.dto';
import { DataBreachReportResponseDto } from './data-breachs-constants';
import { NotificationHelperService } from 'src/notifications/notification-helper.service';
import { EmailTemplateService } from 'src/notifications/email-template.service';
import { User } from 'src/entities/user.entity';
import { TasksService } from 'src/tasks/tasks.service';
export declare class DataBreachReportService {
    private reportRepository;
    private userRepository;
    private notificationHelperService;
    private emailTemplateService;
    private tasksService;
    constructor(reportRepository: Repository<DataBreachReport>, userRepository: Repository<User>, notificationHelperService: NotificationHelperService, emailTemplateService: EmailTemplateService, tasksService: TasksService);
    create(createDto: CreateDataBreachReportDto, reporterId: string): Promise<DataBreachReportResponseDto>;
    findAll(query: PaginateQuery, userId: string, isStaff?: boolean): Promise<Paginated<DataBreachReport>>;
    findOne(reportId: string, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    update(reportId: string, updateDto: UpdateDataBreachReportDto, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    delete(reportId: string, userId: string, isStaff?: boolean): Promise<void>;
    updateStatus(reportId: string, statusDto: UpdateDataBreachReportStatusDto, userId: string): Promise<DataBreachReportResponseDto>;
    private createQueryBuilder;
    private applyFilters;
    private mapToResponseDto;
    private createDataBreachTask;
    private findUsersWithDataBreachRole;
    private mapSeverityToPriority;
}
