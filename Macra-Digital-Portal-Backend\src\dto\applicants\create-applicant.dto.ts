import { IsString, IsEmail, <PERSON><PERSON>ptional, <PERSON>U<PERSON><PERSON>, IsDateString, Length, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateApplicantDto {
  @IsString()
  @Length(1, 255)
  name: string;

  @IsString()
  @Length(1, 255)
  business_registration_number: string;

  @IsString()
  @Length(1, 255)
  tpin: string;

  @IsOptional()
  @IsString()
  @Length(1, 255)
  @Transform(({ value }) => value === '' ? null : value)
  website?: string;

  @IsEmail()
  @Length(1, 255)
  email: string;

  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @IsOptional()
  @IsString()
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid fax number format' })
  @Transform(({ value }) => value === '' ? null : value)
  fax?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value === '' ? null : value)
  level_of_insurance_cover?: string;

  @IsOptional()
  @IsUUID()
  address_id?: string;

  @IsOptional()
  @IsUUID()
  contact_id?: string;

  @IsDateString()
  date_incorporation: string;

  @IsString()
  @Length(1, 255)
  place_incorporation: string;
}
