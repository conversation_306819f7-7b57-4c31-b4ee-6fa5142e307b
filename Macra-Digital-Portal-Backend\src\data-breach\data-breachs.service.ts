import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import {
  DataBreachReport,
} from 'src/entities/data-breachs.entity';
import {
  CreateDataBreachReportDto,
  UpdateDataBreachReportDto,
  DataBreachReportFilterDto,
  UpdateDataBreachReportStatusDto
} from 'src/dto/data-breach/data-breach-report.dto';
import { DataBreachReportResponseDto, DataBreachStatus } from './data-breachs-constants';
import { NotificationHelperService } from 'src/notifications/notification-helper.service';
import { EmailTemplateService } from 'src/notifications/email-template.service';
import { User } from 'src/entities/user.entity';
import { TasksService } from 'src/tasks/tasks.service';
import { TaskType, TaskPriority } from 'src/entities/tasks.entity';

@Injectable()
export class DataBreachReportService {
  constructor(
    @InjectRepository(DataBreachReport)
    private reportRepository: Repository<DataBreachReport>,

    @InjectRepository(User)
    private userRepository: Repository<User>,

    private notificationHelperService: NotificationHelperService,
    private emailTemplateService: EmailTemplateService,
    private tasksService: TasksService,
  ) {}

  async create(
    createDto: CreateDataBreachReportDto,
    reporterId: string
  ): Promise<DataBreachReportResponseDto> {
    const report = this.reportRepository.create({
      ...createDto,
      incident_date: new Date(createDto.incident_date),
      reporter_id: reporterId,
      created_by: reporterId,
    });

    const savedReport = await this.reportRepository.save(report);

    // Create task for data breach handling
    try {
      await this.createDataBreachTask(savedReport, reporterId);
    } catch (taskError) {
      console.error('❌ Failed to create data breach task:', taskError);
      // Don't fail the creation if task creation fails
    }

    // Status tracking is now handled by the polymorphic ActivityNotes service
    // Initial status will be tracked when the first activity note is created

    // Send email notification to the reporter
    try {
      const reporter = await this.userRepository.findOne({
        where: { user_id: reporterId },
      });

      if (reporter && reporter.email) {
        const emailTemplate = this.emailTemplateService.generateDataBreachReportSubmittedTemplate({
          reporterName: `${reporter.first_name} ${reporter.last_name}`,
          reportNumber: savedReport.report_number || savedReport.report_id,
          reportTitle: savedReport.title,
          category: savedReport.category,
          severity: savedReport.severity,
          submissionDate: savedReport.created_at.toLocaleDateString(),
          portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/data-protection`,
        });

        // Send email notification using the notification helper service
        await this.notificationHelperService.sendEmailNotification({
          recipientId: reporterId,
          recipientEmail: reporter.email,
          recipientName: `${reporter.first_name} ${reporter.last_name}`,
          subject: emailTemplate.subject,
          message: `Your data breach report ${savedReport.report_number || savedReport.report_id} has been successfully submitted and is being processed.`,
          htmlContent: emailTemplate.html,
          entityType: 'data_breach_report',
          entityId: savedReport.report_id,
          actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/data-protection`,
          recipientType: 'CUSTOMER' as any,
          createdBy: reporterId,
          sendEmail: true,
          createInApp: true,
        });

        console.log(`✅ Email notification sent for data breach report ${savedReport.report_id}`);
      }
    } catch (error) {
      console.error('❌ Error sending email notification for data breach report:', error);
      // Don't throw error - report creation should succeed even if email fails
    }

    return this.findOne(savedReport.report_id, reporterId);
  }

  async findAll(
    query: PaginateQuery,
    userId: string,
    isStaff: boolean = false
  ): Promise<Paginated<DataBreachReport>> {
    const queryBuilder = this.reportRepository
      .createQueryBuilder('report')
      .leftJoinAndSelect('report.reporter', 'reporter')
      .leftJoinAndSelect('report.assignee', 'assignee')
      .orderBy('report.created_at', 'DESC');

    // Data isolation: customers can only see their own reports
    if (!isStaff) {
      queryBuilder.andWhere('report.reporter_id = :userId', { userId });
    }

    return paginate(query, queryBuilder, {
      sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'severity', 'incident_date'],
      searchableColumns: ['title', 'description', 'organization_involved'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        status: true,
        severity: true,
        priority: true,
      },
    });
  }

  async findOne(
    reportId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<DataBreachReportResponseDto> {
    const queryBuilder = this.createQueryBuilder()
      .where('report.report_id = :reportId', { reportId });

    // Data isolation: customers can only see their own reports
    if (!isStaff) {
      queryBuilder.andWhere('report.reporter_id = :userId', { userId });
    }

    const report = await queryBuilder.getOne();

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    return this.mapToResponseDto(report);
  }

  async update(
    reportId: string, 
    updateDto: UpdateDataBreachReportDto,
    userId: string,
    isStaff: boolean = false
  ): Promise<DataBreachReportResponseDto> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
      relations: ['reporter'],
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Data isolation: customers can only update their own reports
    if (!isStaff && report.reporter_id !== userId) {
      throw new ForbiddenException('You can only update your own reports');
    }

    // Customers can only update certain fields
    if (!isStaff) {
      const allowedFields = ['title', 'description', 'category', 'severity', 'incident_date', 'organization_involved', 'affected_data_types', 'contact_attempts'];
      const updateFields = Object.keys(updateDto);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
      }
    }

    // Status changes are now tracked by the polymorphic ActivityNotes service
    // Status change tracking will be handled when activity notes are created
    if (updateDto.status === DataBreachStatus.RESOLVED) {
      updateDto.resolved_at = new Date();
    }

    // Convert incident_date string to Date if provided
    if (updateDto.incident_date) {
      updateDto.incident_date = new Date(updateDto.incident_date) as any;
    }

    Object.assign(report, updateDto);
    report.updated_by = userId;

    await this.reportRepository.save(report);

    return this.findOne(reportId, userId, isStaff);
  }

  async delete(
    reportId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<void> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Data isolation: customers can only delete their own reports
    if (!isStaff && report.reporter_id !== userId) {
      throw new ForbiddenException('You can only delete your own reports');
    }

    // Soft delete
    await this.reportRepository.softDelete(reportId);
  }

  // Attachment management is now handled by the polymorphic Documents service
  // Use DocumentService.uploadDocument() with entity_type: 'data-breach' and entity_id: report_id

  async updateStatus(
    reportId: string,
    statusDto: UpdateDataBreachReportStatusDto,
    userId: string
  ): Promise<DataBreachReportResponseDto> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Status history is now handled by the polymorphic ActivityNotes service
    // Status change will be tracked when activity notes are created

    // Update report status
    report.status = statusDto.status;
    report.updated_by = userId;

    if (statusDto.status === DataBreachStatus.RESOLVED) {
      report.resolved_at = new Date();
    }

    await this.reportRepository.save(report);

    return this.findOne(reportId, userId, true);
  }

  // Status history is now handled by the polymorphic ActivityNotes service
  // Use ActivityNotesService.create() with entity_type: 'data-breach' and entity_id: report_id

  private createQueryBuilder(): SelectQueryBuilder<DataBreachReport> {
    return this.reportRepository
      .createQueryBuilder('report')
      .leftJoinAndSelect('report.reporter', 'reporter')
      .leftJoinAndSelect('report.assignee', 'assignee');
      // Attachments and status history are now handled by polymorphic entities
      // Documents: entity_type: 'data-breach', entity_id: report_id
      // ActivityNotes: entity_type: 'data-breach', entity_id: report_id
  }

  private applyFilters(
    queryBuilder: SelectQueryBuilder<DataBreachReport>,
    filters: Partial<DataBreachReportFilterDto>
  ): void {
    if (filters.category) {
      queryBuilder.andWhere('report.category = :category', { category: filters.category });
    }

    if (filters.severity) {
      queryBuilder.andWhere('report.severity = :severity', { severity: filters.severity });
    }

    if (filters.status) {
      queryBuilder.andWhere('report.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      queryBuilder.andWhere('report.priority = :priority', { priority: filters.priority });
    }

    if (filters.assigned_to) {
      queryBuilder.andWhere('report.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
    }

    if (filters.from_date) {
      queryBuilder.andWhere('report.created_at >= :from_date', { from_date: filters.from_date });
    }

    if (filters.to_date) {
      queryBuilder.andWhere('report.created_at <= :to_date', { to_date: filters.to_date });
    }

    if (filters.incident_from_date) {
      queryBuilder.andWhere('report.incident_date >= :incident_from_date', { incident_from_date: filters.incident_from_date });
    }

    if (filters.incident_to_date) {
      queryBuilder.andWhere('report.incident_date <= :incident_to_date', { incident_to_date: filters.incident_to_date });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(report.title ILIKE :search OR report.description ILIKE :search OR report.organization_involved ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
  }

  private mapToResponseDto(report: DataBreachReport): DataBreachReportResponseDto {
    return {
      report_id: report.report_id,
      report_number: report.report_number,
      reporter_id: report.reporter_id,
      title: report.title,
      description: report.description,
      category: report.category,
      severity: report.severity,
      status: report.status,
      priority: report.priority,
      incident_date: report.incident_date,
      organization_involved: report.organization_involved,
      respondent_reg_number: report.respondent_reg_number,
      affected_data_types: report.affected_data_types,
      contact_attempts: report.contact_attempts,
      assigned_to: report.assigned_to,
      resolution: report.resolution,
      resolved_at: report.resolved_at,
      created_at: report.created_at,
      updated_at: report.updated_at,
      reporter: report.reporter ? {
        user_id: report.reporter.user_id,
        first_name: report.reporter.first_name,
        last_name: report.reporter.last_name,
        email: report.reporter.email,
      } : undefined,
      assignee: report.assignee ? {
        user_id: report.assignee.user_id,
        first_name: report.assignee.first_name,
        last_name: report.assignee.last_name,
        email: report.assignee.email,
      } : undefined,
      // Attachments are now handled by the polymorphic Documents system
      // Use DocumentService.getDocumentsByEntity('data-breach', report_id) to get attachments
      // Status history is now handled by the polymorphic ActivityNotes system
      // Use ActivityNotesService.getByEntity('data-breach', report_id) to get status history
    };
  }

  /**
   * Create a task for data breach handling and auto-assign to staff with data_breach role
   */
  private async createDataBreachTask(report: DataBreachReport, createdBy: string): Promise<void> {
    try {
      // Find users with data_breach role
      const dataBreachStaff = await this.findUsersWithDataBreachRole();

      // Determine assignee (first available staff member with data_breach role)
      const assignedTo = dataBreachStaff.length > 0 ? dataBreachStaff[0].user_id : undefined;

      // Create task
      const taskData = {
        task_type: TaskType.DATA_BREACH,
        title: `Data Breach Report: ${report.title}`,
        description: `Review and investigate data breach report ${report.report_number || report.report_id}.\n\nCategory: ${report.category}\nSeverity: ${report.severity}\nIncident Date: ${report.incident_date.toLocaleDateString()}\nOrganization: ${report.organization_involved}`,
        priority: this.mapSeverityToPriority(report.severity),
        entity_type: 'data-breach',
        entity_id: report.report_id,
        assigned_to: assignedTo,
        metadata: {
          report_number: report.report_number,
          category: report.category,
          severity: report.severity,
          incident_date: report.incident_date.toISOString(),
          organization_involved: report.organization_involved,
        },
      };

      const task = await this.tasksService.create(taskData, createdBy);

      console.log(`✅ Data breach task created: ${task.task_id} for report ${report.report_id}`);

      if (assignedTo) {
        console.log(`✅ Task auto-assigned to data breach staff: ${assignedTo}`);
      } else {
        console.log(`⚠️ No staff with data_breach role found - task created unassigned`);
      }

    } catch (error) {
      console.error('❌ Error creating data breach task:', error);
      throw error;
    }
  }

  /**
   * Find active users with data_breach role (fallback to officer role if data_breach role doesn't exist)
   */
  private async findUsersWithDataBreachRole(): Promise<User[]> {
    try {
      // First try to find users with data_breach role
      let users = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.roles', 'roles')
        .where('roles.name = :roleName', { roleName: 'data_breach' })
        .andWhere('user.status = :status', { status: 'active' })
        .andWhere('user.email IS NOT NULL')
        .getMany();

      // If no users with data_breach role found, fallback to officer role
      if (users.length === 0) {
        console.log('⚠️ No users with data_breach role found, falling back to officer role');
        users = await this.userRepository
          .createQueryBuilder('user')
          .leftJoinAndSelect('user.roles', 'roles')
          .where('roles.name = :roleName', { roleName: 'officer' })
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.email IS NOT NULL')
          .getMany();
      }

      return users;
    } catch (error) {
      console.error('❌ Error finding users with data_breach role:', error);
      return [];
    }
  }

  /**
   * Map data breach severity to task priority
   */
  private mapSeverityToPriority(severity: string): TaskPriority {
    switch (severity?.toLowerCase()) {
      case 'critical':
      case 'high':
        return TaskPriority.HIGH;
      case 'medium':
        return TaskPriority.MEDIUM;
      case 'low':
        return TaskPriority.LOW;
      default:
        return TaskPriority.MEDIUM;
    }
  }
}
