<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New License Application - Customer Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style type="text/tailwindcss">
      @layer components {
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6 sm:items-start md:items-start lg:items-center;
        }
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-select {
          @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }

        .main-button {
          @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }

        .license-card {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1;
        }

        .license-card-icon {
          @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl;
        }

        .license-card-button {
          @apply w-full bg-primary text-white px-4 py-3 rounded-lg font-medium hover:bg-primary-subtle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 transform hover:scale-105;
        }

        .category-section {
          @apply mb-12;
        }

        .category-header {
          @apply flex items-center mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border-l-4 border-primary;
        }

        .category-icon {
          @apply w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center mr-4;
        }

        .fade-in {
          animation: fadeIn 0.6s ease-in-out;
        }

        .slide-up {
          animation: slideUp 0.8s ease-out;
        }

        .inner-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2 border-b border-gray-200 pb-4;
        }

        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }

        .sub-heading {
          @apply text-gray-700;
        }

        .card-bg {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }

      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .stagger-animation {
        animation-delay: calc(var(--stagger) * 0.1s);
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Application
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <h1 class="text-xl font-medium text-gray-900">License Application</h1>
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->

            <div class="tab-heading">
              <div>
                <h1 class="text-3xl font-bold text-gray-900">Choose License Type</h1>
                <p class="mt-2 text-gray-600">Select the license category you want to apply for and start your application process.</p>
              </div>
            </div>

            <!-- Application Process Steps -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Application Process</h2>
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Choose License</p>
                    <p class="text-xs text-gray-500">Select license type</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Fill Application</p>
                    <p class="text-xs text-gray-500">Complete required forms</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Upload Documents</p>
                    <p class="text-xs text-gray-500">Submit required files</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Review & Submit</p>
                    <p class="text-xs text-gray-500">Final submission</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- License Selection Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <!-- Application Service -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex mb-4 tab-heading">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <i class="ri-global-line text-2xl text-blue-600"></i>
                    </div>
                    <div >
                      <h3 class="text-lg font-semibold text-gray-900">Application Service</h3>
                      <p class="text-sm text-gray-500">Responsible for end-user services...</p>
                    </div>
                  </div>
                  <p class="sub-heading text-sm mb-4">
                    Authorizes reselling or procurement from Network Service operators.
                  </p>
                  <div class="space-y-2 mb-4 inner-section">
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Internet Service Providers (ISPs)</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Mobile Virtual Networks</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Payphone Services</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Telecommunication & IP Telephony</span>
                    </div>
                  </div>
                  <div class="border-b border-gray-200 pb-2 space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-700">Processing Time:</span>
                      <span class="text-sm text-gray-600">30-45 days</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-700">License Fee:</span>
                      <span class="text-sm font-semibold text-primary">MWK 30, 000, 000</span>
                    </div>
                  </div>
                  <div class="inner-section border-none">
                    <div class="relative">
                      <p class="flex-col">Individual</p>
                      <a href="applications/individual-license-B-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                    <div class="relative">
                      <p class="flex-col">ISP</p>
                      <a href="applications/isp-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content Service License -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex tab-heading mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <i class="ri-tv-line text-2xl text-red-600"></i>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">Content Service</h3>
                      <p class="text-sm text-gray-500">
                        Responsible for broadcasting
                      </p>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-4">Authorizes the provision of actual broadcasting content</p>
                  <div class="space-y-2 mb-4 inner-section">
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Satellite Broadcasting</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Terrestrial Broadcasting</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Cable Television</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Other Electronic Media</span>
                    </div>
                  </div>
                  <div class="border-b border-gray-200 pb-2 space-y-3">
                    <div class="flex items-center justify-between mb-4">
                      <span class="text-sm font-medium text-gray-700">Processing Time:</span>
                      <span class="text-sm text-gray-600">45-60 days</span>
                    </div>
                    <div class="flex items-center justify-between mb-6">
                      <span class="text-sm font-medium text-gray-700">License Fee:</span>
                      <span class="text-sm font-semibold text-primary">MWK 6, 000,000</span>
                    </div>
                  </div>
                  <div class="inner-section border-none">
                    <div class="relative">
                      <p class="flex-col">Radio</p>
                      <a href="applications/radio-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                    <div class="relative">
                      <p class="flex-col">Television</p>
                      <a href="applications/tv-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                  </div>
                  
                </div>
              </div>


              <!-- Network Facility License -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex tab-heading mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <i class="ri-global-line text-2xl text-green-600"></i>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">Network Facility Licence</h3>
                      <p class="text-sm text-gray-500">Responsible for resources</p>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-4 sub-heading">Authorizes ownership and control of electronic communications infrastructure.</p>
                  <div class="space-y-2 mb-4 inner-section">
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Earth Stations</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Public Payphone facilities</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Satellite Control Stations</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Switching Centre, Towers, Poles etc</span>
                    </div>
                  </div>
                  <div class="border-b border-gray-200 pb-2 space-y-3">
                    <div class="flex items-center justify-between mb-4">
                      <span class="text-sm font-medium text-gray-700">Processing Time:</span>
                      <span class="text-sm text-gray-600">90-120 days</span>
                    </div>
                    <div class="flex items-center justify-between mb-6">
                      <span class="text-sm font-medium text-gray-700">License Fee:</span>
                      <span class="text-sm font-semibold text-primary">MWK 2,500,000</span>
                    </div>
                  </div>
                  <div class="inner-section border-none">
                    <div class="relative">
                      <p class="flex-col">Satellites</p>
                      <a href="applications/satellite-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Network Service License -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex tab-heading mb-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <i class="ri-satellite-dish-line text-2xl text-indigo-600"></i>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">Network Service Licence</h3>
                      <p class="text-sm text-gray-500">Responsible for interconnection</p>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-4">Gives authorization to operate electronic communication networks to deliver services.</p>
                  <div class="space-y-2 mb-4 inner-section">
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Bandwidth</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Broadcasting Distribution</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Telecommunication Networks</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Access Applications & Space Segment</span>
                    </div>
                  </div>
                  <div class="border-b border-gray-200 pb-2 space-y-3">
                    <div class="flex items-center justify-between mb-4">
                      <span class="text-sm font-medium text-gray-700">Processing Time:</span>
                      <span class="text-sm text-gray-600">60-75 days</span>
                    </div>
                    <div class="flex items-center justify-between mb-6">
                      <span class="text-sm font-medium text-gray-700">License Fee:</span>
                      <span class="text-sm font-semibold text-primary">MWK 1,800,000</span>
                    </div>
                  </div>
                  <div class="inner-section border-none">
                    <div class="relative">
                      <p class="flex-col">Mobile Network</p>
                      <a href="applications/mobile-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Postal Services License -->
              <div class="card-bg">
                <div class="p-6">
                  <div class="flex tab-heading mb-4">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <i class="ri-mail-line text-2xl text-orange-600"></i>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">Postal Services</h3>
                      <p class="text-sm text-gray-500">Responsible for postal licensing</p>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-4 sub-heading">Authorizes operation of postal services including mail management services.</p>
                  <div class="space-y-2 mb-4 inner-section">
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Mail collection & delivery</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Courier services</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>Package handling</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                      <i class="ri-check-line text-green-500 mr-2"></i>
                      <span>International Courier</span>
                    </div>
                  </div>
                  <div class="border-b border-gray-200 pb-2 space-y-3">
                    <div class="flex items-center justify-between mb-4">
                      <span class="text-sm font-medium text-gray-700">Processing Time:</span>
                      <span class="text-sm text-gray-600">30-45 days</span>
                    </div>
                    <div class="flex items-center justify-between mb-6">
                      <span class="text-sm font-medium text-gray-700">License Fee:</span>
                      <span class="text-sm font-semibold text-primary">MWK 3, 000,000</span>
                    </div>
                  </div>
                  <div class="inner-section">
                    <div class="relative">
                      <p class="flex-col">Postal</p>
                      <a href="applications/postal-license-application.html" class="btn-active-primary"
                      role="button">Apply Now</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Requirements Information -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-file-list-line mr-2 text-primary"></i>
                  General Requirements
                </h3>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Valid Business Registration</p>
                      <p class="text-xs text-gray-500">Certificate of incorporation or business registration</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Technical Specifications</p>
                      <p class="text-xs text-gray-500">Detailed technical documentation and equipment specifications</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Financial Statements</p>
                      <p class="text-xs text-gray-500">Audited financial statements for the last 2 years</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Compliance Declaration</p>
                      <p class="text-xs text-gray-500">Declaration of compliance with MACRA regulations</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Support Information -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-customer-service-line mr-2 text-primary"></i>
                  Need Help?
                </h3>
                <div class="space-y-4">
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-phone-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Phone Support</p>
                      <p class="text-xs text-gray-500">+265 1 770 100</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-mail-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Email Support</p>
                      <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <i class="ri-time-line text-primary mr-3"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Office Hours</p>
                      <p class="text-xs text-gray-500">Mon-Fri, 8:00 AM - 5:00 PM</p>
                    </div>
                  </div>
                  <a href="help-center.html" class="block w-full text-center bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-90 transition-all">
                    Visit Help Center
                  </a>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // License application function
      function startApplication(licenseType) {
        // Store the selected license type
        localStorage.setItem('selectedLicenseType', licenseType);

        // Show confirmation modal or redirect to application form
        const licenseNames = {
          'isp': 'Internet Service Provider',
          'radio': 'Radio Broadcasting',
          'tv': 'Television Broadcasting',
          'indivitual': 'Mobile Network',
          'satellite': 'Satellite Communications',
          'postal': 'Postal Services'
        };

        const applicationUrls = {
          'isp': 'applications/isp-license-application.html',
          'radio': 'applications/radio-license-application.html',
          'tv': 'applications/university-radio-license-application.html',
          'indivitual': 'applications/individual-license-A-application.html',
          'satellite': 'applications/satellite-license-application.html',
          'postal': 'applications/postal-license-application.html'
        };

        const licenseName = licenseNames[licenseType];

        if (confirm(`You have selected ${licenseName} License. Do you want to proceed with the application?`)) {
          // Redirect to specific application form
          const applicationUrl = applicationUrls[licenseType];
          if (applicationUrl) {
            window.location.href = applicationUrl;
          } else {
            alert(`Application form for ${licenseName} License is coming soon.`);
          }
        }
      }
    </script>
  </body>
</html>