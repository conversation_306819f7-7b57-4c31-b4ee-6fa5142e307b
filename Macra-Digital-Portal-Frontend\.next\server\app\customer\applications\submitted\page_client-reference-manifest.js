globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/customer/applications/submitted/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientWrapper.tsx":{"*":{"id":"(ssr)/./src/components/ClientWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/LoadingOptimizer.tsx":{"*":{"id":"(ssr)/./src/components/LoadingOptimizer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/payments/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/payments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/layout.tsx":{"*":{"id":"(ssr)/./src/app/tasks/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/page.tsx":{"*":{"id":"(ssr)/./src/app/tasks/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/documentations/page.tsx":{"*":{"id":"(ssr)/./src/app/documentations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/[licenseTypeId]/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/[licenseTypeId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/applicant-info/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/applicant-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/address-info/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/address-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/contact-info/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/contact-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/legal-history/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/legal-history/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/documents/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/submit/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/apply/submit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/submitted/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/applications/submitted/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/applications/layout.tsx":{"*":{"id":"(ssr)/./src/app/applications/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/applications/page.tsx":{"*":{"id":"(ssr)/./src/app/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/consumer-affairs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/data-protection/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/data-protection/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/[taskId]/page.tsx":{"*":{"id":"(ssr)/./src/app/tasks/[taskId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/layout.tsx":{"*":{"id":"(ssr)/./src/app/users/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/page.tsx":{"*":{"id":"(ssr)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/documents/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/resources/layout.tsx":{"*":{"id":"(ssr)/./src/app/resources/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/resources/page.tsx":{"*":{"id":"(ssr)/./src/app/resources/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/audit-trail/layout.tsx":{"*":{"id":"(ssr)/./src/app/audit-trail/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/audit-trail/page.tsx":{"*":{"id":"(ssr)/./src/app/audit-trail/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/my-licenses/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/my-licenses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/lodge-complaint/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/consumer-affairs/lodge-complaint/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/consumer-affairs/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\components\\ClientWrapper.tsx":{"id":"(app-pages-browser)/./src/components/ClientWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\components\\LoadingOptimizer.tsx":{"id":"(app-pages-browser)/./src/components/LoadingOptimizer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\styles\\form-fixes.css":{"id":"(app-pages-browser)/./src/styles/form-fixes.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\payments\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/payments/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\tasks\\layout.tsx":{"id":"(app-pages-browser)/./src/app/tasks/layout.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\tasks\\page.tsx":{"id":"(app-pages-browser)/./src/app/tasks/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/auth/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/page.tsx","name":"*","chunks":["app/customer/page","static/chunks/app/customer/page.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\documentations\\page.tsx":{"id":"(app-pages-browser)/./src/app/documentations/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/page.tsx","name":"*","chunks":["app/customer/applications/page","static/chunks/app/customer/applications/page.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\[licenseTypeId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/[licenseTypeId]/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/applicant-info/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\address-info\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/address-info/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\contact-info\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/contact-info/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\legal-history\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/legal-history/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\documents\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/documents/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\submit\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/apply/submit/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\submitted\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/applications/submitted/page.tsx","name":"*","chunks":["app/customer/applications/submitted/page","static/chunks/app/customer/applications/submitted/page.js"],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\layout.tsx":{"id":"(app-pages-browser)/./src/app/applications/layout.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\applications\\page.tsx":{"id":"(app-pages-browser)/./src/app/applications/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/profile/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\consumer-affairs\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/consumer-affairs/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\data-protection\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/data-protection/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\tasks\\[taskId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/tasks/[taskId]/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\users\\layout.tsx":{"id":"(app-pages-browser)/./src/app/users/layout.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\users\\page.tsx":{"id":"(app-pages-browser)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\documents\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/documents/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\resources\\layout.tsx":{"id":"(app-pages-browser)/./src/app/resources/layout.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\resources\\page.tsx":{"id":"(app-pages-browser)/./src/app/resources/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\layout.tsx":{"id":"(app-pages-browser)/./src/app/audit-trail/layout.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\audit-trail\\page.tsx":{"id":"(app-pages-browser)/./src/app/audit-trail/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\my-licenses\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/my-licenses/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\consumer-affairs\\lodge-complaint\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/consumer-affairs/lodge-complaint/page.tsx","name":"*","chunks":[],"async":false},"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\consumer-affairs\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/consumer-affairs/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\":[],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\page":[],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout":[],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\page":[{"inlined":false,"path":"static/css/app/customer/page.css"}],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\page":[{"inlined":false,"path":"static/css/app/customer/applications/page.css"}],"D:\\Memory Business Solutioins\\Projects\\MACRA\\project\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\submitted\\page":[{"inlined":false,"path":"static/css/app/customer/applications/submitted/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientWrapper.tsx":{"*":{"id":"(rsc)/./src/components/ClientWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/LoadingOptimizer.tsx":{"*":{"id":"(rsc)/./src/components/LoadingOptimizer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/form-fixes.css":{"*":{"id":"(rsc)/./src/styles/form-fixes.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/payments/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/payments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/layout.tsx":{"*":{"id":"(rsc)/./src/app/tasks/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/page.tsx":{"*":{"id":"(rsc)/./src/app/tasks/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/documentations/page.tsx":{"*":{"id":"(rsc)/./src/app/documentations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/[licenseTypeId]/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/[licenseTypeId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/applicant-info/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/applicant-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/address-info/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/address-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/contact-info/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/contact-info/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/legal-history/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/legal-history/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/documents/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/apply/submit/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/apply/submit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/applications/submitted/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/applications/submitted/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/applications/layout.tsx":{"*":{"id":"(rsc)/./src/app/applications/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/applications/page.tsx":{"*":{"id":"(rsc)/./src/app/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/profile/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/consumer-affairs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/data-protection/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/data-protection/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/tasks/[taskId]/page.tsx":{"*":{"id":"(rsc)/./src/app/tasks/[taskId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/layout.tsx":{"*":{"id":"(rsc)/./src/app/users/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/page.tsx":{"*":{"id":"(rsc)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/documents/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/resources/layout.tsx":{"*":{"id":"(rsc)/./src/app/resources/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/resources/page.tsx":{"*":{"id":"(rsc)/./src/app/resources/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/audit-trail/layout.tsx":{"*":{"id":"(rsc)/./src/app/audit-trail/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/audit-trail/page.tsx":{"*":{"id":"(rsc)/./src/app/audit-trail/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/my-licenses/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/my-licenses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/lodge-complaint/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/consumer-affairs/lodge-complaint/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/consumer-affairs/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/customer/consumer-affairs/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}