<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Customer Dashboard - Digital Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }

      .license-card {
        transition: transform 0.3s ease;
      }

      .license-card:hover {
        transform: translateY(-4px);
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }

      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }

      .side-nav::-webkit-scrollbar {
        display: none;
      }

      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      .tab-button {
        position: relative;
        z-index: 1;
      }

      .tab-button.active {
        color: #e02b20;
        font-weight: 500;
      }

      .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e02b20;
      }

      .tab-content {
        display: block;
      }

      .tab-content.hidden {
        display: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Applications
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                    placeholder="Search for licenses or applications..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <div>
                  <h1 class="text-2xl font-semibold text-gray-900">Welcome, John!</h1>
                  <p class="mt-1 text-sm text-gray-500">
                    Manage your licenses and applications from your personal dashboard.
                  </p>
                </div>
                <div>
                  <a
                    href="new-application.html"
                    class="inline-flex items-center px-4 py-3 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-add-line"></i>
                    </div>
                    New License Application
                  </a>
                </div>
              </div>
            </div>

            <!-- Status Cards -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
              <!-- Active Licenses Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                      <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Licenses</dt>
                        <dd>
                          <div class="text-lg font-medium text-gray-900">3</div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="my-licenses.html" class="font-medium text-primary hover:text-primary">View all</a>
                  </div>
                </div>
              </div>

              <!-- Pending Applications Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                      <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Pending Applications</dt>
                        <dd>
                          <div class="text-lg font-medium text-gray-900">2</div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="license-applications.html" class="font-medium text-primary hover:text-primary">View all</a>
                  </div>
                </div>
              </div>

              <!-- Expiring Soon Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-orange-100 rounded-md p-3">
                      <svg class="h-6 w-6 text-orange-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                        <dd>
                          <div class="text-lg font-medium text-gray-900">1</div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="my-licenses.html?filter=expiring" class="font-medium text-primary hover:text-primary">View all</a>
                  </div>
                </div>
              </div>

              <!-- Payments Due Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                      <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Payments Due</dt>
                        <dd>
                          <div class="text-lg font-medium text-gray-900">MK1,250</div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="payments.html" class="font-medium text-primary hover:text-primary">View all</a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main dashboard content -->
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
              <!-- Left Column -->
              <div class="lg:col-span-2 space-y-6">


                <!-- My Licenses -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                  <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="text-lg font-medium text-gray-900">My Licenses</h3>
                      <a href="my-licenses.html" class="text-sm font-medium text-primary hover:text-primary">
                        View all
                      </a>
                    </div>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <!-- License Card 1 -->
                      <div class="license-card bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div class="p-4">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center">
                              <div class="w-10 h-10 flex items-center justify-center rounded-full bg-green-100">
                                <div class="w-5 h-5 flex items-center justify-center text-green-600">
                                  <i class="ri-verified-badge-line"></i>
                                </div>
                              </div>
                              <div class="ml-3">
                                <h3 class="text-sm font-medium text-gray-900">Broadcasting License</h3>
                                <p class="text-xs text-gray-500">LIC-2022-0587</p>
                              </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Active
                            </span>
                          </div>
                          <div class="mt-3 grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span class="text-gray-500">Issue Date</span>
                              <p class="font-medium text-gray-900 mt-1">Jan 15, 2022</p>
                            </div>
                            <div>
                              <span class="text-gray-500">Expiration</span>
                              <p class="font-medium text-gray-900 mt-1">Jan 14, 2025</p>
                            </div>
                          </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-2 flex justify-between">
                          <a href="license-details.html?id=LIC-2022-0587" class="text-xs font-medium text-primary hover:text-primary">
                            View details
                          </a>
                          <a href="renew-license.html?id=LIC-2022-0587" class="text-xs font-medium text-gray-500 hover:text-gray-700">
                            Renew
                          </a>
                        </div>
                      </div>

                      <!-- License Card 2 -->
                      <div class="license-card bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div class="p-4">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center">
                              <div class="w-10 h-10 flex items-center justify-center rounded-full bg-orange-100">
                                <div class="w-5 h-5 flex items-center justify-center text-orange-600">
                                  <i class="ri-verified-badge-line"></i>
                                </div>
                              </div>
                              <div class="ml-3">
                                <h3 class="text-sm font-medium text-gray-900">Telecommunications License</h3>
                                <p class="text-xs text-gray-500">LIC-2021-1245</p>
                              </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              Expiring Soon
                            </span>
                          </div>
                          <div class="mt-3 grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span class="text-gray-500">Issue Date</span>
                              <p class="font-medium text-gray-900 mt-1">Jun 10, 2021</p>
                            </div>
                            <div>
                              <span class="text-gray-500">Expiration</span>
                              <p class="font-medium text-gray-900 mt-1">Jun 9, 2023</p>
                            </div>
                          </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-2 flex justify-between">
                          <a href="license-details.html?id=LIC-2021-1245" class="text-xs font-medium text-primary hover:text-primary">
                            View details
                          </a>
                          <a href="renew-license.html?id=LIC-2021-1245" class="text-xs font-medium text-gray-500 hover:text-gray-700">
                            Renew
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="space-y-6">
                <!-- Application Process -->
                <div class="bg-white rounded-lg shadow">
                  <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">License Application Process</h3>
                    <div class="space-y-4">
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary bg-opacity-10 text-primary">
                            1
                          </div>
                        </div>
                        <div class="ml-4">
                          <h4 class="text-sm font-medium text-gray-900">Submit Application</h4>
                          <p class="mt-1 text-sm text-gray-500">Fill out the application form with your details and submit required documents.</p>
                        </div>
                      </div>
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary bg-opacity-10 text-primary">
                            2
                          </div>
                        </div>
                        <div class="ml-4">
                          <h4 class="text-sm font-medium text-gray-900">Application Review</h4>
                          <p class="mt-1 text-sm text-gray-500">Our team reviews your application and may request additional information if needed.</p>
                        </div>
                      </div>
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary bg-opacity-10 text-primary">
                            3
                          </div>
                        </div>
                        <div class="ml-4">
                          <h4 class="text-sm font-medium text-gray-900">Payment</h4>
                          <p class="mt-1 text-sm text-gray-500">Once approved, you'll receive an invoice for the license fee that must be paid.</p>
                        </div>
                      </div>
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary bg-opacity-10 text-primary">
                            4
                          </div>
                        </div>
                        <div class="ml-4">
                          <h4 class="text-sm font-medium text-gray-900">License Issuance</h4>
                          <p class="mt-1 text-sm text-gray-500">After payment confirmation, your license will be issued and available for download.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Upcoming Payments -->
                <div class="bg-white rounded-lg shadow">
                  <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Upcoming Payments</h3>
                    <div class="space-y-4">

                      <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center">
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                              <i class="ri-calendar-line"></i>
                            </div>
                            <div class="ml-3">
                              <p class="text-sm font-medium text-gray-900">Renewal Fee: LIC-2022-0587</p>
                              <p class="text-xs text-gray-500">Due in 30 days</p>
                            </div>
                          </div>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            MK500
                          </span>
                        </div>
                        <div class="mt-2 text-right">
                          <a href="make-payment.html?id=INV-2023-0092" class="text-xs font-medium text-primary hover:text-primary">
                            Pay Now
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="mt-4">
                      <a href="payments.html" class="text-sm font-medium text-primary hover:text-primary">
                        View all payments
                        <span aria-hidden="true"> &rarr;</span>
                      </a>
                    </div>
                  </div>
                </div>


              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>