<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment History & Status Report - MACRA</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .payment-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .summary-card.total { border-left: 4px solid #007bff; }
        .summary-card.paid { border-left: 4px solid #28a745; }
        .summary-card.pending { border-left: 4px solid #ffc107; }
        .summary-card.overdue { border-left: 4px solid #dc3545; }
        .summary-card.refunded { border-left: 4px solid #6c757d; }
        
        .summary-amount {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .summary-card.total .summary-amount { color: #007bff; }
        .summary-card.paid .summary-amount { color: #28a745; }
        .summary-card.pending .summary-amount { color: #ffc107; }
        .summary-card.overdue .summary-amount { color: #dc3545; }
        .summary-card.refunded .summary-amount { color: #6c757d; }
        
        .summary-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .amount {
            font-weight: bold;
            text-align: right;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-overdue { background: #f8d7da; color: #721c24; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f5c6cb; color: #721c24; }
        .status-refunded { background: #e2e3e5; color: #383d41; }
        
        .method-bank { background: #e7f3ff; color: #0066cc; }

        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            position: relative;
        }

        canvas {
            height: 250px !important;
        }

        .chart-container h3 {
            margin-top: 15px;
            color: #333;
            text-align: center;
        }

        .chart-placeholder {
            width: 100%;
            height: 350px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            canvas { max-width: 100%; height: auto; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Payment History & Status Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> August 11, 2025, 04:45 PM CAT</div>
                <div><strong>Period:</strong> November 1-30, 2024</div>
                <div><strong>Generated By:</strong> Finance Department</div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="payment-summary">
            <div class="summary-card total">
                <div class="summary-amount">MWK 2.8B</div>
                <div class="summary-label">Total Invoiced</div>
            </div>
            <div class="summary-card paid">
                <div class="summary-amount">MWK 2.3B</div>
                <div class="summary-label">Payments Received</div>
            </div>
            <div class="summary-card pending">
                <div class="summary-amount">MWK 385M</div>
                <div class="summary-label">Pending Payments</div>
            </div>
            <div class="summary-card overdue">
                <div class="summary-amount">MWK 125M</div>
                <div class="summary-label">Overdue Payments</div>
            </div>
            <div class="summary-card refunded">
                <div class="summary-amount">MWK 15M</div>
                <div class="summary-label">Refunds Issued</div>
            </div>
        </div>

        <!-- Payment Transaction Trends -->
        <div class="section">
            <h2 class="section-title">Payment Transaction Trends</h2>
            <div class="chart-container">
                <h3 style="color: #333; margin-bottom: 15px; text-align: center;">Daily Payment Volume & Status</h3>
                <!-- Simple Area Chart Mockup -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <div style="position: relative; height: 200px; border-left: 2px solid #dee2e6; border-bottom: 2px solid #dee2e6;">
                        <!-- Y-axis labels -->
                        <div style="position: absolute; left: -40px; top: 0; font-size: 12px; color: #666;">100M</div>
                        <div style="position: absolute; left: -40px; top: 50px; font-size: 12px; color: #666;">75M</div>
                        <div style="position: absolute; left: -40px; top: 100px; font-size: 12px; color: #666;">50M</div>
                        <div style="position: absolute; left: -40px; top: 150px; font-size: 12px; color: #666;">25M</div>
                        <div style="position: absolute; left: -40px; top: 190px; font-size: 12px; color: #666;">0</div>
                        <!-- Stacked Area Chart -->
                        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                            <!-- Paid area (largest) -->
                            <polygon points="0,190 50,40 100,35 150,30 200,25 250,20 300,15 350,10 400,190"
                                    fill="#28a745" opacity="0.7"/>
                            <!-- Pending area -->
                            <polygon points="0,190 50,160 100,155 150,150 200,145 250,140 300,135 350,130 400,190"
                                    fill="#ffc107" opacity="0.7"/>
                            <!-- Processing area -->
                            <polygon points="0,190 50,180 100,178 150,176 200,174 250,172 300,170 350,168 400,190"
                                    fill="#17a2b8" opacity="0.7"/>
                            <!-- Failed area (smallest) -->
                            <polygon points="0,190 50,185 100,184 150,183 200,182 250,181 300,180 350,179 400,190"
                                    fill="#dc3545" opacity="0.7"/>
                        </svg>
                        <!-- X-axis labels -->
                        <div style="position: absolute; bottom: -25px; left: 30px; font-size: 12px; color: #666;">Week 1</div>
                        <div style="position: absolute; bottom: -25px; left: 120px; font-size: 12px; color: #666;">Week 2</div>
                        <div style="position: absolute; bottom: -25px; left: 210px; font-size: 12px; color: #666;">Week 3</div>
                        <div style="position: absolute; bottom: -25px; left: 300px; font-size: 12px; color: #666;">Week 4</div>
                    </div>
                </div>
                <!-- Legend and Stats -->
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; margin-top: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #28a745;"></div>
                        <span style="font-size: 14px;">Paid (MWK 2.3B)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #ffc107;"></div>
                        <span style="font-size: 14px;">Pending (MWK 385M)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #17a2b8;"></div>
                        <span style="font-size: 14px;">Processing (MWK 45M)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #dc3545;"></div>
                        <span style="font-size: 14px;">Failed (MWK 25M)</span>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #333;">
                    Total Transactions: <strong>313</strong> | Success Rate: <strong>94.2%</strong>
                </div>
            </div>
        </div>

        <!-- Payment Status by License Type -->
        <div class="section">
            <h2 class="section-title">Collection Performance by License Type</h2>
            <div class="chart-container">
                <canvas id="collectionPerformanceChart"></canvas>
                <h3>Collection Rates by License Type</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                        <div style="font-weight: bold; color: #007bff;">Telecommunications</div>
                        <div style="font-size: 14px; margin: 5px 0;">MWK 1.25B invoiced</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">88.0%</div>
                        <div style="font-size: 12px; color: #666;">collection rate</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="font-weight: bold; color: #28a745;">Broadcasting</div>
                        <div style="font-size: 14px; margin: 5px 0;">MWK 850M invoiced</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">91.8%</div>
                        <div style="font-size: 12px; color: #666;">collection rate</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <div style="font-weight: bold; color: #ffc107;">Postal Services</div>
                        <div style="font-size: 14px; margin: 5px 0;">MWK 420M invoiced</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">91.7%</div>
                        <div style="font-size: 12px; color: #666;">collection rate</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <div style="font-weight: bold; color: #dc3545;">Standards</div>
                        <div style="font-size: 14px; margin: 5px 0;">MWK 280M invoiced</div>
                        <div style="font-size: 18px; color: #ffc107; margin: 5px 0;">83.9%</div>
                        <div style="font-size: 12px; color: #666;">collection rate</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overdue Payments Analysis -->
        <div class="section">
            <h2 class="section-title">Overdue Payments by Age</h2>
            <div class="chart-container">
                <h3 style="color: #333; margin-bottom: 15px; text-align: center;">Overdue Payment Age Distribution</h3>
                <!-- Simple Donut Chart Mockup -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; display: flex; justify-content: center; align-items: center;">
                    <div style="position: relative; width: 200px; height: 200px;">
                        <!-- Donut Chart using CSS -->
                        <svg width="200" height="200" style="transform: rotate(-90deg);">
                            <!-- 1-30 days: 52% (187.2 degrees) -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#ffc107" stroke-width="30"
                                   stroke-dasharray="187.2 172.8" stroke-dashoffset="0"/>
                            <!-- 31-60 days: 28% (100.8 degrees) -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#fd7e14" stroke-width="30"
                                   stroke-dasharray="100.8 259.2" stroke-dashoffset="-187.2"/>
                            <!-- 61-90 days: 12% (43.2 degrees) -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#dc3545" stroke-width="30"
                                   stroke-dasharray="43.2 316.8" stroke-dashoffset="-288"/>
                            <!-- 90+ days: 8% (28.8 degrees) -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#6f42c1" stroke-width="30"
                                   stroke-dasharray="28.8 331.2" stroke-dashoffset="-331.2"/>
                        </svg>
                        <!-- Center text -->
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                            <div style="font-size: 18px; font-weight: bold; color: #333;">MWK 125M</div>
                            <div style="font-size: 12px; color: #666;">Total Overdue</div>
                            <div style="font-size: 12px; color: #666;">59 invoices</div>
                        </div>
                    </div>
                </div>
                <!-- Legend -->
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #ffc107; border-radius: 50%;"></div>
                        <span style="font-size: 14px;">1-30 days (52.0%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #fd7e14; border-radius: 50%;"></div>
                        <span style="font-size: 14px;">31-60 days (28.0%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #dc3545; border-radius: 50%;"></div>
                        <span style="font-size: 14px;">61-90 days (12.0%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #6f42c1; border-radius: 50%;"></div>
                        <span style="font-size: 14px;">90+ days (8.0%)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Trends Chart -->
        <div class="section">
            <h2 class="section-title">Monthly Payment Trends</h2>
            <div class="chart-container">
                <div>
                    <h3>Payment Collection Trends</h3>
                    <p>Chart visualization would be displayed here</p>
                    <p style="margin-top: 20px; font-size: 14px;">
                        September: MWK 2.1B | October: MWK 2.4B | November: MWK 2.3B
                    </p>
                    <p style="color: #28a745; font-weight: bold;">Collection rate: 82.1% (Target: 85%)</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This payment report is confidential and intended for authorized finance personnel only.</p>
        </div>
    </div>

    <script>
        // Collection Performance Chart (Stacked Bar Chart)
        const collectionPerformanceChart = new Chart(document.getElementById('collectionPerformanceChart'), {
            type: 'bar',
            data: {
                labels: ['Telecommunications', 'Broadcasting', 'Postal Services', 'Standards'],
                datasets: [
                    {
                        label: 'Paid',
                        data: [1100, 780.3, 385.14, 234.92],
                        backgroundColor: '#28a745',
                        borderColor: '#28a745',
                        borderWidth: 1
                    },
                    {
                        label: 'Pending',
                        data: [112.5, 51, 25.2, 33.6],
                        backgroundColor: '#ffc107',
                        borderColor: '#ffc107',
                        borderWidth: 1
                    },
                    {
                        label: 'Overdue',
                        data: [37.5, 18.7, 9.66, 11.48],
                        backgroundColor: '#dc3545',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true,
                        title: {
                            display: true,
                            text: 'License Type',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (MWK Millions)',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            stepSize: 250
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: MWK ${context.raw}M`;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>