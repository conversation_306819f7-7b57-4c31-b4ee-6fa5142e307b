<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MACRA Portal Reports - Sample Templates</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 32px;
            margin: 0 auto 20px;
        }
        
        .header h1 {
            color: #dc3545;
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 18px;
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .report-category {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .report-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }
        
        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .category-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }
        
        .applications .category-icon { background: #007bff; }
        .financial .category-icon { background: #28a745; }
        .analytics .category-icon { background: #17a2b8; }
        
        .category-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .category-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .report-list {
            list-style: none;
        }
        
        .report-item {
            margin-bottom: 12px;
        }
        
        .report-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .report-link:hover {
            background: #e9ecef;
            border-color: #dc3545;
            color: #dc3545;
            transform: translateX(5px);
        }
        
        .report-icon {
            margin-right: 12px;
            font-size: 18px;
            color: #dc3545;
        }
        
        .report-name {
            font-weight: 500;
            flex-grow: 1;
        }
        
        .report-arrow {
            color: #999;
            font-size: 16px;
        }
        
        .info-section {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-section h3 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .info-section p {
            color: #333;
            margin-bottom: 10px;
        }
        
        .footer {
            text-align: center;
            padding: 30px 0;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .category-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">M</div>
            <h1>MACRA Portal Reports</h1>
            <p>Sample HTML Report Templates</p>
        </div>

        <!-- Information Section -->
        <div class="info-section">
            <h3>📋 About These Reports</h3>
            <p>These are sample HTML report templates for the MACRA Digital Portal featuring visual chart representations instead of data tables. Each report demonstrates professional formatting, responsive design, and MACRA branding.</p>
            <p><strong>Features:</strong> Chart-based visualizations, professional styling, responsive design, print-friendly layouts, MACRA color scheme, and interactive graph placeholders ready for chart library integration.</p>
        </div>

        <!-- Reports Grid -->
        <div class="reports-grid">
            <!-- Application Reports -->
            <div class="report-category applications">
                <div class="category-header">
                    <div class="category-icon">📊</div>
                    <div class="category-title">Application Reports</div>
                </div>
                <div class="category-description">
                    Comprehensive reports on application processing, status tracking, and performance analytics.
                </div>
                <ul class="report-list">
                    <li class="report-item">
                        <a href="application-status-summary.html" class="report-link">
                            <span class="report-icon">📈</span>
                            <span class="report-name">Application Status Summary</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                    <li class="report-item">
                        <a href="processing-time-analytics.html" class="report-link">
                            <span class="report-icon">⏱️</span>
                            <span class="report-name">Processing Time Analytics</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                    <li class="report-item">
                        <a href="application-success-rates.html" class="report-link">
                            <span class="report-icon">✅</span>
                            <span class="report-name">Application Success Rates</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Financial Reports -->
            <div class="report-category financial">
                <div class="category-header">
                    <div class="category-icon">💰</div>
                    <div class="category-title">Financial Reports</div>
                </div>
                <div class="category-description">
                    Detailed financial analytics including payment tracking, revenue analysis, and fee calculations.
                </div>
                <ul class="report-list">
                    <li class="report-item">
                        <a href="payment-history-status.html" class="report-link">
                            <span class="report-icon">💳</span>
                            <span class="report-name">Payment History & Status</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                    <li class="report-item">
                        <a href="financial-revenue-analytics.html" class="report-link">
                            <span class="report-icon">📊</span>
                            <span class="report-name">Revenue Analytics (Staff Only)</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Usage Analytics -->
            <div class="report-category analytics">
                <div class="category-header">
                    <div class="category-icon">📈</div>
                    <div class="category-title">Usage Analytics</div>
                </div>
                <div class="category-description">
                    Portal usage statistics, user activity tracking, and system performance metrics.
                </div>
                <ul class="report-list">
                    <li class="report-item">
                        <a href="usage-analytics.html" class="report-link">
                            <span class="report-icon">👥</span>
                            <span class="report-name">Portal Usage Analytics</span>
                            <span class="report-arrow">→</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Technical Information -->
        <div class="info-section">
            <h3>🔧 Technical Details</h3>
            <p><strong>Chart Integration:</strong> Reports feature canvas placeholders ready for chart libraries like Chart.js, D3.js, or similar visualization tools.</p>
            <p><strong>Visual Design:</strong> Data is presented through interactive chart mockups with color-coded legends and summary statistics.</p>
            <p><strong>Responsive:</strong> Chart containers adapt to different screen sizes and are optimized for both desktop and mobile viewing.</p>
            <p><strong>Print-Ready:</strong> Special print styles ensure chart visualizations look professional when printed or saved as PDF.</p>
            <p><strong>MACRA Branding:</strong> Consistent use of MACRA colors (#dc3545 primary red) throughout all chart elements and typography.</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>These are sample report templates for demonstration purposes.</p>
        </div>
    </div>
</body>
</html>
