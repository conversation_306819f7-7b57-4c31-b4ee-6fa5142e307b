import { IsString, Matches, IsUUID, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty } from 'class-validator';

export class TwoFactorDto {

  @IsUUID()
  user_id: string;

  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'Two-factor code must be 6 digits',
  })
  code: string;

  @IsString()
  unique: string;


}

export class RequestTwoFactorDto {
  @IsUUID(4, { message: 'User ID not be valid!' })
  @IsNotEmpty({ message: 'User ID is required' })
  user_id: string;
}

