import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/useToast';
import communicationService from '@/services/communicationService';
import {
  SendMessageData,
  Message,
  MessageRecipient,
  MessageFilter,
  MessageTemplate,
  CommunicationStats,
  NotificationSettings,
  BulkMessageOperation
} from '@/types/communication';

export const useSendMessage = () => {
  const [isSending, setIsSending] = useState(false);
  const [recipients, setRecipients] = useState<MessageRecipient[]>([]);
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const { showSuccess, showError } = useToast();

  // Load available recipients
  const loadRecipients = useCallback(async () => {
    try {
      setLoading(true);
      const recipientList = await communicationService.getAvailableRecipients();
      setRecipients(recipientList);
    } catch (error) {
      console.error('Error loading recipients:', error);
      showError('Failed to load recipients');
    } finally {
      setLoading(false);
    }
  }, [showError]);

  // Load message templates
  const loadTemplates = useCallback(async () => {
    try {
      const templateList = await communicationService.getTemplates();
      setTemplates(templateList);
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  }, []);

  // Send message function
  const sendMessage = useCallback(async (messageData: SendMessageData) => {
    try {
      setIsSending(true);
      const result = await communicationService.sendMessage(messageData);
      
      if (result.status === 'sent') {
        showSuccess('Message sent successfully!');
      } else if (result.status === 'scheduled') {
        showSuccess('Message scheduled successfully!');
      } else {
        showError(result.error || 'Failed to send message');
      }
      
      return result;
    } catch (error: any) {
      console.error('Error sending message:', error);
      showError(error.message || 'Failed to send message');
      throw error;
    } finally {
      setIsSending(false);
    }
  }, [showSuccess, showError]);

  // Initialize data on mount
  useEffect(() => {
    loadRecipients();
    loadTemplates();
  }, [loadRecipients, loadTemplates]);

  return {
    sendMessage,
    recipients,
    templates,
    isSending,
    loading,
    loadRecipients,
    loadTemplates
  };
};

export const useMessages = (initialFilter?: MessageFilter) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<MessageFilter>(initialFilter || {});
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalCount: 0,
    hasMore: false
  });
  const { showSuccess, showError } = useToast();

  // Load messages
  const loadMessages = useCallback(async (page = 1, newFilter?: MessageFilter) => {
    try {
      setLoading(true);
      const currentFilter = newFilter || filter;
      const response = await communicationService.getMessages(currentFilter, page, pagination.limit);
      
      if (page === 1) {
        setMessages(response.messages);
      } else {
        setMessages(prev => [...prev, ...response.messages]);
      }
      
      setPagination({
        page: response.page,
        limit: response.limit,
        totalCount: response.totalCount,
        hasMore: response.hasMore
      });
    } catch (error) {
      console.error('Error loading messages:', error);
      showError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [filter, pagination.limit, showError]);

  // Load more messages (pagination)
  const loadMore = useCallback(() => {
    if (pagination.hasMore && !loading) {
      loadMessages(pagination.page + 1);
    }
  }, [pagination.hasMore, pagination.page, loading, loadMessages]);

  // Refresh messages
  const refresh = useCallback(() => {
    loadMessages(1);
  }, [loadMessages]);

  // Update filter
  const updateFilter = useCallback((newFilter: MessageFilter) => {
    setFilter(newFilter);
    loadMessages(1, newFilter);
  }, [loadMessages]);

  // Message actions
  const markAsRead = useCallback(async (messageId: string) => {
    try {
      await communicationService.markAsRead(messageId);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, status: 'read', readAt: new Date() } : msg
      ));
      showSuccess('Message marked as read');
    } catch (error) {
      console.error('Error marking message as read:', error);
      showError('Failed to mark message as read');
    }
  }, [showSuccess, showError]);

  const markAsUnread = useCallback(async (messageId: string) => {
    try {
      await communicationService.markAsUnread(messageId);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, status: 'delivered', readAt: undefined } : msg
      ));
      showSuccess('Message marked as unread');
    } catch (error) {
      console.error('Error marking message as unread:', error);
      showError('Failed to mark message as unread');
    }
  }, [showSuccess, showError]);

  const archiveMessage = useCallback(async (messageId: string) => {
    try {
      await communicationService.archiveMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      showSuccess('Message archived');
    } catch (error) {
      console.error('Error archiving message:', error);
      showError('Failed to archive message');
    }
  }, [showSuccess, showError]);

  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      await communicationService.deleteMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      showSuccess('Message deleted');
    } catch (error) {
      console.error('Error deleting message:', error);
      showError('Failed to delete message');
    }
  }, [showSuccess, showError]);

  const bulkOperation = useCallback(async (operation: BulkMessageOperation) => {
    try {
      await communicationService.bulkOperation(operation);
      
      // Update local state based on operation
      switch (operation.operation) {
        case 'markRead':
          setMessages(prev => prev.map(msg => 
            operation.messageIds.includes(msg.id) 
              ? { ...msg, status: 'read', readAt: new Date() } 
              : msg
          ));
          break;
        case 'markUnread':
          setMessages(prev => prev.map(msg => 
            operation.messageIds.includes(msg.id) 
              ? { ...msg, status: 'delivered', readAt: undefined } 
              : msg
          ));
          break;
        case 'archive':
        case 'delete':
          setMessages(prev => prev.filter(msg => !operation.messageIds.includes(msg.id)));
          break;
      }
      
      showSuccess(`Bulk operation completed successfully`);
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      showError('Failed to perform bulk operation');
    }
  }, [showSuccess, showError]);

  // Initialize on mount
  useEffect(() => {
    loadMessages(1);
  }, []);

  return {
    messages,
    loading,
    filter,
    pagination,
    loadMessages,
    loadMore,
    refresh,
    updateFilter,
    markAsRead,
    markAsUnread,
    archiveMessage,
    deleteMessage,
    bulkOperation
  };
};

export const useCommunicationStats = () => {
  const [stats, setStats] = useState<CommunicationStats | null>(null);
  const [loading, setLoading] = useState(false);
  const { showError } = useToast();

  const loadStats = useCallback(async () => {
    try {
      setLoading(true);
      const statsData = await communicationService.getStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading communication stats:', error);
      showError('Failed to load communication statistics');
    } finally {
      setLoading(false);
    }
  }, [showError]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return {
    stats,
    loading,
    refresh: loadStats
  };
};

export const useNotificationSettings = () => {
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const { showSuccess, showError } = useToast();

  const loadSettings = useCallback(async () => {
    try {
      setLoading(true);
      const settingsData = await communicationService.getNotificationSettings();
      setSettings(settingsData);
    } catch (error) {
      console.error('Error loading notification settings:', error);
      showError('Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const updateSettings = useCallback(async (newSettings: NotificationSettings) => {
    try {
      setSaving(true);
      const updatedSettings = await communicationService.updateNotificationSettings(newSettings);
      setSettings(updatedSettings);
      showSuccess('Notification settings updated successfully');
    } catch (error) {
      console.error('Error updating notification settings:', error);
      showError('Failed to update notification settings');
    } finally {
      setSaving(false);
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    loading,
    saving,
    updateSettings,
    refresh: loadSettings
  };
};

export const useMessageSearch = () => {
  const [results, setResults] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState('');
  const { showError } = useToast();

  const search = useCallback(async (searchQuery: string, filter?: MessageFilter) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    try {
      setLoading(true);
      setQuery(searchQuery);
      const searchResults = await communicationService.searchMessages(searchQuery, filter);
      setResults(searchResults.messages);
    } catch (error) {
      console.error('Error searching messages:', error);
      showError('Failed to search messages');
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
  }, []);

  return {
    results,
    loading,
    query,
    search,
    clearSearch
  };
};
