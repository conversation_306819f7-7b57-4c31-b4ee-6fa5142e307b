"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/applications/layout",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/activityNotesService.ts":
/*!**********************************************!*\
  !*** ./src/services/activityNotesService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityNotesService: () => (/* binding */ activityNotesService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nclass ActivityNotesService {\n    async create(data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(this.baseUrl, data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findAll(query) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(this.baseUrl, {\n            params: query\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntity(entityType, entityId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntityAndStep(entityType, entityId, step) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId, \"/step/\").concat(step));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findOne(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/\").concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async update(id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async archive(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id, \"/archive\"));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async softDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/soft\"));\n    }\n    async hardDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/hard\"));\n    }\n    // Specialized methods for evaluation workflow\n    async createEvaluationComment(applicationId, step, comment, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/evaluation-comment\"), {\n            applicationId,\n            step,\n            comment,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async createStatusUpdate(applicationId, statusChange, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/status-update\"), {\n            applicationId,\n            statusChange,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    // Helper methods for common use cases\n    async getEvaluationComments(applicationId, step) {\n        const query = {\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'evaluation_comment',\n            status: 'active'\n        };\n        if (step) {\n            query.step = step;\n        }\n        return this.findAll(query);\n    }\n    async getApplicationNotes(applicationId) {\n        return this.findByEntity('application', applicationId);\n    }\n    async getApplicationStatusUpdates(applicationId) {\n        return this.findAll({\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'status_update',\n            status: 'active'\n        });\n    }\n    // Data breach specific methods\n    async createDataBreachAction(reportId, action, note, metadata) {\n        return this.create({\n            entity_type: 'data-breach',\n            entity_id: reportId,\n            note: note,\n            note_type: action === 'close' ? 'status_update' : 'data_breach_action',\n            category: 'data_breach_management',\n            metadata: {\n                action,\n                timestamp: new Date().toISOString(),\n                ...metadata\n            },\n            priority: action === 'close' ? 'high' : 'normal',\n            is_internal: false\n        });\n    }\n    async getDataBreachNotes(reportId) {\n        return this.findByEntity('data-breach', reportId);\n    }\n    constructor(){\n        this.baseUrl = '/activity-notes';\n    }\n}\nconst activityNotesService = new ActivityNotesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/activityNotesService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/task-assignment.ts":
/*!*****************************************!*\
  !*** ./src/services/task-assignment.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taskAssignmentService: () => (/* binding */ taskAssignmentService),\n/* harmony export */   taskService: () => (/* binding */ taskService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n\n\nconst taskService = {\n    // Main task CRUD operations\n    getTasks: async (params)=>{\n        // Handle assignment_status filter by using different endpoints or filters\n        if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'unassigned') {\n            // Use the unassigned tasks endpoint\n            return taskService.getUnassignedTasks(params);\n        } else if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'assigned') {\n            // Use the assigned tasks endpoint instead of recursive call\n            return taskService.getAssignedTasks(params);\n        }\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        if (params === null || params === void 0 ? void 0 : params.assigned_to) searchParams.append('filter.assigned_to', params.assigned_to);\n        const queryString = searchParams.toString();\n        const url = \"/tasks\".concat(queryString ? \"?\".concat(queryString) : '');\n        console.log('🔗 Making API call to:', url);\n        console.log('📊 Request params:', params);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        console.log('✅ API response:', response.data);\n        return response.data;\n    },\n    getUnassignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getAssignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getMyTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskStats: async ()=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/tasks/stats');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTask: async (taskId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/\".concat(taskId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    createTask: async (taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/tasks', taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    updateTask: async (taskId, taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.patch(\"/tasks/\".concat(taskId), taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskForApplication: async (applicationId)=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/application/\".concat(applicationId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return null; // No task found for application\n            }\n            throw error;\n        }\n    },\n    assignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/assign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    reassignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/reassign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    deleteTask: async (taskId)=>{\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete(\"/tasks/\".concat(taskId));\n    },\n    // Get users for assignment (officers only, exclude customers)\n    getUsers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                params: {\n                    limit: 100,\n                    filter: {\n                        exclude_customers: true\n                    }\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching users:', error);\n        }\n    },\n    // Get officers specifically (non-customer users)\n    getOfficers: async ()=>{\n        try {\n            var _result_data;\n            console.log('TaskService: Fetching officers for task assignment...');\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users/list/officers');\n            const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            console.log(\"TaskService: Retrieved \".concat((result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0, \" officers for task assignment\"));\n            return result;\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            // Fallback to regular users endpoint with filtering\n            try {\n                var _result_data1;\n                console.log('TaskService: Falling back to regular users endpoint...');\n                const fallbackResponse = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                    params: {\n                        limit: 100,\n                        filter: {\n                            exclude_customers: true\n                        }\n                    }\n                });\n                const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(fallbackResponse);\n                console.log(\"TaskService: Retrieved \".concat((result === null || result === void 0 ? void 0 : (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.length) || 0, \" users as fallback\"));\n                return result;\n            } catch (fallbackError) {\n                console.error('Error fetching users as fallback:', fallbackError);\n            }\n        }\n    }\n};\n// Legacy service for backward compatibility\nconst taskAssignmentService = {\n    // Generic task management methods\n    getUnassignedTasks: taskService.getUnassignedTasks,\n    getAssignedTasks: taskService.getAssignedTasks,\n    assignTask: taskService.assignTask,\n    getTaskById: taskService.getTask,\n    // Legacy application-specific methods (for backward compatibility)\n    getUnassignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get all applications (including assigned)\n    getAllApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get applications assigned to current user\n    getMyAssignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get officers for assignment\n    getOfficers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users');\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            return {\n                data: []\n            };\n        }\n    },\n    // Assign application to officer\n    assignApplication: async (applicationId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/applications/\".concat(applicationId, \"/assign\"), assignData);\n        return response.data;\n    },\n    // Get application details\n    getApplication: async (applicationId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/applications/\".concat(applicationId));\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/task-assignment.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy91c2VyU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDcUQ7QUFHSDtBQUVsRCx1Q0FBdUM7QUFFaEMsTUFBTUcsY0FBYztJQUN6QixnQ0FBZ0M7SUFDaEMsTUFBTUM7WUFBU0MsUUFBQUEsaUVBQXVCLENBQUM7UUFDckMsTUFBTUMsU0FBUyxJQUFJQztRQUVuQixJQUFJRixNQUFNRyxJQUFJLEVBQUVGLE9BQU9HLEdBQUcsQ0FBQyxRQUFRSixNQUFNRyxJQUFJLENBQUNFLFFBQVE7UUFDdEQsSUFBSUwsTUFBTU0sS0FBSyxFQUFFTCxPQUFPRyxHQUFHLENBQUMsU0FBU0osTUFBTU0sS0FBSyxDQUFDRCxRQUFRO1FBQ3pELElBQUlMLE1BQU1PLE1BQU0sRUFBRU4sT0FBT0csR0FBRyxDQUFDLFVBQVVKLE1BQU1PLE1BQU07UUFDbkQsSUFBSVAsTUFBTVEsTUFBTSxFQUFFO1lBQ2hCUixNQUFNUSxNQUFNLENBQUNDLE9BQU8sQ0FBQ0MsQ0FBQUEsT0FBUVQsT0FBT1UsTUFBTSxDQUFDLFVBQVVEO1FBQ3ZEO1FBQ0EsSUFBSVYsTUFBTVksUUFBUSxFQUFFO1lBQ2xCWixNQUFNWSxRQUFRLENBQUNILE9BQU8sQ0FBQ0YsQ0FBQUEsU0FBVU4sT0FBT1UsTUFBTSxDQUFDLFlBQVlKO1FBQzdEO1FBQ0EsSUFBSVAsTUFBTWEsTUFBTSxFQUFFO1lBQ2hCQyxPQUFPQyxPQUFPLENBQUNmLE1BQU1hLE1BQU0sRUFBRUosT0FBTyxDQUFDO29CQUFDLENBQUNPLEtBQUtDLE1BQU07Z0JBQ2hELElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUTtvQkFDeEJBLE1BQU1SLE9BQU8sQ0FBQ1csQ0FBQUEsSUFBS25CLE9BQU9VLE1BQU0sQ0FBQyxVQUFjLE9BQUpLLE1BQU9JO2dCQUNwRCxPQUFPO29CQUNMbkIsT0FBT0csR0FBRyxDQUFDLFVBQWMsT0FBSlksTUFBT0M7Z0JBQzlCO1lBQ0Y7UUFDRjtRQUVBLE1BQU1JLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDLElBQXNCLE9BQWxCckIsT0FBT0ksUUFBUTtRQUM3RCxPQUFPVixrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNRSxTQUFRQyxFQUFVO1FBQ3RCLE1BQU1ILFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDLElBQU8sT0FBSEU7UUFDOUMsT0FBTzdCLGtFQUFrQkEsQ0FBQzBCO0lBQzVCO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU1JLGVBQWNDLFVBQW1CO1FBQ3JDLElBQUk7Z0JBUWFDO1lBUGYsTUFBTTFCLFNBQVMsSUFBSUM7WUFDbkIsSUFBSXdCLFlBQVl6QixPQUFPRyxHQUFHLENBQUMsVUFBVXNCO1lBQ3JDekIsT0FBT0csR0FBRyxDQUFDLFNBQVMsT0FBTywwQkFBMEI7WUFDckQsTUFBTWlCLFdBQVcsTUFBTXpCLDJDQUFTQSxDQUFDMEIsR0FBRyxDQUFDLFVBQTRCLE9BQWxCckIsT0FBT0ksUUFBUTtZQUM5RCxNQUFNc0IsWUFBWWhDLGtFQUFrQkEsQ0FBQzBCO1lBRXJDLDRCQUE0QjtZQUM1QixNQUFNTyxTQUFTRCxFQUFBQSxrQkFBQUEsVUFBVUUsSUFBSSxjQUFkRixzQ0FBQUEsZ0JBQWdCRyxHQUFHLENBQUMsQ0FBQ0MsT0FBZUEsS0FBS0MsS0FBSyxFQUFFbkIsTUFBTSxDQUFDb0IsYUFBWSxFQUFFO1lBQ3BGLE9BQU9MO1FBQ1QsRUFBRSxPQUFPTSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyxnQ0FBZ0NGO1lBQzdDLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTUcsYUFBWWIsRUFBVTtRQUMxQixPQUFPLElBQUksQ0FBQ0QsT0FBTyxDQUFDQztJQUN0QjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNYztRQUNKLE1BQU1qQixXQUFXLE1BQU14QixnREFBY0EsQ0FBQ3lCLEdBQUcsQ0FBQztRQUMxQyxPQUFPM0Isa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTWtCLFlBQVdDLFFBQXVCO1FBQ3RDLE1BQU1uQixXQUFXLE1BQU14QixnREFBY0EsQ0FBQzRDLElBQUksQ0FBQyxJQUFJRDtRQUMvQyxPQUFPN0Msa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSxjQUFjO0lBQ2QsTUFBTXFCLFlBQVdsQixFQUFVLEVBQUVnQixRQUF1QjtRQUNsRCxNQUFNbkIsV0FBVyxNQUFNeEIsZ0RBQWNBLENBQUM4QyxHQUFHLENBQUMsSUFBTyxPQUFIbkIsS0FBTWdCO1FBQ3BELE9BQU83QyxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNdUIsZUFBY0osUUFBMEI7UUFDNUMsTUFBTW5CLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDOEMsR0FBRyxDQUFDLFlBQVlIO1FBQ3RELE9BQU83QyxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNd0IsZ0JBQWVDLFlBQStCO1FBQ2xELE1BQU16QixXQUFXLE1BQU14QixnREFBY0EsQ0FBQzhDLEdBQUcsQ0FBQyxxQkFBcUJHO1FBQy9ELE9BQU9uRCxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNMEIsY0FBYUMsSUFBVTtRQUMzQmIsUUFBUWMsR0FBRyxDQUFDLG9DQUFvQztZQUM5Q0MsVUFBVUYsS0FBS0csSUFBSTtZQUNuQkMsVUFBVUosS0FBS0ssSUFBSTtZQUNuQkMsVUFBVU4sS0FBS08sSUFBSTtRQUNyQjtRQUVBLE1BQU1DLFdBQVcsSUFBSUM7UUFDckJELFNBQVM3QyxNQUFNLENBQUMsVUFBVXFDO1FBRzFCLElBQUk7WUFDRixNQUFNM0IsV0FBVyxNQUFNeEIsZ0RBQWNBLENBQUM0QyxJQUFJLENBQUMsbUJBQW1CZSxVQUFVO2dCQUN0RUUsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxPQUFPL0Qsa0VBQWtCQSxDQUFDMEI7UUFDNUIsRUFBRSxPQUFPYSxPQUFnQjtnQkFHYnlCLHNCQUNJQSx1QkFDTkE7WUFKUixNQUFNQSxhQUFhekI7WUFDbkJDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEI7Z0JBQzFDMEIsTUFBTSxHQUFFRCx1QkFBQUEsV0FBV3RDLFFBQVEsY0FBbkJzQywyQ0FBQUEscUJBQXFCQyxNQUFNO2dCQUNuQ0MsVUFBVSxHQUFFRix3QkFBQUEsV0FBV3RDLFFBQVEsY0FBbkJzQyw0Q0FBQUEsc0JBQXFCRSxVQUFVO2dCQUMzQ2hDLElBQUksR0FBRThCLHdCQUFBQSxXQUFXdEMsUUFBUSxjQUFuQnNDLDRDQUFBQSxzQkFBcUI5QixJQUFJO2dCQUMvQmlDLFNBQVNILFdBQVdHLE9BQU87WUFDN0I7WUFDQSxNQUFNNUI7UUFDUjtJQUNGO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU02QjtRQUNKLE1BQU0xQyxXQUFXLE1BQU14QixnREFBY0EsQ0FBQ21FLE1BQU0sQ0FBQztRQUM3QyxPQUFPckUsa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTTRDO1FBQ0osTUFBTTVDLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDO1FBQzFDLE9BQU8zQixrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGNBQWM7SUFDZCxNQUFNNkMsWUFBVzFDLEVBQVU7UUFDekIsTUFBTTNCLGdEQUFjQSxDQUFDbUUsTUFBTSxDQUFDLElBQU8sT0FBSHhDO0lBQ2xDO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU0yQztRQUNKLE1BQU05QyxXQUFXLE1BQU16QiwyQ0FBU0EsQ0FBQzBCLEdBQUcsQ0FBQztRQUNyQyxPQUFPM0Isa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTStDLGlCQUFnQnBCLElBQVU7UUFDOUIsTUFBTVEsV0FBVyxJQUFJQztRQUNyQkQsU0FBUzdDLE1BQU0sQ0FBQyxhQUFhcUM7UUFFN0IsTUFBTTNCLFdBQVcsTUFBTXpCLDJDQUFTQSxDQUFDNkMsSUFBSSxDQUFDLDZCQUE2QmUsVUFBVTtZQUMzRUUsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUNBLE9BQU8vRCxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNZ0Q7UUFDSixNQUFNekUsMkNBQVNBLENBQUNvRSxNQUFNLENBQUM7SUFDekI7QUFDRixFQUFFIiwic291cmNlcyI6WyJEOlxcTWVtb3J5IEJ1c2luZXNzIFNvbHV0aW9pbnNcXFByb2plY3RzXFxNQUNSQVxccHJvamVjdFxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcc2VydmljZXNcXHVzZXJTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF4aW9zRXJyb3IgfSBmcm9tICdheGlvcyc7XHJcbmltcG9ydCB7IHByb2Nlc3NBcGlSZXNwb25zZSB9IGZyb20gJ0AvbGliL2F1dGhVdGlscyc7XHJcbmltcG9ydCB7IENoYW5nZVBhc3N3b3JkRHRvLCBDcmVhdGVVc2VyRHRvLCBVcGRhdGVQcm9maWxlRHRvLCBVcGRhdGVVc2VyRHRvLCBVc2VyIH0gZnJvbSAnQC90eXBlcy91c2VyJztcclxuaW1wb3J0IHsgUGFnaW5hdGVkUmVzcG9uc2UsIFBhZ2luYXRlUXVlcnkgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgYXBpQ2xpZW50LCB1c2Vyc0FwaUNsaWVudCB9IGZyb20gJ0AvbGliJztcclxuXHJcbi8vIFJlLWV4cG9ydCBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZXJTZXJ2aWNlID0ge1xyXG4gIC8vIEdldCBhbGwgdXNlcnMgd2l0aCBwYWdpbmF0aW9uXHJcbiAgYXN5bmMgZ2V0VXNlcnMocXVlcnk6IFBhZ2luYXRlUXVlcnkgPSB7fSk6IFByb21pc2U8UGFnaW5hdGVkUmVzcG9uc2U8VXNlcj4+IHtcclxuICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcclxuXHJcbiAgICBpZiAocXVlcnkucGFnZSkgcGFyYW1zLnNldCgncGFnZScsIHF1ZXJ5LnBhZ2UudG9TdHJpbmcoKSk7XHJcbiAgICBpZiAocXVlcnkubGltaXQpIHBhcmFtcy5zZXQoJ2xpbWl0JywgcXVlcnkubGltaXQudG9TdHJpbmcoKSk7XHJcbiAgICBpZiAocXVlcnkuc2VhcmNoKSBwYXJhbXMuc2V0KCdzZWFyY2gnLCBxdWVyeS5zZWFyY2gpO1xyXG4gICAgaWYgKHF1ZXJ5LnNvcnRCeSkge1xyXG4gICAgICBxdWVyeS5zb3J0QnkuZm9yRWFjaChzb3J0ID0+IHBhcmFtcy5hcHBlbmQoJ3NvcnRCeScsIHNvcnQpKTtcclxuICAgIH1cclxuICAgIGlmIChxdWVyeS5zZWFyY2hCeSkge1xyXG4gICAgICBxdWVyeS5zZWFyY2hCeS5mb3JFYWNoKHNlYXJjaCA9PiBwYXJhbXMuYXBwZW5kKCdzZWFyY2hCeScsIHNlYXJjaCkpO1xyXG4gICAgfVxyXG4gICAgaWYgKHF1ZXJ5LmZpbHRlcikge1xyXG4gICAgICBPYmplY3QuZW50cmllcyhxdWVyeS5maWx0ZXIpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xyXG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xyXG4gICAgICAgICAgdmFsdWUuZm9yRWFjaCh2ID0+IHBhcmFtcy5hcHBlbmQoYGZpbHRlci4ke2tleX1gLCB2KSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHBhcmFtcy5zZXQoYGZpbHRlci4ke2tleX1gLCB2YWx1ZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LmdldChgPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBHZXQgdXNlciBieSBJRFxyXG4gIGFzeW5jIGdldFVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5nZXQoYC8ke2lkfWApO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHVzZXIgZW1haWxzIGZvciBwcmVkaWN0aXZlIGlucHV0XHJcbiAgYXN5bmMgZ2V0VXNlckVtYWlscyhzZWFyY2hUZXJtPzogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG4gICAgICBpZiAoc2VhcmNoVGVybSkgcGFyYW1zLnNldCgnc2VhcmNoJywgc2VhcmNoVGVybSk7XHJcbiAgICAgIHBhcmFtcy5zZXQoJ2xpbWl0JywgJzIwJyk7IC8vIExpbWl0IHRvIDIwIHN1Z2dlc3Rpb25zXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL3VzZXJzPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICAgIGNvbnN0IHVzZXJzRGF0YSA9IHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcblxyXG4gICAgICAvLyBFeHRyYWN0IGVtYWlscyBmcm9tIHVzZXJzXHJcbiAgICAgIGNvbnN0IGVtYWlscyA9IHVzZXJzRGF0YS5kYXRhPy5tYXAoKHVzZXI6IFVzZXIpID0+IHVzZXIuZW1haWwpLmZpbHRlcihCb29sZWFuKSB8fCBbXTtcclxuICAgICAgcmV0dXJuIGVtYWlscztcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGZldGNoIHVzZXIgZW1haWxzOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIFtdO1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8vIEdldCB1c2VyIGJ5IElEIChhbGlhcyBmb3IgY29uc2lzdGVuY3kpXHJcbiAgYXN5bmMgZ2V0VXNlckJ5SWQoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgcmV0dXJuIHRoaXMuZ2V0VXNlcihpZCk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IGN1cnJlbnQgdXNlciBwcm9maWxlXHJcbiAgYXN5bmMgZ2V0UHJvZmlsZSgpOiBQcm9taXNlPFVzZXI+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXNlcnNBcGlDbGllbnQuZ2V0KCcvcHJvZmlsZScpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gQ3JlYXRlIG5ldyB1c2VyXHJcbiAgYXN5bmMgY3JlYXRlVXNlcih1c2VyRGF0YTogQ3JlYXRlVXNlckR0byk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5wb3N0KCcnLCB1c2VyRGF0YSk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGRhdGUgdXNlclxyXG4gIGFzeW5jIHVwZGF0ZVVzZXIoaWQ6IHN0cmluZywgdXNlckRhdGE6IFVwZGF0ZVVzZXJEdG8pOiBQcm9taXNlPFVzZXI+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXNlcnNBcGlDbGllbnQucHV0KGAvJHtpZH1gLCB1c2VyRGF0YSk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGRhdGUgY3VycmVudCB1c2VyIHByb2ZpbGVcclxuICBhc3luYyB1cGRhdGVQcm9maWxlKHVzZXJEYXRhOiBVcGRhdGVQcm9maWxlRHRvKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LnB1dCgnL3Byb2ZpbGUnLCB1c2VyRGF0YSk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBDaGFuZ2UgcGFzc3dvcmRcclxuICBhc3luYyBjaGFuZ2VQYXNzd29yZChwYXNzd29yZERhdGE6IENoYW5nZVBhc3N3b3JkRHRvKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LnB1dCgnL3Byb2ZpbGUvcGFzc3dvcmQnLCBwYXNzd29yZERhdGEpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBsb2FkIGF2YXRhclxyXG4gIGFzeW5jIHVwbG9hZEF2YXRhcihmaWxlOiBGaWxlKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBjb25zb2xlLmxvZygndXNlclNlcnZpY2U6IHVwbG9hZEF2YXRhciBjYWxsZWQnLCB7XHJcbiAgICAgIGZpbGVOYW1lOiBmaWxlLm5hbWUsXHJcbiAgICAgIGZpbGVTaXplOiBmaWxlLnNpemUsXHJcbiAgICAgIGZpbGVUeXBlOiBmaWxlLnR5cGVcclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2F2YXRhcicsIGZpbGUpO1xyXG5cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LnBvc3QoJy9wcm9maWxlL2F2YXRhcicsIGZvcm1EYXRhLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgICB9LFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJldHVybiBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IHVua25vd24pIHtcclxuICAgICAgY29uc3QgYXhpb3NFcnJvciA9IGVycm9yIGFzIEF4aW9zRXJyb3I7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ3VzZXJTZXJ2aWNlOiBVcGxvYWQgZmFpbGVkJywge1xyXG4gICAgICAgIHN0YXR1czogYXhpb3NFcnJvci5yZXNwb25zZT8uc3RhdHVzLFxyXG4gICAgICAgIHN0YXR1c1RleHQ6IGF4aW9zRXJyb3IucmVzcG9uc2U/LnN0YXR1c1RleHQsXHJcbiAgICAgICAgZGF0YTogYXhpb3NFcnJvci5yZXNwb25zZT8uZGF0YSxcclxuICAgICAgICBtZXNzYWdlOiBheGlvc0Vycm9yLm1lc3NhZ2VcclxuICAgICAgfSk7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8vIFJlbW92ZSBhdmF0YXJcclxuICBhc3luYyByZW1vdmVBdmF0YXIoKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LmRlbGV0ZSgnL3Byb2ZpbGUvYXZhdGFyJyk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBHZXQgdXNlciBhdmF0YXIgZG9jdW1lbnRcclxuICBhc3luYyBnZXRVc2VyQXZhdGFyKCk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LmdldCgnL3Byb2ZpbGUvYXZhdGFyJyk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBEZWxldGUgdXNlclxyXG4gIGFzeW5jIGRlbGV0ZVVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgYXdhaXQgdXNlcnNBcGlDbGllbnQuZGVsZXRlKGAvJHtpZH1gKTtcclxuICB9LFxyXG5cclxuICAvLyBHZXQgdXNlciBzaWduYXR1cmVcclxuICBhc3luYyBnZXRVc2VyU2lnbmF0dXJlKCk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoJy9kb2N1bWVudHMvdXNlci9zaWduYXR1cmUnKTtcclxuICAgIHJldHVybiBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG4gIH0sXHJcblxyXG4gIC8vIFVwbG9hZCB1c2VyIHNpZ25hdHVyZVxyXG4gIGFzeW5jIHVwbG9hZFNpZ25hdHVyZShmaWxlOiBGaWxlKTogUHJvbWlzZTxhbnk+IHtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3NpZ25hdHVyZScsIGZpbGUpO1xyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9kb2N1bWVudHMvdXNlci9zaWduYXR1cmUnLCBmb3JtRGF0YSwge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gRGVsZXRlIHVzZXIgc2lnbmF0dXJlXHJcbiAgYXN5bmMgZGVsZXRlU2lnbmF0dXJlKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZSgnL2RvY3VtZW50cy91c2VyL3NpZ25hdHVyZScpO1xyXG4gIH0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJwcm9jZXNzQXBpUmVzcG9uc2UiLCJhcGlDbGllbnQiLCJ1c2Vyc0FwaUNsaWVudCIsInVzZXJTZXJ2aWNlIiwiZ2V0VXNlcnMiLCJxdWVyeSIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsInBhZ2UiLCJzZXQiLCJ0b1N0cmluZyIsImxpbWl0Iiwic2VhcmNoIiwic29ydEJ5IiwiZm9yRWFjaCIsInNvcnQiLCJhcHBlbmQiLCJzZWFyY2hCeSIsImZpbHRlciIsIk9iamVjdCIsImVudHJpZXMiLCJrZXkiLCJ2YWx1ZSIsIkFycmF5IiwiaXNBcnJheSIsInYiLCJyZXNwb25zZSIsImdldCIsImdldFVzZXIiLCJpZCIsImdldFVzZXJFbWFpbHMiLCJzZWFyY2hUZXJtIiwidXNlcnNEYXRhIiwiZW1haWxzIiwiZGF0YSIsIm1hcCIsInVzZXIiLCJlbWFpbCIsIkJvb2xlYW4iLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiZ2V0VXNlckJ5SWQiLCJnZXRQcm9maWxlIiwiY3JlYXRlVXNlciIsInVzZXJEYXRhIiwicG9zdCIsInVwZGF0ZVVzZXIiLCJwdXQiLCJ1cGRhdGVQcm9maWxlIiwiY2hhbmdlUGFzc3dvcmQiLCJwYXNzd29yZERhdGEiLCJ1cGxvYWRBdmF0YXIiLCJmaWxlIiwibG9nIiwiZmlsZU5hbWUiLCJuYW1lIiwiZmlsZVNpemUiLCJzaXplIiwiZmlsZVR5cGUiLCJ0eXBlIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImhlYWRlcnMiLCJheGlvc0Vycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsIm1lc3NhZ2UiLCJyZW1vdmVBdmF0YXIiLCJkZWxldGUiLCJnZXRVc2VyQXZhdGFyIiwiZGVsZXRlVXNlciIsImdldFVzZXJTaWduYXR1cmUiLCJ1cGxvYWRTaWduYXR1cmUiLCJkZWxldGVTaWduYXR1cmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy90eXBlcy90YXNrLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUtPLHNDQUFLQTs7Ozs7Ozs7Ozs7O1dBQUFBO01BWVg7QUFFTSx3Q0FBS0M7Ozs7OztXQUFBQTtNQU1YO0FBRU0sMENBQUtDOzs7OztXQUFBQTtNQUtYIiwic291cmNlcyI6WyJEOlxcTWVtb3J5IEJ1c2luZXNzIFNvbHV0aW9pbnNcXFByb2plY3RzXFxNQUNSQVxccHJvamVjdFxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcdHlwZXNcXHRhc2sudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVudGl0eSwgVXNlclJlZmVyZW5jZSB9IGZyb20gJy4vaW5kZXgnO1xyXG5cclxuLy8gRGVmaW5lIHByb3BlciB0eXBlcyBmb3IgdGFzayBtZXRhZGF0YVxyXG5leHBvcnQgdHlwZSBUYXNrTWV0YWRhdGEgPSBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBudW1iZXIgfCBib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZD47XHJcblxyXG5leHBvcnQgZW51bSBUYXNrVHlwZSB7XHJcbiAgQVBQTElDQVRJT04gPSAnYXBwbGljYXRpb24nLFxyXG4gIENPTVBMQUlOVCA9ICdjb21wbGFpbnQnLFxyXG4gIERBVEFfQlJFQUNIID0gJ2RhdGFfYnJlYWNoJyxcclxuICBFVkFMVUFUSU9OID0gJ2V2YWx1YXRpb24nLFxyXG4gIElOU1BFQ1RJT04gPSAnaW5zcGVjdGlvbicsXHJcbiAgRE9DVU1FTlRfUkVWSUVXID0gJ2RvY3VtZW50X3JldmlldycsXHJcbiAgQ09NUExJQU5DRV9DSEVDSyA9ICdjb21wbGlhbmNlX2NoZWNrJyxcclxuICBGT0xMT1dfVVAgPSAnZm9sbG93X3VwJyxcclxuICBQQVlNRU5UX1ZFUklGSUNBVElPTiA9ICdwYXltZW50X3ZlcmlmaWNhdGlvbicsXHJcbiAgVVNFUiA9ICd1c2VyJyxcclxuICBPVEhFUiA9ICdvdGhlcicsXHJcbn1cclxuXHJcbmV4cG9ydCBlbnVtIFRhc2tTdGF0dXMge1xyXG4gIFBFTkRJTkcgPSAncGVuZGluZycsXHJcbiAgSU5fUFJPR1JFU1MgPSAnaW5fcHJvZ3Jlc3MnLFxyXG4gIENPTVBMRVRFRCA9ICdjb21wbGV0ZWQnLFxyXG4gIENBTkNFTExFRCA9ICdjYW5jZWxsZWQnLFxyXG4gIE9OX0hPTEQgPSAnb25faG9sZCdcclxufVxyXG5cclxuZXhwb3J0IGVudW0gVGFza1ByaW9yaXR5IHtcclxuICBMT1cgPSAnbG93JyxcclxuICBNRURJVU0gPSAnbWVkaXVtJyxcclxuICBISUdIID0gJ2hpZ2gnLFxyXG4gIFVSR0VOVCA9ICd1cmdlbnQnXHJcbn1cclxuXHJcblxyXG4vLyBUYXNrIGludGVyZmFjZXNcclxuZXhwb3J0IGludGVyZmFjZSBUYXNrIGV4dGVuZHMgQmFzZUVudGl0eSB7XHJcbiAgdGFza19pZDogc3RyaW5nO1xyXG4gIHRhc2tfbnVtYmVyOiBzdHJpbmc7XHJcbiAgdGFza190eXBlOiBUYXNrVHlwZTtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHN0YXR1czogVGFza1N0YXR1cztcclxuICBwcmlvcml0eTogVGFza1ByaW9yaXR5O1xyXG4gIGVudGl0eV90eXBlPzogc3RyaW5nO1xyXG4gIGVudGl0eV9pZD86IHN0cmluZztcclxuICBhc3NpZ25lZF90bz86IHN0cmluZztcclxuICBhc3NpZ25lZF9ieT86IHN0cmluZztcclxuICBhc3NpZ25lZF9hdD86IHN0cmluZztcclxuICBkdWVfZGF0ZT86IHN0cmluZztcclxuICBjb21wbGV0ZWRfYXQ/OiBzdHJpbmc7XHJcbiAgcmV2aWV3X25vdGVzPzogc3RyaW5nO1xyXG4gIG1ldGFkYXRhPzogVGFza01ldGFkYXRhO1xyXG4gIFxyXG4gIC8vIFJlbGF0aW9uc1xyXG4gIGFzc2lnbmVlPzogVXNlclJlZmVyZW5jZTtcclxuICBhc3NpZ25lcj86IFVzZXJSZWZlcmVuY2U7XHJcbiAgY3JlYXRvcj86IFVzZXJSZWZlcmVuY2U7XHJcbiAgdXBkYXRlcj86IFVzZXJSZWZlcmVuY2U7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlVGFza0R0byB7XHJcbiAgdGFza190eXBlOiBUYXNrVHlwZTtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHByaW9yaXR5Pzogc3RyaW5nO1xyXG4gIHN0YXR1cz86IHN0cmluZztcclxuICBlbnRpdHlfdHlwZT86IHN0cmluZztcclxuICBlbnRpdHlfaWQ/OiBzdHJpbmc7XHJcbiAgYXNzaWduZWRfdG8/OiBzdHJpbmc7XHJcbiAgZHVlX2RhdGU/OiBzdHJpbmc7XHJcbiAgbWV0YWRhdGE/OiBUYXNrTWV0YWRhdGE7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXBkYXRlVGFza0R0byB7XHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgc3RhdHVzPzogc3RyaW5nO1xyXG4gIHByaW9yaXR5Pzogc3RyaW5nO1xyXG4gIGFzc2lnbmVkX3RvPzogc3RyaW5nO1xyXG4gIGR1ZV9kYXRlPzogc3RyaW5nO1xyXG4gIHJldmlld19ub3Rlcz86IHN0cmluZztcclxuICBjb21wbGV0aW9uX25vdGVzPzogc3RyaW5nO1xyXG4gIGVudGl0eV90eXBlPzogc3RyaW5nO1xyXG4gIGVudGl0eV9pZD86IHN0cmluZztcclxuICBtZXRhZGF0YT86IFRhc2tNZXRhZGF0YTtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBc3NpZ25UYXNrRHRvIHtcclxuICBhc3NpZ25lZFRvPzogc3RyaW5nOyAvLyBPcHRpb25hbCBmb3IgdW5hc3NpZ25pbmdcclxuICBjb21tZW50Pzogc3RyaW5nO1xyXG4gIGFzc2lnbm1lbnRfbm90ZXM/OiBzdHJpbmc7XHJcbiAgZHVlX2RhdGU/OiBzdHJpbmc7XHJcbiAgcHJpb3JpdHk/OiBUYXNrUHJpb3JpdHk7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGFza0ZpbHRlcnMge1xyXG4gIHNlYXJjaD86IHN0cmluZztcclxuICBzdGF0dXM/OiBUYXNrU3RhdHVzO1xyXG4gIHByaW9yaXR5PzogVGFza1ByaW9yaXR5O1xyXG4gIHRhc2tfdHlwZT86IFRhc2tUeXBlO1xyXG4gIGVudGl0eV90eXBlPzogc3RyaW5nO1xyXG4gIGFzc2lnbmVkX3RvPzogc3RyaW5nO1xyXG4gIGFzc2lnbmVkX2J5Pzogc3RyaW5nO1xyXG4gIGFzc2lnbm1lbnRfc3RhdHVzPzogc3RyaW5nO1xyXG4gIGR1ZV9kYXRlPzogc3RyaW5nO1xyXG4gIG92ZXJkdWU/OiBib29sZWFuO1xyXG59XHJcblxyXG4vLyBUYXNrIHJlc3BvbnNlc1xyXG5leHBvcnQgdHlwZSBUYXNrc1Jlc3BvbnNlID0ge1xyXG4gIGRhdGE6IFRhc2tbXTtcclxuICBtZXRhOiB7XHJcbiAgICBpdGVtc1BlclBhZ2U6IG51bWJlcjtcclxuICAgIHRvdGFsSXRlbXM/OiBudW1iZXI7XHJcbiAgICBjdXJyZW50UGFnZT86IG51bWJlcjtcclxuICAgIHRvdGFsUGFnZXM/OiBudW1iZXI7XHJcbiAgICBzb3J0Qnk6IFtzdHJpbmcsIHN0cmluZ11bXTtcclxuICAgIHNlYXJjaEJ5OiBzdHJpbmdbXTtcclxuICAgIHNlYXJjaDogc3RyaW5nO1xyXG4gICAgc2VsZWN0OiBzdHJpbmdbXTtcclxuICAgIGZpbHRlcj86IFJlY29yZDxzdHJpbmcsIHN0cmluZyB8IHN0cmluZ1tdPjtcclxuICB9O1xyXG4gIGxpbmtzOiB7XHJcbiAgICBmaXJzdD86IHN0cmluZztcclxuICAgIHByZXZpb3VzPzogc3RyaW5nO1xyXG4gICAgY3VycmVudDogc3RyaW5nO1xyXG4gICAgbmV4dD86IHN0cmluZztcclxuICAgIGxhc3Q/OiBzdHJpbmc7XHJcbiAgfTtcclxufTtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGFza1N0YXRpc3RpY3Mge1xyXG4gIHRvdGFsOiBudW1iZXI7XHJcbiAgcGVuZGluZzogbnVtYmVyO1xyXG4gIGluX3Byb2dyZXNzOiBudW1iZXI7XHJcbiAgY29tcGxldGVkOiBudW1iZXI7XHJcbiAgb3ZlcmR1ZTogbnVtYmVyO1xyXG4gIGJ5X3ByaW9yaXR5OiB7XHJcbiAgICBsb3c6IG51bWJlcjtcclxuICAgIG1lZGl1bTogbnVtYmVyO1xyXG4gICAgaGlnaDogbnVtYmVyO1xyXG4gICAgdXJnZW50OiBudW1iZXI7XHJcbiAgfTtcclxuICBieV90eXBlOiBSZWNvcmQ8VGFza1R5cGUsIG51bWJlcj47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGFza05hdmlnYXRpb25JbmZvIHtcclxuICB0YXNrOiBUYXNrO1xyXG4gIGNhbk5hdmlnYXRlVG9FbnRpdHk6IGJvb2xlYW47XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuLy8gVXNlciBpbnRlcmZhY2UgZm9yIHRhc2sgYXNzaWdubWVudHNcclxuZXhwb3J0IGludGVyZmFjZSBUYXNrVXNlciB7XHJcbiAgdXNlcl9pZDogc3RyaW5nO1xyXG4gIGZpcnN0X25hbWU6IHN0cmluZztcclxuICBsYXN0X25hbWU6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG59XHJcbi8vIExlZ2FjeSB0eXBlIGFsaWFzIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XHJcbmV4cG9ydCB0eXBlIEdlbmVyaWNUYXNrID0gVGFzaztcclxuXHJcbi8vIExlZ2FjeSBpbnRlcmZhY2UgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuZXhwb3J0IGludGVyZmFjZSBUYXNrQXNzaWdubWVudEFwcGxpY2F0aW9uIHtcclxuICBhcHBsaWNhdGlvbl9pZDogc3RyaW5nO1xyXG4gIGFwcGxpY2F0aW9uX251bWJlcjogc3RyaW5nO1xyXG4gIHN0YXR1czogc3RyaW5nO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxuICB1cGRhdGVkX2F0OiBzdHJpbmc7XHJcbiAgYXBwbGljYW50OiB7XHJcbiAgICBhcHBsaWNhbnRfaWQ6IHN0cmluZztcclxuICAgIGNvbXBhbnlfbmFtZTogc3RyaW5nO1xyXG4gICAgZmlyc3RfbmFtZTogc3RyaW5nO1xyXG4gICAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgfTtcclxuICBsaWNlbnNlX2NhdGVnb3J5OiB7XHJcbiAgICBsaWNlbnNlX2NhdGVnb3J5X2lkOiBzdHJpbmc7XHJcbiAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICBsaWNlbnNlX3R5cGU6IHtcclxuICAgICAgbGljZW5zZV90eXBlX2lkOiBzdHJpbmc7XHJcbiAgICAgIG5hbWU6IHN0cmluZztcclxuICAgIH07XHJcbiAgfTtcclxuICBhc3NpZ25lZT86IHtcclxuICAgIHVzZXJfaWQ6IHN0cmluZztcclxuICAgIGZpcnN0X25hbWU6IHN0cmluZztcclxuICAgIGxhc3RfbmFtZTogc3RyaW5nO1xyXG4gICAgZW1haWw6IHN0cmluZztcclxuICB9O1xyXG4gIGFzc2lnbmVkX2F0Pzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tBc3NpZ25tZW50T2ZmaWNlciB7XHJcbiAgdXNlcl9pZDogc3RyaW5nO1xyXG4gIGZpcnN0X25hbWU6IHN0cmluZztcclxuICBsYXN0X25hbWU6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIGRlcGFydG1lbnQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tTdGF0cyB7XHJcbiAgdG90YWw6IG51bWJlcjtcclxuICBwZW5kaW5nOiBudW1iZXI7XHJcbiAgaW5fcHJvZ3Jlc3M6IG51bWJlcjtcclxuICBjb21wbGV0ZWQ6IG51bWJlcjtcclxuICBjYW5jZWxsZWQ6IG51bWJlcjtcclxuICBvbl9ob2xkOiBudW1iZXI7XHJcbiAgdW5hc3NpZ25lZDogbnVtYmVyO1xyXG4gIGFzc2lnbmVkOiBudW1iZXI7XHJcbiAgb3ZlcmR1ZTogbnVtYmVyO1xyXG59XHJcblxyXG4vLyBMZWdhY3kgaW50ZXJmYWNlcyBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG5leHBvcnQgaW50ZXJmYWNlIEFzc2lnbkFwcGxpY2F0aW9uUmVxdWVzdCB7XHJcbiAgYXNzaWduZWRUbzogc3RyaW5nO1xyXG59Il0sIm5hbWVzIjpbIlRhc2tUeXBlIiwiVGFza1N0YXR1cyIsIlRhc2tQcmlvcml0eSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});