import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON>, IsJ<PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  NotificationType, 
  NotificationStatus, 
  NotificationPriority, 
  RecipientType 
} from '../../entities/notifications.entity';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'Notification type',
    enum: NotificationType,
    example: NotificationType.EMAIL
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiPropertyOptional({
    description: 'Notification status',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiPropertyOptional({
    description: 'Notification priority',
    enum: NotificationPriority,
    default: NotificationPriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @ApiProperty({
    description: 'Recipient type',
    enum: RecipientType,
    example: RecipientType.CUSTOMER
  })
  @IsEnum(RecipientType)
  recipient_type: RecipientType;

  @ApiPropertyOptional({
    description: 'Recipient user ID',
    example: 'user-uuid-here'
  })
  @IsOptional()
  @IsUUID()
  recipient_id?: string;

  @ApiPropertyOptional({
    description: 'Recipient email address (required for email notifications)',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsEmail()
  recipient_email?: string;

  @ApiPropertyOptional({
    description: 'Recipient phone number (required for SMS notifications)',
    example: '+************'
  })
  @IsOptional()
  @IsString()
  recipient_phone?: string;

  @ApiProperty({
    description: 'Notification subject/title',
    example: 'Application Status Update'
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Notification message content',
    example: 'Your application has been approved.'
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'HTML content for email notifications'
  })
  @IsOptional()
  @IsString()
  html_content?: string;

  @ApiPropertyOptional({
    description: 'Entity type this notification relates to',
    example: 'application'
  })
  @IsOptional()
  @IsString()
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'Entity ID this notification relates to',
    example: 'application-uuid-here'
  })
  @IsOptional()
  @IsUUID()
  entity_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata as JSON'
  })
  @IsOptional()
  @IsJSON()
  metadata?: any;

  @ApiPropertyOptional({
    description: 'Action URL for the notification'
  })
  @IsOptional()
  @IsString()
  action_url?: string;

  @ApiPropertyOptional({
    description: 'When the notification expires'
  })
  @IsOptional()
  expires_at?: Date;
}
