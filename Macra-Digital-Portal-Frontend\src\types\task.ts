import { BaseEntity, UserReference } from './index';

// Define proper types for task metadata
export type TaskMetadata = Record<string, string | number | boolean | null | undefined>;

export enum TaskType {
  APPLICATION = 'application',
  COMPLAINT = 'complaint',
  DATA_BREACH = 'data_breach',
  EVALUATION = 'evaluation',
  INSPECTION = 'inspection',
  DOCUMENT_REVIEW = 'document_review',
  COMPLIANCE_CHECK = 'compliance_check',
  FOLLOW_UP = 'follow_up',
  PAYMENT_VERIFICATION = 'payment_verification',
  USER = 'user',
  OTHER = 'other',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}


// Task interfaces
export interface Task extends BaseEntity {
  task_id: string;
  task_number: string;
  task_type: TaskType;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  assigned_by?: string;
  assigned_at?: string;
  due_date?: string;
  completed_at?: string;
  review_notes?: string;
  metadata?: TaskMetadata;
  
  // Relations
  assignee?: UserReference;
  assigner?: UserReference;
  creator?: UserReference;
  updater?: UserReference;
}

export interface CreateTaskDto {
  task_type: TaskType;
  title: string;
  description?: string;
  priority?: string;
  status?: string;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  due_date?: string;
  metadata?: TaskMetadata;
}

export interface UpdateTaskDto {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
  due_date?: string;
  review_notes?: string;
  completion_notes?: string;
  entity_type?: string;
  entity_id?: string;
  metadata?: TaskMetadata;
}

export interface AssignTaskDto {
  assignedTo?: string; // Optional for unassigning
  comment?: string;
  assignment_notes?: string;
  due_date?: string;
  priority?: TaskPriority;
}

export interface TaskFilters {
  search?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  task_type?: TaskType;
  entity_type?: string;
  assigned_to?: string;
  assigned_by?: string;
  assignment_status?: string;
  due_date?: string;
  overdue?: boolean;
}

// Task responses
export type TasksResponse = {
  data: Task[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
};

export interface TaskStatistics {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  overdue: number;
  by_priority: {
    low: number;
    medium: number;
    high: number;
    urgent: number;
  };
  by_type: Record<TaskType, number>;
}

export interface TaskNavigationInfo {
  task: Task;
  canNavigateToEntity: boolean;
}






// User interface for task assignments
export interface TaskUser {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
}
// Legacy type alias for backward compatibility
export type GenericTask = Task;

// Legacy interface for backward compatibility
export interface TaskAssignmentApplication {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  updated_at: string;
  applicant: {
    applicant_id: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  license_category: {
    license_category_id: string;
    name: string;
    license_type: {
      license_type_id: string;
      name: string;
    };
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_at?: string;
}

export interface TaskAssignmentOfficer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}





export interface TaskStats {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  cancelled: number;
  on_hold: number;
  unassigned: number;
  assigned: number;
  overdue: number;
}

// Legacy interfaces for backward compatibility
export interface AssignApplicationRequest {
  assignedTo: string;
}