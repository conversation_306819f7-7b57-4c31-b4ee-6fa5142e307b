import { IsEmail, IsString, IsStrong<PERSON>assword, IsUUID, IsJW<PERSON> } from 'class-validator';

export class ForgotPasswordDto {
  @IsEmail()
  email: string;
}

export class ResetPasswordDto {
  @IsString()
  code: string;

  @IsUUID()
  user_id: string;

  @IsString()
  unique: string;

  @IsString()
  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 3,
    minSymbols: 1,
  })
  new_password: string;
}

export class ResetPasswordWithTokenDto {
  @IsJWT({ message: 'Invalid token' })
  token: string;

  @IsString({ message: 'Invalid characters in password!'})
  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 1,
    minSymbols: 1,
  },
    {
      message: 'Password must be at least 8 characters long and contain at least 1 lowercase letter, 1 uppercase letter, 1 number, and 1 symbol',
    }
  )
  new_password: string;
}
