/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/toast.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* Toast container and positioning styles */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none; /* Allow clicks to pass through container */
}

/* Make individual toasts clickable */
.toast-wrapper {
  pointer-events: auto;
}

/* Individual toast wrapper with dynamic positioning */
.toast-wrapper {
  position: relative;
  transition: transform 0.3s ease-in-out;
  pointer-events: auto;
}

/* Dynamic positioning classes for toast stacking */
.toast-wrapper[data-index="0"] {
  transform: translateY(0px);
}

.toast-wrapper[data-index="1"] {
  transform: translateY(10px);
}

.toast-wrapper[data-index="2"] {
  transform: translateY(20px);
}

.toast-wrapper[data-index="3"] {
  transform: translateY(30px);
}

.toast-wrapper[data-index="4"] {
  transform: translateY(40px);
}

.toast-wrapper[data-index="5"] {
  transform: translateY(50px);
}

.toast-wrapper[data-index="6"] {
  transform: translateY(60px);
}

.toast-wrapper[data-index="7"] {
  transform: translateY(70px);
}

.toast-wrapper[data-index="8"] {
  transform: translateY(80px);
}

.toast-wrapper[data-index="9"] {
  transform: translateY(90px);
}

/* Additional indexes beyond 9 - extend as needed */
.toast-wrapper[data-index="10"] {
  transform: translateY(100px);
}

.toast-wrapper[data-index="11"] {
  transform: translateY(110px);
}

.toast-wrapper[data-index="12"] {
  transform: translateY(120px);
}

.toast-wrapper[data-index="13"] {
  transform: translateY(130px);
}

.toast-wrapper[data-index="14"] {
  transform: translateY(140px);
}

.toast-wrapper[data-index="15"] {
  transform: translateY(150px);
}

.toast-wrapper[data-index="16"] {
  transform: translateY(160px);
}

.toast-wrapper[data-index="17"] {
  transform: translateY(170px);
}

.toast-wrapper[data-index="18"] {
  transform: translateY(180px);
}

.toast-wrapper[data-index="19"] {
  transform: translateY(190px);
}

/* For any toasts beyond 19, they'll stack naturally */

/* Responsive positioning for mobile devices */
@media (max-width: 640px) {
  .toast-container {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    width: auto;
  }
}

/* Ensure toasts are visible above modals and other overlays */
.toast-container {
  z-index: 9999 !important;
}

/* Ensure toasts have proper backdrop and are always visible */
.toast-wrapper > div {
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/DataTable.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* DataTable Component Styles */

.data-table-container {
  min-height: 500px;
}
