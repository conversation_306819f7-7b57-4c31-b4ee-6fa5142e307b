import { BaseEntity, UserReference, PaginatedResponse } from './index';

// Enums matching backend
export enum DataBreachCategory {
  PERSONAL_DATA = 'Personal Data',
  FINANCIAL_DATA = 'Financial Data',
  HEALTH_DATA = 'Health Data',
  TECHNICAL_DATA = 'Technical Data',
  COMMUNICATION_DATA = 'Communication Data',
  OTHER = 'Other',
}



// Main data breach report interface
export interface DataBreachReport extends BaseEntity {
  report_id: string;
  report_number: string;
  reporter_id: string;
  title: string;
  description: string;
  category: DataBreachCategory;
  severity: string;
  status?: string;
  priority?: string;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;

  // Related data
  reporter?: UserReference;
  assignee?: UserReference;
}

// Status history interface
export interface DataBreachReportStatusHistory extends BaseEntity {
  history_id: string;
  report_id: string;
  status: string;
  comment?: string;
}

// DTOs for API operations
export interface CreateDataBreachReportData {
  title: string;
  description: string;
  category: string;
  severity: string;
  priority?: string;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  attachments?: File[];
}

export interface UpdateDataBreachReportData {
  title?: string;
  description?: string;
  category?: string;
  severity?: string;
  status?: string;
  priority?: string;
  incident_date?: string;
  organization_involved?: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
}

// Response types
export type DataBreachReportsResponse = PaginatedResponse<DataBreachReport>;

// Filter and query types
export interface DataBreachFilters {
  status?: string;
  severity?: string;
  category?: DataBreachCategory;
  priority?: string;
  assigned_to?: string;
  reporter_id?: string;
  date_from?: string;
  date_to?: string;
}

// Option types for dropdowns
export interface DataBreachOption {
  value: string;
  label: string;
}

// Action types for data breach management
export interface DataBreachAction {
  type: 'close' | 'request_info' | 'assign' | 'update_status';
  note: string;
  attachments?: File[];
  metadata?: Record<string, any>;
}
