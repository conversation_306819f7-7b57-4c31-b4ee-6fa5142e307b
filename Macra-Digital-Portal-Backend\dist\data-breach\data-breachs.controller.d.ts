import { DataBreachReportService } from './data-breachs.service';
import { CreateDataBreachReportDto, UpdateDataBreachReportDto, DataBreachReportFilterDto, UpdateDataBreachReportStatusDto } from 'src/dto/data-breach/data-breach-report.dto';
import { PaginateQuery } from 'nestjs-paginate';
export declare class DataBreachReportController {
    private readonly reportService;
    constructor(reportService: DataBreachReportService);
    create(createDto: CreateDataBreachReportDto, req: any): Promise<import("./data-breachs-constants").DataBreachReportResponseDto>;
    findAll(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<import("../entities").DataBreachReport>>;
    findOne(id: string, req: any): Promise<import("./data-breachs-constants").DataBreachReportResponseDto>;
    update(id: string, updateDto: UpdateDataBreachReportDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breachs-constants").DataBreachReportResponseDto;
    }>;
    delete(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    updateStatus(id: string, statusDto: UpdateDataBreachReportStatusDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breachs-constants").DataBreachReportResponseDto;
    }>;
    assignReport(id: string, assignedTo: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breachs-constants").DataBreachReportResponseDto;
    }>;
    getStatsSummary(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            by_status: {};
            by_category: {};
            by_severity: {};
            by_priority: {};
        };
    }>;
    exportToCsv(filterDto: DataBreachReportFilterDto, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getUrgentAlerts(req: any): Promise<{
        success: boolean;
        message: string;
        data: import("../entities").DataBreachReport[];
    }>;
}
