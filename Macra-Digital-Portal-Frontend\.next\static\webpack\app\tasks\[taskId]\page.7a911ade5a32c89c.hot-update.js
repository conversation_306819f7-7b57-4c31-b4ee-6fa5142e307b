"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/[taskId]/page",{

/***/ "(app-pages-browser)/./src/app/tasks/[taskId]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/tasks/[taskId]/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TaskViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _components_tasks_PaymentTaskComponent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/tasks/PaymentTaskComponent */ \"(app-pages-browser)/./src/components/tasks/PaymentTaskComponent.tsx\");\n/* harmony import */ var _components_tasks_ApplicationTaskComponent__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/tasks/ApplicationTaskComponent */ \"(app-pages-browser)/./src/components/tasks/ApplicationTaskComponent.tsx\");\n/* harmony import */ var _components_tasks_GeneralTaskComponent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/tasks/GeneralTaskComponent */ \"(app-pages-browser)/./src/components/tasks/GeneralTaskComponent.tsx\");\n/* harmony import */ var _components_tasks_DataBreachTaskComponent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/tasks/DataBreachTaskComponent */ \"(app-pages-browser)/./src/components/tasks/DataBreachTaskComponent.tsx\");\n/* harmony import */ var _components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/ConfirmationModal */ \"(app-pages-browser)/./src/components/common/ConfirmationModal.tsx\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _components_evaluation_ActivityNotesModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/evaluation/ActivityNotesModal */ \"(app-pages-browser)/./src/components/evaluation/ActivityNotesModal.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TaskViewPage() {\n    var _task_assignee, _task_assigner;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { showToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    const taskId = params.taskId;\n    const [task, setTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [closingTask, setClosingTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCommunicationModalOpen, setIsCommunicationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCloseTaskModal, setShowCloseTaskModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TaskViewPage.useEffect\": ()=>{\n            if (taskId) {\n                loadTask();\n            }\n        }\n    }[\"TaskViewPage.useEffect\"], [\n        taskId\n    ]);\n    const loadTask = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const task = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskAssignmentService.getTaskById(taskId);\n            setTask(task);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Failed to load task';\n            showToast(errorMessage, 'error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTaskUpdate = async ()=>{\n        // Reload task data after updates\n        await loadTask();\n    };\n    const handleCloseTaskClick = ()=>{\n        setShowCloseTaskModal(true);\n    };\n    const handleCloseTask = async ()=>{\n        if (!task) return;\n        try {\n            setClosingTask(true);\n            const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.updateTask(task.task_id, {\n                status: 'completed'\n            });\n            if (response) {\n                showToast('Task closed successfully', 'success');\n                await loadTask(); // Reload to show updated status\n                setShowCloseTaskModal(false);\n            } else {\n                throw new Error('Failed to close task');\n            }\n        } catch (error) {\n            console.error('Error closing task:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Failed to close task';\n            showToast(errorMessage, 'error');\n        } finally{\n            setClosingTask(false);\n        }\n    };\n    const handleEmailClient = ()=>{\n        setIsCommunicationModalOpen(true);\n    };\n    const renderTaskComponent = ()=>{\n        if (!task) return null;\n        switch(task.task_type){\n            case _types__WEBPACK_IMPORTED_MODULE_10__.TaskType.PAYMENT_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_PaymentTaskComponent__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    task: task,\n                    onTaskUpdate: handleTaskUpdate\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this);\n            case _types__WEBPACK_IMPORTED_MODULE_10__.TaskType.APPLICATION:\n            case _types__WEBPACK_IMPORTED_MODULE_10__.TaskType.EVALUATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_ApplicationTaskComponent__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    task: task,\n                    onTaskUpdate: handleTaskUpdate\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this);\n            case _types__WEBPACK_IMPORTED_MODULE_10__.TaskType.DATA_BREACH:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_DataBreachTaskComponent__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    task: task,\n                    onTaskUpdate: handleTaskUpdate\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_GeneralTaskComponent__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    task: task,\n                    onTaskUpdate: handleTaskUpdate\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getTaskTypeIcon = (taskType)=>{\n        switch(taskType){\n            case 'payment_verification':\n                return 'ri-money-dollar-circle-line';\n            case 'application':\n                return 'ri-file-list-line';\n            case 'evaluation':\n                return 'ri-search-line';\n            case 'document_review':\n                return 'ri-file-text-line';\n            case 'compliance_check':\n                return 'ri-shield-check-line';\n            case 'data_breach':\n                return 'ri-shield-cross-line';\n            default:\n                return 'ri-task-line';\n        }\n    };\n    const getTaskTypeColor = (taskType)=>{\n        switch(taskType){\n            case 'payment_verification':\n                return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\n            case 'application':\n                return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';\n            case 'evaluation':\n                return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900/20';\n            case 'document_review':\n                return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\n            case 'compliance_check':\n                return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\n            case 'data_breach':\n                return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\n            default:\n                return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'pending':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';\n            case 'completed':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\n            case 'on_hold':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';\n            case 'urgent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading task...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !task) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 dark:bg-red-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-error-warning-line text-2xl text-red-600 dark:text-red-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: \"Task Not Found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                        children: error || 'The requested task could not be found.'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.back(),\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-arrow-left-line mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            \"Go Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex mb-6\",\n                \"aria-label\": \"Breadcrumb\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"inline-flex items-center space-x-1 md:space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"inline-flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"inline-flex items-center text-sm font-medium text-gray-700 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-home-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Admin\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-arrow-right-s-line text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/tasks\",\n                                        className: \"ml-1 text-sm font-medium text-gray-700 hover:text-red-600 md:ml-2 dark:text-gray-400 dark:hover:text-red-400\",\n                                        children: \"Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            \"aria-current\": \"page\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-arrow-right-s-line text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400\",\n                                        children: task.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.back(),\n                                            className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            title: \"Go back\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-arrow-left-line text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(getTaskTypeColor(task.task_type)),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"\".concat(getTaskTypeIcon(task.task_type), \" text-xl\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap items-center gap-2 lg:gap-4 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"Task #\",\n                                                                        task.task_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                task.assigned_to && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"Assigned to: \",\n                                                                        (_task_assignee = task.assignee) === null || _task_assignee === void 0 ? void 0 : _task_assignee.first_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                task.due_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        new Date(task.due_date).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-start sm:items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(task.status)),\n                                                children: task.status.replace('_', ' ').toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: (task.entity_type === 'application' || task.entity_type === 'invoice' || task.entity_type === 'payment' || task.task_type === 'application' || task.task_type === 'payment_verification') && task.entity_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleEmailClient,\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                                title: \"Email \".concat(task.entity_type === 'application' ? 'Applicant' : 'Client'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-mail-line mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                children: task.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderTaskComponent()\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            task && task.entity_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_evaluation_ActivityNotesModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isCommunicationModalOpen,\n                onClose: ()=>setIsCommunicationModalOpen(false),\n                entityType: task.entity_type || 'task',\n                entityId: task.entity_id,\n                initialEmails: (_task_assigner = task.assigner) === null || _task_assigner === void 0 ? void 0 : _task_assigner.email\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showCloseTaskModal,\n                onClose: ()=>setShowCloseTaskModal(false),\n                onConfirm: handleCloseTask,\n                title: \"Close Task\",\n                message: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Are you sure you want to close this task?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-green-900 dark:text-green-100 mb-2\",\n                                    children: \"This action will:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-green-700 dark:text-green-300 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Mark the task as completed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Update the task status permanently\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Create an activity log entry\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Notify relevant stakeholders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: \"This action cannot be undone. Make sure all work is completed before closing.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, void 0),\n                confirmText: \"Close Task\",\n                confirmVariant: \"primary\",\n                loading: closingTask,\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-check-line text-green-600 dark:text-green-400 text-xl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\[taskId]\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(TaskViewPage, \"6L+E0RevE04jBhM8CT8isw7/m+Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth\n    ];\n});\n_c = TaskViewPage;\nvar _c;\n$RefreshReg$(_c, \"TaskViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/[taskId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/data-breach/DataBreachActions.tsx":
/*!**********************************************************!*\
  !*** ./src/components/data-breach/DataBreachActions.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/documentService */ \"(app-pages-browser)/./src/services/documentService.ts\");\n/* harmony import */ var _services_activityNotesService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/activityNotesService */ \"(app-pages-browser)/./src/services/activityNotesService.ts\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst DataBreachActions = (param)=>{\n    let { report, onUpdate, onRefreshActivity, className = '' } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [actionType, setActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionNote, setActionNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSubmittingAction, setIsSubmittingAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAction = async (type)=>{\n        if (!report.report_id || !actionNote.trim()) {\n            showError('Please enter a note for this action');\n            return;\n        }\n        setIsSubmittingAction(true);\n        try {\n            // Step 1: Upload attachments if any\n            let uploadedAttachments = [];\n            if (attachments.length > 0) {\n                for (const file of attachments){\n                    try {\n                        const uploadedDoc = await _services_documentService__WEBPACK_IMPORTED_MODULE_3__.documentService.uploadDocument(file, {\n                            entity_type: 'data-breach',\n                            entity_id: report.report_id,\n                            document_type: 'supporting_document',\n                            category: 'data_breach_action'\n                        });\n                        uploadedAttachments.push({\n                            document_id: uploadedDoc.document_id,\n                            file_name: uploadedDoc.file_name,\n                            file_size: uploadedDoc.file_size\n                        });\n                    } catch (uploadError) {\n                        console.error('Error uploading attachment:', uploadError);\n                    // Continue with other attachments even if one fails\n                    }\n                }\n            }\n            // Step 2: Create activity note with the action\n            try {\n                const activityNote = await createDataBreachActivityNote(type, actionNote, uploadedAttachments);\n                console.log('✅ Activity note created:', activityNote);\n            } catch (noteError) {\n                console.error('❌ Failed to create activity note:', noteError);\n                // Continue with status update even if activity note fails\n                showError('Warning: Activity note creation failed, but action will continue');\n            }\n            // Step 3: Update report status if closing\n            if (type === 'close') {\n                await _services_data_breach__WEBPACK_IMPORTED_MODULE_2__.dataBreachService.updateStatus(report.report_id, 'closed');\n                showSuccess('Report closed successfully and activity note created');\n            } else {\n                showSuccess('Information request sent successfully and activity note created');\n            }\n            // Reset form\n            setActionType(null);\n            setActionNote('');\n            setAttachments([]);\n            // Trigger callbacks\n            if (onUpdate) onUpdate();\n            if (onRefreshActivity) onRefreshActivity();\n        } catch (err) {\n            console.error('Error performing action:', err);\n            const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n            showError(\"Failed to \".concat(type === 'close' ? 'close report' : 'send information request', \": \").concat(errorMessage));\n        } finally{\n            setIsSubmittingAction(false);\n        }\n    };\n    const handleFileUpload = (event)=>{\n        const files = Array.from(event.target.files || []);\n        setAttachments((prev)=>[\n                ...prev,\n                ...files\n            ]);\n    };\n    const removeAttachment = (index)=>{\n        setAttachments((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const resetForm = ()=>{\n        setActionType(null);\n        setActionNote('');\n        setAttachments([]);\n    };\n    // Helper function to create data breach activity notes\n    const createDataBreachActivityNote = async function(action, note) {\n        let attachments = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n        return await _services_activityNotesService__WEBPACK_IMPORTED_MODULE_4__.activityNotesService.createDataBreachAction(report.report_id, action, note.trim(), {\n            attachments: attachments,\n            original_status: report.status,\n            severity: report.severity,\n            report_title: report.title,\n            report_category: report.category\n        });\n    };\n    const renderActionForm = ()=>{\n        if (!actionType) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 dark:bg-gray-700 p-6 rounded-lg space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                            children: actionType === 'close' ? 'Close Report' : 'Request Information'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetForm,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                            children: [\n                                actionType === 'close' ? 'Closure Note' : 'Information Request',\n                                \" *\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: actionNote,\n                            onChange: (e)=>setActionNote(e.target.value),\n                            rows: 4,\n                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\",\n                            placeholder: actionType === 'close' ? 'Enter closure notes...' : 'Describe what information is needed...',\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined),\n                actionType === 'request_info' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                            children: \"Attachments (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            multiple: true,\n                            onChange: handleFileUpload,\n                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-gray-100\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, undefined),\n                        attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-1\",\n                            children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-white dark:bg-gray-800 p-2 rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                            children: file.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(index),\n                                            className: \"text-red-500 hover:text-red-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-close-line\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetForm,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleAction(actionType),\n                            disabled: !actionNote.trim() || isSubmittingAction,\n                            className: \"px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed \".concat(actionType === 'close' ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'),\n                            children: isSubmittingAction ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    actionType === 'close' ? 'Closing...' : 'Sending...'\n                                ]\n                            }, void 0, true) : actionType === 'close' ? 'Close Report' : 'Send Request'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                    children: \"Actions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: !actionType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActionType('close'),\n                            disabled: report.status === 'closed',\n                            className: \"px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-close-circle-line mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined),\n                                report.status === 'closed' ? 'Already Closed' : 'Close Report'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActionType('request_info'),\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-question-line mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Request Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, undefined) : renderActionForm()\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachActions.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataBreachActions, \"jWfTWGZIP4wQ3VmOx4noAdSyFdM=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = DataBreachActions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataBreachActions);\nvar _c;\n$RefreshReg$(_c, \"DataBreachActions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RhdGEtYnJlYWNoL0RhdGFCcmVhY2hBY3Rpb25zLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ21CO0FBRUU7QUFDVTtBQUNwQjtBQVNuRCxNQUFNTSxvQkFBc0Q7UUFBQyxFQUMzREMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLGlCQUFpQixFQUNqQkMsWUFBWSxFQUFFLEVBQ2Y7O0lBQ0MsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHUCxnRUFBUUE7SUFDM0MsTUFBTSxDQUFDUSxZQUFZQyxjQUFjLEdBQUdiLCtDQUFRQSxDQUFrQztJQUM5RSxNQUFNLENBQUNjLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQVMsRUFBRTtJQUN6RCxNQUFNLENBQUNrQixvQkFBb0JDLHNCQUFzQixHQUFHbkIsK0NBQVFBLENBQUM7SUFFN0QsTUFBTW9CLGVBQWUsT0FBT0M7UUFDMUIsSUFBSSxDQUFDZixPQUFPZ0IsU0FBUyxJQUFJLENBQUNSLFdBQVdTLElBQUksSUFBSTtZQUMzQ1osVUFBVTtZQUNWO1FBQ0Y7UUFFQVEsc0JBQXNCO1FBQ3RCLElBQUk7WUFDRixvQ0FBb0M7WUFDcEMsSUFBSUssc0JBQTZCLEVBQUU7WUFDbkMsSUFBSVIsWUFBWVMsTUFBTSxHQUFHLEdBQUc7Z0JBQzFCLEtBQUssTUFBTUMsUUFBUVYsWUFBYTtvQkFDOUIsSUFBSTt3QkFDRixNQUFNVyxjQUFjLE1BQU16QixzRUFBZUEsQ0FBQzBCLGNBQWMsQ0FBQ0YsTUFBTTs0QkFDN0RHLGFBQWE7NEJBQ2JDLFdBQVd4QixPQUFPZ0IsU0FBUzs0QkFDM0JTLGVBQWU7NEJBQ2ZDLFVBQVU7d0JBQ1o7d0JBQ0FSLG9CQUFvQlMsSUFBSSxDQUFDOzRCQUN2QkMsYUFBYVAsWUFBWU8sV0FBVzs0QkFDcENDLFdBQVdSLFlBQVlRLFNBQVM7NEJBQ2hDQyxXQUFXVCxZQUFZUyxTQUFTO3dCQUNsQztvQkFDRixFQUFFLE9BQU9DLGFBQWE7d0JBQ3BCQyxRQUFRQyxLQUFLLENBQUMsK0JBQStCRjtvQkFDN0Msb0RBQW9EO29CQUN0RDtnQkFDRjtZQUNGO1lBRUEsK0NBQStDO1lBQy9DLElBQUk7Z0JBQ0YsTUFBTUcsZUFBZSxNQUFNQyw2QkFBNkJwQixNQUFNUCxZQUFZVTtnQkFDMUVjLFFBQVFJLEdBQUcsQ0FBQyw0QkFBNEJGO1lBQzFDLEVBQUUsT0FBT0csV0FBVztnQkFDbEJMLFFBQVFDLEtBQUssQ0FBQyxxQ0FBcUNJO2dCQUNuRCwwREFBMEQ7Z0JBQzFEaEMsVUFBVTtZQUNaO1lBRUEsMENBQTBDO1lBQzFDLElBQUlVLFNBQVMsU0FBUztnQkFDcEIsTUFBTXBCLG9FQUFpQkEsQ0FBQzJDLFlBQVksQ0FBQ3RDLE9BQU9nQixTQUFTLEVBQUU7Z0JBQ3ZEWixZQUFZO1lBQ2QsT0FBTztnQkFDTEEsWUFBWTtZQUNkO1lBRUEsYUFBYTtZQUNiRyxjQUFjO1lBQ2RFLGNBQWM7WUFDZEUsZUFBZSxFQUFFO1lBRWpCLG9CQUFvQjtZQUNwQixJQUFJVixVQUFVQTtZQUNkLElBQUlDLG1CQUFtQkE7UUFFekIsRUFBRSxPQUFPcUMsS0FBYztZQUNyQlAsUUFBUUMsS0FBSyxDQUFDLDRCQUE0Qk07WUFDMUMsTUFBTUMsZUFBZUQsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO1lBQzFEckMsVUFBVSxhQUFnRm1DLE9BQW5FekIsU0FBUyxVQUFVLGlCQUFpQiw0QkFBMkIsTUFBaUIsT0FBYnlCO1FBQzVGLFNBQVU7WUFDUjNCLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsTUFBTThCLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxRQUFRQyxNQUFNQyxJQUFJLENBQUNILE1BQU1JLE1BQU0sQ0FBQ0gsS0FBSyxJQUFJLEVBQUU7UUFDakRsQyxlQUFlc0MsQ0FBQUEsT0FBUTttQkFBSUE7bUJBQVNKO2FBQU07SUFDNUM7SUFFQSxNQUFNSyxtQkFBbUIsQ0FBQ0M7UUFDeEJ4QyxlQUFlc0MsQ0FBQUEsT0FBUUEsS0FBS0csTUFBTSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLE1BQU1IO0lBQ3JEO0lBRUEsTUFBTUksWUFBWTtRQUNoQmhELGNBQWM7UUFDZEUsY0FBYztRQUNkRSxlQUFlLEVBQUU7SUFDbkI7SUFFQSx1REFBdUQ7SUFDdkQsTUFBTXdCLCtCQUErQixlQUNuQ3FCLFFBQ0FDO1lBQ0EvQywrRUFBcUIsRUFBRTtRQUV2QixPQUFPLE1BQU1iLGdGQUFvQkEsQ0FBQzZELHNCQUFzQixDQUN0RDFELE9BQU9nQixTQUFTLEVBQ2hCd0MsUUFDQUMsS0FBS3hDLElBQUksSUFDVDtZQUNFUCxhQUFhQTtZQUNiaUQsaUJBQWlCM0QsT0FBTzRELE1BQU07WUFDOUJDLFVBQVU3RCxPQUFPNkQsUUFBUTtZQUN6QkMsY0FBYzlELE9BQU8rRCxLQUFLO1lBQzFCQyxpQkFBaUJoRSxPQUFPMEIsUUFBUTtRQUNsQztJQUVKO0lBRUEsTUFBTXVDLG1CQUFtQjtRQUN2QixJQUFJLENBQUMzRCxZQUFZLE9BQU87UUFFeEIscUJBQ0UsOERBQUM0RDtZQUFJL0QsV0FBVTs7OEJBQ2IsOERBQUMrRDtvQkFBSS9ELFdBQVU7O3NDQUNiLDhEQUFDZ0U7NEJBQUdoRSxXQUFVO3NDQUNYRyxlQUFlLFVBQVUsaUJBQWlCOzs7Ozs7c0NBRTdDLDhEQUFDOEQ7NEJBQ0NDLFNBQVNkOzRCQUNUcEQsV0FBVTtzQ0FFViw0RUFBQ21EO2dDQUFFbkQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSWpCLDhEQUFDK0Q7O3NDQUNDLDhEQUFDSTs0QkFBTW5FLFdBQVU7O2dDQUNkRyxlQUFlLFVBQVUsaUJBQWlCO2dDQUFzQjs7Ozs7OztzQ0FFbkUsOERBQUNpRTs0QkFDQ0MsT0FBT2hFOzRCQUNQaUUsVUFBVSxDQUFDQyxJQUFNakUsY0FBY2lFLEVBQUUxQixNQUFNLENBQUN3QixLQUFLOzRCQUM3Q0csTUFBTTs0QkFDTnhFLFdBQVU7NEJBQ1Z5RSxhQUFhdEUsZUFBZSxVQUFVLDJCQUEyQjs0QkFDakV1RSxRQUFROzs7Ozs7Ozs7Ozs7Z0JBSVh2RSxlQUFlLGdDQUNkLDhEQUFDNEQ7O3NDQUNDLDhEQUFDSTs0QkFBTW5FLFdBQVU7c0NBQWtFOzs7Ozs7c0NBR25GLDhEQUFDMkU7NEJBQ0MvRCxNQUFLOzRCQUNMZ0UsUUFBUTs0QkFDUk4sVUFBVTlCOzRCQUNWeEMsV0FBVTs7Ozs7O3dCQUVYTyxZQUFZUyxNQUFNLEdBQUcsbUJBQ3BCLDhEQUFDK0M7NEJBQUkvRCxXQUFVO3NDQUNaTyxZQUFZc0UsR0FBRyxDQUFDLENBQUM1RCxNQUFNK0Isc0JBQ3RCLDhEQUFDZTtvQ0FBZ0IvRCxXQUFVOztzREFDekIsOERBQUM4RTs0Q0FBSzlFLFdBQVU7c0RBQTRDaUIsS0FBSzhELElBQUk7Ozs7OztzREFDckUsOERBQUNkOzRDQUNDQyxTQUFTLElBQU1uQixpQkFBaUJDOzRDQUNoQ2hELFdBQVU7c0RBRVYsNEVBQUNtRDtnREFBRW5ELFdBQVU7Ozs7Ozs7Ozs7OzttQ0FOUGdEOzs7Ozs7Ozs7Ozs7Ozs7OzhCQWVwQiw4REFBQ2U7b0JBQUkvRCxXQUFVOztzQ0FDYiw4REFBQ2lFOzRCQUNDQyxTQUFTZDs0QkFDVHBELFdBQVU7c0NBQ1g7Ozs7OztzQ0FHRCw4REFBQ2lFOzRCQUNDQyxTQUFTLElBQU12RCxhQUFhUjs0QkFDNUI2RSxVQUFVLENBQUMzRSxXQUFXUyxJQUFJLE1BQU1MOzRCQUNoQ1QsV0FBVyx1R0FJVixPQUhDRyxlQUFlLFVBQ1gsZ0NBQ0E7c0NBR0xNLG1DQUNDOztrREFDRSw4REFBQ3NEO3dDQUFJL0QsV0FBVTs7Ozs7O29DQUNkRyxlQUFlLFVBQVUsZUFBZTs7K0NBRzNDQSxlQUFlLFVBQVUsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdEQ7SUFFQSxxQkFDRSw4REFBQzREO1FBQUkvRCxXQUFXLG9GQUE4RixPQUFWQTs7MEJBQ2xHLDhEQUFDK0Q7Z0JBQUkvRCxXQUFVOzBCQUNiLDRFQUFDaUY7b0JBQUdqRixXQUFVOzhCQUF1RDs7Ozs7Ozs7Ozs7MEJBRXZFLDhEQUFDK0Q7Z0JBQUkvRCxXQUFVOzBCQUNaLENBQUNHLDJCQUNBLDhEQUFDNEQ7b0JBQUkvRCxXQUFVOztzQ0FDYiw4REFBQ2lFOzRCQUNDQyxTQUFTLElBQU05RCxjQUFjOzRCQUM3QjRFLFVBQVVuRixPQUFPNEQsTUFBTSxLQUFLOzRCQUM1QnpELFdBQVU7OzhDQUVWLDhEQUFDbUQ7b0NBQUVuRCxXQUFVOzs7Ozs7Z0NBQ1pILE9BQU80RCxNQUFNLEtBQUssV0FBVyxtQkFBbUI7Ozs7Ozs7c0NBRW5ELDhEQUFDUTs0QkFDQ0MsU0FBUyxJQUFNOUQsY0FBYzs0QkFDN0JKLFdBQVU7OzhDQUVWLDhEQUFDbUQ7b0NBQUVuRCxXQUFVOzs7Ozs7Z0NBQTRCOzs7Ozs7Ozs7Ozs7Z0NBSzdDOEQ7Ozs7Ozs7Ozs7OztBQUtWO0dBMU9NbEU7O1FBTStCRCw0REFBUUE7OztLQU52Q0M7QUE0T04saUVBQWVBLGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGRhdGEtYnJlYWNoXFxEYXRhQnJlYWNoQWN0aW9ucy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBkYXRhQnJlYWNoU2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvZGF0YS1icmVhY2gnO1xyXG5pbXBvcnQgeyBEYXRhQnJlYWNoUmVwb3J0IH0gZnJvbSAnQC90eXBlcy9kYXRhLWJyZWFjaCc7XHJcbmltcG9ydCB7IGRvY3VtZW50U2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvZG9jdW1lbnRTZXJ2aWNlJztcclxuaW1wb3J0IHsgYWN0aXZpdHlOb3Rlc1NlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL2FjdGl2aXR5Tm90ZXNTZXJ2aWNlJztcclxuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2NvbnRleHRzL1RvYXN0Q29udGV4dCc7XHJcblxyXG5pbnRlcmZhY2UgRGF0YUJyZWFjaEFjdGlvbnNQcm9wcyB7XHJcbiAgcmVwb3J0OiBEYXRhQnJlYWNoUmVwb3J0O1xyXG4gIG9uVXBkYXRlPzogKCkgPT4gdm9pZDtcclxuICBvblJlZnJlc2hBY3Rpdml0eT86ICgpID0+IHZvaWQ7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59XHJcblxyXG5jb25zdCBEYXRhQnJlYWNoQWN0aW9uczogUmVhY3QuRkM8RGF0YUJyZWFjaEFjdGlvbnNQcm9wcz4gPSAoe1xyXG4gIHJlcG9ydCxcclxuICBvblVwZGF0ZSxcclxuICBvblJlZnJlc2hBY3Rpdml0eSxcclxuICBjbGFzc05hbWUgPSAnJ1xyXG59KSA9PiB7XHJcbiAgY29uc3QgeyBzaG93U3VjY2Vzcywgc2hvd0Vycm9yIH0gPSB1c2VUb2FzdCgpO1xyXG4gIGNvbnN0IFthY3Rpb25UeXBlLCBzZXRBY3Rpb25UeXBlXSA9IHVzZVN0YXRlPCdjbG9zZScgfCAncmVxdWVzdF9pbmZvJyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFthY3Rpb25Ob3RlLCBzZXRBY3Rpb25Ob3RlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbYXR0YWNobWVudHMsIHNldEF0dGFjaG1lbnRzXSA9IHVzZVN0YXRlPEZpbGVbXT4oW10pO1xyXG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmdBY3Rpb24sIHNldElzU3VibWl0dGluZ0FjdGlvbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUFjdGlvbiA9IGFzeW5jICh0eXBlOiAnY2xvc2UnIHwgJ3JlcXVlc3RfaW5mbycpID0+IHtcclxuICAgIGlmICghcmVwb3J0LnJlcG9ydF9pZCB8fCAhYWN0aW9uTm90ZS50cmltKCkpIHtcclxuICAgICAgc2hvd0Vycm9yKCdQbGVhc2UgZW50ZXIgYSBub3RlIGZvciB0aGlzIGFjdGlvbicpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0SXNTdWJtaXR0aW5nQWN0aW9uKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gU3RlcCAxOiBVcGxvYWQgYXR0YWNobWVudHMgaWYgYW55XHJcbiAgICAgIGxldCB1cGxvYWRlZEF0dGFjaG1lbnRzOiBhbnlbXSA9IFtdO1xyXG4gICAgICBpZiAoYXR0YWNobWVudHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGZvciAoY29uc3QgZmlsZSBvZiBhdHRhY2htZW50cykge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgdXBsb2FkZWREb2MgPSBhd2FpdCBkb2N1bWVudFNlcnZpY2UudXBsb2FkRG9jdW1lbnQoZmlsZSwge1xyXG4gICAgICAgICAgICAgIGVudGl0eV90eXBlOiAnZGF0YS1icmVhY2gnLFxyXG4gICAgICAgICAgICAgIGVudGl0eV9pZDogcmVwb3J0LnJlcG9ydF9pZCxcclxuICAgICAgICAgICAgICBkb2N1bWVudF90eXBlOiAnc3VwcG9ydGluZ19kb2N1bWVudCcsXHJcbiAgICAgICAgICAgICAgY2F0ZWdvcnk6ICdkYXRhX2JyZWFjaF9hY3Rpb24nXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB1cGxvYWRlZEF0dGFjaG1lbnRzLnB1c2goe1xyXG4gICAgICAgICAgICAgIGRvY3VtZW50X2lkOiB1cGxvYWRlZERvYy5kb2N1bWVudF9pZCxcclxuICAgICAgICAgICAgICBmaWxlX25hbWU6IHVwbG9hZGVkRG9jLmZpbGVfbmFtZSxcclxuICAgICAgICAgICAgICBmaWxlX3NpemU6IHVwbG9hZGVkRG9jLmZpbGVfc2l6ZVxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH0gY2F0Y2ggKHVwbG9hZEVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwbG9hZGluZyBhdHRhY2htZW50OicsIHVwbG9hZEVycm9yKTtcclxuICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aCBvdGhlciBhdHRhY2htZW50cyBldmVuIGlmIG9uZSBmYWlsc1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU3RlcCAyOiBDcmVhdGUgYWN0aXZpdHkgbm90ZSB3aXRoIHRoZSBhY3Rpb25cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBhY3Rpdml0eU5vdGUgPSBhd2FpdCBjcmVhdGVEYXRhQnJlYWNoQWN0aXZpdHlOb3RlKHR5cGUsIGFjdGlvbk5vdGUsIHVwbG9hZGVkQXR0YWNobWVudHMpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgQWN0aXZpdHkgbm90ZSBjcmVhdGVkOicsIGFjdGl2aXR5Tm90ZSk7XHJcbiAgICAgIH0gY2F0Y2ggKG5vdGVFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gY3JlYXRlIGFjdGl2aXR5IG5vdGU6Jywgbm90ZUVycm9yKTtcclxuICAgICAgICAvLyBDb250aW51ZSB3aXRoIHN0YXR1cyB1cGRhdGUgZXZlbiBpZiBhY3Rpdml0eSBub3RlIGZhaWxzXHJcbiAgICAgICAgc2hvd0Vycm9yKCdXYXJuaW5nOiBBY3Rpdml0eSBub3RlIGNyZWF0aW9uIGZhaWxlZCwgYnV0IGFjdGlvbiB3aWxsIGNvbnRpbnVlJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFN0ZXAgMzogVXBkYXRlIHJlcG9ydCBzdGF0dXMgaWYgY2xvc2luZ1xyXG4gICAgICBpZiAodHlwZSA9PT0gJ2Nsb3NlJykge1xyXG4gICAgICAgIGF3YWl0IGRhdGFCcmVhY2hTZXJ2aWNlLnVwZGF0ZVN0YXR1cyhyZXBvcnQucmVwb3J0X2lkLCAnY2xvc2VkJyk7XHJcbiAgICAgICAgc2hvd1N1Y2Nlc3MoJ1JlcG9ydCBjbG9zZWQgc3VjY2Vzc2Z1bGx5IGFuZCBhY3Rpdml0eSBub3RlIGNyZWF0ZWQnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzaG93U3VjY2VzcygnSW5mb3JtYXRpb24gcmVxdWVzdCBzZW50IHN1Y2Nlc3NmdWxseSBhbmQgYWN0aXZpdHkgbm90ZSBjcmVhdGVkJyk7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIFJlc2V0IGZvcm1cclxuICAgICAgc2V0QWN0aW9uVHlwZShudWxsKTtcclxuICAgICAgc2V0QWN0aW9uTm90ZSgnJyk7XHJcbiAgICAgIHNldEF0dGFjaG1lbnRzKFtdKTtcclxuICAgICAgXHJcbiAgICAgIC8vIFRyaWdnZXIgY2FsbGJhY2tzXHJcbiAgICAgIGlmIChvblVwZGF0ZSkgb25VcGRhdGUoKTtcclxuICAgICAgaWYgKG9uUmVmcmVzaEFjdGl2aXR5KSBvblJlZnJlc2hBY3Rpdml0eSgpO1xyXG4gICAgICBcclxuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwZXJmb3JtaW5nIGFjdGlvbjonLCBlcnIpO1xyXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InO1xyXG4gICAgICBzaG93RXJyb3IoYEZhaWxlZCB0byAke3R5cGUgPT09ICdjbG9zZScgPyAnY2xvc2UgcmVwb3J0JyA6ICdzZW5kIGluZm9ybWF0aW9uIHJlcXVlc3QnfTogJHtlcnJvck1lc3NhZ2V9YCk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc1N1Ym1pdHRpbmdBY3Rpb24oZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUZpbGVVcGxvYWQgPSAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XHJcbiAgICBjb25zdCBmaWxlcyA9IEFycmF5LmZyb20oZXZlbnQudGFyZ2V0LmZpbGVzIHx8IFtdKTtcclxuICAgIHNldEF0dGFjaG1lbnRzKHByZXYgPT4gWy4uLnByZXYsIC4uLmZpbGVzXSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVtb3ZlQXR0YWNobWVudCA9IChpbmRleDogbnVtYmVyKSA9PiB7XHJcbiAgICBzZXRBdHRhY2htZW50cyhwcmV2ID0+IHByZXYuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHJlc2V0Rm9ybSA9ICgpID0+IHtcclxuICAgIHNldEFjdGlvblR5cGUobnVsbCk7XHJcbiAgICBzZXRBY3Rpb25Ob3RlKCcnKTtcclxuICAgIHNldEF0dGFjaG1lbnRzKFtdKTtcclxuICB9O1xyXG5cclxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY3JlYXRlIGRhdGEgYnJlYWNoIGFjdGl2aXR5IG5vdGVzXHJcbiAgY29uc3QgY3JlYXRlRGF0YUJyZWFjaEFjdGl2aXR5Tm90ZSA9IGFzeW5jIChcclxuICAgIGFjdGlvbjogJ2Nsb3NlJyB8ICdyZXF1ZXN0X2luZm8nLFxyXG4gICAgbm90ZTogc3RyaW5nLFxyXG4gICAgYXR0YWNobWVudHM6IGFueVtdID0gW11cclxuICApID0+IHtcclxuICAgIHJldHVybiBhd2FpdCBhY3Rpdml0eU5vdGVzU2VydmljZS5jcmVhdGVEYXRhQnJlYWNoQWN0aW9uKFxyXG4gICAgICByZXBvcnQucmVwb3J0X2lkLFxyXG4gICAgICBhY3Rpb24sXHJcbiAgICAgIG5vdGUudHJpbSgpLFxyXG4gICAgICB7XHJcbiAgICAgICAgYXR0YWNobWVudHM6IGF0dGFjaG1lbnRzLFxyXG4gICAgICAgIG9yaWdpbmFsX3N0YXR1czogcmVwb3J0LnN0YXR1cyxcclxuICAgICAgICBzZXZlcml0eTogcmVwb3J0LnNldmVyaXR5LFxyXG4gICAgICAgIHJlcG9ydF90aXRsZTogcmVwb3J0LnRpdGxlLFxyXG4gICAgICAgIHJlcG9ydF9jYXRlZ29yeTogcmVwb3J0LmNhdGVnb3J5XHJcbiAgICAgIH1cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVuZGVyQWN0aW9uRm9ybSA9ICgpID0+IHtcclxuICAgIGlmICghYWN0aW9uVHlwZSkgcmV0dXJuIG51bGw7XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGRhcms6YmctZ3JheS03MDAgcC02IHJvdW5kZWQtbGcgc3BhY2UteS00XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgIHthY3Rpb25UeXBlID09PSAnY2xvc2UnID8gJ0Nsb3NlIFJlcG9ydCcgOiAnUmVxdWVzdCBJbmZvcm1hdGlvbid9XHJcbiAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXtyZXNldEZvcm19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCBkYXJrOmhvdmVyOnRleHQtZ3JheS0zMDBcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1jbG9zZS1saW5lIHRleHQteGxcIj48L2k+XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAge2FjdGlvblR5cGUgPT09ICdjbG9zZScgPyAnQ2xvc3VyZSBOb3RlJyA6ICdJbmZvcm1hdGlvbiBSZXF1ZXN0J30gKlxyXG4gICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgICB2YWx1ZT17YWN0aW9uTm90ZX1cclxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBY3Rpb25Ob3RlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgcm93cz17NH1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMTAwXCJcclxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2FjdGlvblR5cGUgPT09ICdjbG9zZScgPyAnRW50ZXIgY2xvc3VyZSBub3Rlcy4uLicgOiAnRGVzY3JpYmUgd2hhdCBpbmZvcm1hdGlvbiBpcyBuZWVkZWQuLi4nfVxyXG4gICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAge2FjdGlvblR5cGUgPT09ICdyZXF1ZXN0X2luZm8nICYmIChcclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICBBdHRhY2htZW50cyAoT3B0aW9uYWwpXHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcclxuICAgICAgICAgICAgICBtdWx0aXBsZVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWxlVXBsb2FkfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIHRleHQtc20gZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICB7YXR0YWNobWVudHMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAge2F0dGFjaG1lbnRzLm1hcCgoZmlsZSwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTIgcm91bmRlZCBib3JkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+e2ZpbGUubmFtZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlQXR0YWNobWVudChpbmRleCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgaG92ZXI6dGV4dC1yZWQtNzAwXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1jbG9zZS1saW5lXCI+PC9pPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXtyZXNldEZvcm19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTYwMFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFjdGlvbihhY3Rpb25UeXBlKX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9eyFhY3Rpb25Ob3RlLnRyaW0oKSB8fCBpc1N1Ym1pdHRpbmdBY3Rpb259XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgcm91bmRlZC1tZCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCAke1xyXG4gICAgICAgICAgICAgIGFjdGlvblR5cGUgPT09ICdjbG9zZScgXHJcbiAgICAgICAgICAgICAgICA/ICdiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAnIFxyXG4gICAgICAgICAgICAgICAgOiAnYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAnXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7aXNTdWJtaXR0aW5nQWN0aW9uID8gKFxyXG4gICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci13aGl0ZSBtci0yIGlubGluZS1ibG9ja1wiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAge2FjdGlvblR5cGUgPT09ICdjbG9zZScgPyAnQ2xvc2luZy4uLicgOiAnU2VuZGluZy4uLid9XHJcbiAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgYWN0aW9uVHlwZSA9PT0gJ2Nsb3NlJyA/ICdDbG9zZSBSZXBvcnQnIDogJ1NlbmQgUmVxdWVzdCdcclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgJHtjbGFzc05hbWV9YH0+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMFwiPlxyXG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+QWN0aW9uczwvaDM+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxyXG4gICAgICAgIHshYWN0aW9uVHlwZSA/IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTNcIj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGlvblR5cGUoJ2Nsb3NlJyl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlcG9ydC5zdGF0dXMgPT09ICdjbG9zZWQnfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1yZWQtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCB0ZXh0LXNtIGhvdmVyOmJnLXJlZC03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktY2xvc2UtY2lyY2xlLWxpbmUgbXItMlwiPjwvaT5cclxuICAgICAgICAgICAgICB7cmVwb3J0LnN0YXR1cyA9PT0gJ2Nsb3NlZCcgPyAnQWxyZWFkeSBDbG9zZWQnIDogJ0Nsb3NlIFJlcG9ydCd9XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aW9uVHlwZSgncmVxdWVzdF9pbmZvJyl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCB0ZXh0LXNtIGhvdmVyOmJnLWJsdWUtNzAwIGZsZXggaXRlbXMtY2VudGVyXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cInJpLXF1ZXN0aW9uLWxpbmUgbXItMlwiPjwvaT5cclxuICAgICAgICAgICAgICBSZXF1ZXN0IEluZm9ybWF0aW9uXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgIHJlbmRlckFjdGlvbkZvcm0oKVxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IERhdGFCcmVhY2hBY3Rpb25zO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsImRhdGFCcmVhY2hTZXJ2aWNlIiwiZG9jdW1lbnRTZXJ2aWNlIiwiYWN0aXZpdHlOb3Rlc1NlcnZpY2UiLCJ1c2VUb2FzdCIsIkRhdGFCcmVhY2hBY3Rpb25zIiwicmVwb3J0Iiwib25VcGRhdGUiLCJvblJlZnJlc2hBY3Rpdml0eSIsImNsYXNzTmFtZSIsInNob3dTdWNjZXNzIiwic2hvd0Vycm9yIiwiYWN0aW9uVHlwZSIsInNldEFjdGlvblR5cGUiLCJhY3Rpb25Ob3RlIiwic2V0QWN0aW9uTm90ZSIsImF0dGFjaG1lbnRzIiwic2V0QXR0YWNobWVudHMiLCJpc1N1Ym1pdHRpbmdBY3Rpb24iLCJzZXRJc1N1Ym1pdHRpbmdBY3Rpb24iLCJoYW5kbGVBY3Rpb24iLCJ0eXBlIiwicmVwb3J0X2lkIiwidHJpbSIsInVwbG9hZGVkQXR0YWNobWVudHMiLCJsZW5ndGgiLCJmaWxlIiwidXBsb2FkZWREb2MiLCJ1cGxvYWREb2N1bWVudCIsImVudGl0eV90eXBlIiwiZW50aXR5X2lkIiwiZG9jdW1lbnRfdHlwZSIsImNhdGVnb3J5IiwicHVzaCIsImRvY3VtZW50X2lkIiwiZmlsZV9uYW1lIiwiZmlsZV9zaXplIiwidXBsb2FkRXJyb3IiLCJjb25zb2xlIiwiZXJyb3IiLCJhY3Rpdml0eU5vdGUiLCJjcmVhdGVEYXRhQnJlYWNoQWN0aXZpdHlOb3RlIiwibG9nIiwibm90ZUVycm9yIiwidXBkYXRlU3RhdHVzIiwiZXJyIiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlRmlsZVVwbG9hZCIsImV2ZW50IiwiZmlsZXMiLCJBcnJheSIsImZyb20iLCJ0YXJnZXQiLCJwcmV2IiwicmVtb3ZlQXR0YWNobWVudCIsImluZGV4IiwiZmlsdGVyIiwiXyIsImkiLCJyZXNldEZvcm0iLCJhY3Rpb24iLCJub3RlIiwiY3JlYXRlRGF0YUJyZWFjaEFjdGlvbiIsIm9yaWdpbmFsX3N0YXR1cyIsInN0YXR1cyIsInNldmVyaXR5IiwicmVwb3J0X3RpdGxlIiwidGl0bGUiLCJyZXBvcnRfY2F0ZWdvcnkiLCJyZW5kZXJBY3Rpb25Gb3JtIiwiZGl2IiwiaDQiLCJidXR0b24iLCJvbkNsaWNrIiwibGFiZWwiLCJ0ZXh0YXJlYSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwicm93cyIsInBsYWNlaG9sZGVyIiwicmVxdWlyZWQiLCJpbnB1dCIsIm11bHRpcGxlIiwibWFwIiwic3BhbiIsIm5hbWUiLCJkaXNhYmxlZCIsImgzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/data-breach/DataBreachActions.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/data-breach/DataBreachCard.tsx":
/*!*******************************************************!*\
  !*** ./src/components/data-breach/DataBreachCard.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_evaluation_DataDisplayCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/evaluation/DataDisplayCard */ \"(app-pages-browser)/./src/components/evaluation/DataDisplayCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst DataBreachCard = (param)=>{\n    let { report, className = '', showEmptyFields = true, defaultCollapsed = false } = param;\n    var _report_reporter, _report_reporter1;\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'submitted':\n                return 'text-blue-600';\n            case 'under_review':\n                return 'text-yellow-600';\n            case 'investigating':\n                return 'text-orange-600';\n            case 'resolved':\n                return 'text-green-600';\n            case 'closed':\n                return 'text-gray-600';\n            default:\n                return 'text-gray-600';\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case 'low':\n                return 'text-green-600';\n            case 'medium':\n                return 'text-yellow-600';\n            case 'high':\n                return 'text-orange-600';\n            case 'critical':\n                return 'text-red-600';\n            default:\n                return 'text-gray-600';\n        }\n    };\n    const formatStatus = (status)=>{\n        return (status === null || status === void 0 ? void 0 : status.replace('_', ' ').toUpperCase()) || 'UNKNOWN';\n    };\n    const formatSeverity = (severity)=>{\n        return (severity === null || severity === void 0 ? void 0 : severity.toUpperCase()) || 'UNKNOWN';\n    };\n    const formatPriority = (priority)=>{\n        return (priority === null || priority === void 0 ? void 0 : priority.toUpperCase()) || 'MEDIUM';\n    };\n    const reportFields = [\n        {\n            label: 'Report Number',\n            value: report.report_number,\n            icon: 'ri-file-text-line',\n            type: 'text'\n        },\n        {\n            label: 'Title',\n            value: report.title,\n            icon: 'ri-article-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Status',\n            value: formatStatus(report.status),\n            icon: 'ri-flag-line',\n            type: 'text'\n        },\n        {\n            label: 'Severity',\n            value: formatSeverity(report.severity),\n            icon: 'ri-alert-line',\n            type: 'text'\n        },\n        {\n            label: 'Priority',\n            value: formatPriority(report.priority),\n            icon: 'ri-star-line',\n            type: 'text'\n        },\n        {\n            label: 'Category',\n            value: report.category,\n            icon: 'ri-folder-line',\n            type: 'text'\n        },\n        {\n            label: 'Incident Date',\n            value: report.incident_date,\n            icon: 'ri-calendar-line',\n            type: 'date'\n        },\n        {\n            label: 'Organization Involved',\n            value: report.organization_involved,\n            icon: 'ri-building-line',\n            type: 'text'\n        },\n        {\n            label: 'Reporter Name',\n            value: report.reporter ? \"\".concat(report.reporter.first_name, \" \").concat(report.reporter.last_name) : 'Unknown',\n            icon: 'ri-user-line',\n            type: 'text'\n        },\n        {\n            label: 'Reporter Email',\n            value: (_report_reporter = report.reporter) === null || _report_reporter === void 0 ? void 0 : _report_reporter.email,\n            icon: 'ri-mail-line',\n            type: 'email'\n        },\n        {\n            label: 'Assigned To',\n            value: report.assigned_to || 'Unassigned',\n            icon: 'ri-user-star-line',\n            type: 'text'\n        },\n        {\n            label: 'Affected Data Types',\n            value: report.affected_data_types,\n            icon: 'ri-database-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Contact Attempts',\n            value: report.contact_attempts,\n            icon: 'ri-phone-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Description',\n            value: report.description,\n            icon: 'ri-file-text-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Resolution',\n            value: report.resolution,\n            icon: 'ri-check-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Internal Notes',\n            value: report.internal_notes,\n            icon: 'ri-sticky-note-line',\n            type: 'text',\n            fullWidth: true\n        },\n        {\n            label: 'Created Date',\n            value: report.created_at,\n            icon: 'ri-calendar-line',\n            type: 'date'\n        },\n        {\n            label: 'Last Updated',\n            value: report.updated_at,\n            icon: 'ri-calendar-line',\n            type: 'date'\n        },\n        {\n            label: 'Resolved Date',\n            value: report.resolved_at,\n            icon: 'ri-calendar-check-line',\n            type: 'date'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_evaluation_DataDisplayCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"Data Breach Report Details\",\n        icon: \"ri-shield-cross-line\",\n        fields: reportFields,\n        className: className,\n        showEmptyFields: showEmptyFields,\n        defaultCollapsed: defaultCollapsed,\n        creatorEmail: (_report_reporter1 = report.reporter) === null || _report_reporter1 === void 0 ? void 0 : _report_reporter1.email,\n        creatorName: report.reporter ? \"\".concat(report.reporter.first_name, \" \").concat(report.reporter.last_name) : undefined,\n        entityId: report.report_id,\n        entityType: \"data-breach\",\n        showEmailButton: true\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\data-breach\\\\DataBreachCard.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_c = DataBreachCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataBreachCard);\nvar _c;\n$RefreshReg$(_c, \"DataBreachCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/data-breach/DataBreachCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/tasks/DataBreachTaskComponent.tsx":
/*!**********************************************************!*\
  !*** ./src/components/tasks/DataBreachTaskComponent.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _data_breach_DataBreachCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../data-breach/DataBreachCard */ \"(app-pages-browser)/./src/components/data-breach/DataBreachCard.tsx\");\n/* harmony import */ var _data_breach_DataBreachActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data-breach/DataBreachActions */ \"(app-pages-browser)/./src/components/data-breach/DataBreachActions.tsx\");\n/* harmony import */ var _components_evaluation_ActivityNotesModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/evaluation/ActivityNotesModal */ \"(app-pages-browser)/./src/components/evaluation/ActivityNotesModal.tsx\");\n/* harmony import */ var _components_common_ActivityHistory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/common/ActivityHistory */ \"(app-pages-browser)/./src/components/common/ActivityHistory.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DataBreachTaskComponent = (param)=>{\n    let { task, onTaskUpdate } = param;\n    var _task_status, _task_priority, _report_reporter;\n    _s();\n    const { showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [report, setReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isActivityNotesModalOpen, setIsActivityNotesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshTrigger, setRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataBreachTaskComponent.useEffect\": ()=>{\n            if (task.entity_id) {\n                fetchDataBreachReport();\n            }\n        }\n    }[\"DataBreachTaskComponent.useEffect\"], [\n        task.entity_id\n    ]);\n    const fetchDataBreachReport = async ()=>{\n        if (!task.entity_id) {\n            setError('No data breach report ID provided');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _services_data_breach__WEBPACK_IMPORTED_MODULE_2__.dataBreachService.getReportById(task.entity_id);\n            setReport(response);\n        } catch (err) {\n            console.error('Error fetching data breach report:', err);\n            const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n            setError(\"Failed to load data breach report: \".concat(errorMessage));\n            showError(\"Failed to load data breach report: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleReportUpdate = ()=>{\n        // Refresh the report data\n        fetchDataBreachReport();\n        // Trigger task update if callback provided\n        if (onTaskUpdate) {\n            onTaskUpdate();\n        }\n    };\n    const handleRefreshActivity = ()=>{\n        setRefreshTrigger((prev)=>prev + 1);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-3 text-gray-600 dark:text-gray-400\",\n                    children: \"Loading data breach report...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-error-warning-line text-red-400 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-700 dark:text-red-200\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchDataBreachReport,\n                                className: \"mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline\",\n                                children: \"Try again\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!report) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"ri-file-warning-line text-4xl text-gray-300 dark:text-gray-600 mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"No data breach report found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-400 dark:text-gray-500 mt-1\",\n                    children: [\n                        \"Report ID: \",\n                        task.entity_id\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-task-line text-blue-600 dark:text-blue-400 mr-3 mt-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-900 dark:text-blue-100\",\n                                    children: \"Data Breach Investigation Task\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-700 dark:text-blue-300 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Task:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        task.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-700 dark:text-blue-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Description:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        task.description\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2 text-sm text-blue-600 dark:text-blue-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" \",\n                                                (_task_status = task.status) === null || _task_status === void 0 ? void 0 : _task_status.replace('_', ' ').toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Priority:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" \",\n                                                (_task_priority = task.priority) === null || _task_priority === void 0 ? void 0 : _task_priority.toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Due:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" \",\n                                                task.due_date ? new Date(task.due_date).toLocaleDateString() : 'Not set'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_data_breach_DataBreachCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                report: report,\n                showEmptyFields: true,\n                defaultCollapsed: false\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                children: \"Activity History\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsActivityNotesModalOpen(true),\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-mail-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Send Message\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ActivityHistory__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            entityType: \"data-breach\",\n                            entityId: report.report_id,\n                            title: \"\",\n                            showSearch: true,\n                            showFilters: false,\n                            maxHeight: \"max-h-96\",\n                            refreshTrigger: refreshTrigger,\n                            className: \"border-0 rounded-none\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_data_breach_DataBreachActions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                report: report,\n                onUpdate: handleReportUpdate,\n                onRefreshActivity: handleRefreshActivity\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_evaluation_ActivityNotesModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isActivityNotesModalOpen,\n                onClose: ()=>{\n                    setIsActivityNotesModalOpen(false);\n                    handleRefreshActivity();\n                },\n                entityId: report.report_id,\n                entityType: \"data-breach\",\n                initialEmails: ((_report_reporter = report.reporter) === null || _report_reporter === void 0 ? void 0 : _report_reporter.email) || '',\n                title: \"Data Breach Report - \".concat(report.report_number)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\tasks\\\\DataBreachTaskComponent.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataBreachTaskComponent, \"UnpQ7FVcYdKYyfm5LQfJzS90kaU=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = DataBreachTaskComponent;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataBreachTaskComponent);\nvar _c;\n$RefreshReg$(_c, \"DataBreachTaskComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/tasks/DataBreachTaskComponent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/activityNotesService.ts":
/*!**********************************************!*\
  !*** ./src/services/activityNotesService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityNotesService: () => (/* binding */ activityNotesService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nclass ActivityNotesService {\n    async create(data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(this.baseUrl, data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findAll(query) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(this.baseUrl, {\n            params: query\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntity(entityType, entityId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntityAndStep(entityType, entityId, step) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId, \"/step/\").concat(step));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findOne(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/\").concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async update(id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async archive(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id, \"/archive\"));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async softDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/soft\"));\n    }\n    async hardDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/hard\"));\n    }\n    // Specialized methods for evaluation workflow\n    async createEvaluationComment(applicationId, step, comment, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/evaluation-comment\"), {\n            applicationId,\n            step,\n            comment,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async createStatusUpdate(applicationId, statusChange, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/status-update\"), {\n            applicationId,\n            statusChange,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    // Helper methods for common use cases\n    async getEvaluationComments(applicationId, step) {\n        const query = {\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'evaluation_comment',\n            status: 'active'\n        };\n        if (step) {\n            query.step = step;\n        }\n        return this.findAll(query);\n    }\n    async getApplicationNotes(applicationId) {\n        return this.findByEntity('application', applicationId);\n    }\n    async getApplicationStatusUpdates(applicationId) {\n        return this.findAll({\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'status_update',\n            status: 'active'\n        });\n    }\n    // Data breach specific methods\n    async createDataBreachAction(reportId, action, note, metadata) {\n        return this.create({\n            entity_type: 'data-breach',\n            entity_id: reportId,\n            note: note,\n            note_type: action === 'close' ? 'status_update' : 'data_breach_action',\n            category: 'data_breach_management',\n            metadata: {\n                action,\n                timestamp: new Date().toISOString(),\n                ...metadata\n            },\n            priority: action === 'close' ? 'high' : 'normal',\n            is_internal: false\n        });\n    }\n    async getDataBreachNotes(reportId) {\n        return this.findByEntity('data-breach', reportId);\n    }\n    constructor(){\n        this.baseUrl = '/activity-notes';\n    }\n}\nconst activityNotesService = new ActivityNotesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/activityNotesService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/dataBreachService.ts":
/*!*******************************************************!*\
  !*** ./src/services/data-breach/dataBreachService.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataBreachService: () => (/* binding */ dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_documentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/documentService */ \"(app-pages-browser)/./src/services/documentService.ts\");\n\n\n\nconst dataBreachService = {\n    // Create new report\n    async createReport (data) {\n        try {\n            console.log('🔄 Creating data breach report:', {\n                title: data.title,\n                category: data.category,\n                severity: data.severity,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            // Create report without attachments first\n            const reportData = {\n                title: data.title,\n                description: data.description,\n                category: data.category,\n                severity: data.severity,\n                incident_date: data.incident_date,\n                organization_involved: data.organization_involved,\n                priority: data.priority,\n                affected_data_types: data.affected_data_types,\n                contact_attempts: data.contact_attempts\n            };\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/data-breach-reports', reportData);\n            const report = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n            // Upload attachments to polymorphic documents system if provided\n            if (data.attachments && data.attachments.length > 0) {\n                await this.uploadAttachments(report.report_id, data.attachments);\n            }\n            console.log('✅ Data breach report created successfully:', report);\n            return report;\n        } catch (error) {\n            console.error('❌ Error creating data breach report:', error);\n            throw error;\n        }\n    },\n    // Upload attachments using polymorphic documents system\n    async uploadAttachments (reportId, files) {\n        try {\n            for (const file of files){\n                await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.uploadDocument(file, {\n                    document_type: 'data_breach_attachment',\n                    entity_type: 'data-breach',\n                    entity_id: reportId,\n                    is_required: false\n                });\n            }\n            console.log(\"✅ Uploaded \".concat(files.length, \" attachments for report \").concat(reportId));\n        } catch (error) {\n            console.error('❌ Error uploading attachments:', error);\n            throw error;\n        }\n    },\n    // Get report by ID\n    async getReportById (reportId) {\n        try {\n            console.log('🔄 Fetching data breach report by ID:', reportId);\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(reportId));\n            console.log('✅ Data breach report fetched successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error fetching data breach report:', error);\n            throw error;\n        }\n    },\n    // Update report status\n    async updateStatus (reportId, status, comment) {\n        try {\n            console.log('🔄 Updating data breach report status:', {\n                reportId,\n                status,\n                comment\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/status\"), {\n                status,\n                comment\n            });\n            console.log('✅ Data breach report status updated successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error updating data breach report status:', error);\n            throw error;\n        }\n    },\n    // Assign report to officer\n    async assignReport (reportId, assignedTo) {\n        try {\n            console.log('🔄 Assigning data breach report:', {\n                reportId,\n                assignedTo\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/assign\"), {\n                assigned_to: assignedTo\n            });\n            console.log('✅ Data breach report assigned successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error assigning data breach report:', error);\n            throw error;\n        }\n    },\n    // Get all reports with pagination\n    async getReports () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get report by ID\n    async getReport (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Update report\n    async updateReport (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Delete report\n    async deleteReport (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/data-breach-reports/\".concat(id));\n    },\n    // Update report status (for staff)\n    async updateReportStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Add attachment to report using polymorphic documents\n    async addAttachment (reportId, file) {\n        try {\n            const document = await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.uploadDocument(file, {\n                document_type: 'data_breach_attachment',\n                entity_type: 'data-breach',\n                entity_id: reportId,\n                is_required: false\n            });\n            console.log('✅ Attachment added to data breach report:', document);\n            return document;\n        } catch (error) {\n            console.error('❌ Error adding attachment:', error);\n            throw error;\n        }\n    },\n    // Remove attachment from report using polymorphic documents\n    async removeAttachment (documentId) {\n        try {\n            await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.deleteDocument(documentId);\n            console.log('✅ Attachment removed from data breach report');\n        } catch (error) {\n            console.error('❌ Error removing attachment:', error);\n            throw error;\n        }\n    },\n    // Get attachments for a report\n    async getAttachments (reportId) {\n        try {\n            const documents = await _services_documentService__WEBPACK_IMPORTED_MODULE_2__.documentService.getDocumentsByEntity('data-breach', reportId);\n            return documents;\n        } catch (error) {\n            console.error('❌ Error fetching attachments:', error);\n            throw error;\n        }\n    },\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getSeverityColor (severity) {\n        switch(severity === null || severity === void 0 ? void 0 : severity.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'critical':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Personal Data',\n                label: 'Personal Data'\n            },\n            {\n                value: 'Financial Data',\n                label: 'Financial Data'\n            },\n            {\n                value: 'Health Data',\n                label: 'Health Data'\n            },\n            {\n                value: 'Technical Data',\n                label: 'Technical Data'\n            },\n            {\n                value: 'Communication Data',\n                label: 'Communication Data'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getSeverityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'critical',\n                label: 'Critical'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/index.ts":
/*!*******************************************!*\
  !*** ./src/services/data-breach/index.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataBreachService: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _dataBreachService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataBreachService */ \"(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhLWJyZWFjaC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQyIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXHNlcnZpY2VzXFxkYXRhLWJyZWFjaFxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9kYXRhQnJlYWNoU2VydmljZSc7XHJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});