import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { Applications } from '../entities/applications.entity';
import { User } from '../entities/user.entity';
import { AuditTrail } from '../entities/audit-trail.entity';
import { Licenses } from '../entities/licenses.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Applications, User, AuditTrail, Licenses]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}
