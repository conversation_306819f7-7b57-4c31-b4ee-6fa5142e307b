"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth */ \"(app-pages-browser)/./src/components/auth/index.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoginForm() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [fieldErrors, setFieldErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isCustomerPortal, setIsCustomerPortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [requires2FA, setRequires2FA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingMessage, setLoadingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [forgotPasswordLoading, setForgotPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dynamicMessages, setDynamicMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'Connecting to staff portal...'\n    ]);\n    const { login, isAuthenticated, loading: staffLoading, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Client-side initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginForm.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            // Check if we're on staff portal - fix the logic completely\n            const message = searchParams.get('message');\n            if (message) {\n                setSuccessMessage(message);\n            }\n            // Add timeout to prevent infinite loading - but don't redirect on timeout\n            const loadingTimeout = setTimeout({\n                \"LoginForm.useEffect.loadingTimeout\": ()=>{\n                    if (loading && !error) {\n                        setLoading(false);\n                    }\n                }\n            }[\"LoginForm.useEffect.loadingTimeout\"], 10000); // 10 second timeout\n            return ({\n                \"LoginForm.useEffect\": ()=>clearTimeout(loadingTimeout)\n            })[\"LoginForm.useEffect\"];\n        }\n    }[\"LoginForm.useEffect\"], [\n        searchParams,\n        loading,\n        isClient,\n        error,\n        isCustomerPortal,\n        router\n    ]);\n    // Redirect if already authenticated - only within staff portal, never to customer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            // Don't redirect in any of these conditions:\n            if (requires2FA) return;\n            if (error) return; // Explicit error check first\n            if (loading || staffLoading) return; // Don't redirect during loading\n            if (!isClient) return; // Wait for client-side hydration\n            if (isCustomerPortal) return; // Only redirect in staff portal\n            // Only redirect if user is authenticated and no errors\n            if (isAuthenticated && !error) {\n                router.replace('/dashboard');\n            }\n        }\n    }[\"LoginForm.useEffect\"], [\n        requires2FA,\n        isCustomerPortal,\n        isAuthenticated,\n        staffLoading,\n        loading,\n        router,\n        error,\n        isClient\n    ]);\n    const validateEmail = (email)=>{\n        if (!email.trim()) {\n            return 'Email address is required';\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return 'Please enter a valid email address';\n        }\n        return null;\n    };\n    const validatePassword = (password)=>{\n        if (!password) {\n            return 'Password is required';\n        }\n        if (password.length < 8) {\n            return 'Password must be at least 8 characters long';\n        }\n        return null;\n    };\n    const validateForm = ()=>{\n        const emailError = validateEmail(email);\n        const passwordError = validatePassword(password);\n        setFieldErrors({\n            email: emailError || undefined,\n            password: passwordError || undefined\n        });\n        if (emailError) return emailError;\n        if (passwordError) return passwordError;\n        return null;\n    };\n    const handleEmailChange = (e)=>{\n        const value = e.target.value;\n        setEmail(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.email) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    email: undefined\n                }));\n        }\n        // Clear general error when user starts typing (with slight delay to let user see the error)\n        if (error) {\n            setTimeout(()=>{\n                setError('');\n            }, 100);\n        }\n    };\n    const handlePasswordChange = (e)=>{\n        const value = e.target.value;\n        setPassword(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.password) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    password: undefined\n                }));\n        }\n        // Clear general error when user starts typing (with slight delay to let user see the error)\n        if (error) {\n            setTimeout(()=>{\n                setError('');\n            }, 100);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Clear previous messages\n        setError('');\n        setSuccessMessage('');\n        // Validate form before submission\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            setLoading(false);\n            return;\n        }\n        setLoading(true);\n        try {\n            // Always treat this as staff login since we're on staff portal\n            setDynamicMessages([\n                'Verifying your credentials...',\n                'Please wait...'\n            ]);\n            const response = await login(email.trim().toLowerCase(), password, rememberMe);\n            if (response) {\n                // Check if email verification is required (for unverified accounts)\n                const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n                if (requiresEmailVerification) {\n                    setRequires2FA(true);\n                    setSuccessMessage('Email verification required. Please check your email for the verification code.');\n                    setDynamicMessages([\n                        'Email verification required!',\n                        'Verification code sent...',\n                        'Redirecting to verification...'\n                    ]);\n                    setTimeout(()=>{\n                        router.replace('/auth/verify-2fa');\n                    }, 1500);\n                } else {\n                    // Check if 2FA is required for verified accounts\n                    const requires2FA = response.user.two_factor_enabled;\n                    if (requires2FA == true || requires2FA == 'true') {\n                        setRequires2FA(true);\n                        setSuccessMessage('Login successful! Two-factor authentication is enabled for your account. Please check your email for the verification code.');\n                        setDynamicMessages([\n                            'Login successful!',\n                            'Sending verification code...',\n                            'Redirecting to 2FA verification...'\n                        ]);\n                        setTimeout(()=>{\n                            router.replace('/auth/verify-2fa');\n                        }, 1500);\n                    } else {\n                        setTimeout(()=>{\n                            setSuccessMessage('Login successful! Redirecting to your dashboard...');\n                            setDynamicMessages([\n                                'Login successful!',\n                                'Setting up your session...',\n                                'Redirecting...'\n                            ]);\n                            router.replace('/dashboard');\n                        }, 3000);\n                    }\n                }\n            } else {\n                setError('Invalid user session. Please try again.');\n                setLoading(false);\n                return;\n            }\n        } catch (err) {\n            // AuthService now provides clean error messages, so we can use them directly\n            setLoading(false);\n            const errorMessage = (0,_lib__WEBPACK_IMPORTED_MODULE_5__.getErrorMessage)(err);\n            setError(errorMessage);\n            // Return early - stay on login page when authentication fails\n            return;\n        }\n    };\n    const handleForgotPasswordClick = (e)=>{\n        e.preventDefault();\n        setForgotPasswordLoading(true);\n        setLoadingMessage('Redirecting to forgot password...');\n        setTimeout(()=>{\n            router.push('/auth/forgot-password');\n        }, 3000);\n    };\n    // Show loading while client is initializing, checking authentication, or during login/redirect\n    if (!isClient || loading || forgotPasswordLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.PageTransition, {\n            isLoading: true,\n            loadingMessage: loadingMessage || (!isClient ? 'Loading...' : 'Signing in...'),\n            loadingSubmessage: \"Please wait while we process your request\",\n            dynamicMessages: loading ? dynamicMessages : undefined,\n            showProgress: loading,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.AuthLayout, {\n        title: \"Welcome Back!\",\n        subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                \"Staff Portal Access\",\n                ' ',\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-red-600 dark:text-red-400\",\n                    children: \"• Secure Dashboard Login\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 17\n                }, void 0)\n            ]\n        }, void 0, true),\n        isCustomerPortal: false,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.StatusMessage, {\n                type: \"error\",\n                message: error,\n                className: \"mb-4\",\n                dismissible: true,\n                onDismiss: ()=>setError('')\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 13\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.StatusMessage, {\n                type: \"success\",\n                message: successMessage,\n                className: \"mb-4\",\n                dismissible: true,\n                onDismiss: ()=>setSuccessMessage('')\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-6 animate-fadeIn animate-delay-200\",\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Email address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"email\",\n                                    name: \"email\",\n                                    type: \"email\",\n                                    autoComplete: \"email\",\n                                    required: true,\n                                    value: email,\n                                    onChange: handleEmailChange,\n                                    className: \"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth \".concat(fieldErrors.email ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'),\n                                    placeholder: \"Enter your email address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this),\n                            fieldErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 19\n                                    }, this),\n                                    fieldErrors.email\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"password\",\n                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Password\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"password\",\n                                    name: \"password\",\n                                    type: \"password\",\n                                    autoComplete: \"current-password\",\n                                    required: true,\n                                    value: password,\n                                    onChange: handlePasswordChange,\n                                    className: \"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth \".concat(fieldErrors.password ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'),\n                                    placeholder: \"Enter your password\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            fieldErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, this),\n                                    fieldErrors.password\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 13\n                    }, this),\n                    !isCustomerPortal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between animate-fadeIn animate-delay-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"remember-me\",\n                                        name: \"remember-me\",\n                                        type: \"checkbox\",\n                                        checked: rememberMe,\n                                        onChange: (e)=>setRememberMe(e.target.checked),\n                                        className: \"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"remember-me\",\n                                        className: \"ml-2 block text-sm text-gray-900 dark:text-gray-100\",\n                                        children: \"Remember me\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/auth/forgot-password\",\n                                    onClick: handleForgotPasswordClick,\n                                    className: \"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline cursor-pointer\",\n                                    children: \"Forgot your password?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 21\n                                    }, this),\n                                    \"Signing in...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3v1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 21\n                                    }, this),\n                                    \"Sign in\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 11\n    }, this);\n}\n_s(LoginForm, \"G+yCezb3G+MzNyciTxKYfhgsaV4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = LoginForm;\nfunction LoginPage() {\n    console.log('LoginPage component rendering...');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"Loading login page...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginForm, {}, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 388,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 382,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoginPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"LoginForm\");\n$RefreshReg$(_c1, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFeUY7QUFDbEM7QUFDdkI7QUFDNkM7QUFFeEI7QUFlckQsTUFBTVUsNEJBQWNULG9EQUFhQSxDQUE4QlU7QUFFeEQsTUFBTUMsVUFBVTs7SUFDckIsTUFBTUMsVUFBVVgsaURBQVVBLENBQUNRO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0dBTldEO0FBWU4sTUFBTUcsZUFBNEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHZiwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNnQixPQUFPQyxTQUFTLEdBQUdqQiwrQ0FBUUEsQ0FBYztJQUNoRCxNQUFNLENBQUNrQixPQUFPQyxTQUFTLEdBQUduQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDb0IsU0FBU0MsV0FBVyxHQUFHckIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDc0IsU0FBU0MsV0FBVyxHQUFHdkIsK0NBQVFBLENBQUM7SUFDdkMsc0NBQXNDO0lBQ3RDQyxnREFBU0E7a0NBQUM7WUFDUnNCLFdBQVc7UUFDYjtpQ0FBRyxFQUFFO0lBRUwsOERBQThEO0lBQzlEdEIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSSxDQUFDcUIsV0FBVyxDQUFDUixRQUFRLENBQUNJLE9BQU87WUFDakMsOEVBQThFO1lBQzlFLE1BQU1NLGtCQUFrQnBCLHlFQUF5QkEsQ0FBQztZQUNsRDswQ0FBTztvQkFDTHFCLGNBQWNEO2dCQUNoQjs7UUFDRjtpQ0FBRztRQUFDRjtRQUFTUjtRQUFNSTtLQUFNO0lBRXpCakIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSSxDQUFDcUIsU0FBUztZQUNkLCtEQUErRDtZQUMvREk7UUFDRjtpQ0FBRztRQUFDSjtLQUFRO0lBRVosb0RBQW9EO0lBQ3BELE1BQU1JLFdBQVc7UUFDZiw4QkFBOEI7UUFDOUIsTUFBTUMsYUFBYXhCLGlEQUFPQSxDQUFDeUIsR0FBRyxDQUFDO1FBQy9CLE1BQU1DLFlBQVkxQixpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztRQUM5QixJQUFJRCxjQUFjRSxXQUFXO1lBQzNCLElBQUk7Z0JBQ0YsTUFBTWYsT0FBT2dCLEtBQUtDLEtBQUssQ0FBQ0Y7Z0JBQ3hCLGdDQUFnQztnQkFDaEMsSUFBSSxDQUFDeEIsOERBQWNBLENBQUNzQixhQUFhO29CQUMvQlIsU0FBU1E7b0JBQ1RaLFFBQVFEO29CQUNSWiwrREFBV0EsQ0FBQzhCLFlBQVksQ0FBQ0w7Z0JBQzNCLE9BQU87b0JBQ0x6QiwrREFBV0EsQ0FBQytCLGNBQWM7Z0JBQzVCO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkaEMsK0RBQVdBLENBQUMrQixjQUFjO2dCQUMxQkUsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDdkQ7WUFFQSxJQUFHO2dCQUNDLE1BQU1FLGVBQWVqQyxpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztnQkFDakMsTUFBTVMsWUFBWUQsZUFBY04sS0FBS0MsS0FBSyxDQUFDSyxnQkFBZ0I7Z0JBQzNEbkIsU0FBU29CO1lBQ2IsRUFBRSxPQUFNQyxHQUFHO2dCQUNUSCxRQUFRRCxLQUFLLENBQUMsc0NBQXNDSTtZQUN0RDtRQUVGO1FBQ0FqQixXQUFXO0lBQ2I7SUFFQSxNQUFNa0IsUUFBUSxlQUFPQyxPQUFlQztZQUFrQkMsOEVBQXNCO1FBQzFFLElBQUk7WUFDRixNQUFNQyxXQUF5QixNQUFNekMsK0RBQVdBLENBQUNxQyxLQUFLLENBQUM7Z0JBQUVDO2dCQUFPQztZQUFTO1lBQ3pFLCtDQUErQztZQUMvQyxJQUFJLENBQUNFLFlBQVksQ0FBQ0EsU0FBUzdCLElBQUksRUFBRTtnQkFDL0IsTUFBTSxJQUFJSCxNQUFNO1lBQ2xCO1lBRUEsa0RBQWtEO1lBQ2xELElBQUkrQixZQUFZO2dCQUNkRSxlQUFlQyxPQUFPLENBQUMsZUFBZTtZQUN4QyxPQUFPO2dCQUNMRCxlQUFlRSxVQUFVLENBQUM7WUFDNUI7WUFFQSxtRkFBbUY7WUFDbkYsTUFBTUMsY0FBY0osU0FBUzdCLElBQUksQ0FBQ2tDLGtCQUFrQixJQUFLLEVBQUNMLFNBQVNNLFlBQVksSUFBSU4sU0FBU00sWUFBWSxLQUFLLEVBQUM7WUFFOUcsbUVBQW1FO1lBQ25FLE1BQU1DLDRCQUE0QlAsU0FBU1EsWUFBWSxJQUFJUixTQUFTUyxNQUFNLEtBQUs7WUFFL0UsSUFBSSxDQUFDTCxlQUFlLENBQUNHLDJCQUEyQjtnQkFDOUMsc0VBQXNFO2dCQUN0RS9DLGlEQUFPQSxDQUFDa0QsR0FBRyxDQUFDLGFBQWF2QixLQUFLd0IsU0FBUyxDQUFDWCxTQUFTN0IsSUFBSTtnQkFDckRYLGlEQUFPQSxDQUFDa0QsR0FBRyxDQUFDLGNBQWNWLFNBQVNNLFlBQVksSUFBSTtnQkFDbkQsSUFBR04sU0FBUzdCLElBQUksQ0FBQ2tDLGtCQUFrQixJQUFJLFFBQVFMLFNBQVM3QixJQUFJLENBQUNrQyxrQkFBa0IsSUFBSSxRQUFPO29CQUN4RixNQUFNOUMsK0RBQVdBLENBQUNxRCxxQkFBcUIsQ0FBQ1osU0FBUzdCLElBQUksQ0FBQzBDLE9BQU8sRUFBRTtvQkFDL0RyRCxpREFBT0EsQ0FBQ2tELEdBQUcsQ0FBQyxrQkFBbUJ2QixLQUFLd0IsU0FBUyxDQUFDWCxTQUFTN0IsSUFBSTtnQkFDN0Q7Z0JBQ0EsTUFBTVk7WUFDUixPQUFPLElBQUl3QiwyQkFBMkI7Z0JBQ3BDLHNFQUFzRTtnQkFDdEUvQyxpREFBT0EsQ0FBQ2tELEdBQUcsQ0FBQyxrQkFBa0J2QixLQUFLd0IsU0FBUyxDQUFDWCxTQUFTN0IsSUFBSTtnQkFDMURxQixRQUFRc0IsR0FBRyxDQUFDLHNEQUFzRGQsU0FBUzdCLElBQUksQ0FBQzBCLEtBQUs7WUFDdkY7WUFFQSxPQUFPRztRQUNULEVBQUUsT0FBT1QsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNd0IseUJBQXlCLGVBQU94QyxPQUFleUM7WUFBZ0JqQiw4RUFBc0I7UUFDekYsSUFBSTtZQUNGLDJCQUEyQjtZQUMzQixNQUFNa0IsUUFBa0JDLE1BQU1DLE9BQU8sQ0FBQ0gsU0FBU0MsS0FBSyxJQUFJRCxTQUFTQyxLQUFLLEdBQUcsRUFBRTtZQUUzRSwyREFBMkQ7WUFDM0QsTUFBTTlDLE9BQU87Z0JBQ1gsR0FBRzZDLFFBQVE7Z0JBQ1hDO2dCQUNBRyxTQUFTSCxNQUFNSSxRQUFRLENBQUM7Z0JBQ3hCQyxZQUFZTCxNQUFNSSxRQUFRLENBQUM7WUFDN0I7WUFFQSxJQUFJRSxJQUFzQyxFQUFFO2dCQUMxQy9CLFFBQVFzQixHQUFHLENBQUMsbURBQW1EM0M7WUFDakU7WUFFQUssU0FBU0Q7WUFDVEgsUUFBUUQ7WUFFUiw4REFBOEQ7WUFDOUQsTUFBTXFELG1CQUFtQnpCLGFBQWEsS0FBSyxHQUFHLDBDQUEwQztZQUN4RnZDLGlEQUFPQSxDQUFDa0QsR0FBRyxDQUFDLGNBQWNuQyxPQUFPO2dCQUFFa0QsU0FBU0Q7WUFBaUI7WUFDN0RoRSxpREFBT0EsQ0FBQ2tELEdBQUcsQ0FBQyxhQUFhdkIsS0FBS3dCLFNBQVMsQ0FBQ3hDLE9BQU87Z0JBQUVzRCxTQUFTRDtZQUFpQjtZQUMzRWpFLCtEQUFXQSxDQUFDOEIsWUFBWSxDQUFDZDtRQUMzQixFQUFFLE9BQU9nQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0Q0FBNENBO1lBQzFELE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1tQyxXQUFXLE9BQU9WO1FBQ3RCLE1BQU1XLFNBQVMsTUFBTXBFLCtEQUFXQSxDQUFDbUUsUUFBUSxDQUFDVjtRQUMxQyxJQUFHVyxPQUFPeEQsSUFBSSxFQUFFO1lBQ2R5QixNQUFNb0IsU0FBU25CLEtBQUssRUFBRW1CLFNBQVNsQixRQUFRO1FBQ3pDO1FBQ0EsSUFBSXlCLElBQXNDLEVBQUU7WUFDMUMvQixRQUFRc0IsR0FBRyxDQUFDO1FBQ2Q7UUFFQSx5REFBeUQ7UUFDekQsNkRBQTZEO1FBQzdELDREQUE0RDtRQUM1RCwwREFBMEQ7UUFFMUQsT0FBT2E7SUFDVDtJQUVBLE1BQU1DLFNBQVM7UUFDYixJQUFJTCxJQUFzQyxFQUFFO1lBQzFDL0IsUUFBUXNCLEdBQUcsQ0FBQztRQUNkO1FBQ0EsY0FBYztRQUNkMUMsUUFBUTtRQUNSSSxTQUFTO1FBQ1RGLFNBQVM7UUFDVCwyQkFBMkI7UUFDM0JmLCtEQUFXQSxDQUFDK0IsY0FBYztRQUMxQixJQUFJaUMsSUFBc0MsRUFBRTtZQUMxQy9CLFFBQVFzQixHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTWUsYUFBYSxDQUFDQztRQUNsQixJQUFJUCxJQUFzQyxFQUFFO1lBQzFDL0IsUUFBUXNCLEdBQUcsQ0FBQyw4QkFBOEJnQjtRQUM1QztRQUVBLG1EQUFtRDtRQUNuRCxJQUFJYixRQUFrQixFQUFFO1FBQ3hCLElBQUlhLFlBQVliLEtBQUssRUFBRTtZQUNyQkEsUUFBUWEsWUFBWWIsS0FBSyxDQUFDYyxHQUFHLENBQUMsQ0FBQ0MsT0FDN0IsT0FBT0EsU0FBUyxXQUFXQSxPQUFPQSxLQUFLQyxJQUFJLElBQUlELEtBQUtFLFNBQVMsSUFBSTtRQUVyRTtRQUVBdkUsOERBQVdBLENBQUN3RSxXQUFXLENBQUNMLFlBQVlqQixPQUFPLEVBQUV1QixJQUFJLENBQUNqRSxDQUFBQTtZQUNoREEsS0FBS21ELFVBQVUsR0FBSUwsTUFBTUksUUFBUSxDQUFDO1lBQ2xDLElBQUlFLElBQXNDLEVBQUUsQ0FDNUM7WUFDQW5ELFFBQVFEO1lBQ1Isb0VBQW9FO1lBQ3BFLE1BQU1rRSxnQkFBZ0I3RSxpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztZQUNsQyxJQUFJb0QsZUFBZTtnQkFDakIsaUVBQWlFO2dCQUNqRSxNQUFNYixtQkFBbUIsR0FBRyxvQ0FBb0M7Z0JBQ2hFaEUsaURBQU9BLENBQUNrRCxHQUFHLENBQUMsYUFBYXZCLEtBQUt3QixTQUFTLENBQUNtQixjQUFjO29CQUFFTCxTQUFTRDtnQkFBaUI7WUFDcEY7UUFDRjtJQUNBLDREQUE0RDtJQUU5RDtJQUVBLE1BQU1jLFFBQXlCO1FBQzdCbkU7UUFDQUk7UUFDQXFCO1FBQ0F2QjtRQUNBMEM7UUFDQVc7UUFDQUU7UUFDQUM7UUFDQXBELFNBQVNBLFdBQVcsQ0FBQ0U7UUFDckI0RCxpQkFBaUI1RCxXQUFXLENBQUMsQ0FBQ1IsUUFBUSxDQUFDLENBQUNJO0lBRTFDO0lBRUEscUJBQU8sOERBQUNYLFlBQVk0RSxRQUFRO1FBQUNGLE9BQU9BO2tCQUFRcEU7Ozs7OztBQUM5QyxFQUFFO0lBbk5XRDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxBdXRoQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgYXV0aFNlcnZpY2UgfSBmcm9tICcuLi9zZXJ2aWNlcy9hdXRoLnNlcnZpY2UnO1xyXG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xyXG5pbXBvcnQgeyBzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyLCBpc1Rva2VuRXhwaXJlZCB9IGZyb20gJy4uL2xpYi9hdXRoVXRpbHMnO1xyXG5pbXBvcnQgeyBBdXRoUmVzcG9uc2UsIFVzZXIsIFJlZ2lzdGVyRGF0YSwgVXBkYXRlVXNlckRhdGEgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgdXNlclNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL3VzZXJTZXJ2aWNlJztcclxuXHJcbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xyXG4gIHVzZXI6IFVzZXIgfCBudWxsO1xyXG4gIHRva2VuOiBzdHJpbmcgfCBudWxsO1xyXG4gIHR3b0ZhOiBVc2VyIHwgbnVsbDtcclxuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHJlbWVtYmVyTWU/OiBib29sZWFuKSA9PiBQcm9taXNlPEF1dGhSZXNwb25zZT47XHJcbiAgY29tcGxldGVUd29GYWN0b3JMb2dpbjogKHRva2VuOiBzdHJpbmcsIHVzZXJEYXRhOiBVc2VyLCByZW1lbWJlck1lPzogYm9vbGVhbikgPT4gUHJvbWlzZTx2b2lkPjtcclxuICByZWdpc3RlcjogKHVzZXJEYXRhOiBSZWdpc3RlckRhdGEpID0+IFByb21pc2U8QXV0aFJlc3BvbnNlPjtcclxuICBsb2dvdXQ6ICgpID0+IHZvaWQ7XHJcbiAgdXBkYXRlVXNlcjogKHVzZXI6IFVwZGF0ZVVzZXJEYXRhKSA9PiB2b2lkO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xyXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xyXG4gIH1cclxuICByZXR1cm4gY29udGV4dDtcclxufTtcclxuXHJcbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdHdvRmEsIHNldFR3b0ZhXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdG9rZW4sIHNldFRva2VuXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAvLyBTZXQgbW91bnRlZCB0byB0cnVlIGFmdGVyIGh5ZHJhdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRNb3VudGVkKHRydWUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gU3RhcnQgdG9rZW4gdmFsaWRhdGlvbiB0aW1lciB3aGVuIG1vdW50ZWQgYW5kIGF1dGhlbnRpY2F0ZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFtb3VudGVkIHx8ICF1c2VyIHx8ICF0b2tlbikgcmV0dXJuO1xyXG4gICAgLy8gU3RhcnQgcGVyaW9kaWMgdG9rZW4gdmFsaWRhdGlvbiAoY2hlY2sgZXZlcnkgNSBtaW51dGVzIGluc3RlYWQgb2YgMSBtaW51dGUpXHJcbiAgICBjb25zdCB2YWxpZGF0aW9uVGltZXIgPSBzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyKDMwMDAwMCk7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBjbGVhckludGVydmFsKHZhbGlkYXRpb25UaW1lcik7XHJcbiAgICB9O1xyXG4gIH0sIFttb3VudGVkLCB1c2VyLCB0b2tlbl0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFtb3VudGVkKSByZXR1cm47XHJcbiAgICAvLyBFeGVjdXRlIGltbWVkaWF0ZWx5IGluc3RlYWQgb2Ygd2FpdGluZyBmb3IgbmV4dCByZW5kZXIgY3ljbGVcclxuICAgIGluaXRBdXRoKCk7XHJcbiAgfSwgW21vdW50ZWRdKTtcclxuXHJcbiAgLy8gQ2hlY2sgZm9yIGV4aXN0aW5nIHRva2VuIG9uIG1vdW50IHdpdGggdmFsaWRhdGlvblxyXG4gIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8gR2V0IHNhdmVkIGRhdGEgZnJvbSBjb29raWVzXHJcbiAgICBjb25zdCBzYXZlZFRva2VuID0gQ29va2llcy5nZXQoJ2F1dGhfdG9rZW4nKTtcclxuICAgIGNvbnN0IHNhdmVkVXNlciA9IENvb2tpZXMuZ2V0KCdhdXRoX3VzZXInKTtcclxuICAgIGlmIChzYXZlZFRva2VuICYmIHNhdmVkVXNlcikge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKHNhdmVkVXNlcik7XHJcbiAgICAgICAgLy8gVmFsaWRhdGUgdG9rZW4gaXMgbm90IGV4cGlyZWRcclxuICAgICAgICBpZiAoIWlzVG9rZW5FeHBpcmVkKHNhdmVkVG9rZW4pKSB7XHJcbiAgICAgICAgICBzZXRUb2tlbihzYXZlZFRva2VuKTtcclxuICAgICAgICAgIHNldFVzZXIodXNlcik7XHJcbiAgICAgICAgICBhdXRoU2VydmljZS5zZXRBdXRoVG9rZW4oc2F2ZWRUb2tlbik7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aFRva2VuKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aFRva2VuKCk7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IFVzZXIgZGF0YSBwYXJzZSBmYWlsZWQnLCBlcnJvcik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRyeXtcclxuICAgICAgICAgIGNvbnN0IHNhdmVkMmZhVXNlciA9IENvb2tpZXMuZ2V0KCcyZmFfbG9naW5fdXNlcicpO1xyXG4gICAgICAgICAgY29uc3QgdHdvRmFVc2VyID0gc2F2ZWQyZmFVc2VyPyBKU09OLnBhcnNlKHNhdmVkMmZhVXNlciApOiBudWxsO1xyXG4gICAgICAgICAgc2V0VHdvRmEodHdvRmFVc2VyKTtcclxuICAgICAgfSBjYXRjaChlKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IDJGQSB1c2VyIHBhcnNlIGZhaWxlZCcsIGUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgfVxyXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBsb2dpbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCByZW1lbWJlck1lOiBib29sZWFuID0gZmFsc2UpOiBQcm9taXNlPEF1dGhSZXNwb25zZT4gPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2U6IEF1dGhSZXNwb25zZSA9IGF3YWl0IGF1dGhTZXJ2aWNlLmxvZ2luKHsgZW1haWwsIHBhc3N3b3JkIH0pO1xyXG4gICAgICAvLyBWYWxpZGF0ZSByZXNwb25zZSBzdHJ1Y3R1cmUgZm9yIG5vcm1hbCBsb2dpblxyXG4gICAgICBpZiAoIXJlc3BvbnNlIHx8ICFyZXNwb25zZS51c2VyKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHJlc3BvbnNlIGZyb20gYXV0aGVudGljYXRpb24gc2VydmljZScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTdG9yZSByZW1lbWJlciBtZSBwcmVmZXJlbmNlIGZvciAyRkEgY29tcGxldGlvblxyXG4gICAgICBpZiAocmVtZW1iZXJNZSkge1xyXG4gICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3JlbWVtYmVyX21lJywgJ3RydWUnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdyZW1lbWJlcl9tZScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDaGVjayBpZiAyRkEgaXMgcmVxdWlyZWQgKGJhY2tlbmQgcmV0dXJucyBlbXB0eSBhY2Nlc3NfdG9rZW4gd2hlbiAyRkEgaXMgbmVlZGVkKVxyXG4gICAgICBjb25zdCByZXF1aXJlczJGQSA9IHJlc3BvbnNlLnVzZXIudHdvX2ZhY3Rvcl9lbmFibGVkICYmICghcmVzcG9uc2UuYWNjZXNzX3Rva2VuIHx8IHJlc3BvbnNlLmFjY2Vzc190b2tlbiA9PT0gJycpO1xyXG5cclxuICAgICAgLy8gQ2hlY2sgaWYgZW1haWwgdmVyaWZpY2F0aW9uIGlzIHJlcXVpcmVkIChuZXcgZmllbGQgZnJvbSBiYWNrZW5kKVxyXG4gICAgICBjb25zdCByZXF1aXJlc0VtYWlsVmVyaWZpY2F0aW9uID0gcmVzcG9uc2UucmVxdWlyZXNfMmZhICYmIHJlc3BvbnNlLmFjdGlvbiA9PT0gJ3JlZ2lzdGVyJztcclxuXHJcbiAgICAgIGlmICghcmVxdWlyZXMyRkEgJiYgIXJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24pIHtcclxuICAgICAgICAvLyBObyAyRkEgb3IgZW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkLCBzZXQgYXV0aCBjb250ZXh0IGltbWVkaWF0ZWx5XHJcbiAgICAgICAgQ29va2llcy5zZXQoJ2F1dGhfdXNlcicsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLnVzZXIpKTtcclxuICAgICAgICBDb29raWVzLnNldCgnYXV0aF90b2tlbicsIHJlc3BvbnNlLmFjY2Vzc190b2tlbiB8fCAnJyk7XHJcbiAgICAgICAgaWYocmVzcG9uc2UudXNlci50d29fZmFjdG9yX2VuYWJsZWQgPT0gdHJ1ZSB8fCByZXNwb25zZS51c2VyLnR3b19mYWN0b3JfZW5hYmxlZCA9PSAndHJ1ZScpe1xyXG4gICAgICAgICAgYXdhaXQgYXV0aFNlcnZpY2UuZ2VuZXJhdGVUd29GYWN0b3JDb2RlKHJlc3BvbnNlLnVzZXIudXNlcl9pZCwgJ2xvZ2luJyk7XHJcbiAgICAgICAgICBDb29raWVzLnNldCgnMmZhX2xvZ2luX3VzZXInLCAgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlcikpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBhd2FpdCBpbml0QXV0aCgpO1xyXG4gICAgICB9IGVsc2UgaWYgKHJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24pIHtcclxuICAgICAgICAvLyBFbWFpbCB2ZXJpZmljYXRpb24gcmVxdWlyZWQgLSBzdG9yZSB1c2VyIGRhdGEgZm9yIHZlcmlmaWNhdGlvbiBwYWdlXHJcbiAgICAgICAgQ29va2llcy5zZXQoJzJmYV9sb2dpbl91c2VyJywgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlcikpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogRW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkIGZvciB1c2VyOicsIHJlc3BvbnNlLnVzZXIuZW1haWwpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gcmVzcG9uc2U7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoQ29udGV4dDogTG9naW4gZmFpbGVkJywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBjb21wbGV0ZVR3b0ZhY3RvckxvZ2luID0gYXN5bmMgKHRva2VuOiBzdHJpbmcsIHVzZXJEYXRhOiBVc2VyLCByZW1lbWJlck1lOiBib29sZWFuID0gZmFsc2UpOiBQcm9taXNlPHZvaWQ+ID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEVuc3VyZSByb2xlcyBpcyBhbiBhcnJheVxyXG4gICAgICBjb25zdCByb2xlczogc3RyaW5nW10gPSBBcnJheS5pc0FycmF5KHVzZXJEYXRhLnJvbGVzKSA/IHVzZXJEYXRhLnJvbGVzIDogW107XHJcblxyXG4gICAgICAvLyBBZGQgY29tcHV0ZWQgaXNBZG1pbiBwcm9wZXJ0eSBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG4gICAgICBjb25zdCB1c2VyID0ge1xyXG4gICAgICAgIC4uLnVzZXJEYXRhLFxyXG4gICAgICAgIHJvbGVzLFxyXG4gICAgICAgIGlzQWRtaW46IHJvbGVzLmluY2x1ZGVzKCdhZG1pbmlzdHJhdG9yJyksXHJcbiAgICAgICAgaXNDdXN0b21lcjogcm9sZXMuaW5jbHVkZXMoJ2N1c3RvbWVyJyksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IDJGQSBsb2dpbiBzdWNjZXNzZnVsLCBzZXR0aW5nIHVzZXInLCB1c2VyKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgc2V0VG9rZW4odG9rZW4pO1xyXG4gICAgICBzZXRVc2VyKHVzZXIpO1xyXG5cclxuICAgICAgLy8gU2V0IGNvb2tpZXMgd2l0aCBhcHByb3ByaWF0ZSBleHBpcmF0aW9uIGJhc2VkIG9uIHJlbWVtYmVyTWVcclxuICAgICAgY29uc3QgY29va2llRXhwaXJhdGlvbiA9IHJlbWVtYmVyTWUgPyAzMCA6IDE7IC8vIDMwIGRheXMgaWYgcmVtZW1iZXIgbWUsIDEgZGF5IG90aGVyd2lzZVxyXG4gICAgICBDb29raWVzLnNldCgnYXV0aF90b2tlbicsIHRva2VuLCB7IGV4cGlyZXM6IGNvb2tpZUV4cGlyYXRpb24gfSk7XHJcbiAgICAgIENvb2tpZXMuc2V0KCdhdXRoX3VzZXInLCBKU09OLnN0cmluZ2lmeSh1c2VyKSwgeyBleHBpcmVzOiBjb29raWVFeHBpcmF0aW9uIH0pO1xyXG4gICAgICBhdXRoU2VydmljZS5zZXRBdXRoVG9rZW4odG9rZW4pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IDJGQSBsb2dpbiBjb21wbGV0aW9uIGZhaWxlZCcsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiA9PiB7XHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBhdXRoU2VydmljZS5yZWdpc3Rlcih1c2VyRGF0YSk7XHJcbiAgICBpZihyZXN1bHQudXNlcikge1xyXG4gICAgICBsb2dpbih1c2VyRGF0YS5lbWFpbCwgdXNlckRhdGEucGFzc3dvcmQpO1xyXG4gICAgfVxyXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogUmVnaXN0cmF0aW9uIHN1Y2Nlc3NmdWwgLSB1c2VyIHNob3VsZCBsb2dpbiBtYW51YWxseScpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIERvbid0IGF1dG9tYXRpY2FsbHkgbG9nIGluIHRoZSB1c2VyIGFmdGVyIHJlZ2lzdHJhdGlvblxyXG4gICAgLy8gVXNlciBzaG91bGQgYmUgcmVkaXJlY3RlZCB0byBsb2dpbiBwYWdlIHRvIG1hbnVhbGx5IGxvZyBpblxyXG4gICAgLy8gVGhpcyBmb2xsb3dzIHRoZSByZXF1aXJlbWVudCB0aGF0IGFmdGVyIGFjY291bnQgY3JlYXRpb24sXHJcbiAgICAvLyB1c2VycyBzaG91bGQgYmUgcmVkaXJlY3RlZCB0byBsb2dpbiBwYWdlLCBub3QgZGFzaGJvYXJkXHJcblxyXG4gICAgcmV0dXJuIHJlc3VsdDtcclxuICB9O1xyXG5cclxuICBjb25zdCBsb2dvdXQgPSAoKTogdm9pZCA9PiB7XHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBMb2dnaW5nIG91dCB1c2VyJyk7XHJcbiAgICB9XHJcbiAgICAvLyBDbGVhciBzdGF0ZVxyXG4gICAgc2V0VXNlcihudWxsKTtcclxuICAgIHNldFRva2VuKG51bGwpO1xyXG4gICAgc2V0VHdvRmEobnVsbCk7XHJcbiAgICAvLyBDbGVhciBhdXRoIHNlcnZpY2UgdG9rZW5cclxuICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aFRva2VuKCk7XHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBMb2dvdXQgY29tcGxldGUnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVVc2VyID0gKHVwZGF0ZWRVc2VyOiBVcGRhdGVVc2VyRGF0YSk6IHZvaWQgPT4ge1xyXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogVXBkYXRpbmcgdXNlcicsIHVwZGF0ZWRVc2VyKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDb252ZXJ0IHJvbGVzIHRvIHN0cmluZyBhcnJheSBpZiB0aGV5J3JlIG9iamVjdHNcclxuICAgIGxldCByb2xlczogc3RyaW5nW10gPSBbXTtcclxuICAgIGlmICh1cGRhdGVkVXNlci5yb2xlcykge1xyXG4gICAgICByb2xlcyA9IHVwZGF0ZWRVc2VyLnJvbGVzLm1hcCgocm9sZTogc3RyaW5nIHwgeyBuYW1lPzogc3RyaW5nOyByb2xlX25hbWU/OiBzdHJpbmcgfSkgPT5cclxuICAgICAgICB0eXBlb2Ygcm9sZSA9PT0gJ3N0cmluZycgPyByb2xlIDogcm9sZS5uYW1lIHx8IHJvbGUucm9sZV9uYW1lIHx8ICd1bmtub3duJ1xyXG4gICAgICApO1xyXG4gICAgfVxyXG5cclxuICAgIHVzZXJTZXJ2aWNlLmdldFVzZXJCeUlkKHVwZGF0ZWRVc2VyLnVzZXJfaWQpLnRoZW4odXNlciA9PiB7XHJcbiAgICAgIHVzZXIuaXNDdXN0b21lciA9ICByb2xlcy5pbmNsdWRlcygnY3VzdG9tZXInKVxyXG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgfVxyXG4gICAgICBzZXRVc2VyKHVzZXIpO1xyXG4gICAgICAvLyBVcGRhdGUgY29va2llcyB3aXRoIG5ldyB1c2VyIGRhdGEgLSB1c2UgZXhpc3RpbmcgdG9rZW4gZXhwaXJhdGlvblxyXG4gICAgICBjb25zdCBleGlzdGluZ1Rva2VuID0gQ29va2llcy5nZXQoJ2F1dGhfdG9rZW4nKTtcclxuICAgICAgaWYgKGV4aXN0aW5nVG9rZW4pIHtcclxuICAgICAgICAvLyBUcnkgdG8gZGV0ZXJtaW5lIG9yaWdpbmFsIGV4cGlyYXRpb24gZnJvbSB0b2tlbiBvciB1c2UgZGVmYXVsdFxyXG4gICAgICAgIGNvbnN0IGNvb2tpZUV4cGlyYXRpb24gPSAxOyAvLyBEZWZhdWx0IHRvIDEgZGF5IGZvciB1c2VyIHVwZGF0ZXNcclxuICAgICAgICBDb29raWVzLnNldCgnYXV0aF91c2VyJywgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFVzZXIpLCB7IGV4cGlyZXM6IGNvb2tpZUV4cGlyYXRpb24gfSk7XHJcbiAgICAgIH1cclxuICAgIH0pXHJcbiAgICAvLyBFbnN1cmUgaXNBZG1pbiBwcm9wZXJ0eSBpcyBzZXQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdmFsdWU6IEF1dGhDb250ZXh0VHlwZSA9IHtcclxuICAgIHVzZXIsXHJcbiAgICB0b2tlbixcclxuICAgIGxvZ2luLFxyXG4gICAgdHdvRmEsXHJcbiAgICBjb21wbGV0ZVR3b0ZhY3RvckxvZ2luLFxyXG4gICAgcmVnaXN0ZXIsXHJcbiAgICBsb2dvdXQsXHJcbiAgICB1cGRhdGVVc2VyLFxyXG4gICAgbG9hZGluZzogbG9hZGluZyB8fCAhbW91bnRlZCxcclxuICAgIGlzQXV0aGVudGljYXRlZDogbW91bnRlZCAmJiAhIXVzZXIgJiYgISF0b2tlbixcclxuICAgIFxyXG4gIH07XHJcblxyXG4gIHJldHVybiA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT57Y2hpbGRyZW59PC9BdXRoQ29udGV4dC5Qcm92aWRlcj47XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJhdXRoU2VydmljZSIsIkNvb2tpZXMiLCJzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyIiwiaXNUb2tlbkV4cGlyZWQiLCJ1c2VyU2VydmljZSIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJ0d29GYSIsInNldFR3b0ZhIiwidG9rZW4iLCJzZXRUb2tlbiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJ2YWxpZGF0aW9uVGltZXIiLCJjbGVhckludGVydmFsIiwiaW5pdEF1dGgiLCJzYXZlZFRva2VuIiwiZ2V0Iiwic2F2ZWRVc2VyIiwiSlNPTiIsInBhcnNlIiwic2V0QXV0aFRva2VuIiwiY2xlYXJBdXRoVG9rZW4iLCJlcnJvciIsImNvbnNvbGUiLCJzYXZlZDJmYVVzZXIiLCJ0d29GYVVzZXIiLCJlIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicmVtZW1iZXJNZSIsInJlc3BvbnNlIiwic2Vzc2lvblN0b3JhZ2UiLCJzZXRJdGVtIiwicmVtb3ZlSXRlbSIsInJlcXVpcmVzMkZBIiwidHdvX2ZhY3Rvcl9lbmFibGVkIiwiYWNjZXNzX3Rva2VuIiwicmVxdWlyZXNFbWFpbFZlcmlmaWNhdGlvbiIsInJlcXVpcmVzXzJmYSIsImFjdGlvbiIsInNldCIsInN0cmluZ2lmeSIsImdlbmVyYXRlVHdvRmFjdG9yQ29kZSIsInVzZXJfaWQiLCJsb2ciLCJjb21wbGV0ZVR3b0ZhY3RvckxvZ2luIiwidXNlckRhdGEiLCJyb2xlcyIsIkFycmF5IiwiaXNBcnJheSIsImlzQWRtaW4iLCJpbmNsdWRlcyIsImlzQ3VzdG9tZXIiLCJwcm9jZXNzIiwiY29va2llRXhwaXJhdGlvbiIsImV4cGlyZXMiLCJyZWdpc3RlciIsInJlc3VsdCIsImxvZ291dCIsInVwZGF0ZVVzZXIiLCJ1cGRhdGVkVXNlciIsIm1hcCIsInJvbGUiLCJuYW1lIiwicm9sZV9uYW1lIiwiZ2V0VXNlckJ5SWQiLCJ0aGVuIiwiZXhpc3RpbmdUb2tlbiIsInZhbHVlIiwiaXNBdXRoZW50aWNhdGVkIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ })

});