import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { SeederService } from './seeder.service';
import { PostalCodeSeederService } from './postal-code.seeder';
import { LicenseSeederService } from './license.seeder.service';
import { CeirSeederService } from './ceir.seeder.service';

async function bootstrap() {

  const app = await NestFactory.createApplicationContext(AppModule);

  const seederService = app.get(SeederService);
  const postalCodeSeeder = app.get(PostalCodeSeederService);
  const licenseSeeder = app.get(LicenseSeederService);
  const ceirSeeder = app.get(CeirSeederService);

  const command = process.argv[2];

  try {
    switch (command) {
      case 'seed':
        await seederService.seedAll();
        break;
      case 'seed:safe':
        await seederService.seedAllSafe();
        break;
      case 'clear':
        await seederService.clearAll();
        break;
      case 'reset':
        await seederService.clearAll();
        await seederService.seedAll();
        break;
      case 'reset:safe':
        await seederService.clearAll();
        await seederService.seedAllSafe();
        break;
      case 'seed:postal':
        await postalCodeSeeder.seedPostalCodes();
        break;
      case 'clear:postal':
        await postalCodeSeeder.clearAll();
        break;
      case 'seed:licenses':
        await licenseSeeder.seedAll();
        break;
      case 'clear:licenses':
        await licenseSeeder.clearAll();
        break;
      case 'seed:ceir':
        await ceirSeeder.seedAll();
        break;
      case 'clear:ceir':
        await ceirSeeder.clearAll();
        break;
      default:
        console.log('Available commands:');
        console.log('  npm run seed          - Seed the main database (includes licenses)');
        console.log('  npm run seed:safe     - Seed the main database (safe mode with constraint handling)');
        console.log('  npm run seed:clear    - Clear main data');
        console.log('  npm run seed:reset    - Clear and re-seed main data');
        console.log('  npm run reset:safe    - Clear and re-seed main data (safe mode)');
        console.log('  npm run seed:postal   - Seed postal codes only');
        console.log('  npm run clear:postal  - Clear postal codes only');
        console.log('  npm run seed:licenses - Seed license types and categories only');
        console.log('  npm run clear:licenses- Clear license types and categories only');
        console.log('  npm run seed:ceir     - Seed CEIR equipment categories and technical standards only');
        console.log('  npm run clear:ceir    - Clear CEIR equipment categories and technical standards only');
        break;
    }
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap();
