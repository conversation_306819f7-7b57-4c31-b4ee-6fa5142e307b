import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  BeforeInsert,
  OneToMany,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsUUID, IsString, IsOptional, IsBoolean, IsDateString, IsEmail, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';
import { Address } from '../../entities/address.entity';
import { Contacts } from '../../entities/contacts.entity';

// Certification body type constants for validation
export const CERTIFICATION_BODY_TYPES = [
  'accredited_lab',
  'notified_body',
  'government_agency',
  'international_org',
  'private_lab',
] as const;

// Accreditation status constants for validation
export const ACCREDITATION_STATUSES = [
  'active',
  'suspended',
  'expired',
  'pending',
  'revoked',
] as const;

// Certification scope constants for validation
export const CERTIFICATION_SCOPES = [
  'rf_testing',
  'sar_testing',
  'emc_testing',
  'safety_testing',
  'environmental_testing',
  'security_testing',
  'interoperability_testing',
  'full_type_approval',
] as const;

@Entity('ceir_certification_bodies')
export class CeirCertificationBodies {
  @ApiProperty({
    description: 'Unique identifier for the certification body',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID()
  certification_body_id: string;

  @ApiProperty({
    description: 'Official name of the certification body',
    example: 'SGS Telecommunications Testing Laboratory'
  })
  @Column({ type: 'varchar', length: 255 })
  @IsString()
  organization_name: string;

  @ApiProperty({
    description: 'Short name or acronym',
    example: 'SGS-TTL'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  short_name?: string;

  @ApiProperty({
    description: 'Type of certification body',
    example: 'accredited_lab'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  body_type: string;

  @ApiProperty({
    description: 'Unique registration or license number',
    example: 'CB-2024-001'
  })
  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  registration_number: string;

  @ApiProperty({
    description: 'Country where the certification body is located',
    example: 'South Africa'
  })
  @Column({ type: 'varchar', length: 100 })
  @IsString()
  country: string;

  @ApiProperty({
    description: 'Address ID reference',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  address_id?: string;

  @ApiProperty({
    description: 'Contact information ID reference',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  contact_id?: string;

  @ApiProperty({
    description: 'Primary contact email',
    example: '<EMAIL>'
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsEmail()
  primary_email?: string;

  @ApiProperty({
    description: 'Primary contact phone number',
    example: '+27-11-681-2500'
  })
  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @IsString()
  primary_phone?: string;

  @ApiProperty({
    description: 'Official website URL',
    example: 'https://www.sgs.com/telecommunications'
  })
  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @IsUrl()
  website_url?: string;

  @ApiProperty({
    description: 'Accreditation body that accredited this organization',
    example: 'SANAS (South African National Accreditation System)'
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString()
  accreditation_body?: string;

  @ApiProperty({
    description: 'Accreditation number',
    example: 'T0001'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  accreditation_number?: string;

  @ApiProperty({
    description: 'Date when accreditation was granted',
    example: '2020-01-15'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  accreditation_date?: Date;

  @ApiProperty({
    description: 'Date when accreditation expires',
    example: '2025-01-15'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  accreditation_expiry_date?: Date;

  @ApiProperty({
    description: 'Current accreditation status',
    example: 'active'
  })
  @Column({ type: 'varchar', length: 50, default: 'active' })
  @IsString()
  accreditation_status: string;

  @ApiProperty({
    description: 'Certification scopes this body is authorized for',
    type: 'array',
    example: ['rf_testing', 'sar_testing', 'emc_testing']
  })
  @Column({ type: 'simple-array' })
  certification_scopes: string[];

  @ApiProperty({
    description: 'Equipment categories this body can certify',
    example: ['mobile_phone', 'smartphone', 'tablet', 'modem']
  })
  @Column({ type: 'simple-array', nullable: true })
  authorized_equipment_categories?: string[];

  @ApiProperty({
    description: 'Technical standards this body is competent to test against',
    example: ['3GPP TS 51.010', 'ETSI EN 301 511', 'IEC 62209-1']
  })
  @Column({ type: 'simple-array', nullable: true })
  competent_standards?: string[];

  @ApiProperty({
    description: 'Frequency bands this body can test',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100', 'LTE 800']
  })
  @Column({ type: 'simple-array', nullable: true })
  authorized_frequency_bands?: string[];

  @ApiProperty({
    description: 'Whether this body is recognized by MACRA',
    example: true
  })
  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  is_macra_recognized: boolean;

  @ApiProperty({
    description: 'Whether this body can issue CEIR certificates',
    example: true
  })
  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  can_issue_ceir_certificates: boolean;

  @ApiProperty({
    description: 'Whether this certification body is currently active',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    description: 'Additional notes or comments',
    example: 'Specialized in mobile device testing with state-of-the-art facilities'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToOne(() => Address, { nullable: true })
  @JoinColumn({ name: 'address_id' })
  address?: Address;

  @OneToOne(() => Contacts, { nullable: true })
  @JoinColumn({ name: 'contact_id' })
  contact?: Contacts;

  @BeforeInsert()
  generateId() {
    if (!this.certification_body_id) {
      this.certification_body_id = uuidv4();
    }
  }
}
