"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintFilterDto = exports.UpdateConsumerAffairsComplaineeDto = exports.UpdateConsumerAffairsComplaintStatusDto = exports.UpdateConsumerAffairsComplaintDto = exports.CreateConsumerAffairsComplaintDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const consumer_affairs_constants_1 = require("../../consumer-affairs/consumer-affairs-constants");
class CreateConsumerAffairsComplaintDto {
}
exports.CreateConsumerAffairsComplaintDto = CreateConsumerAffairsComplaintDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintCategory, { message: 'Invalid complaint category' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "priority", void 0);
class UpdateConsumerAffairsComplaintDto extends (0, swagger_1.PartialType)(CreateConsumerAffairsComplaintDto) {
}
exports.UpdateConsumerAffairsComplaintDto = UpdateConsumerAffairsComplaintDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "resolution", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "internal_notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], UpdateConsumerAffairsComplaintDto.prototype, "resolved_at", void 0);
class UpdateConsumerAffairsComplaintStatusDto {
}
exports.UpdateConsumerAffairsComplaintStatusDto = UpdateConsumerAffairsComplaintStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintStatusDto.prototype, "comment", void 0);
class UpdateConsumerAffairsComplaineeDto {
}
exports.UpdateConsumerAffairsComplaineeDto = UpdateConsumerAffairsComplaineeDto;
__decorate([
    (0, class_validator_1.IsString)({ message: 'Invalid business registration number!' }),
    (0, class_validator_1.IsNotEmpty)({ message: "Complainee's business registration number is required to link complaint to complainee" }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaineeDto.prototype, "complainee_reg_number", void 0);
class ConsumerAffairsComplaintFilterDto {
}
exports.ConsumerAffairsComplaintFilterDto = ConsumerAffairsComplaintFilterDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintCategory, { message: 'Invalid complaint category' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_constants_1.ComplaintPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid complainant ID' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "complainant_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for from_date' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for to_date' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ConsumerAffairsComplaintFilterDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ConsumerAffairsComplaintFilterDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "sort_by", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "sort_order", void 0);
//# sourceMappingURL=consumer-affairs-complaint.dto.js.map