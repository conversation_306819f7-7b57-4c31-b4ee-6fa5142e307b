'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon, UserIcon, DocumentIcon } from '@heroicons/react/24/outline';
import SendMessageComponent from './SendMessageComponent';
import { MessageRecipient, SendMessageData } from '@/types/communication';
import { useToast } from '@/contexts/ToastContext';

interface EmailApplicantModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicant: {
    id: string;
    user_id: string;
    full_name: string;
    email: string;
    application_number: string;
    status: string;
  };
  applicationId?: string;
  onEmailSent?: () => void;
}

const EmailApplicantModal: React.FC<EmailApplicantModalProps> = ({
  isOpen,
  onClose,
  applicant,
  applicationId,
  onEmailSent
}) => {
  const { showSuccess, showError } = useToast();
  const [isSending, setIsSending] = useState(false);
  const [applicationNotes, setApplicationNotes] = useState<any[]>([]);
  const [loadingNotes, setLoadingNotes] = useState(false);

  // Create recipient from applicant data
  const recipient: MessageRecipient = {
    id: applicant.user_id,
    name: applicant.full_name,
    email: applicant.email,
    role: 'Applicant'
  };

  // Load application notes when modal opens
  useEffect(() => {
    if (isOpen && applicationId) {
      loadApplicationNotes();
    }
  }, [isOpen, applicationId]);

  const loadApplicationNotes = async () => {
    try {
      setLoadingNotes(true);
      const response = await fetch(`/api/applications/${applicationId}/notes`);
      if (response.ok) {
        const notes = await response.json();
        setApplicationNotes(notes);
      }
    } catch (error) {
      console.error('Error loading application notes:', error);
    } finally {
      setLoadingNotes(false);
    }
  };

  const handleSendEmail = async (messageData: SendMessageData) => {
    try {
      setIsSending(true);

      // Create form data for the email
      const formData = new FormData();
      formData.append('applicantId', applicant.id);
      formData.append('applicationId', applicationId || '');
      formData.append('subject', messageData.subject);
      formData.append('message', messageData.message);
      formData.append('priority', messageData.priority);
      
      // Add attachments
      messageData.attachments.forEach((file, index) => {
        formData.append(`attachments`, file);
      });

      // Send email via API
      const response = await fetch('/api/communication/email-applicant', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send email');
      }

      const result = await response.json();
      
      // Save as activity note
      if (applicationId) {
        await saveAsActivityNote(messageData, result.messageId);
      }

      showSuccess('Email sent successfully to applicant');
      onEmailSent?.();
      onClose();
    } catch (error: any) {
      console.error('Error sending email:', error);
      showError(error.message || 'Failed to send email to applicant');
    } finally {
      setIsSending(false);
    }
  };

  const saveAsActivityNote = async (messageData: SendMessageData, messageId: string) => {
    try {
      const noteData = {
        applicationId,
        noteType: 'email_sent',
        content: `Email sent to applicant: ${messageData.subject}\n\nMessage: ${messageData.message}`,
        metadata: {
          messageId,
          recipientEmail: applicant.email,
          priority: messageData.priority,
          attachmentCount: messageData.attachments.length
        }
      };

      await fetch('/api/activity-notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(noteData)
      });
    } catch (error) {
      console.error('Error saving activity note:', error);
      // Don't throw error here as email was sent successfully
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <UserIcon className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Email Applicant
              </h2>
              <p className="text-sm text-gray-600">
                {applicant.full_name} ({applicant.application_number})
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSending}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-80px)]">
          {/* Left Panel - Application Notes */}
          <div className="w-1/3 border-r border-gray-200 p-4 overflow-y-auto">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <DocumentIcon className="h-5 w-5" />
              Application Notes
            </h3>
            
            {loadingNotes ? (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : applicationNotes.length > 0 ? (
              <div className="space-y-3">
                {applicationNotes.map((note, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-3">
                    <div className="text-xs text-gray-500 mb-1">
                      {new Date(note.created_at).toLocaleDateString()} - {note.created_by_name}
                    </div>
                    <div className="text-sm text-gray-700">
                      {note.content}
                    </div>
                    {note.note_type && (
                      <div className="text-xs text-blue-600 mt-1">
                        Type: {note.note_type}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentIcon className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No notes available</p>
              </div>
            )}
          </div>

          {/* Right Panel - Email Composer */}
          <div className="flex-1 p-6 overflow-y-auto">
            <SendMessageComponent
              onSendMessage={handleSendEmail}
              availableRecipients={[recipient]}
              defaultRecipients={[recipient]}
              defaultSubject={`Regarding your application - ${applicant.application_number}`}
              messageType="email"
              allowAttachments={true}
              allowPriority={true}
              allowMultipleRecipients={false}
              placeholder="Type your message to the applicant here..."
              disabled={isSending}
              className="border-0 shadow-none p-0"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            <span className="font-medium">Recipient:</span> {applicant.email}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              disabled={isSending}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailApplicantModal;
