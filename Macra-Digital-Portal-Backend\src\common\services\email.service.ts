import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { assetsDir } from '../../app.module';
import { AuthConstants, AuthUtils } from '../constants/auth.constants';
import { EmailTemplateService } from '../../notifications/email-template.service';

export interface EmailContext {
  [key: string]: any;
}

export interface EmailAttachment {
  filename: string;
  path: string;
  cid: string;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  /**
   * Send email with standard MACRA logo attachment
   */
  async sendEmail(
    to: string,
    template: string,
    subject: string,
    context: EmailContext,
    additionalAttachments: EmailAttachment[] = []
  ): Promise<void> {
    try {
      // Check if email service is properly configured
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PWD) {
        this.logger.warn(`Email service not configured. Skipping email to ${to} with template ${template}`);
        return;
      }

      const attachments = [
        {
          filename: AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
          path: join(assetsDir, AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
          cid: AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
        },
        ...additionalAttachments,
      ];

      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context: {
          ...context,
          year: AuthUtils.getCurrentYear(),
        },
        attachments,
      } as any);

      this.logger.log(`Email sent successfully to ${to} with template ${template}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${to} with template ${template}:`, error);
      this.logger.error('Email error details:', error.stack);
      // Don't throw error to prevent registration from failing
      this.logger.warn('Email sending failed, but continuing with the process');
    }
  }

  /**
   * Send 2FA verification email
   */
  async send2FAEmail(
    to: string,
    template: string,
    subject: string,
    context: {
      name: string;
      message: string;
      verifyUrl: string;
      otp: string;
    }
  ): Promise<void> {
    return this.sendEmail(to, template, subject, context);
  }

  /**
   * Send login alert email
   */
  async sendLoginAlertEmail(
    to: string,
    subject: string,
    context: {
      userName: string;
      loginUrl: string;
      ip: string;
      country: string;
      city: string;
      userAgent: string;
      message: string;
    }
  ): Promise<void> {
    return this.sendEmail(to, 'login-alert', subject, context);
  }

  /**
   * Send password reset confirmation success email
   */
  async sendPasswordResetConfirmationEmail(
    to: string,
    context: {
      userName: string;
      loginUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(
      to,
      'reset',
      'Password Reset Confirmation - MACRA Digital Portal',
      context
    );
  }

  /* Send a password reset request email  */
  async sendPasswordResetRequestEmail(
    to: string,
    context: {
      name: string;
      resetUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(
      to,
      'resetrequest',
      'Reset Password - MACRA Digital Portal',
      context
    );
  }

  /**
   * Send email verification after registering.
   */
  async sendVerifyEmail(
    to: string,
    context: {
      userName: string;
      verifyUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(
      to,
      '2fa',
      'Verify Email - MACRA Digital Portal',
      context
    );
  }

  /**
   * Create verification URL for 2FA
   */
  static createVerificationUrl(
    userId: string,
    secret: string,
    token: string,
    action: string,
    roles: Array<{ name: string }> | undefined
  ): string {
    const urlPrefix = AuthUtils.getUrlPrefix(roles);
    const urlRedirect = AuthUtils.getRedirectUrl(action as any);
    console.log('URL utils', { urlPrefix: urlPrefix, urlRedirect: urlRedirect });

    return `${process.env.FRONTEND_URL}/${urlPrefix}/${urlRedirect}?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=${encodeURIComponent(token)}`;
  }

  /**
   * Send 2FA email using centralized template
   */
  async send2FAEmailWithTemplate(
    to: string,
    data: {
      name: string;
      message: string;
      otp: string;
      verifyUrl: string;
    }
  ): Promise<void> {
    try {
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PWD) {
        this.logger.warn(`Email service not configured. Skipping 2FA email to ${to}`);
        return;
      }

      const template = this.emailTemplateService.generate2FATemplate(data);

      const attachments = [
        {
          filename: AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
          path: join(assetsDir, AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
          cid: AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
        },
      ];

      await this.mailerService.sendMail({
        to,
        subject: template.subject,
        html: template.html,
        attachments,
      });

      this.logger.log(`2FA email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send 2FA email to ${to}:`, error);
      this.logger.warn('2FA email sending failed, but continuing with the process');
    }
  }

  /**
   * Send password reset email using centralized template
   */
  async sendPasswordResetEmailWithTemplate(
    to: string,
    data: {
      userName: string;
      loginUrl: string;
    }
  ): Promise<void> {
    try {
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PWD) {
        this.logger.warn(`Email service not configured. Skipping password reset email to ${to}`);
        return;
      }

      const template = this.emailTemplateService.generatePasswordResetTemplate(data);

      const attachments = [
        {
          filename: AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
          path: join(assetsDir, AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
          cid: AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
        },
      ];

      await this.mailerService.sendMail({
        to,
        subject: template.subject,
        html: template.html,
        attachments,
      });

      this.logger.log(`Password reset email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${to}:`, error);
      this.logger.warn('Password reset email sending failed, but continuing with the process');
    }
  }

  /**
   * Send login alert email using centralized template
   */
  async sendLoginAlertEmailWithTemplate(
    to: string,
    data: {
      userName: string;
      loginUrl: string;
      ip: string;
      country: string;
      city: string;
      userAgent: string;
      message: string;
    }
  ): Promise<void> {
    try {
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PWD) {
        this.logger.warn(`Email service not configured. Skipping login alert email to ${to}`);
        return;
      }

      const template = this.emailTemplateService.generateLoginAlertTemplate(data);

      const attachments = [
        {
          filename: AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
          path: join(assetsDir, AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
          cid: AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
        },
      ];

      await this.mailerService.sendMail({
        to,
        subject: template.subject,
        html: template.html,
        attachments,
      });

      this.logger.log(`Login alert email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send login alert email to ${to}:`, error);
      this.logger.warn('Login alert email sending failed, but continuing with the process');
    }
  }

}
