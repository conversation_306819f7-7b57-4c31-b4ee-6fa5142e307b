<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>University Radio License Application - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link rel="stylesheet" href="assets/main.css">
  <style type="text/tailwindcss">
    @layer components {
      .custom-form-label {
        @apply block text-sm font-medium text-gray-700 pb-2;
      }
      .enhanced-input {
        @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
      }

      .main-button {
        @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
      }

      .secondary-main-button {
        @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
      }

      .custom-input {
        @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
      }

      .form-section {
        @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
      }

      .inner-form-section {
        @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
      }

      .tab-heading {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
      }

      .form-group {
        @apply mb-6;
      }

      .step-indicator {
        @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium;
      }

      .step-indicator.active {
        @apply bg-primary text-white;
      }

      .step-indicator.completed {
        @apply bg-green-500 text-white;
      }

      .step-indicator.inactive {
        @apply bg-gray-200 text-gray-500;
      }

      .step-content {
        @apply hidden;
      }

      .step-content.active {
        @apply block;
      }

      .progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2;
      }

      .progress-fill {
        @apply bg-primary h-2 rounded-full transition-all duration-300;
      }

      .file-upload-area {
        @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary cursor-pointer transition-colors;
      }

      .tracking-status {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .status-draft {
        @apply bg-gray-100 text-gray-800;
      }

      .status-submitted {
        @apply bg-blue-100 text-blue-800;
      }

      .status-review {
        @apply bg-yellow-100 text-yellow-800;
      }

      .status-approved {
        @apply bg-green-100 text-green-800;
      }

      .status-rejected {
        @apply bg-red-100 text-red-800;
      }
    }

    @layer utilities {
      :root {
        --color-primary: #e02b20;
        --color-secondary: #20d5e0;
        --color-primary-subtle: #e4463c;
        --color-secondary-subtle: #abeff3;
      }
    }

  </style>

  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>

<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="index.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="new-application.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-add-line"></i>
            </div>
            New Application
          </a>
          <a href="my-licenses.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            My Licenses
          </a>
          <a href="invoices.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Invoices
          </a>
          <a href="payments.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-bank-card-line"></i>
            </div>
            Payments
          </a>
          <a href="documents.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-folder-line"></i>
            </div>
            Documents
          </a>
          <a href="contact-support.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-customer-service-line"></i>
            </div>
            Support
          </a>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">John Doe</p>
            <p class="text-xs text-gray-500">Customer</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                  placeholder="Search for licenses, users, or transactions..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="help-center.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Help Center</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header-->
          <div class="tab-heading">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">University Radio Licence Application</h1>
              <p class="mt-1 text-sm text-gray-600">Apply for a university radio licence</p>
            </div>
            <div class="relative">
              <a href="my-licenses.html" class="secondary-main-button" role="button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-arrow-left-line"></i>
                </div>
                Back to My Licenses
              </a>
            </div>
          </div>

          <!-- Application Status & Tracking -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Application Status</h3>
                  <p class="text-sm text-gray-500">Application ID: <span id="applicationId" class="font-mono">UNI-RAD-2024-001</span></p>
                </div>
                <div>
                  <span id="applicationStatus" class="tracking-status status-draft">Draft</span>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                  <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
              </div>

              <!-- Step Indicators -->
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div id="step1Indicator" class="step-indicator active">1</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step2Indicator" class="step-indicator inactive">2</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step3Indicator" class="step-indicator inactive">3</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step4Indicator" class="step-indicator inactive">4</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step5Indicator" class="step-indicator inactive">5</div>
                </div>
              </div>

              <!-- Step Labels -->
              <div class="flex justify-between text-xs text-gray-500 mt-2">
                <span>Basic Info</span>
                <span>Company & Legal</span>
                <span>Programming</span>
                <span>Technical & Financial</span>
                <span>Review & Submit</span>
              </div>
            </div>
          </div>

          <!-- Application Form -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <form id="universityRadioForm" class="space-y-8">

                <!-- Step 1: Basic Information -->
                <div id="step1" class="step-content active">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Basic Information</h3>

                    <div class="inner-form-section">
                      <!-- Proposed Station Name -->
                      <div class="sm:col-span-2">
                        <label for="stationName" class="custom-form-label">Proposed station name *</label>
                        <input type="text" name="stationName" id="stationName" class="custom-input" required>
                      </div>

                      <!-- Type of Licence -->
                      <div>
                        <label for="licenceType" class="custom-form-label">Type of licence *</label>
                        <select id="licenceType" name="licenceType" class="custom-input" required onchange="toggleLicenceSubtype()">
                          <option value="">Select Type</option>
                          <option value="sound">Sound Broadcasting</option>
                          <option value="tv">Television Broadcasting</option>
                        </select>
                      </div>

                      <!-- Type of Sound Licence (conditional) -->
                      <div id="soundLicenceDiv" style="display: none;">
                        <label for="soundLicenceType" class="custom-form-label">Type of sound licence</label>
                        <select id="soundLicenceType" name="soundLicenceType" class="custom-input">
                          <option value="">Select Sound Type</option>
                          <option value="fm">FM Radio</option>
                          <option value="am">AM Radio</option>
                          <option value="digital">Digital Radio</option>
                          <option value="community">Community Radio</option>
                          <option value="campus">Campus Radio</option>
                        </select>
                      </div>

                      <!-- Type of TV Licence (conditional) -->
                      <div id="tvLicenceDiv" style="display: none;">
                        <label for="tvLicenceType" class="custom-form-label">Type of TV licence</label>
                        <select id="tvLicenceType" name="tvLicenceType" class="custom-input">
                          <option value="">Select TV Type</option>
                          <option value="terrestrial">Terrestrial TV</option>
                          <option value="satellite">Satellite TV</option>
                          <option value="cable">Cable TV</option>
                          <option value="digital">Digital TV</option>
                          <option value="community">Community TV</option>
                        </select>
                      </div>

                      <!-- Address of Station -->
                      <div class="sm:col-span-2">
                        <label for="stationAddress" class="custom-form-label">Address of station *</label>
                        <textarea id="stationAddress" name="stationAddress" rows="3" class="custom-input" required placeholder="Provide the physical address where the broadcasting station will be located"></textarea>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Company Information & Legal -->
                <div id="step2" class="step-content">
                  <!-- Company Information Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Company Information</h3>

                    <div class="inner-form-section">
                      <!-- Company Name -->
                      <div class="sm:col-span-2">
                        <label for="companyName" class="custom-form-label">Company name *</label>
                        <input type="text" name="companyName" id="companyName" class="custom-input" required>
                      </div>

                      <!-- Company Profile -->
                      <div class="sm:col-span-2">
                        <label for="companyProfile" class="custom-form-label">Company Profile *</label>
                        <textarea id="companyProfile" name="companyProfile" rows="4" class="custom-input" required placeholder="Provide a detailed description of your company's background, mission, and activities"></textarea>
                      </div>

                      <!-- Company Website -->
                      <div class="sm:col-span-2">
                        <label for="companyWebsite" class="custom-form-label">Company website *</label>
                        <input type="url" name="companyWebsite" id="companyWebsite" class="custom-input" required placeholder="https://www.company.com">
                      </div>

                      <!-- Contact Person Name -->
                      <div>
                        <label for="companyContactName" class="custom-form-label">Contact person name *</label>
                        <input type="text" name="companyContactName" id="companyContactName" class="custom-input" required>
                      </div>

                      <!-- Contact Email -->
                      <div>
                        <label for="companyContactEmail" class="custom-form-label">Contact email *</label>
                        <input type="email" name="companyContactEmail" id="companyContactEmail" class="custom-input" required>
                      </div>

                      <!-- Contact Phone -->
                      <div>
                        <label for="companyContactPhone" class="custom-form-label">Contact phone *</label>
                        <input type="tel" name="companyContactPhone" id="companyContactPhone" class="custom-input" required>
                      </div>

                      <!-- Date of Incorporation -->
                      <div>
                        <label for="companyDateIncorporation" class="custom-form-label">Date of incorporation *</label>
                        <input type="date" name="companyDateIncorporation" id="companyDateIncorporation" class="custom-input" required>
                      </div>
                    </div>
                  </div>

                  <!-- Applicant Legal Status Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Applicant Legal Status (Founding documents)</h3>

                    <div class="space-y-6">
                      <!-- Constitution -->
                      <div>
                        <label class="custom-form-label">Constitution *</label>
                        <div class="file-upload-area" onclick="document.getElementById('legalConstitution').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Constitution Document</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="legalConstitution" name="legalConstitution" accept=".pdf" class="hidden" required>
                        <div id="legalConstitutionList" class="mt-2"></div>
                      </div>

                      <!-- Memorandum -->
                      <div>
                        <label class="custom-form-label">Memorandum *</label>
                        <div class="file-upload-area" onclick="document.getElementById('legalMemorandum').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Memorandum Document</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="legalMemorandum" name="legalMemorandum" accept=".pdf" class="hidden" required>
                        <div id="legalMemorandumList" class="mt-2"></div>
                      </div>

                      <!-- Articles of Association -->
                      <div>
                        <label class="custom-form-label">Articles of association</label>
                        <div class="file-upload-area" onclick="document.getElementById('legalArticles').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Articles of Association</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="legalArticles" name="legalArticles" accept=".pdf" class="hidden">
                        <div id="legalArticlesList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>

                  <!-- Shareholder Information Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Shareholder Information</h3>

                    <div class="mb-4">
                      <label for="shareholderCount" class="custom-form-label">Number of shareholders (minimum 2) *</label>
                      <input type="number" name="shareholderCount" id="shareholderCount" class="custom-input" min="2" required onchange="generateShareholderForms()">
                    </div>

                    <div id="shareholderFormsContainer" class="space-y-6">
                      <!-- Shareholder forms will be generated dynamically -->
                    </div>
                  </div>

                  <!-- Proposed Management Section -->
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Proposed Management</h3>

                    <div class="mb-4">
                      <label for="managementCount" class="custom-form-label">Number of managing employees (minimum 3) *</label>
                      <input type="number" name="managementCount" id="managementCount" class="custom-input" min="3" required onchange="generateManagementForms()">
                    </div>

                    <div id="managementFormsContainer" class="space-y-6">
                      <!-- Management forms will be generated dynamically -->
                    </div>
                  </div>
                </div>

                <!-- Step 3: Programming & Market Research -->
                <div id="step3" class="step-content">
                  <!-- Other Interested Parties Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Other Interested Parties</h3>

                    <div class="mb-4">
                      <button type="button" onclick="addInterestedParty()" class="secondary-main-button">
                        <i class="ri-add-line mr-2"></i>
                        Add Interested Party
                      </button>
                    </div>

                    <div id="interestedPartiesContainer" class="space-y-6">
                      <!-- Interested parties forms will be generated dynamically -->
                    </div>
                  </div>

                  <!-- Demand, Need And Support For The Proposed Service Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Demand, Need And Support For The Proposed Service</h3>

                    <!-- Market Appeal Subsection -->
                    <div class="mb-6">
                      <h4 class="text-md font-medium text-gray-800 mb-3">Market Appeal</h4>

                      <div class="space-y-4">
                        <!-- Market Appeal Audience -->
                        <div>
                          <label for="marketAppealAudience" class="custom-form-label">Describe how the proposed programme service will cater for the demand and interests of its intended audience. *</label>
                          <textarea id="marketAppealAudience" name="marketAppealAudience" rows="4" class="custom-input" required placeholder="Explain how your programming will meet the specific needs and interests of your target audience"></textarea>
                        </div>

                        <!-- Market Appeal Unique -->
                        <div>
                          <label for="marketAppealUnique" class="custom-form-label">Describe how the broadcasting service will be different from any existing broadcasting services available in the proposed coverage area. *</label>
                          <textarea id="marketAppealUnique" name="marketAppealUnique" rows="4" class="custom-input" required placeholder="Highlight what makes your service unique compared to existing broadcasters in the area"></textarea>
                        </div>
                      </div>
                    </div>

                    <!-- Market Research Subsection -->
                    <div class="mb-6">
                      <h4 class="text-md font-medium text-gray-800 mb-3">Market Research</h4>

                      <div>
                        <label for="marketResearch" class="custom-form-label">Describe the demand for the proposed broadcasting service. Summarise the main findings of any market research undertaken, analysis of existing audience, research information, or other forms of evidence as proof that the proposed service will cater for the tastes and interests of people in the area. *</label>
                        <textarea id="marketResearch" name="marketResearch" rows="5" class="custom-input" required placeholder="Provide detailed market research findings, audience analysis, and evidence supporting demand for your service"></textarea>
                      </div>
                    </div>

                    <!-- Local Support Subsection -->
                    <div class="mb-6">
                      <h4 class="text-md font-medium text-gray-800 mb-3">Local Support For The Proposed Broadcasting Service</h4>

                      <div>
                        <label class="custom-form-label">Required fifty (50) signatures from a place where you propose to establish the broadcasting station *</label>
                        <p class="text-sm text-gray-600 mb-3">Upload a file containing 50 signatures with the following format:</p>
                        <ul class="text-sm text-gray-600 mb-3 list-disc list-inside">
                          <li>The name and address of each signatory</li>
                          <li>The personal characteristics of each signatory (gender, age, religion, language, and other relevant characteristics)</li>
                        </ul>
                        <div class="file-upload-area" onclick="document.getElementById('localSupportFile').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Local Support Document</p>
                          <p class="text-xs text-gray-500">PDF or JPG up to 10MB</p>
                        </div>
                        <input type="file" id="localSupportFile" name="localSupportFile" accept=".pdf,.jpg,.jpeg" class="hidden" required>
                        <div id="localSupportFileList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>

                  <!-- Complaints And Codes Of Operation Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Complaints And Codes Of Operation</h3>

                    <div>
                      <label for="codesOperation" class="custom-form-label">Describe the way you proposed to handle and consider comments and complaints about the proposed service. *</label>
                      <textarea id="codesOperation" name="codesOperation" rows="4" class="custom-input" required placeholder="Outline your procedures for handling viewer/listener complaints and feedback"></textarea>
                    </div>
                  </div>

                  <!-- Programming Section -->
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Programming</h3>

                    <div class="inner-form-section">
                      <!-- Programme Format -->
                      <div>
                        <label for="programmeFormat" class="custom-form-label">Programme format *</label>
                        <select id="programmeFormat" name="programmeFormat" class="custom-input" required>
                          <option value="">Select Format</option>
                          <option value="youth">Youth</option>
                          <option value="talk">Talk</option>
                          <option value="religious">Religious</option>
                          <option value="educational">Educational</option>
                          <option value="music">Music</option>
                          <option value="general">General</option>
                        </select>
                      </div>

                      <!-- Proposed Broadcasting Service Approach -->
                      <div class="sm:col-span-2">
                        <label for="programmeApproach" class="custom-form-label">Proposed broadcasting service approach *</label>
                        <textarea id="programmeApproach" name="programmeApproach" rows="4" class="custom-input" required placeholder="Describe your overall approach to broadcasting and programming strategy"></textarea>
                      </div>

                      <!-- Start Time -->
                      <div>
                        <label for="programmeStart" class="custom-form-label">Start time *</label>
                        <input type="time" name="programmeStart" id="programmeStart" class="custom-input" required>
                      </div>

                      <!-- End Time -->
                      <div>
                        <label for="programmeEnd" class="custom-form-label">End time *</label>
                        <input type="time" name="programmeEnd" id="programmeEnd" class="custom-input" required>
                      </div>

                      <!-- Peak Time -->
                      <div>
                        <label for="programmePeak" class="custom-form-label">Peak time *</label>
                        <input type="time" name="programmePeak" id="programmePeak" class="custom-input" required>
                      </div>

                      <!-- Programme Sourcing -->
                      <div>
                        <label for="programmeSourcing" class="custom-form-label">Programme sourcing *</label>
                        <select id="programmeSourcing" name="programmeSourcing" class="custom-input" required>
                          <option value="">Select Sourcing</option>
                          <option value="external">External sources</option>
                          <option value="local">Locally generated sources</option>
                        </select>
                      </div>

                      <!-- Programme Objectives -->
                      <div class="sm:col-span-2">
                        <label for="programmeObjectives" class="custom-form-label">Expected broadcasting approach and objectives *</label>
                        <textarea id="programmeObjectives" name="programmeObjectives" rows="4" class="custom-input" required placeholder="Describe your broadcasting objectives and expected outcomes"></textarea>
                      </div>

                      <!-- Programme Benefit -->
                      <div class="sm:col-span-2">
                        <label for="programmeBenefit" class="custom-form-label">Expected broadcasting service benefit *</label>
                        <textarea id="programmeBenefit" name="programmeBenefit" rows="4" class="custom-input" required placeholder="Describe how the proposed broadcasting service will encourage members of the society served by it or persons associated with or promoting the interests of such society to participate in the selection and provision of programmes to be broadcast during such broadcasting service."></textarea>
                      </div>

                      <!-- Promotion of Local Content -->
                      <div class="sm:col-span-2">
                        <label for="programmePromote" class="custom-form-label">Promotion Of Local Content *</label>
                        <p class="text-sm text-gray-600 mb-2">Please explain how your proposed broadcasting station shall promote local content i.e. Malawian news items, songs, and drama.</p>
                        <textarea id="programmePromote" name="programmePromote" rows="4" class="custom-input" required placeholder="Explain your strategy for promoting Malawian content including news, music, and drama"></textarea>
                      </div>

                      <!-- Editorial Policy -->
                      <div class="sm:col-span-2">
                        <label class="custom-form-label">Editorial Policy *</label>
                        <p class="text-sm text-gray-600 mb-3">Provide an editorial policy for your proposed broadcasting station mentioning in detail how your station shall conduct itself in terms of news coverage, reporting truthful issues, news balancing, obscenity, and issues concerning national security.</p>
                        <div class="file-upload-area" onclick="document.getElementById('programmePolicy').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Editorial Policy Document</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="programmePolicy" name="programmePolicy" accept=".pdf" class="hidden" required>
                        <div id="programmePolicyList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 4: Financial & Technical Capacity -->
                <div id="step4" class="step-content">
                  <!-- Financial Capacity Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Financial Capacity</h3>

                    <div class="space-y-6">
                      <!-- Business Plan -->
                      <div>
                        <label class="custom-form-label">Business Plan *</label>
                        <div class="file-upload-area" onclick="document.getElementById('businessPlan').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Business Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessPlan" name="businessPlan" accept=".pdf" class="hidden" required>
                        <div id="businessPlanList" class="mt-2"></div>
                      </div>

                      <!-- Funding Source -->
                      <div>
                        <label for="businessFunding" class="custom-form-label">Funding Source *</label>
                        <select id="businessFunding" name="businessFunding" class="custom-input" required>
                          <option value="">Select Funding Source</option>
                          <option value="grants">Grants</option>
                          <option value="donations">Donations</option>
                          <option value="sponsorships">Sponsorships</option>
                          <option value="advertising">Advertising</option>
                          <option value="membership">Membership Fees</option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <!-- Proof of Funding Source -->
                      <div>
                        <label class="custom-form-label">Attach proof of funding source *</label>
                        <div class="file-upload-area" onclick="document.getElementById('businessProofFunding').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Proof of Funding</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessProofFunding" name="businessProofFunding" accept=".pdf" class="hidden" required>
                        <div id="businessProofFundingList" class="mt-2"></div>
                      </div>

                      <!-- Income and Expenditure Statement -->
                      <div>
                        <label class="custom-form-label">Attach Income and Expenditure Statement *</label>
                        <div class="file-upload-area" onclick="document.getElementById('businessIncomeExpenditure').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Income and Expenditure Statement</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessIncomeExpenditure" name="businessIncomeExpenditure" accept=".pdf" class="hidden" required>
                        <div id="businessIncomeExpenditureList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>

                  <!-- Technical Capacity Section -->
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Technical Capacity</h3>

                    <div class="space-y-6">
                      <!-- Technical Personnel -->
                      <div>
                        <label class="custom-form-label">Attach resumes of all technical personnel *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalPersonnel').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical Personnel Resumes</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalPersonnel" name="technicalPersonnel" accept=".pdf" class="hidden" required>
                        <div id="technicalPersonnelList" class="mt-2"></div>
                      </div>

                      <!-- Transmission Sites -->
                      <div>
                        <label for="technicalTransmission" class="custom-form-label">Transmission sites *</label>
                        <p class="text-sm text-gray-600 mb-2">Does the applicant propose to locate the transmitter and the mast/antenna at the same site as used by an existing broadcasting or telecommunications license? If not, provide full details of the linking arrangements between the studio and the transmitter site.</p>
                        <textarea id="technicalTransmission" name="technicalTransmission" rows="4" class="custom-input" required placeholder="Describe your transmission site arrangements and any linking between studio and transmitter"></textarea>
                      </div>

                      <!-- Existing Sites -->
                      <div>
                        <label class="custom-form-label">Existing sites *</label>
                        <p class="text-sm text-gray-600 mb-3">Provide an outline description of the existing transmission site. Has the applicant entered negotiations with the owner/operator of the site regarding arrangements for sharing the site should this application be successful? If so, provide details. If not, state what arrangements are anticipated. When is it expected that these negotiations will be concluded?</p>
                        <div class="file-upload-area" onclick="document.getElementById('technicalSites').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Existing Sites Documentation</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalSites" name="technicalSites" accept=".pdf" class="hidden" required>
                        <div id="technicalSitesList" class="mt-2"></div>
                      </div>

                      <!-- Roll out Plans -->
                      <div>
                        <label class="custom-form-label">Roll out plans *</label>
                        <p class="text-sm text-gray-600 mb-3">Provide a detailed plan of your broadcasting station's intentions to start providing broadcasting services in your proposed area. Please indicate the targets and time frame.</p>
                        <div class="file-upload-area" onclick="document.getElementById('technicalRollOut').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Roll Out Plans</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalRollOut" name="technicalRollOut" accept=".pdf" class="hidden" required>
                        <div id="technicalRollOutList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 5: Conclusion & Declaration -->
                <div id="step5" class="step-content">
                  <!-- Application Summary Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Application Summary</h3>

                    <div class="bg-gray-50 p-4 rounded-lg">
                      <div id="applicationSummary" class="space-y-2 text-sm text-gray-600">
                        <!-- Summary will be populated by JavaScript -->
                      </div>
                    </div>
                  </div>

                  <!-- Conclusion Section -->
                  <div class="form-section mb-8">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Conclusion</h3>

                    <!-- Other Matters Subsection -->
                    <div class="space-y-6">
                      <!-- Why Authority Should Grant Licence -->
                      <div>
                        <label for="conclusionMotivation" class="custom-form-label">State briefly why the Authority should grant you the licence *</label>
                        <textarea id="conclusionMotivation" name="conclusionMotivation" rows="4" class="custom-input" required placeholder="Provide compelling reasons why MACRA should approve your broadcasting license application"></textarea>
                      </div>

                      <!-- Other Matters -->
                      <div>
                        <label for="conclusionOther" class="custom-form-label">Give details of any other matters of which you consider the Authority should be aware.</label>
                        <textarea id="conclusionOther" name="conclusionOther" rows="3" class="custom-input" placeholder="Optional: Include any additional information relevant to your application"></textarea>
                      </div>
                    </div>
                  </div>

                  <!-- Declaration Section -->
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Declaration</h3>

                    <div class="space-y-6">
                      <!-- Declaration Text -->
                      <div class="border border-gray-200 p-4 rounded-lg">
                        <div class="space-y-3 text-sm text-gray-700">
                          <p class="font-medium">Applicants must conclude their submission with the following certificates:</p>

                          <div class="bg-gray-50 p-4 rounded">
                            <p class="mb-3">I acknowledge that the Malawi Communications Regulatory Authority reserves the right to have any licence issued set aside should it be found that at any time any material statement is found to be false and to have been made by the applicant or any officer thereof knowing it to be false.</p>

                            <div class="space-y-4">
                              <div>
                                <label class="flex items-start">
                                  <input type="checkbox" id="agreeDeclaration" name="agreeDeclaration" class="enhanced-checkbox mt-1" required>
                                  <span class="ml-2 text-sm text-gray-700">I acknowledge and agree to the above declaration.</span>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Applicant Signature Section -->
                      <div class="border border-gray-200 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Applicant Signature</h4>
                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                          <div>
                            <label for="applicantName" class="custom-form-label">Applicant Full Name *</label>
                            <input type="text" name="applicantName" id="applicantName" class="custom-input" required>
                          </div>
                          <div>
                            <label for="applicantTitle" class="custom-form-label">Title/Position *</label>
                            <input type="text" name="applicantTitle" id="applicantTitle" class="custom-input" required>
                          </div>
                          <div class="sm:col-span-2">
                            <label for="applicantSignatureDate" class="custom-form-label">Date of Signature *</label>
                            <input type="date" name="applicantSignatureDate" id="applicantSignatureDate" class="custom-input" required>
                          </div>
                        </div>
                      </div>

                      <!-- Commissioner of Oath Section -->
                      <div class="border border-gray-200 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Commissioner of Oath</h4>
                        <div class="space-y-4">
                          <p class="text-sm text-gray-700">I certify that on the __________ day ________________, in my presence at _________________________ the Deponent signed this declaration and declared that he/she:</p>

                          <ul class="text-sm text-gray-700 list-disc list-inside space-y-1 ml-4">
                            <li>Knows and understands the contents hereof.</li>
                            <li>Had no objection to taking the prescribed oath.</li>
                            <li>Considers the oath to be binding on his/her conscience.</li>
                          </ul>

                          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                              <label for="commissionerName" class="custom-form-label">Commissioner of Oath Name</label>
                              <input type="text" name="commissionerName" id="commissionerName" class="custom-input">
                            </div>
                            <div>
                              <label for="commissionerDate" class="custom-form-label">Date</label>
                              <input type="date" name="commissionerDate" id="commissionerDate" class="custom-input">
                            </div>
                            <div class="sm:col-span-2">
                              <label for="commissionerLocation" class="custom-form-label">Location</label>
                              <input type="text" name="commissionerLocation" id="commissionerLocation" class="custom-input" placeholder="Location where oath was taken">
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Final Confirmation -->
                      <div class="border border-gray-200 p-4 rounded-lg bg-blue-50">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Final Confirmation</h4>
                        <div class="space-y-3">
                          <label class="flex items-start">
                            <input type="checkbox" id="confirmAccuracy" name="confirmAccuracy" class="enhanced-checkbox mt-1" required>
                            <span class="ml-2 text-sm text-gray-700">I confirm that all information provided in this application is accurate and complete to the best of my knowledge.</span>
                          </label>

                          <label class="flex items-start">
                            <input type="checkbox" id="confirmCompliance" name="confirmCompliance" class="enhanced-checkbox mt-1" required>
                            <span class="ml-2 text-sm text-gray-700">I understand and agree to comply with all MACRA regulations and broadcasting standards.</span>
                          </label>

                          <label class="flex items-start">
                            <input type="checkbox" id="confirmSubmission" name="confirmSubmission" class="enhanced-checkbox mt-1" required>
                            <span class="ml-2 text-sm text-gray-700">I authorize the submission of this application to the Malawi Communications Regulatory Authority.</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Form Navigation -->
                <div class="flex justify-between pt-6 border-t border-gray-200">
                  <button type="button" id="prevButton" class="secondary-main-button" style="display: none;">
                    <i class="ri-arrow-left-line mr-2"></i>
                    Previous
                  </button>

                  <div class="flex space-x-3">
                    <button type="button" id="saveDraftButton" class="secondary-main-button">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>

                    <button type="button" id="nextButton" class="main-button">
                      Next
                      <i class="ri-arrow-right-line ml-2"></i>
                    </button>

                    <button type="submit" id="submitButton" class="main-button" style="display: none;">
                      <i class="ri-send-plane-line mr-2"></i>
                      Submit Application
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    // Global variables
    let currentStep = 1;
    const totalSteps = 5;
    let applicationData = {};
    let uploadedFiles = {};

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
      initializeForm();
      loadDraftData();
      updateProgress();
      setupEventListeners();

      // Set default date for signature
      document.getElementById('signatureDate').value = new Date().toISOString().split('T')[0];
    });

    // Initialize form
    function initializeForm() {
      showStep(currentStep);
      updateStepIndicators();
      updateNavigationButtons();
    }

    // Setup event listeners
    function setupEventListeners() {
      // Navigation buttons
      document.getElementById('nextButton').addEventListener('click', nextStep);
      document.getElementById('prevButton').addEventListener('click', prevStep);
      document.getElementById('saveDraftButton').addEventListener('click', saveDraft);

      // Form submission
      document.getElementById('universityRadioForm').addEventListener('submit', submitApplication);

      // File upload handlers
      setupFileUploadHandlers();

      // Form field change handlers for live tracking
      const formInputs = document.querySelectorAll('input, select, textarea');
      formInputs.forEach(input => {
        input.addEventListener('change', updateProgress);
        input.addEventListener('input', updateProgress);
      });

      // Content percentage validation
      const percentageInputs = ['educationalContent', 'musicContent', 'newsContent', 'otherContent'];
      percentageInputs.forEach(id => {
        document.getElementById(id).addEventListener('input', validatePercentages);
      });
    }

    // Show specific step
    function showStep(step) {
      // Hide all steps
      for (let i = 1; i <= totalSteps; i++) {
        document.getElementById(`step${i}`).classList.remove('active');
      }

      // Show current step
      document.getElementById(`step${step}`).classList.add('active');

      // Update step indicators
      updateStepIndicators();
      updateNavigationButtons();

      // Update summary if on last step
      if (step === totalSteps) {
        updateApplicationSummary();
      }
    }

    // Update step indicators
    function updateStepIndicators() {
      for (let i = 1; i <= totalSteps; i++) {
        const indicator = document.getElementById(`step${i}Indicator`);
        indicator.classList.remove('active', 'completed', 'inactive');

        if (i < currentStep) {
          indicator.classList.add('completed');
          indicator.innerHTML = '<i class="ri-check-line"></i>';
        } else if (i === currentStep) {
          indicator.classList.add('active');
          indicator.innerHTML = i;
        } else {
          indicator.classList.add('inactive');
          indicator.innerHTML = i;
        }
      }
    }

    // Update navigation buttons
    function updateNavigationButtons() {
      const prevButton = document.getElementById('prevButton');
      const nextButton = document.getElementById('nextButton');
      const submitButton = document.getElementById('submitButton');

      // Previous button
      if (currentStep === 1) {
        prevButton.style.display = 'none';
      } else {
        prevButton.style.display = 'inline-flex';
      }

      // Next/Submit buttons
      if (currentStep === totalSteps) {
        nextButton.style.display = 'none';
        submitButton.style.display = 'inline-flex';
      } else {
        nextButton.style.display = 'inline-flex';
        submitButton.style.display = 'none';
      }
    }

    // Next step
    function nextStep() {
      if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
          currentStep++;
          showStep(currentStep);
          updateProgress();
          saveDraft(); // Auto-save on step change
        }
      }
    }

    // Previous step
    function prevStep() {
      if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateProgress();
      }
    }

    // Validate current step
    function validateCurrentStep() {
      const currentStepElement = document.getElementById(`step${currentStep}`);
      const requiredFields = currentStepElement.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          field.classList.add('border-red-500');
          isValid = false;
        } else {
          field.classList.remove('border-red-500');
        }
      });

      // Special validation for step 3 (percentages must add up to 100)
      if (currentStep === 3) {
        const total = getContentPercentageTotal();
        if (total !== 100) {
          alert('Content percentages must add up to 100%');
          isValid = false;
        }
      }

      // Special validation for step 5 (checkboxes must be checked)
      if (currentStep === 5) {
        const agreeTerms = document.getElementById('agreeTerms');
        const agreeCompliance = document.getElementById('agreeCompliance');

        if (!agreeTerms.checked || !agreeCompliance.checked) {
          alert('Please agree to all terms and conditions before submitting.');
          isValid = false;
        }
      }

      if (!isValid) {
        alert('Please fill in all required fields before proceeding.');
      }

      return isValid;
    }

    // Update progress
    function updateProgress() {
      const form = document.getElementById('universityRadioForm');
      const formData = new FormData(form);
      const allFields = form.querySelectorAll('input, select, textarea');
      const requiredFields = form.querySelectorAll('[required]');

      let filledFields = 0;
      let totalFields = allFields.length;

      // Count filled fields
      allFields.forEach(field => {
        if (field.type === 'checkbox' || field.type === 'radio') {
          if (field.checked) filledFields++;
        } else if (field.type === 'file') {
          if (field.files.length > 0) filledFields++;
        } else if (field.value.trim() !== '') {
          filledFields++;
        }
      });

      // Calculate progress percentage
      const progressPercentage = Math.round((filledFields / totalFields) * 100);

      // Update progress bar
      document.getElementById('progressFill').style.width = `${progressPercentage}%`;
      document.getElementById('progressPercentage').textContent = `${progressPercentage}%`;

      // Update application status based on progress
      updateApplicationStatus(progressPercentage);
    }

    // Update application status
    function updateApplicationStatus(progressPercentage) {
      const statusElement = document.getElementById('applicationStatus');

      if (progressPercentage === 100) {
        statusElement.className = 'tracking-status status-review';
        statusElement.textContent = 'Ready for Review';
      } else if (progressPercentage >= 75) {
        statusElement.className = 'tracking-status status-submitted';
        statusElement.textContent = 'Nearly Complete';
      } else if (progressPercentage >= 25) {
        statusElement.className = 'tracking-status status-review';
        statusElement.textContent = 'In Progress';
      } else {
        statusElement.className = 'tracking-status status-draft';
        statusElement.textContent = 'Draft';
      }
    }

    // Validate content percentages
    function validatePercentages() {
      const total = getContentPercentageTotal();
      const inputs = ['educationalContent', 'musicContent', 'newsContent', 'otherContent'];

      inputs.forEach(id => {
        const input = document.getElementById(id);
        if (total > 100) {
          input.classList.add('border-red-500');
        } else {
          input.classList.remove('border-red-500');
        }
      });

      // Show warning if total exceeds 100
      if (total > 100) {
        // You could add a warning message here
      }
    }

    // Get total content percentage
    function getContentPercentageTotal() {
      const educational = parseInt(document.getElementById('educationalContent').value) || 0;
      const music = parseInt(document.getElementById('musicContent').value) || 0;
      const news = parseInt(document.getElementById('newsContent').value) || 0;
      const other = parseInt(document.getElementById('otherContent').value) || 0;

      return educational + music + news + other;
    }

    // Setup file upload handlers
    function setupFileUploadHandlers() {
      const fileInputs = document.querySelectorAll('input[type="file"]');

      fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
          handleFileUpload(e.target);
        });
      });
    }

    // Handle file upload
    function handleFileUpload(input) {
      const files = input.files;
      const listElementId = input.id + 'List';
      const listElement = document.getElementById(listElementId);

      if (listElement) {
        listElement.innerHTML = '';

        Array.from(files).forEach(file => {
          const fileItem = document.createElement('div');
          fileItem.className = 'flex items-center justify-between p-2 bg-gray-50 rounded mt-2';
          fileItem.innerHTML = `
            <div class="flex items-center">
              <i class="ri-file-line text-gray-400 mr-2"></i>
              <span class="text-sm text-gray-700">${file.name}</span>
              <span class="text-xs text-gray-500 ml-2">(${formatFileSize(file.size)})</span>
            </div>
            <button type="button" onclick="removeFile('${input.id}', '${file.name}')" class="text-red-500 hover:text-red-700">
              <i class="ri-close-line"></i>
            </button>
          `;
          listElement.appendChild(fileItem);
        });
      }

      // Store files for later use
      uploadedFiles[input.id] = files;
      updateProgress();
    }

    // Format file size
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Remove file
    function removeFile(inputId, fileName) {
      const input = document.getElementById(inputId);
      const listElement = document.getElementById(inputId + 'List');

      // Clear the input
      input.value = '';

      // Clear the list
      if (listElement) {
        listElement.innerHTML = '';
      }

      // Remove from stored files
      delete uploadedFiles[inputId];
      updateProgress();
    }

    // Update application summary
    function updateApplicationSummary() {
      const summaryElement = document.getElementById('applicationSummary');
      const form = document.getElementById('universityRadioForm');
      const formData = new FormData(form);

      let summaryHTML = '';

      // University Information
      const universityName = document.getElementById('universityName').value;
      const callSign = document.getElementById('callSign').value;
      const frequency = document.getElementById('frequency').value;
      const powerOutput = document.getElementById('powerOutput').value;

      if (universityName) {
        summaryHTML += `<div><strong>University:</strong> ${universityName}</div>`;
      }
      if (callSign) {
        summaryHTML += `<div><strong>Call Sign:</strong> ${callSign}</div>`;
      }
      if (frequency) {
        summaryHTML += `<div><strong>Frequency:</strong> ${frequency} MHz</div>`;
      }
      if (powerOutput) {
        summaryHTML += `<div><strong>Power:</strong> ${powerOutput} Watts</div>`;
      }

      // Programming content
      const educational = document.getElementById('educationalContent').value;
      const music = document.getElementById('musicContent').value;
      const news = document.getElementById('newsContent').value;
      const other = document.getElementById('otherContent').value;

      if (educational || music || news || other) {
        summaryHTML += `<div><strong>Content Mix:</strong> ${educational}% Educational, ${music}% Music, ${news}% News, ${other}% Other</div>`;
      }

      // File uploads
      const uploadCount = Object.keys(uploadedFiles).length;
      if (uploadCount > 0) {
        summaryHTML += `<div><strong>Documents:</strong> ${uploadCount} file(s) uploaded</div>`;
      }

      summaryElement.innerHTML = summaryHTML || '<div class="text-gray-500">Complete the form to see summary</div>';
    }

    // Save draft
    function saveDraft() {
      const form = document.getElementById('universityRadioForm');
      const formData = new FormData(form);

      // Convert FormData to object
      const draftData = {};
      for (let [key, value] of formData.entries()) {
        draftData[key] = value;
      }

      // Add current step and files
      draftData.currentStep = currentStep;
      draftData.uploadedFiles = uploadedFiles;

      // Save to localStorage
      localStorage.setItem('universityRadioApplicationDraft', JSON.stringify(draftData));

      // Show success message
      showNotification('Draft saved successfully!', 'success');
    }

    // Load draft data
    function loadDraftData() {
      const draftData = localStorage.getItem('universityRadioApplicationDraft');

      if (draftData) {
        try {
          const data = JSON.parse(draftData);

          // Restore form fields
          Object.keys(data).forEach(key => {
            if (key !== 'currentStep' && key !== 'uploadedFiles') {
              const field = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
              if (field) {
                if (field.type === 'checkbox' || field.type === 'radio') {
                  field.checked = data[key] === field.value;
                } else {
                  field.value = data[key];
                }
              }
            }
          });

          // Restore current step
          if (data.currentStep) {
            currentStep = data.currentStep;
          }

          // Restore uploaded files info (files themselves can't be restored)
          if (data.uploadedFiles) {
            uploadedFiles = data.uploadedFiles;
          }

          showNotification('Draft loaded successfully!', 'info');
        } catch (error) {
          console.error('Error loading draft:', error);
        }
      }
    }

    // Submit application
    function submitApplication(e) {
      e.preventDefault();

      if (!validateCurrentStep()) {
        return;
      }

      // Show loading state
      const submitButton = document.getElementById('submitButton');
      const originalText = submitButton.innerHTML;
      submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Submitting...';
      submitButton.disabled = true;

      // Simulate submission (replace with actual API call)
      setTimeout(() => {
        // Update status
        document.getElementById('applicationStatus').className = 'tracking-status status-submitted';
        document.getElementById('applicationStatus').textContent = 'Submitted';

        // Clear draft
        localStorage.removeItem('universityRadioApplicationDraft');

        // Show success message
        showNotification('Application submitted successfully! You will receive a confirmation email shortly.', 'success');

        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;

        // Redirect after delay
        setTimeout(() => {
          window.location.href = './license-management.html';
        }, 3000);
      }, 2000);
    }

    // Show notification
    function showNotification(message, type = 'info') {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
      }`;
      notification.innerHTML = `
        <div class="flex items-center">
          <i class="ri-${type === 'success' ? 'check' : type === 'error' ? 'error-warning' : 'information'}-line mr-2"></i>
          <span>${message}</span>
        </div>
      `;

      // Add to page
      document.body.appendChild(notification);

      // Remove after 5 seconds
      setTimeout(() => {
        notification.remove();
      }, 5000);
    }

    // Sidebar and dropdown functions (from original template)
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('.dropdown button');

      if (!button && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
      }
    });

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // Toggle licence subtype based on main licence type
    function toggleLicenceSubtype() {
      const licenceType = document.getElementById('licenceType').value;
      const soundDiv = document.getElementById('soundLicenceDiv');
      const tvDiv = document.getElementById('tvLicenceDiv');

      if (licenceType === 'sound') {
        soundDiv.style.display = 'block';
        tvDiv.style.display = 'none';
        document.getElementById('soundLicenceType').required = true;
        document.getElementById('tvLicenceType').required = false;
      } else if (licenceType === 'tv') {
        soundDiv.style.display = 'none';
        tvDiv.style.display = 'block';
        document.getElementById('soundLicenceType').required = false;
        document.getElementById('tvLicenceType').required = true;
      } else {
        soundDiv.style.display = 'none';
        tvDiv.style.display = 'none';
        document.getElementById('soundLicenceType').required = false;
        document.getElementById('tvLicenceType').required = false;
      }
    }

    // Generate shareholder forms dynamically
    function generateShareholderForms() {
      const count = parseInt(document.getElementById('shareholderCount').value) || 0;
      const container = document.getElementById('shareholderFormsContainer');

      container.innerHTML = '';

      for (let i = 1; i <= count; i++) {
        const shareholderForm = document.createElement('div');
        shareholderForm.className = 'border border-gray-200 p-4 rounded-lg';
        shareholderForm.innerHTML = `
          <h4 class="text-md font-medium text-gray-900 mb-3">Shareholder ${i}</h4>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div class="sm:col-span-2">
              <label for="sHolderName${i}" class="custom-form-label">Full name *</label>
              <input type="text" name="sHolderName${i}" id="sHolderName${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderEmail${i}" class="custom-form-label">Email Address *</label>
              <input type="email" name="sHolderEmail${i}" id="sHolderEmail${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderPhone${i}" class="custom-form-label">Cell Phone *</label>
              <input type="tel" name="sHolderPhone${i}" id="sHolderPhone${i}" class="custom-input" required>
            </div>

            <div class="sm:col-span-2">
              <label for="sHolderAddress${i}" class="custom-form-label">Physical Address *</label>
              <textarea name="sHolderAddress${i}" id="sHolderAddress${i}" rows="2" class="custom-input" required></textarea>
            </div>

            <div class="sm:col-span-2">
              <label for="sHolderPostal${i}" class="custom-form-label">Postal Address *</label>
              <input type="text" name="sHolderPostal${i}" id="sHolderPostal${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderVillage${i}" class="custom-form-label">Village *</label>
              <input type="text" name="sHolderVillage${i}" id="sHolderVillage${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderTA${i}" class="custom-form-label">Traditional Authority *</label>
              <input type="text" name="sHolderTA${i}" id="sHolderTA${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderDistrict${i}" class="custom-form-label">District *</label>
              <input type="text" name="sHolderDistrict${i}" id="sHolderDistrict${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderDOB${i}" class="custom-form-label">Date of birth *</label>
              <input type="date" name="sHolderDOB${i}" id="sHolderDOB${i}" class="custom-input" required>
            </div>

            <div>
              <label for="sHolderNationality${i}" class="custom-form-label">Citizenship *</label>
              <select name="sHolderNationality${i}" id="sHolderNationality${i}" class="custom-input" required onchange="togglePassportFields(${i})">
                <option value="">Select Citizenship</option>
                <option value="malawian">Malawian</option>
                <option value="non-malawian">Non-Malawian</option>
              </select>
            </div>

            <div>
              <label for="sHolderCountry${i}" class="custom-form-label">Country *</label>
              <select name="sHolderCountry${i}" id="sHolderCountry${i}" class="custom-input" required>
                <option value="">Select Country</option>
                <option value="malawi">Malawi</option>
                <option value="zambia">Zambia</option>
                <option value="zimbabwe">Zimbabwe</option>
                <option value="mozambique">Mozambique</option>
                <option value="tanzania">Tanzania</option>
                <option value="south-africa">South Africa</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div id="passportSection${i}" style="display: none;">
              <label for="sHolderPassportNumber${i}" class="custom-form-label">Passport Number *</label>
              <input type="text" name="sHolderPassportNumber${i}" id="sHolderPassportNumber${i}" class="custom-input">
            </div>

            <div id="passportUploadSection${i}" style="display: none;">
              <label class="custom-form-label">Upload Passport *</label>
              <div class="file-upload-area" onclick="document.getElementById('sHolderPassport${i}').click()">
                <i class="ri-upload-cloud-2-line text-2xl text-gray-400 mb-1"></i>
                <p class="text-xs text-gray-600">Upload Passport</p>
              </div>
              <input type="file" id="sHolderPassport${i}" name="sHolderPassport${i}" accept=".pdf,.jpg,.jpeg,.png" class="hidden">
            </div>

            <div id="idSection${i}" style="display: none;">
              <label for="sHolderID${i}" class="custom-form-label">National Identity Number *</label>
              <input type="text" name="sHolderID${i}" id="sHolderID${i}" class="custom-input">
            </div>
          </div>
        `;
        container.appendChild(shareholderForm);
      }
    }

    // Toggle passport/ID fields based on nationality
    function togglePassportFields(shareholderIndex) {
      const nationality = document.getElementById(`sHolderNationality${shareholderIndex}`).value;
      const passportSection = document.getElementById(`passportSection${shareholderIndex}`);
      const passportUploadSection = document.getElementById(`passportUploadSection${shareholderIndex}`);
      const idSection = document.getElementById(`idSection${shareholderIndex}`);
      const passportNumber = document.getElementById(`sHolderPassportNumber${shareholderIndex}`);
      const passportUpload = document.getElementById(`sHolderPassport${shareholderIndex}`);
      const idNumber = document.getElementById(`sHolderID${shareholderIndex}`);

      if (nationality === 'non-malawian') {
        passportSection.style.display = 'block';
        passportUploadSection.style.display = 'block';
        idSection.style.display = 'none';
        passportNumber.required = true;
        passportUpload.required = true;
        idNumber.required = false;
      } else if (nationality === 'malawian') {
        passportSection.style.display = 'none';
        passportUploadSection.style.display = 'none';
        idSection.style.display = 'block';
        passportNumber.required = false;
        passportUpload.required = false;
        idNumber.required = true;
      } else {
        passportSection.style.display = 'none';
        passportUploadSection.style.display = 'none';
        idSection.style.display = 'none';
        passportNumber.required = false;
        passportUpload.required = false;
        idNumber.required = false;
      }
    }

    // Generate management forms dynamically
    function generateManagementForms() {
      const count = parseInt(document.getElementById('managementCount').value) || 0;
      const container = document.getElementById('managementFormsContainer');

      container.innerHTML = '';

      for (let i = 1; i <= count; i++) {
        const managementForm = document.createElement('div');
        managementForm.className = 'border border-gray-200 p-4 rounded-lg';
        managementForm.innerHTML = `
          <h4 class="text-md font-medium text-gray-900 mb-3">Management Employee ${i}</h4>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div class="sm:col-span-2">
              <label for="managementName${i}" class="custom-form-label">Full name *</label>
              <input type="text" name="managementName${i}" id="managementName${i}" class="custom-input" required>
            </div>

            <div>
              <label for="managementEmail${i}" class="custom-form-label">Email Address *</label>
              <input type="email" name="managementEmail${i}" id="managementEmail${i}" class="custom-input" required>
            </div>

            <div>
              <label for="managementPhone${i}" class="custom-form-label">Cell Phone *</label>
              <input type="tel" name="managementPhone${i}" id="managementPhone${i}" class="custom-input" required>
            </div>

            <div class="sm:col-span-2">
              <label for="managementDescription${i}" class="custom-form-label">Brief Description *</label>
              <textarea name="managementDescription${i}" id="managementDescription${i}" rows="3" class="custom-input" required placeholder="Describe the role and responsibilities"></textarea>
            </div>

            <div>
              <label for="managementExperience${i}" class="custom-form-label">Number of years of relevant experience *</label>
              <input type="number" name="managementExperience${i}" id="managementExperience${i}" class="custom-input" min="0" required>
            </div>

            <div>
              <label for="managementAgreementType${i}" class="custom-form-label">Type of agreement *</label>
              <select name="managementAgreementType${i}" id="managementAgreementType${i}" class="custom-input" required>
                <option value="">Select Agreement Type</option>
                <option value="management">Management Agreement</option>
                <option value="consultancy">Consultancy Agreement</option>
                <option value="network">Network Agreement</option>
                <option value="franchise">Franchise Agreement</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div class="sm:col-span-2">
              <label class="custom-form-label">Attach Agreements *</label>
              <div class="file-upload-area" onclick="document.getElementById('managementAgreement${i}').click()">
                <i class="ri-upload-cloud-2-line text-2xl text-gray-400 mb-1"></i>
                <p class="text-xs text-gray-600">Upload Agreement Document</p>
              </div>
              <input type="file" id="managementAgreement${i}" name="managementAgreement${i}" accept=".pdf,.doc,.docx" class="hidden" required>
            </div>

            <div class="sm:col-span-2">
              <label for="managementPolitical${i}" class="custom-form-label">Person has political affiliation? *</label>
              <select name="managementPolitical${i}" id="managementPolitical${i}" class="custom-input" required onchange="togglePoliticalDetails(${i})">
                <option value="">Select</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
            </div>

            <div id="politicalDetails${i}" class="sm:col-span-2" style="display: none;">
              <label for="managementPoliticalDetails${i}" class="custom-form-label">Specify whether office bearers / affiliation with organisation with political affiliation</label>
              <textarea name="managementPoliticalDetails${i}" id="managementPoliticalDetails${i}" rows="2" class="custom-input" placeholder="Provide details of political affiliations"></textarea>
            </div>
          </div>
        `;
        container.appendChild(managementForm);
      }
    }

    // Toggle political details based on political affiliation
    function togglePoliticalDetails(managementIndex) {
      const political = document.getElementById(`managementPolitical${managementIndex}`).value;
      const politicalDetails = document.getElementById(`politicalDetails${managementIndex}`);
      const politicalDetailsTextarea = document.getElementById(`managementPoliticalDetails${managementIndex}`);

      if (political === 'yes') {
        politicalDetails.style.display = 'block';
        politicalDetailsTextarea.required = true;
      } else {
        politicalDetails.style.display = 'none';
        politicalDetailsTextarea.required = false;
      }
    }

    // Add interested party form
    let interestedPartyCount = 0;
    function addInterestedParty() {
      interestedPartyCount++;
      const container = document.getElementById('interestedPartiesContainer');

      const partyForm = document.createElement('div');
      partyForm.className = 'border border-gray-200 p-4 rounded-lg relative';
      partyForm.innerHTML = `
        <button type="button" onclick="removeInterestedParty(${interestedPartyCount})" class="absolute top-2 right-2 text-red-500 hover:text-red-700">
          <i class="ri-close-line"></i>
        </button>
        <h4 class="text-md font-medium text-gray-900 mb-3">Interested Party ${interestedPartyCount}</h4>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label for="otherRole${interestedPartyCount}" class="custom-form-label">Role *</label>
            <select name="otherRole${interestedPartyCount}" id="otherRole${interestedPartyCount}" class="custom-input" required>
              <option value="">Select Role</option>
              <option value="legal-advisor">Legal Advisor</option>
              <option value="financial-advisor">Financial Advisor</option>
              <option value="sponsor">Sponsor</option>
              <option value="investor">Investor</option>
              <option value="research-consultant">Research Consultant</option>
              <option value="other">Other (specify)</option>
            </select>
          </div>

          <div>
            <label for="otherName${interestedPartyCount}" class="custom-form-label">Full Name *</label>
            <input type="text" name="otherName${interestedPartyCount}" id="otherName${interestedPartyCount}" class="custom-input" required>
          </div>

          <div class="sm:col-span-2">
            <label for="otherCompany${interestedPartyCount}" class="custom-form-label">Company Details *</label>
            <textarea name="otherCompany${interestedPartyCount}" id="otherCompany${interestedPartyCount}" rows="2" class="custom-input" required placeholder="Provide company name and details"></textarea>
          </div>

          <div>
            <label for="otherEmail${interestedPartyCount}" class="custom-form-label">Contact Email *</label>
            <input type="email" name="otherEmail${interestedPartyCount}" id="otherEmail${interestedPartyCount}" class="custom-input" required>
          </div>

          <div>
            <label for="otherCountry${interestedPartyCount}" class="custom-form-label">Contact Country *</label>
            <select name="otherCountry${interestedPartyCount}" id="otherCountry${interestedPartyCount}" class="custom-input" required>
              <option value="">Select Country</option>
              <option value="malawi">Malawi</option>
              <option value="zambia">Zambia</option>
              <option value="zimbabwe">Zimbabwe</option>
              <option value="mozambique">Mozambique</option>
              <option value="tanzania">Tanzania</option>
              <option value="south-africa">South Africa</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="sm:col-span-2">
            <label for="otherAddress${interestedPartyCount}" class="custom-form-label">Contact Address *</label>
            <textarea name="otherAddress${interestedPartyCount}" id="otherAddress${interestedPartyCount}" rows="2" class="custom-input" required></textarea>
          </div>
        </div>
      `;
      partyForm.id = `interestedParty${interestedPartyCount}`;
      container.appendChild(partyForm);
    }

    // Remove interested party form
    function removeInterestedParty(partyId) {
      const partyForm = document.getElementById(`interestedParty${partyId}`);
      if (partyForm) {
        partyForm.remove();
      }
    }

    // Update the validation for step 5 to include new required fields
    function validateCurrentStep() {
      const currentStepElement = document.getElementById(`step${currentStep}`);
      const requiredFields = currentStepElement.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          field.classList.add('border-red-500');
          isValid = false;
        } else {
          field.classList.remove('border-red-500');
        }
      });

      // Special validation for step 5 (checkboxes must be checked)
      if (currentStep === 5) {
        const requiredCheckboxes = ['agreeDeclaration', 'confirmAccuracy', 'confirmCompliance', 'confirmSubmission'];
        requiredCheckboxes.forEach(checkboxId => {
          const checkbox = document.getElementById(checkboxId);
          if (checkbox && !checkbox.checked) {
            checkbox.classList.add('border-red-500');
            isValid = false;
          } else if (checkbox) {
            checkbox.classList.remove('border-red-500');
          }
        });
      }

      if (!isValid) {
        alert('Please fill in all required fields before proceeding.');
      }

      return isValid;
    }
  </script>

</body>
</html>