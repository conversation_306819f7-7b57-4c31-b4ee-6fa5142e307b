<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Revenue Analytics Report - MACRA</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .confidential-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            text-align: center;
            color: #856404;
            font-weight: bold;
        }
        
        .revenue-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .revenue-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .revenue-card.total { border-left: 4px solid #28a745; }
        .revenue-card.collected { border-left: 4px solid #007bff; }
        .revenue-card.outstanding { border-left: 4px solid #ffc107; }
        .revenue-card.growth { border-left: 4px solid #17a2b8; }
        
        .revenue-amount {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .revenue-card.total .revenue-amount { color: #28a745; }
        .revenue-card.collected .revenue-amount { color: #007bff; }
        .revenue-card.outstanding .revenue-amount { color: #ffc107; }
        .revenue-card.growth .revenue-amount { color: #17a2b8; }
        
        .revenue-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .amount {
            font-weight: bold;
            text-align: right;
        }
        
        .amount.positive { color: #28a745; }
        .amount.negative { color: #dc3545; }
        
        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .growth-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .growth-positive { background: #d4edda; color: #155724; }
        .growth-negative { background: #f8d7da; color: #721c24; }
        .growth-neutral { background: #e2e3e5; color: #383d41; }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Financial Revenue Analytics Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> December 15, 2024</div>
                <div><strong>Period:</strong> Q4 2024 (Oct-Dec)</div>
                <div><strong>Generated By:</strong> Finance Department</div>
            </div>
        </div>

        <!-- Confidential Notice -->
        <div class="confidential-notice">
            ⚠️ CONFIDENTIAL - FOR AUTHORIZED MACRA STAFF ONLY
        </div>

        <!-- Revenue Summary Cards -->
        <div class="revenue-cards">
            <div class="revenue-card total">
                <div class="revenue-amount">MWK 2.8B</div>
                <div class="revenue-label">Total Revenue</div>
            </div>
            <div class="revenue-card collected">
                <div class="revenue-amount">MWK 2.3B</div>
                <div class="revenue-label">Collected</div>
            </div>
            <div class="revenue-card outstanding">
                <div class="revenue-amount">MWK 485M</div>
                <div class="revenue-label">Outstanding</div>
            </div>
            <div class="revenue-card growth">
                <div class="revenue-amount">+12.5%</div>
                <div class="revenue-label">YoY Growth</div>
            </div>
        </div>

        <!-- Revenue by License Type -->
        <div class="section">
            <h2 class="section-title">Revenue by License Type</h2>
            <div class="chart-container">
                <canvas id="revenueByTypeChart" style="width: 100%; height: 400px;"></canvas>
            </div>
        </div>

        <!-- Monthly Revenue Trends -->
        <div class="section">
            <h2 class="section-title">Monthly Revenue Trends</h2>
            <div class="chart-container">
                <canvas id="monthlyRevenueChart" style="width: 100%; height: 400px;"></canvas>
            </div>
        </div>

        <!-- Outstanding Payments Analysis -->
        <div class="section">
            <h2 class="section-title">Outstanding Payments by Age</h2>
            <div class="chart-container">
                <canvas id="outstandingPaymentsChart" style="width: 100%; height: 350px;"></canvas>
            </div>
        </div>

        <!-- Revenue Trend Visualization -->
        <div class="section">
            <h2 class="section-title">Revenue Trend Visualization</h2>
            <div class="chart-container">
                <canvas id="quarterlyRevenueChart" style="width: 100%; height: 300px;"></canvas>
            </div>
        </div>

        <!-- Key Performance Indicators -->
        <div class="section">
            <h2 class="section-title">Key Financial KPIs</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>KPI</th>
                            <th>Current Quarter</th>
                            <th>Previous Quarter</th>
                            <th>Target</th>
                            <th>Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Collection Rate</td>
                            <td>82.7%</td>
                            <td>79.3%</td>
                            <td>85.0%</td>
                            <td><span class="growth-indicator growth-positive">Improving</span></td>
                        </tr>
                        <tr>
                            <td>Average Payment Time</td>
                            <td>28 days</td>
                            <td>32 days</td>
                            <td>21 days</td>
                            <td><span class="growth-indicator growth-positive">Improving</span></td>
                        </tr>
                        <tr>
                            <td>Revenue per Application</td>
                            <td>MWK 23.3M</td>
                            <td>MWK 21.8M</td>
                            <td>MWK 25.0M</td>
                            <td><span class="growth-indicator growth-neutral">Below Target</span></td>
                        </tr>
                        <tr>
                            <td>Bad Debt Ratio</td>
                            <td>2.1%</td>
                            <td>2.8%</td>
                            <td>< 2.0%</td>
                            <td><span class="growth-indicator growth-positive">Improving</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This financial report contains confidential information and is intended for authorized MACRA staff only.</p>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // 1. Revenue by License Type (Stacked Bar Chart)
        const revenueByTypeChart = document.getElementById('revenueByTypeChart').getContext('2d');
        new Chart(revenueByTypeChart, {
            type: 'bar',
            data: {
                labels: ['Telecommunications', 'Broadcasting', 'Postal Services', 'Standards'],
                datasets: [
                    {
                        label: 'Collected',
                        data: [
                            1250 * 0.88, // Telecommunications: 88.0% of 1.25B MWK
                            850 * 0.918, // Broadcasting: 91.8% of 850M MWK
                            420 * 0.917, // Postal Services: 91.7% of 420M MWK
                            280 * 0.893  // Standards: 89.3% of 280M MWK
                        ],
                        backgroundColor: '#007bff',
                        borderColor: '#0056b3',
                        borderWidth: 1
                    },
                    {
                        label: 'Outstanding',
                        data: [
                            1250 * (1 - 0.88), // Telecommunications: remaining
                            850 * (1 - 0.918), // Broadcasting: remaining
                            420 * (1 - 0.917), // Postal Services: remaining
                            280 * (1 - 0.893)  // Standards: remaining
                        ],
                        backgroundColor: '#ffc107',
                        borderColor: '#d39e00',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' },
                    title: { display: true, text: 'Revenue Distribution by License Type (MWK Millions)', font: { size: 16 } }
                },
                scales: {
                    x: { stacked: true },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        title: { display: true, text: 'Revenue (MWK Millions)' }
                    }
                }
            }
        });

        // 2. Monthly Revenue Trends (Dual-Axis Chart)
        const monthlyRevenueChart = document.getElementById('monthlyRevenueChart').getContext('2d');
        new Chart(monthlyRevenueChart, {
            type: 'bar',
            data: {
                labels: ['October', 'November', 'December'],
                datasets: [
                    {
                        label: 'Generated Revenue',
                        data: [950, 920, 930], // Generated: Oct 950M, Nov 920M, Dec 930M
                        backgroundColor: '#dc3545',
                        borderColor: '#a71d2a',
                        borderWidth: 1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Collected Revenue',
                        data: [820, 780, 700], // Collected: Oct 820M, Nov 780M, Dec 700M
                        type: 'line',
                        fill: false,
                        borderColor: '#28a745',
                        backgroundColor: '#28a745',
                        tension: 0.1,
                        yAxisID: 'y'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' },
                    title: { display: true, text: 'Monthly Revenue & Collection Trends (MWK Millions)', font: { size: 16 } }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Revenue (MWK Millions)' }
                    }
                }
            }
        });

        // 3. Outstanding Payments by Age (Donut Chart)
        const outstandingPaymentsChart = document.getElementById('outstandingPaymentsChart').getContext('2d');
        new Chart(outstandingPaymentsChart, {
            type: 'doughnut',
            data: {
                labels: ['0-30 days', '31-60 days', '61-90 days', '90+ days'],
                datasets: [{
                    data: [37.1, 29.9, 19.6, 13.4], // Percentages
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderColor: ['#1d7b33', '#d39e00', '#d95f02', '#a71d2a'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' },
                    title: { display: true, text: 'Outstanding Payments by Age (MWK 485M Total)', font: { size: 16 } }
                }
            }
        });

        // 4. Quarterly Revenue Comparison (Bar Chart)
        const quarterlyRevenueChart = document.getElementById('quarterlyRevenueChart').getContext('2d');
        new Chart(quarterlyRevenueChart, {
            type: 'bar',
            data: {
                labels: ['Q4 2023', 'Q4 2024'],
                datasets: [{
                    label: 'Revenue',
                    data: [2500, 2800], // Q4 2023: 2.5B, Q4 2024: 2.8B
                    backgroundColor: ['#6c757d', '#dc3545'],
                    borderColor: ['#5a6268', '#a71d2a'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    title: { display: true, text: 'Quarterly Revenue Comparison (MWK Millions)', font: { size: 16 } }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Revenue (MWK Millions)' }
                    }
                }
            }
        });
    });
    </script>
</body>
</html>