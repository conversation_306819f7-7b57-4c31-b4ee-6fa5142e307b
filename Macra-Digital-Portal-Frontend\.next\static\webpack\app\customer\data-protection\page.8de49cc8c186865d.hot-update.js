"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/data-protection/page",{

/***/ "(app-pages-browser)/./src/app/customer/data-protection/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/customer/data-protection/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/customer/CustomerLayout */ \"(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\");\n/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loader */ \"(app-pages-browser)/./src/components/Loader.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/customer/DataBreachModal */ \"(app-pages-browser)/./src/components/customer/DataBreachModal.tsx\");\n/* harmony import */ var _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/customer/ComplaintStatusBar */ \"(app-pages-browser)/./src/components/customer/ComplaintStatusBar.tsx\");\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst DataProtectionPage = ()=>{\n    _s();\n    const { isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [complaints, setComplaints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [showDataBreachModal, setShowDataBreachModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect to customer login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            if (!authLoading && !isAuthenticated) {\n                router.push('/customer/auth/login');\n            }\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Fetch data function\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataProtectionPage.useCallback[fetchData]\": async ()=>{\n            if (!isAuthenticated) {\n                console.log('❌ User not authenticated, skipping data fetch');\n                return;\n            }\n            console.log('✅ User authenticated, fetching data...');\n            try {\n                setIsLoading(true);\n                setError('');\n                // Fetch only data breach reports (this is the Data Breach page)\n                const dataBreachResponse = await _services_data_breach__WEBPACK_IMPORTED_MODULE_8__.dataBreachService.getReports({\n                    limit: 100\n                });\n                console.log('🔍 Data Breach Response:', dataBreachResponse);\n                console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);\n                console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);\n                // Ensure data is an array (services return data directly)\n                const dataBreachData = Array.isArray(dataBreachResponse.data) ? dataBreachResponse.data : [];\n                console.log('🔍 Data Breach Data Array:', dataBreachData);\n                // Transform only data breach reports\n                const combinedComplaints = dataBreachData.map({\n                    \"DataProtectionPage.useCallback[fetchData].combinedComplaints\": (report)=>{\n                        var _report_assignee, _report_assignee1;\n                        return {\n                            id: report.report_id,\n                            title: report.title,\n                            description: report.description,\n                            category: report.category,\n                            type: 'data_breach',\n                            priority: report.priority,\n                            status: report.status,\n                            submittedAt: report.created_at,\n                            updatedAt: report.updated_at,\n                            assignedTo: ((_report_assignee = report.assignee) === null || _report_assignee === void 0 ? void 0 : _report_assignee.first_name) && ((_report_assignee1 = report.assignee) === null || _report_assignee1 === void 0 ? void 0 : _report_assignee1.last_name) ? \"\".concat(report.assignee.first_name, \" \").concat(report.assignee.last_name) : undefined,\n                            resolution: report.resolution,\n                            number: report.report_number\n                        };\n                    }\n                }[\"DataProtectionPage.useCallback[fetchData].combinedComplaints\"]);\n                // Sort by creation date (newest first)\n                combinedComplaints.sort({\n                    \"DataProtectionPage.useCallback[fetchData]\": (a, b)=>new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()\n                }[\"DataProtectionPage.useCallback[fetchData]\"]);\n                setComplaints(combinedComplaints);\n            } catch (err) {\n                var _axiosError_response, _axiosError_response1;\n                console.error('Error fetching complaints:', err);\n                const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n                const isAxiosError = err && typeof err === 'object' && 'response' in err;\n                const axiosError = isAxiosError ? err : null;\n                const status = axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status;\n                console.error('Error details:', {\n                    message: errorMessage,\n                    response: axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.data,\n                    status: status\n                });\n                if (status === 401) {\n                    setError('Authentication required. Please log in again.');\n                } else if (status === 404) {\n                    setError('API endpoints not found. Please check if the backend is running.');\n                } else {\n                    setError(\"Failed to load complaints: \".concat(errorMessage));\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"DataProtectionPage.useCallback[fetchData]\"], [\n        isAuthenticated\n    ]);\n    // Fetch data on mount and when authentication changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        fetchData\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800';\n            case 'resolved':\n                return 'bg-green-100 text-green-800';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'low':\n                return 'bg-gray-100 text-gray-800';\n            case 'medium':\n                return 'bg-blue-100 text-blue-800';\n            case 'high':\n                return 'bg-orange-100 text-orange-800';\n            case 'urgent':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // All complaints are now data breach reports only\n    const dataBreachComplaints = complaints; // All complaints are data breach reports\n    // Calculate data breach statistics\n    const totalComplaints = complaints.length;\n    const pendingComplaints = complaints.filter((c)=>c.status === 'submitted' || c.status === 'under_review').length;\n    const investigatingComplaints = complaints.filter((c)=>c.status === 'investigating').length;\n    const resolvedComplaints = complaints.filter((c)=>c.status === 'resolved' || c.status === 'closed').length;\n    if (authLoading || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    message: \"Loading Data Breach...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-2 text-sm underline hover:no-underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                                    children: \"Data Breach\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Report and track data breach incidents and privacy violations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setShowDataBreachModal(true),\n                            className: \"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-shield-keyhole-line mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Report Data Breach\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200 dark:border-gray-700 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            {\n                                key: 'overview',\n                                label: 'Overview',\n                                icon: 'ri-dashboard-line'\n                            },\n                            {\n                                key: 'track',\n                                label: 'Track Complaints',\n                                icon: 'ri-search-eye-line',\n                                count: complaints.length\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab(tab.key),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center \".concat(activeTab === tab.key ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"\".concat(tab.icon, \" mr-2\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tab.label,\n                                    tab.count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\",\n                                        children: tab.count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, tab.key, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined),\n                activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-file-list-3-line text-2xl text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Total Complaints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: totalComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-time-line text-2xl text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: pendingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-search-eye-line text-2xl text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Investigating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: investigatingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-check-double-line text-2xl text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Resolved\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: resolvedComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-shield-keyhole-line text-3xl text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                    children: \"Data Breach Reporting\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"Report unauthorized access, misuse, or breach of your personal data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"What to Report:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Unauthorized data access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Data misuse or sharing\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Privacy violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Identity theft concerns\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, undefined),\n                activeTab === 'track' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: complaints.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-file-search-line text-4xl text-gray-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                children: \"No complaints found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 dark:text-gray-400 mb-4\",\n                                children: \"You haven't submitted any complaints yet.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\",\n                                children: \"Submit Your First Complaint\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: complaints.map((complaint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3\",\n                                                            children: complaint.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(complaint.status)),\n                                                            children: complaint.status.replace('_', ' ').toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(complaint.priority)),\n                                                            children: complaint.priority.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'),\n                                                            children: complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        complaint.id,\n                                                        \" | Category: \",\n                                                        complaint.category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300 mb-3\",\n                                                    children: complaint.description.length > 150 ? \"\".concat(complaint.description.substring(0, 150), \"...\") : complaint.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        currentStage: (0,_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.getStageIndexFromStatus)(complaint.status),\n                                                        stages: complaint.type === 'consumer_affairs' ? _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.COMPLAINT_STAGES.CONSUMER_AFFAIRS : _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.COMPLAINT_STAGES.DATA_BREACH,\n                                                        status: complaint.status,\n                                                        size: \"sm\",\n                                                        variant: \"horizontal\",\n                                                        showPercentage: false,\n                                                        showStageNames: true,\n                                                        className: \"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Submitted: \",\n                                                                formatDate(complaint.submittedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mx-2\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated: \",\n                                                                formatDate(complaint.updatedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        complaint.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-2\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Assigned to: \",\n                                                                        complaint.assignedTo\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: \"View Details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, complaint.id, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 19\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-900 dark:text-red-100 mb-4\",\n                            children: \"Need Help with Data Breach Reporting?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Contact Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-phone-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"+265 1 770 100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-mail-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-time-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mon-Fri, 8:00 AM - 5:00 PM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Data Breach Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Unauthorized Access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Misuse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Privacy Violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Identity Theft\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Sharing Issues\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, undefined),\n                showDataBreachModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onClose: ()=>setShowDataBreachModal(false),\n                    onSubmit: (data)=>{\n                        console.log('Data breach report submitted:', data);\n                        setShowDataBreachModal(false);\n                        // Refresh complaints list without full page reload\n                        fetchData();\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataProtectionPage, \"WcRhFHta3QR3R/w2BRQUB/tnuEs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DataProtectionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataProtectionPage);\nvar _c;\n$RefreshReg$(_c, \"DataProtectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY3VzdG9tZXIvZGF0YS1wcm90ZWN0aW9uL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0U7QUFDcEI7QUFDc0I7QUFDekI7QUFDUTtBQUNtQjtBQUNxRDtBQUM1QztBQWlCN0UsTUFBTWEscUJBQXFCOztJQUN6QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsU0FBU0MsV0FBVyxFQUFFLEdBQUdULDhEQUFPQTtJQUN6RCxNQUFNVSxTQUFTYiwwREFBU0E7SUFFeEIsTUFBTSxDQUFDYyxZQUFZQyxjQUFjLEdBQUdsQiwrQ0FBUUEsQ0FBc0IsRUFBRTtJQUNwRSxNQUFNLENBQUNtQixXQUFXQyxhQUFhLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNxQixPQUFPQyxTQUFTLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUN1QixXQUFXQyxhQUFhLEdBQUd4QiwrQ0FBUUEsQ0FBdUI7SUFDakUsTUFBTSxDQUFDeUIscUJBQXFCQyx1QkFBdUIsR0FBRzFCLCtDQUFRQSxDQUFDO0lBRS9ELGtEQUFrRDtJQUNsREMsZ0RBQVNBO3dDQUFDO1lBQ1IsSUFBSSxDQUFDYyxlQUFlLENBQUNGLGlCQUFpQjtnQkFDcENHLE9BQU9XLElBQUksQ0FBQztZQUNkO1FBQ0Y7dUNBQUc7UUFBQ2Q7UUFBaUJFO1FBQWFDO0tBQU87SUFFekMsc0JBQXNCO0lBQ3RCLE1BQU1ZLFlBQVkxQixrREFBV0E7cURBQUM7WUFDNUIsSUFBSSxDQUFDVyxpQkFBaUI7Z0JBQ3BCZ0IsUUFBUUMsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQUQsUUFBUUMsR0FBRyxDQUFDO1lBRVosSUFBSTtnQkFDRlYsYUFBYTtnQkFDYkUsU0FBUztnQkFFVCxnRUFBZ0U7Z0JBQ2hFLE1BQU1TLHFCQUFxQixNQUFNcEIsb0VBQWlCQSxDQUFDcUIsVUFBVSxDQUFDO29CQUFFQyxPQUFPO2dCQUFJO2dCQUV6RUosUUFBUUMsR0FBRyxDQUFDLDRCQUE0QkM7Z0JBQ3hDRixRQUFRQyxHQUFHLENBQUMsc0NBQXNDLE9BQU9DLG1CQUFtQkcsSUFBSTtnQkFDaEZMLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNDLG1CQUFtQkcsSUFBSTtnQkFFcEUsMERBQTBEO2dCQUMxRCxNQUFNQyxpQkFBaUJDLE1BQU1DLE9BQU8sQ0FBQ04sbUJBQW1CRyxJQUFJLElBQ3hESCxtQkFBbUJHLElBQUksR0FDdkIsRUFBRTtnQkFFTkwsUUFBUUMsR0FBRyxDQUFDLDhCQUE4Qks7Z0JBRTFDLHFDQUFxQztnQkFDckMsTUFBTUcscUJBQTBDSCxlQUFlSSxHQUFHO29GQUFDLENBQUNDOzRCQVV0REEsa0JBQStCQTsrQkFWcUQ7NEJBQ2hHQyxJQUFJRCxPQUFPRSxTQUFTOzRCQUNwQkMsT0FBT0gsT0FBT0csS0FBSzs0QkFDbkJDLGFBQWFKLE9BQU9JLFdBQVc7NEJBQy9CQyxVQUFVTCxPQUFPSyxRQUFROzRCQUN6QkMsTUFBTTs0QkFDTkMsVUFBVVAsT0FBT08sUUFBUTs0QkFDekJDLFFBQVFSLE9BQU9RLE1BQU07NEJBQ3JCQyxhQUFhVCxPQUFPVSxVQUFVOzRCQUM5QkMsV0FBV1gsT0FBT1ksVUFBVTs0QkFDNUJDLFlBQVliLEVBQUFBLG1CQUFBQSxPQUFPYyxRQUFRLGNBQWZkLHVDQUFBQSxpQkFBaUJlLFVBQVUsT0FBSWYsb0JBQUFBLE9BQU9jLFFBQVEsY0FBZmQsd0NBQUFBLGtCQUFpQmdCLFNBQVMsSUFDakUsR0FBaUNoQixPQUE5QkEsT0FBT2MsUUFBUSxDQUFDQyxVQUFVLEVBQUMsS0FBNkIsT0FBMUJmLE9BQU9jLFFBQVEsQ0FBQ0UsU0FBUyxJQUMxREM7NEJBQ0pDLFlBQVlsQixPQUFPa0IsVUFBVTs0QkFDN0JDLFFBQVFuQixPQUFPb0IsYUFBYTt3QkFDOUI7OztnQkFFQSx1Q0FBdUM7Z0JBQ3ZDdEIsbUJBQW1CdUIsSUFBSTtpRUFBQyxDQUFDQyxHQUFHQyxJQUFNLElBQUlDLEtBQUtELEVBQUVkLFdBQVcsRUFBRWdCLE9BQU8sS0FBSyxJQUFJRCxLQUFLRixFQUFFYixXQUFXLEVBQUVnQixPQUFPOztnQkFFckcvQyxjQUFjb0I7WUFFaEIsRUFBRSxPQUFPNEIsS0FBYztvQkFNTkMsc0JBSUhBO2dCQVRadEMsUUFBUVIsS0FBSyxDQUFDLDhCQUE4QjZDO2dCQUU1QyxNQUFNRSxlQUFlRixlQUFlRyxRQUFRSCxJQUFJSSxPQUFPLEdBQUc7Z0JBQzFELE1BQU1DLGVBQWVMLE9BQU8sT0FBT0EsUUFBUSxZQUFZLGNBQWNBO2dCQUNyRSxNQUFNQyxhQUFhSSxlQUFlTCxNQUE0RDtnQkFDOUYsTUFBTWxCLFNBQVNtQix1QkFBQUEsa0NBQUFBLHVCQUFBQSxXQUFZSyxRQUFRLGNBQXBCTCwyQ0FBQUEscUJBQXNCbkIsTUFBTTtnQkFFM0NuQixRQUFRUixLQUFLLENBQUMsa0JBQWtCO29CQUM5QmlELFNBQVNGO29CQUNUSSxRQUFRLEVBQUVMLHVCQUFBQSxrQ0FBQUEsd0JBQUFBLFdBQVlLLFFBQVEsY0FBcEJMLDRDQUFBQSxzQkFBc0JqQyxJQUFJO29CQUNwQ2MsUUFBUUE7Z0JBQ1Y7Z0JBRUEsSUFBSUEsV0FBVyxLQUFLO29CQUNsQjFCLFNBQVM7Z0JBQ1gsT0FBTyxJQUFJMEIsV0FBVyxLQUFLO29CQUN6QjFCLFNBQVM7Z0JBQ1gsT0FBTztvQkFDTEEsU0FBUyw4QkFBMkMsT0FBYjhDO2dCQUN6QztZQUNGLFNBQVU7Z0JBQ1JoRCxhQUFhO1lBQ2Y7UUFDRjtvREFBRztRQUFDUDtLQUFnQjtJQUV0QixzREFBc0Q7SUFDdERaLGdEQUFTQTt3Q0FBQztZQUNSMkI7UUFDRjt1Q0FBRztRQUFDZjtRQUFpQmU7S0FBVTtJQUUvQixNQUFNNkMsaUJBQWlCLENBQUN6QjtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFnQixPQUFPO1lBQzVCLEtBQUs7Z0JBQWlCLE9BQU87WUFDN0IsS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNMEIsbUJBQW1CLENBQUMzQjtRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQU8sT0FBTztZQUNuQixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNNEIsYUFBYSxDQUFDQztRQUNsQixPQUFPLElBQUlaLEtBQUtZLFlBQVlDLGtCQUFrQixDQUFDLFNBQVM7WUFDdERDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTUMsdUJBQXVCbEUsWUFBWSx5Q0FBeUM7SUFFbEYsbUNBQW1DO0lBQ25DLE1BQU1tRSxrQkFBa0JuRSxXQUFXb0UsTUFBTTtJQUN6QyxNQUFNQyxvQkFBb0JyRSxXQUFXc0UsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFeEMsTUFBTSxLQUFLLGVBQWV3QyxFQUFFeEMsTUFBTSxLQUFLLGdCQUFnQnFDLE1BQU07SUFDaEgsTUFBTUksMEJBQTBCeEUsV0FBV3NFLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXhDLE1BQU0sS0FBSyxpQkFBaUJxQyxNQUFNO0lBQzNGLE1BQU1LLHFCQUFxQnpFLFdBQVdzRSxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUV4QyxNQUFNLEtBQUssY0FBY3dDLEVBQUV4QyxNQUFNLEtBQUssVUFBVXFDLE1BQU07SUFFMUcsSUFBSXRFLGVBQWVJLFdBQVc7UUFDNUIscUJBQ0UsOERBQUNmLDJFQUFjQTtzQkFDYiw0RUFBQ3VGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDdkYsMERBQU1BO29CQUFDaUUsU0FBUTs7Ozs7Ozs7Ozs7Ozs7OztJQUl4QjtJQUVBLElBQUlqRCxPQUFPO1FBQ1QscUJBQ0UsOERBQUNqQiwyRUFBY0E7c0JBQ2IsNEVBQUN1RjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO2tDQUFHeEU7Ozs7OztrQ0FDSiw4REFBQ3lFO3dCQUNDaEQsTUFBSzt3QkFDTGlELFNBQVMsSUFBTUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO3dCQUNyQ04sV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNVDtJQUVBLHFCQUNFLDhEQUFDeEYsMkVBQWNBO2tCQUNiLDRFQUFDdUY7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDUTtvQ0FBR1AsV0FBVTs4Q0FBMkQ7Ozs7Ozs4Q0FHekUsOERBQUNDO29DQUFFRCxXQUFVOzhDQUFtQzs7Ozs7Ozs7Ozs7O3NDQUlsRCw4REFBQ0U7NEJBQ0NoRCxNQUFLOzRCQUNMaUQsU0FBUyxJQUFNckUsdUJBQXVCOzRCQUN0Q2tFLFdBQVU7OzhDQUVWLDhEQUFDUTtvQ0FBRVIsV0FBVTs7Ozs7O2dDQUFrQzs7Ozs7Ozs7Ozs7Ozs4QkFNbkQsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDUzt3QkFBSVQsV0FBVTtrQ0FDWjs0QkFDQztnQ0FBRVUsS0FBSztnQ0FBWUMsT0FBTztnQ0FBWUMsTUFBTTs0QkFBb0I7NEJBQ2hFO2dDQUFFRixLQUFLO2dDQUFTQyxPQUFPO2dDQUFvQkMsTUFBTTtnQ0FBc0JDLE9BQU94RixXQUFXb0UsTUFBTTs0QkFBQzt5QkFDakcsQ0FBQzlDLEdBQUcsQ0FBQyxDQUFDbUUsb0JBQ0wsOERBQUNaO2dDQUNDaEQsTUFBSztnQ0FFTGlELFNBQVMsSUFBTXZFLGFBQWFrRixJQUFJSixHQUFHO2dDQUNuQ1YsV0FBVyxnRkFJVixPQUhDckUsY0FBY21GLElBQUlKLEdBQUcsR0FDakIsZ0NBQ0E7O2tEQUdOLDhEQUFDRjt3Q0FBRVIsV0FBVyxHQUFZLE9BQVRjLElBQUlGLElBQUksRUFBQzs7Ozs7O29DQUN6QkUsSUFBSUgsS0FBSztvQ0FDVEcsSUFBSUQsS0FBSyxLQUFLaEQsMkJBQ2IsOERBQUNrRDt3Q0FBS2YsV0FBVTtrREFDYmMsSUFBSUQsS0FBSzs7Ozs7OzsrQkFaVEMsSUFBSUosR0FBRzs7Ozs7Ozs7Ozs7Ozs7O2dCQXFCbkIvRSxjQUFjLDRCQUNiLDhEQUFDb0U7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBRVIsV0FBVTs7Ozs7Ozs7Ozs7MERBRWYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXVEOzs7Ozs7a0VBQ3BFLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBMkRSOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLOUUsOERBQUNPO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBRVIsV0FBVTs7Ozs7Ozs7Ozs7MERBRWYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXVEOzs7Ozs7a0VBQ3BFLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBMkROOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLOUUsOERBQUNLO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBRVIsV0FBVTs7Ozs7Ozs7Ozs7MERBRWYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXVEOzs7Ozs7a0VBQ3BFLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBMkRIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLOUUsOERBQUNFO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBRVIsV0FBVTs7Ozs7Ozs7Ozs7MERBRWYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXVEOzs7Ozs7a0VBQ3BFLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBMkRGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPaEYsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ1E7Z0RBQUVSLFdBQVU7Ozs7Ozs7Ozs7O3NEQUVmLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNnQjtvREFBR2hCLFdBQVU7OERBQXVEOzs7Ozs7OERBQ3JFLDhEQUFDQztvREFBRUQsV0FBVTs4REFBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLNUQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2lCOzRDQUFHakIsV0FBVTtzREFBK0M7Ozs7OztzREFDN0QsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0M7OERBQUU7Ozs7Ozs4REFDSCw4REFBQ0E7OERBQUU7Ozs7Ozs4REFDSCw4REFBQ0E7OERBQUU7Ozs7Ozs4REFDSCw4REFBQ0E7OERBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFRWnRFLGNBQWMseUJBQ2IsOERBQUNvRTs4QkFDRTFFLFdBQVdvRSxNQUFNLEtBQUssa0JBQ3JCLDhEQUFDTTt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFFUixXQUFVOzs7Ozs7MENBQ2IsOERBQUNnQjtnQ0FBR2hCLFdBQVU7MENBQTREOzs7Ozs7MENBQzFFLDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBd0M7Ozs7OzswQ0FHckQsOERBQUNFO2dDQUNDaEQsTUFBSztnQ0FDTGlELFNBQVMsSUFBTXZFLGFBQWE7Z0NBQzVCb0UsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7a0RBS0gsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaM0UsV0FBV3NCLEdBQUcsQ0FBQyxDQUFDdUUsMEJBQ2YsOERBQUNuQjtnQ0FBdUJDLFdBQVU7MENBQ2hDLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDZ0I7NERBQUdoQixXQUFVO3NFQUNYa0IsVUFBVW5FLEtBQUs7Ozs7OztzRUFFbEIsOERBQUNnRTs0REFBS2YsV0FBVyw4Q0FBK0UsT0FBakNuQixlQUFlcUMsVUFBVTlELE1BQU07c0VBQzNGOEQsVUFBVTlELE1BQU0sQ0FBQytELE9BQU8sQ0FBQyxLQUFLLEtBQUtDLFdBQVc7Ozs7OztzRUFFakQsOERBQUNMOzREQUFLZixXQUFXLG1EQUF3RixPQUFyQ2xCLGlCQUFpQm9DLFVBQVUvRCxRQUFRO3NFQUNwRytELFVBQVUvRCxRQUFRLENBQUNpRSxXQUFXOzs7Ozs7c0VBRWpDLDhEQUFDTDs0REFBS2YsV0FBVyxtREFFaEIsT0FEQ2tCLFVBQVVoRSxJQUFJLEtBQUsscUJBQXFCLDhCQUE4QjtzRUFFckVnRSxVQUFVaEUsSUFBSSxLQUFLLHFCQUFxQixxQkFBcUI7Ozs7Ozs7Ozs7Ozs4REFHbEUsOERBQUMrQztvREFBRUQsV0FBVTs7d0RBQWdEO3dEQUN0RGtCLFVBQVVyRSxFQUFFO3dEQUFDO3dEQUFjcUUsVUFBVWpFLFFBQVE7Ozs7Ozs7OERBRXBELDhEQUFDZ0Q7b0RBQUVELFdBQVU7OERBQ1ZrQixVQUFVbEUsV0FBVyxDQUFDeUMsTUFBTSxHQUFHLE1BQzVCLEdBQTJDLE9BQXhDeUIsVUFBVWxFLFdBQVcsQ0FBQ3FFLFNBQVMsQ0FBQyxHQUFHLE1BQUssU0FDM0NILFVBQVVsRSxXQUFXOzs7Ozs7OERBSTNCLDhEQUFDK0M7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNwRiwrRUFBa0JBO3dEQUNqQjBHLGNBQWN4RyxnR0FBdUJBLENBQUNvRyxVQUFVOUQsTUFBTTt3REFDdERtRSxRQUFRTCxVQUFVaEUsSUFBSSxLQUFLLHFCQUFxQnJDLHFGQUFnQkEsQ0FBQzJHLGdCQUFnQixHQUFHM0cscUZBQWdCQSxDQUFDNEcsV0FBVzt3REFDaEhyRSxRQUFROEQsVUFBVTlELE1BQU07d0RBQ3hCc0UsTUFBSzt3REFDTEMsU0FBUTt3REFDUkMsZ0JBQWdCO3dEQUNoQkMsZ0JBQWdCO3dEQUNoQjdCLFdBQVU7Ozs7Ozs7Ozs7OzhEQUlkLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNlOztnRUFBSztnRUFBWWhDLFdBQVdtQyxVQUFVN0QsV0FBVzs7Ozs7OztzRUFDbEQsOERBQUMwRDs0REFBS2YsV0FBVTtzRUFBTzs7Ozs7O3NFQUN2Qiw4REFBQ2U7O2dFQUFLO2dFQUFVaEMsV0FBV21DLFVBQVUzRCxTQUFTOzs7Ozs7O3dEQUM3QzJELFVBQVV6RCxVQUFVLGtCQUNuQjs7OEVBQ0UsOERBQUNzRDtvRUFBS2YsV0FBVTs4RUFBTzs7Ozs7OzhFQUN2Qiw4REFBQ2U7O3dFQUFLO3dFQUFjRyxVQUFVekQsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUtoRCw4REFBQ3lDOzRDQUNDaEQsTUFBSzs0Q0FDTDhDLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7OzsrQkF6REtrQixVQUFVckUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzhCQXFFaEMsOERBQUNrRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNnQjs0QkFBR2hCLFdBQVU7c0NBQTBEOzs7Ozs7c0NBR3hFLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ2tCOzRDQUFHakIsV0FBVTtzREFBMEQ7Ozs7OztzREFHeEUsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDUTs0REFBRVIsV0FBVTs7Ozs7O3NFQUNiLDhEQUFDZTtzRUFBSzs7Ozs7Ozs7Ozs7OzhEQUVSLDhEQUFDaEI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDUTs0REFBRVIsV0FBVTs7Ozs7O3NFQUNiLDhEQUFDZTtzRUFBSzs7Ozs7Ozs7Ozs7OzhEQUVSLDhEQUFDaEI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDUTs0REFBRVIsV0FBVTs7Ozs7O3NFQUNiLDhEQUFDZTtzRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlaLDhEQUFDaEI7O3NEQUNDLDhEQUFDa0I7NENBQUdqQixXQUFVO3NEQUEwRDs7Ozs7O3NEQUd4RSw4REFBQzhCOzRDQUFHOUIsV0FBVTs7OERBQ1osOERBQUMrQjs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU9YbEcscUNBQ0MsOERBQUNsQiw0RUFBZUE7b0JBQ2RxSCxTQUFTLElBQU1sRyx1QkFBdUI7b0JBQ3RDbUcsVUFBVSxDQUFDM0Y7d0JBQ1RMLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNJO3dCQUM3Q1IsdUJBQXVCO3dCQUN2QixtREFBbUQ7d0JBQ25ERTtvQkFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWjtHQXpiTWhCOztRQUM4Q04sMERBQU9BO1FBQzFDSCxzREFBU0E7OztLQUZwQlM7QUEyYk4saUVBQWVBLGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGFwcFxcY3VzdG9tZXJcXGRhdGEtcHJvdGVjdGlvblxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgQ3VzdG9tZXJMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL2N1c3RvbWVyL0N1c3RvbWVyTGF5b3V0JztcclxuaW1wb3J0IExvYWRlciBmcm9tICdAL2NvbXBvbmVudHMvTG9hZGVyJztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xyXG5pbXBvcnQgRGF0YUJyZWFjaE1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9jdXN0b21lci9EYXRhQnJlYWNoTW9kYWwnO1xyXG5pbXBvcnQgQ29tcGxhaW50U3RhdHVzQmFyLCB7IENPTVBMQUlOVF9TVEFHRVMsIGdldFN0YWdlSW5kZXhGcm9tU3RhdHVzIH0gZnJvbSAnQC9jb21wb25lbnRzL2N1c3RvbWVyL0NvbXBsYWludFN0YXR1c0Jhcic7XHJcbmltcG9ydCB7IGRhdGFCcmVhY2hTZXJ2aWNlLCBEYXRhQnJlYWNoUmVwb3J0IH0gZnJvbSAnQC9zZXJ2aWNlcy9kYXRhLWJyZWFjaCc7XHJcblxyXG5pbnRlcmZhY2UgQ29tYmluZWRDb21wbGFpbnQge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdGl0bGU6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGNhdGVnb3J5OiBzdHJpbmc7XHJcbiAgdHlwZTogJ2NvbnN1bWVyX2FmZmFpcnMnIHwgJ2RhdGFfYnJlYWNoJztcclxuICBwcmlvcml0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICd1cmdlbnQnO1xyXG4gIHN0YXR1czogJ3N1Ym1pdHRlZCcgfCAndW5kZXJfcmV2aWV3JyB8ICdpbnZlc3RpZ2F0aW5nJyB8ICdyZXNvbHZlZCcgfCAnY2xvc2VkJztcclxuICBzdWJtaXR0ZWRBdDogc3RyaW5nO1xyXG4gIHVwZGF0ZWRBdDogc3RyaW5nO1xyXG4gIGFzc2lnbmVkVG8/OiBzdHJpbmc7XHJcbiAgcmVzb2x1dGlvbj86IHN0cmluZztcclxuICBudW1iZXI/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IERhdGFQcm90ZWN0aW9uUGFnZSA9ICgpID0+IHtcclxuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgbG9hZGluZzogYXV0aExvYWRpbmcgfSA9IHVzZUF1dGgoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuXHJcbiAgY29uc3QgW2NvbXBsYWludHMsIHNldENvbXBsYWludHNdID0gdXNlU3RhdGU8Q29tYmluZWRDb21wbGFpbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8J292ZXJ2aWV3JyB8ICd0cmFjayc+KCdvdmVydmlldycpO1xyXG4gIGNvbnN0IFtzaG93RGF0YUJyZWFjaE1vZGFsLCBzZXRTaG93RGF0YUJyZWFjaE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gUmVkaXJlY3QgdG8gY3VzdG9tZXIgbG9naW4gaWYgbm90IGF1dGhlbnRpY2F0ZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFhdXRoTG9hZGluZyAmJiAhaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvY3VzdG9tZXIvYXV0aC9sb2dpbicpO1xyXG4gICAgfVxyXG4gIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIGF1dGhMb2FkaW5nLCByb3V0ZXJdKTtcclxuXHJcbiAgLy8gRmV0Y2ggZGF0YSBmdW5jdGlvblxyXG4gIGNvbnN0IGZldGNoRGF0YSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfinYwgVXNlciBub3QgYXV0aGVudGljYXRlZCwgc2tpcHBpbmcgZGF0YSBmZXRjaCcpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ+KchSBVc2VyIGF1dGhlbnRpY2F0ZWQsIGZldGNoaW5nIGRhdGEuLi4nKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIHNldEVycm9yKCcnKTtcclxuXHJcbiAgICAgIC8vIEZldGNoIG9ubHkgZGF0YSBicmVhY2ggcmVwb3J0cyAodGhpcyBpcyB0aGUgRGF0YSBCcmVhY2ggcGFnZSlcclxuICAgICAgY29uc3QgZGF0YUJyZWFjaFJlc3BvbnNlID0gYXdhaXQgZGF0YUJyZWFjaFNlcnZpY2UuZ2V0UmVwb3J0cyh7IGxpbWl0OiAxMDAgfSk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIERhdGEgQnJlYWNoIFJlc3BvbnNlOicsIGRhdGFCcmVhY2hSZXNwb25zZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gRGF0YSBCcmVhY2ggUmVzcG9uc2UuZGF0YSB0eXBlOicsIHR5cGVvZiBkYXRhQnJlYWNoUmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gRGF0YSBCcmVhY2ggUmVzcG9uc2UuZGF0YTonLCBkYXRhQnJlYWNoUmVzcG9uc2UuZGF0YSk7XHJcblxyXG4gICAgICAgIC8vIEVuc3VyZSBkYXRhIGlzIGFuIGFycmF5IChzZXJ2aWNlcyByZXR1cm4gZGF0YSBkaXJlY3RseSlcclxuICAgICAgICBjb25zdCBkYXRhQnJlYWNoRGF0YSA9IEFycmF5LmlzQXJyYXkoZGF0YUJyZWFjaFJlc3BvbnNlLmRhdGEpXHJcbiAgICAgICAgICA/IGRhdGFCcmVhY2hSZXNwb25zZS5kYXRhXHJcbiAgICAgICAgICA6IFtdO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBEYXRhIEJyZWFjaCBEYXRhIEFycmF5OicsIGRhdGFCcmVhY2hEYXRhKTtcclxuXHJcbiAgICAgICAgLy8gVHJhbnNmb3JtIG9ubHkgZGF0YSBicmVhY2ggcmVwb3J0c1xyXG4gICAgICAgIGNvbnN0IGNvbWJpbmVkQ29tcGxhaW50czogQ29tYmluZWRDb21wbGFpbnRbXSA9IGRhdGFCcmVhY2hEYXRhLm1hcCgocmVwb3J0OiBEYXRhQnJlYWNoUmVwb3J0KSA9PiAoe1xyXG4gICAgICAgICAgaWQ6IHJlcG9ydC5yZXBvcnRfaWQsXHJcbiAgICAgICAgICB0aXRsZTogcmVwb3J0LnRpdGxlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IHJlcG9ydC5kZXNjcmlwdGlvbixcclxuICAgICAgICAgIGNhdGVnb3J5OiByZXBvcnQuY2F0ZWdvcnksXHJcbiAgICAgICAgICB0eXBlOiAnZGF0YV9icmVhY2gnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgcHJpb3JpdHk6IHJlcG9ydC5wcmlvcml0eSxcclxuICAgICAgICAgIHN0YXR1czogcmVwb3J0LnN0YXR1cyxcclxuICAgICAgICAgIHN1Ym1pdHRlZEF0OiByZXBvcnQuY3JlYXRlZF9hdCxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogcmVwb3J0LnVwZGF0ZWRfYXQsXHJcbiAgICAgICAgICBhc3NpZ25lZFRvOiByZXBvcnQuYXNzaWduZWU/LmZpcnN0X25hbWUgJiYgcmVwb3J0LmFzc2lnbmVlPy5sYXN0X25hbWVcclxuICAgICAgICAgICAgPyBgJHtyZXBvcnQuYXNzaWduZWUuZmlyc3RfbmFtZX0gJHtyZXBvcnQuYXNzaWduZWUubGFzdF9uYW1lfWBcclxuICAgICAgICAgICAgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICByZXNvbHV0aW9uOiByZXBvcnQucmVzb2x1dGlvbixcclxuICAgICAgICAgIG51bWJlcjogcmVwb3J0LnJlcG9ydF9udW1iZXJcclxuICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgIC8vIFNvcnQgYnkgY3JlYXRpb24gZGF0ZSAobmV3ZXN0IGZpcnN0KVxyXG4gICAgICAgIGNvbWJpbmVkQ29tcGxhaW50cy5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLnN1Ym1pdHRlZEF0KS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLnN1Ym1pdHRlZEF0KS5nZXRUaW1lKCkpO1xyXG5cclxuICAgICAgICBzZXRDb21wbGFpbnRzKGNvbWJpbmVkQ29tcGxhaW50cyk7XHJcblxyXG4gICAgICB9IGNhdGNoIChlcnI6IHVua25vd24pIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjb21wbGFpbnRzOicsIGVycik7XHJcblxyXG4gICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcic7XHJcbiAgICAgICAgY29uc3QgaXNBeGlvc0Vycm9yID0gZXJyICYmIHR5cGVvZiBlcnIgPT09ICdvYmplY3QnICYmICdyZXNwb25zZScgaW4gZXJyO1xyXG4gICAgICAgIGNvbnN0IGF4aW9zRXJyb3IgPSBpc0F4aW9zRXJyb3IgPyBlcnIgYXMgeyByZXNwb25zZT86IHsgc3RhdHVzPzogbnVtYmVyOyBkYXRhPzogdW5rbm93biB9IH0gOiBudWxsO1xyXG4gICAgICAgIGNvbnN0IHN0YXR1cyA9IGF4aW9zRXJyb3I/LnJlc3BvbnNlPy5zdGF0dXM7XHJcblxyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRldGFpbHM6Jywge1xyXG4gICAgICAgICAgbWVzc2FnZTogZXJyb3JNZXNzYWdlLFxyXG4gICAgICAgICAgcmVzcG9uc2U6IGF4aW9zRXJyb3I/LnJlc3BvbnNlPy5kYXRhLFxyXG4gICAgICAgICAgc3RhdHVzOiBzdGF0dXNcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKHN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICBzZXRFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XHJcbiAgICAgICAgfSBlbHNlIGlmIChzdGF0dXMgPT09IDQwNCkge1xyXG4gICAgICAgICAgc2V0RXJyb3IoJ0FQSSBlbmRwb2ludHMgbm90IGZvdW5kLiBQbGVhc2UgY2hlY2sgaWYgdGhlIGJhY2tlbmQgaXMgcnVubmluZy4nKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBsb2FkIGNvbXBsYWludHM6ICR7ZXJyb3JNZXNzYWdlfWApO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9LCBbaXNBdXRoZW50aWNhdGVkXSk7XHJcblxyXG4gIC8vIEZldGNoIGRhdGEgb24gbW91bnQgYW5kIHdoZW4gYXV0aGVudGljYXRpb24gY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaERhdGEoKTtcclxuICB9LCBbaXNBdXRoZW50aWNhdGVkLCBmZXRjaERhdGFdKTtcclxuXHJcbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcclxuICAgIHN3aXRjaCAoc3RhdHVzKSB7XHJcbiAgICAgIGNhc2UgJ3N1Ym1pdHRlZCc6IHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCc7XHJcbiAgICAgIGNhc2UgJ3VuZGVyX3Jldmlldyc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnO1xyXG4gICAgICBjYXNlICdpbnZlc3RpZ2F0aW5nJzogcmV0dXJuICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCc7XHJcbiAgICAgIGNhc2UgJ3Jlc29sdmVkJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnO1xyXG4gICAgICBjYXNlICdjbG9zZWQnOiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldFByaW9yaXR5Q29sb3IgPSAocHJpb3JpdHk6IHN0cmluZykgPT4ge1xyXG4gICAgc3dpdGNoIChwcmlvcml0eSkge1xyXG4gICAgICBjYXNlICdsb3cnOiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gICAgICBjYXNlICdtZWRpdW0nOiByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnO1xyXG4gICAgICBjYXNlICdoaWdoJzogcmV0dXJuICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCc7XHJcbiAgICAgIGNhc2UgJ3VyZ2VudCc6IHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnO1xyXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcclxuICAgICAgeWVhcjogJ251bWVyaWMnLFxyXG4gICAgICBtb250aDogJ3Nob3J0JyxcclxuICAgICAgZGF5OiAnbnVtZXJpYycsXHJcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcclxuICAgICAgbWludXRlOiAnMi1kaWdpdCdcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIC8vIEFsbCBjb21wbGFpbnRzIGFyZSBub3cgZGF0YSBicmVhY2ggcmVwb3J0cyBvbmx5XHJcbiAgY29uc3QgZGF0YUJyZWFjaENvbXBsYWludHMgPSBjb21wbGFpbnRzOyAvLyBBbGwgY29tcGxhaW50cyBhcmUgZGF0YSBicmVhY2ggcmVwb3J0c1xyXG5cclxuICAvLyBDYWxjdWxhdGUgZGF0YSBicmVhY2ggc3RhdGlzdGljc1xyXG4gIGNvbnN0IHRvdGFsQ29tcGxhaW50cyA9IGNvbXBsYWludHMubGVuZ3RoO1xyXG4gIGNvbnN0IHBlbmRpbmdDb21wbGFpbnRzID0gY29tcGxhaW50cy5maWx0ZXIoYyA9PiBjLnN0YXR1cyA9PT0gJ3N1Ym1pdHRlZCcgfHwgYy5zdGF0dXMgPT09ICd1bmRlcl9yZXZpZXcnKS5sZW5ndGg7XHJcbiAgY29uc3QgaW52ZXN0aWdhdGluZ0NvbXBsYWludHMgPSBjb21wbGFpbnRzLmZpbHRlcihjID0+IGMuc3RhdHVzID09PSAnaW52ZXN0aWdhdGluZycpLmxlbmd0aDtcclxuICBjb25zdCByZXNvbHZlZENvbXBsYWludHMgPSBjb21wbGFpbnRzLmZpbHRlcihjID0+IGMuc3RhdHVzID09PSAncmVzb2x2ZWQnIHx8IGMuc3RhdHVzID09PSAnY2xvc2VkJykubGVuZ3RoO1xyXG5cclxuICBpZiAoYXV0aExvYWRpbmcgfHwgaXNMb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8Q3VzdG9tZXJMYXlvdXQ+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC05NlwiPlxyXG4gICAgICAgICAgPExvYWRlciBtZXNzYWdlPVwiTG9hZGluZyBEYXRhIEJyZWFjaC4uLlwiIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQ3VzdG9tZXJMYXlvdXQ+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKGVycm9yKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8Q3VzdG9tZXJMYXlvdXQ+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZCBtYi02XCI+XHJcbiAgICAgICAgICA8cD57ZXJyb3J9PC9wPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdW5kZXJsaW5lIGhvdmVyOm5vLXVuZGVybGluZVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIFRyeSBhZ2FpblxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQ3VzdG9tZXJMYXlvdXQ+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDdXN0b21lckxheW91dD5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxyXG4gICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04IGZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICBEYXRhIEJyZWFjaFxyXG4gICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgIFJlcG9ydCBhbmQgdHJhY2sgZGF0YSBicmVhY2ggaW5jaWRlbnRzIGFuZCBwcml2YWN5IHZpb2xhdGlvbnNcclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RGF0YUJyZWFjaE1vZGFsKHRydWUpfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1yZWQtNjAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbWQgaG92ZXI6YmctcmVkLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpyaW5nLW9mZnNldC0yIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cInJpLXNoaWVsZC1rZXlob2xlLWxpbmUgbXItMlwiPjwvaT5cclxuICAgICAgICAgICAgUmVwb3J0IERhdGEgQnJlYWNoXHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIFRhYnMgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgbWItNlwiPlxyXG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCItbWItcHggZmxleCBzcGFjZS14LThcIj5cclxuICAgICAgICAgICAge1tcclxuICAgICAgICAgICAgICB7IGtleTogJ292ZXJ2aWV3JywgbGFiZWw6ICdPdmVydmlldycsIGljb246ICdyaS1kYXNoYm9hcmQtbGluZScgfSxcclxuICAgICAgICAgICAgICB7IGtleTogJ3RyYWNrJywgbGFiZWw6ICdUcmFjayBDb21wbGFpbnRzJywgaWNvbjogJ3JpLXNlYXJjaC1leWUtbGluZScsIGNvdW50OiBjb21wbGFpbnRzLmxlbmd0aCB9XHJcbiAgICAgICAgICAgIF0ubWFwKCh0YWIpID0+IChcclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgIGtleT17dGFiLmtleX1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYih0YWIua2V5IGFzICdvdmVydmlldycgfCAndHJhY2snKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gd2hpdGVzcGFjZS1ub3dyYXAgZmxleCBpdGVtcy1jZW50ZXIgJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIua2V5XHJcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXByaW1hcnkgdGV4dC1wcmltYXJ5J1xyXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwIGRhcms6dGV4dC1ncmF5LTQwMCBkYXJrOmhvdmVyOnRleHQtZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9e2Ake3RhYi5pY29ufSBtci0yYH0+PC9pPlxyXG4gICAgICAgICAgICAgICAge3RhYi5sYWJlbH1cclxuICAgICAgICAgICAgICAgIHt0YWIuY291bnQgIT09IHVuZGVmaW5lZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBweS0wLjUgcHgtMiByb3VuZGVkLWZ1bGwgdGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIHt0YWIuY291bnR9XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgPC9uYXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBPdmVydmlldyBUYWIgKi99XHJcbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ292ZXJ2aWV3JyAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICB7LyogU3RhdGlzdGljcyBDYXJkcyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1maWxlLWxpc3QtMy1saW5lIHRleHQtMnhsIHRleHQtYmx1ZS02MDBcIj48L2k+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+VG90YWwgQ29tcGxhaW50czwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+e3RvdGFsQ29tcGxhaW50c308L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktdGltZS1saW5lIHRleHQtMnhsIHRleHQteWVsbG93LTYwMFwiPjwvaT5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5QZW5kaW5nPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDBcIj57cGVuZGluZ0NvbXBsYWludHN9PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cInJpLXNlYXJjaC1leWUtbGluZSB0ZXh0LTJ4bCB0ZXh0LW9yYW5nZS02MDBcIj48L2k+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+SW52ZXN0aWdhdGluZzwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+e2ludmVzdGlnYXRpbmdDb21wbGFpbnRzfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1jaGVjay1kb3VibGUtbGluZSB0ZXh0LTJ4bCB0ZXh0LWdyZWVuLTYwMFwiPjwvaT5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5SZXNvbHZlZDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+e3Jlc29sdmVkQ29tcGxhaW50c308L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIERhdGEgQnJlYWNoIEluZm9ybWF0aW9uIENhcmQgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1zaGllbGQta2V5aG9sZS1saW5lIHRleHQtM3hsIHRleHQtcmVkLTYwMFwiPjwvaT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+RGF0YSBCcmVhY2ggUmVwb3J0aW5nPC9oMz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIFJlcG9ydCB1bmF1dGhvcml6ZWQgYWNjZXNzLCBtaXN1c2UsIG9yIGJyZWFjaCBvZiB5b3VyIHBlcnNvbmFsIGRhdGFcclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPldoYXQgdG8gUmVwb3J0OjwvaDQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgPHA+4oCiIFVuYXV0aG9yaXplZCBkYXRhIGFjY2VzczwvcD5cclxuICAgICAgICAgICAgICAgICAgPHA+4oCiIERhdGEgbWlzdXNlIG9yIHNoYXJpbmc8L3A+XHJcbiAgICAgICAgICAgICAgICAgIDxwPuKAoiBQcml2YWN5IHZpb2xhdGlvbnM8L3A+XHJcbiAgICAgICAgICAgICAgICAgIDxwPuKAoiBJZGVudGl0eSB0aGVmdCBjb25jZXJuczwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBUcmFjayBDb21wbGFpbnRzIFRhYiAqL31cclxuICAgICAgICB7YWN0aXZlVGFiID09PSAndHJhY2snICYmIChcclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIHtjb21wbGFpbnRzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1maWxlLXNlYXJjaC1saW5lIHRleHQtNHhsIHRleHQtZ3JheS00MDAgbWItNFwiPjwvaT5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIG1iLTJcIj5ObyBjb21wbGFpbnRzIGZvdW5kPC9oMz5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgWW91IGhhdmVuJmFwb3M7dCBzdWJtaXR0ZWQgYW55IGNvbXBsYWludHMgeWV0LlxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdvdmVydmlldycpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcHJpbWFyeSB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctcmVkLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpyaW5nLW9mZnNldC0yIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIFN1Ym1pdCBZb3VyIEZpcnN0IENvbXBsYWludFxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgIHtjb21wbGFpbnRzLm1hcCgoY29tcGxhaW50KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtjb21wbGFpbnQuaWR9IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMCBtci0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29tcGxhaW50LnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7Z2V0U3RhdHVzQ29sb3IoY29tcGxhaW50LnN0YXR1cyl9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29tcGxhaW50LnN0YXR1cy5yZXBsYWNlKCdfJywgJyAnKS50b1VwcGVyQ2FzZSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BtbC0yIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldFByaW9yaXR5Q29sb3IoY29tcGxhaW50LnByaW9yaXR5KX1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb21wbGFpbnQucHJpb3JpdHkudG9VcHBlckNhc2UoKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMiBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBsYWludC50eXBlID09PSAnY29uc3VtZXJfYWZmYWlycycgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbXBsYWludC50eXBlID09PSAnY29uc3VtZXJfYWZmYWlycycgPyAnQ09OU1VNRVIgQUZGQUlSUycgOiAnREFUQSBCUkVBQ0gnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIElEOiB7Y29tcGxhaW50LmlkfSB8IENhdGVnb3J5OiB7Y29tcGxhaW50LmNhdGVnb3J5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb21wbGFpbnQuZGVzY3JpcHRpb24ubGVuZ3RoID4gMTUwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGAke2NvbXBsYWludC5kZXNjcmlwdGlvbi5zdWJzdHJpbmcoMCwgMTUwKX0uLi5gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGNvbXBsYWludC5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIENvbXBsYWludCBTdGF0dXMgQmFyICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29tcGxhaW50U3RhdHVzQmFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50U3RhZ2U9e2dldFN0YWdlSW5kZXhGcm9tU3RhdHVzKGNvbXBsYWludC5zdGF0dXMpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhZ2VzPXtjb21wbGFpbnQudHlwZSA9PT0gJ2NvbnN1bWVyX2FmZmFpcnMnID8gQ09NUExBSU5UX1NUQUdFUy5DT05TVU1FUl9BRkZBSVJTIDogQ09NUExBSU5UX1NUQUdFUy5EQVRBX0JSRUFDSH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1cz17Y29tcGxhaW50LnN0YXR1cyBhcyAnc3VibWl0dGVkJyB8ICd1bmRlcl9yZXZpZXcnIHwgJ2ludmVzdGlnYXRpbmcnIHwgJ3Jlc29sdmVkJyB8ICdjbG9zZWQnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJob3Jpem9udGFsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dQZXJjZW50YWdlPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dTdGFnZU5hbWVzPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBkYXJrOmJnLWdyYXktNzAwIHAtMyByb3VuZGVkLWxnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlN1Ym1pdHRlZDoge2Zvcm1hdERhdGUoY29tcGxhaW50LnN1Ym1pdHRlZEF0KX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXgtMlwiPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5VcGRhdGVkOiB7Zm9ybWF0RGF0ZShjb21wbGFpbnQudXBkYXRlZEF0KX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbXBsYWludC5hc3NpZ25lZFRvICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm14LTJcIj7igKI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkFzc2lnbmVkIHRvOiB7Y29tcGxhaW50LmFzc2lnbmVkVG99PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTQgcHgtMyBweS0xIHRleHQtc20gYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCByb3VuZGVkIGhvdmVyOmJnLWdyYXktMjAwIGRhcms6aG92ZXI6YmctZ3JheS02MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBWaWV3IERldGFpbHNcclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIEhlbHAgU2VjdGlvbiAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggYmctcmVkLTUwIGRhcms6YmctcmVkLTkwMC8yMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcmVkLTIwMCBkYXJrOmJvcmRlci1yZWQtODAwIHAtNlwiPlxyXG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1yZWQtOTAwIGRhcms6dGV4dC1yZWQtMTAwIG1iLTRcIj5cclxuICAgICAgICAgICAgTmVlZCBIZWxwIHdpdGggRGF0YSBCcmVhY2ggUmVwb3J0aW5nP1xyXG4gICAgICAgICAgPC9oMz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTgwMCBkYXJrOnRleHQtcmVkLTIwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBDb250YWN0IEluZm9ybWF0aW9uXHJcbiAgICAgICAgICAgICAgPC9oND5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtIHRleHQtcmVkLTcwMCBkYXJrOnRleHQtcmVkLTMwMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS1waG9uZS1saW5lIG1yLTJcIj48L2k+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPisyNjUgMSA3NzAgMTAwPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cInJpLW1haWwtbGluZSBtci0yXCI+PC9pPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj5kYXRhcHJvdGVjdGlvbkBtYWNyYS5tdzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJyaS10aW1lLWxpbmUgbXItMlwiPjwvaT5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+TW9uLUZyaSwgODowMCBBTSAtIDU6MDAgUE08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtODAwIGRhcms6dGV4dC1yZWQtMjAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgIERhdGEgQnJlYWNoIENhdGVnb3JpZXNcclxuICAgICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTEgdGV4dC1zbSB0ZXh0LXJlZC03MDAgZGFyazp0ZXh0LXJlZC0zMDBcIj5cclxuICAgICAgICAgICAgICAgIDxsaT7igKIgVW5hdXRob3JpemVkIEFjY2VzczwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIERhdGEgTWlzdXNlPC9saT5cclxuICAgICAgICAgICAgICAgIDxsaT7igKIgUHJpdmFjeSBWaW9sYXRpb25zPC9saT5cclxuICAgICAgICAgICAgICAgIDxsaT7igKIgSWRlbnRpdHkgVGhlZnQ8L2xpPlxyXG4gICAgICAgICAgICAgICAgPGxpPuKAoiBEYXRhIFNoYXJpbmcgSXNzdWVzPC9saT5cclxuICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTW9kYWxzICovfVxyXG4gICAgICAgIHtzaG93RGF0YUJyZWFjaE1vZGFsICYmIChcclxuICAgICAgICAgIDxEYXRhQnJlYWNoTW9kYWxcclxuICAgICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0RhdGFCcmVhY2hNb2RhbChmYWxzZSl9XHJcbiAgICAgICAgICAgIG9uU3VibWl0PXsoZGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdEYXRhIGJyZWFjaCByZXBvcnQgc3VibWl0dGVkOicsIGRhdGEpO1xyXG4gICAgICAgICAgICAgIHNldFNob3dEYXRhQnJlYWNoTW9kYWwoZmFsc2UpO1xyXG4gICAgICAgICAgICAgIC8vIFJlZnJlc2ggY29tcGxhaW50cyBsaXN0IHdpdGhvdXQgZnVsbCBwYWdlIHJlbG9hZFxyXG4gICAgICAgICAgICAgIGZldGNoRGF0YSgpO1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvQ3VzdG9tZXJMYXlvdXQ+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IERhdGFQcm90ZWN0aW9uUGFnZTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJvdXRlciIsIkN1c3RvbWVyTGF5b3V0IiwiTG9hZGVyIiwidXNlQXV0aCIsIkRhdGFCcmVhY2hNb2RhbCIsIkNvbXBsYWludFN0YXR1c0JhciIsIkNPTVBMQUlOVF9TVEFHRVMiLCJnZXRTdGFnZUluZGV4RnJvbVN0YXR1cyIsImRhdGFCcmVhY2hTZXJ2aWNlIiwiRGF0YVByb3RlY3Rpb25QYWdlIiwiaXNBdXRoZW50aWNhdGVkIiwibG9hZGluZyIsImF1dGhMb2FkaW5nIiwicm91dGVyIiwiY29tcGxhaW50cyIsInNldENvbXBsYWludHMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic2hvd0RhdGFCcmVhY2hNb2RhbCIsInNldFNob3dEYXRhQnJlYWNoTW9kYWwiLCJwdXNoIiwiZmV0Y2hEYXRhIiwiY29uc29sZSIsImxvZyIsImRhdGFCcmVhY2hSZXNwb25zZSIsImdldFJlcG9ydHMiLCJsaW1pdCIsImRhdGEiLCJkYXRhQnJlYWNoRGF0YSIsIkFycmF5IiwiaXNBcnJheSIsImNvbWJpbmVkQ29tcGxhaW50cyIsIm1hcCIsInJlcG9ydCIsImlkIiwicmVwb3J0X2lkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwidHlwZSIsInByaW9yaXR5Iiwic3RhdHVzIiwic3VibWl0dGVkQXQiLCJjcmVhdGVkX2F0IiwidXBkYXRlZEF0IiwidXBkYXRlZF9hdCIsImFzc2lnbmVkVG8iLCJhc3NpZ25lZSIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJ1bmRlZmluZWQiLCJyZXNvbHV0aW9uIiwibnVtYmVyIiwicmVwb3J0X251bWJlciIsInNvcnQiLCJhIiwiYiIsIkRhdGUiLCJnZXRUaW1lIiwiZXJyIiwiYXhpb3NFcnJvciIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSIsImlzQXhpb3NFcnJvciIsInJlc3BvbnNlIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRQcmlvcml0eUNvbG9yIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwiZGF0YUJyZWFjaENvbXBsYWludHMiLCJ0b3RhbENvbXBsYWludHMiLCJsZW5ndGgiLCJwZW5kaW5nQ29tcGxhaW50cyIsImZpbHRlciIsImMiLCJpbnZlc3RpZ2F0aW5nQ29tcGxhaW50cyIsInJlc29sdmVkQ29tcGxhaW50cyIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwid2luZG93IiwibG9jYXRpb24iLCJyZWxvYWQiLCJoMSIsImkiLCJuYXYiLCJrZXkiLCJsYWJlbCIsImljb24iLCJjb3VudCIsInRhYiIsInNwYW4iLCJoMyIsImg0IiwiY29tcGxhaW50IiwicmVwbGFjZSIsInRvVXBwZXJDYXNlIiwic3Vic3RyaW5nIiwiY3VycmVudFN0YWdlIiwic3RhZ2VzIiwiQ09OU1VNRVJfQUZGQUlSUyIsIkRBVEFfQlJFQUNIIiwic2l6ZSIsInZhcmlhbnQiLCJzaG93UGVyY2VudGFnZSIsInNob3dTdGFnZU5hbWVzIiwidWwiLCJsaSIsIm9uQ2xvc2UiLCJvblN1Ym1pdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customer/data-protection/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/customer/DataBreachModal.tsx":
/*!*****************************************************!*\
  !*** ./src/components/customer/DataBreachModal.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/forms/TextInput */ \"(app-pages-browser)/./src/components/forms/TextInput.tsx\");\n/* harmony import */ var _components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/forms/TextArea */ \"(app-pages-browser)/./src/components/forms/TextArea.tsx\");\n/* harmony import */ var _components_forms_Select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/forms/Select */ \"(app-pages-browser)/./src/components/forms/Select.tsx\");\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst breachCategories = [\n    'Unauthorized Data Access',\n    'Data Misuse or Sharing',\n    'Privacy Violations',\n    'Identity Theft',\n    'Phishing Attempts',\n    'Data Loss or Theft',\n    'Consent Violations',\n    'Other'\n];\nconst severityLevels = [\n    {\n        value: 'low',\n        label: 'Low - Minor privacy concern'\n    },\n    {\n        value: 'medium',\n        label: 'Medium - Moderate data exposure'\n    },\n    {\n        value: 'high',\n        label: 'High - Significant data breach'\n    },\n    {\n        value: 'critical',\n        label: 'Critical - Severe security incident'\n    }\n];\nconst DataBreachModal = (param)=>{\n    let { onClose, onSubmit } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        category: '',\n        severity: '',\n        incidentDate: '',\n        affectedData: '',\n        organization: '',\n        contactAttempts: ''\n    });\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (formErrors[name]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files) {\n            const newFiles = Array.from(e.target.files);\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n    };\n    const removeAttachment = (index)=>{\n        setAttachments((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const validateForm = ()=>{\n        const errors = {};\n        if (!formData.title.trim()) {\n            errors.title = 'Title is required';\n        }\n        if (!formData.description.trim()) {\n            errors.description = 'Description is required';\n        } else if (formData.description.trim().length < 20) {\n            errors.description = 'Description must be at least 20 characters';\n        }\n        if (!formData.category) {\n            errors.category = 'Category is required';\n        }\n        if (!formData.severity) {\n            errors.severity = 'Severity level is required';\n        }\n        if (!formData.incidentDate) {\n            errors.incidentDate = 'Incident date is required';\n        }\n        if (!formData.organization.trim()) {\n            errors.organization = 'Organization involved is required';\n        }\n        setFormErrors(errors);\n        return Object.keys(errors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const reportData = {\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                severity: formData.severity,\n                incident_date: formData.incidentDate,\n                organization_involved: formData.organization,\n                affected_data_types: formData.affectedData,\n                contact_attempts: formData.contactAttempts,\n                attachments: attachments\n            };\n            const response = await _services_data_breach__WEBPACK_IMPORTED_MODULE_5__.dataBreachService.createReport(reportData);\n            // Show success message\n            showSuccess(\"Your data breach report has been submitted successfully! Reference ID: \".concat(response.report_id || 'N/A'), 6000);\n            onSubmit(response);\n            // Reset form\n            setFormData({\n                title: '',\n                description: '',\n                category: '',\n                severity: '',\n                incidentDate: '',\n                affectedData: '',\n                organization: '',\n                contactAttempts: ''\n            });\n            setAttachments([]);\n        } catch (error) {\n            console.error('Error submitting data breach report:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Failed to submit data breach report. Please try again.';\n            showError(errorMessage);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                            children: \"Report Data Breach\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            \"aria-label\": \"Close modal\",\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                label: \"Incident Title *\",\n                                id: \"breach-title\",\n                                name: \"title\",\n                                value: formData.title,\n                                onChange: handleInputChange,\n                                placeholder: \"Brief summary of the data breach incident\",\n                                error: formErrors.title,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        label: \"Breach Category *\",\n                                        name: \"category\",\n                                        value: formData.category,\n                                        onChange: handleInputChange,\n                                        error: formErrors.category,\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select a category\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            breachCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        label: \"Severity Level *\",\n                                        name: \"severity\",\n                                        value: formData.severity,\n                                        onChange: handleInputChange,\n                                        error: formErrors.severity,\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select severity level\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            severityLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: level.value,\n                                                    children: level.label\n                                                }, level.value, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        label: \"Incident Date *\",\n                                        id: \"incident-date\",\n                                        name: \"incidentDate\",\n                                        type: \"date\",\n                                        value: formData.incidentDate,\n                                        onChange: handleInputChange,\n                                        error: formErrors.incidentDate,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        label: \"Organization Involved *\",\n                                        id: \"organization\",\n                                        name: \"organization\",\n                                        value: formData.organization,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Name of the organization responsible\",\n                                        error: formErrors.organization,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                label: \"Affected Data Types\",\n                                id: \"affected-data\",\n                                name: \"affectedData\",\n                                value: formData.affectedData,\n                                onChange: handleInputChange,\n                                rows: 3,\n                                placeholder: \"Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)\",\n                                error: formErrors.affectedData\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                label: \"Detailed Description *\",\n                                id: \"breach-description\",\n                                name: \"description\",\n                                value: formData.description,\n                                onChange: handleInputChange,\n                                rows: 6,\n                                placeholder: \"Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you...\",\n                                error: formErrors.description,\n                                helperText: \"Minimum 20 characters required\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                label: \"Previous Contact Attempts\",\n                                id: \"contact-attempts\",\n                                name: \"contactAttempts\",\n                                value: formData.contactAttempts,\n                                onChange: handleInputChange,\n                                rows: 3,\n                                placeholder: \"Describe any attempts you made to contact the organization about this incident\",\n                                error: formErrors.contactAttempts\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"breach-attachments\",\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"Supporting Evidence (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"breach-attachments\",\n                                        type: \"file\",\n                                        multiple: true,\n                                        accept: \".pdf,.doc,.docx,.jpg,.jpeg,.png\",\n                                        onChange: handleFileChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                        children: \"Screenshots, emails, documents, or other evidence (Max 5MB per file)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 space-y-2\",\n                                        children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"ri-file-line text-gray-400 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                children: file.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 ml-2\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    formatFileSize(file.size),\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeAttachment(index),\n                                                        className: \"text-red-500 hover:text-red-700\",\n                                                        \"aria-label\": \"Remove \".concat(file.name),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-close-line\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-loader-4-line animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Submitting...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-shield-keyhole-line mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Submit Report\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\DataBreachModal.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataBreachModal, \"nXFFH3YxOelVP9W7oXIslBfPMoQ=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = DataBreachModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataBreachModal);\nvar _c;\n$RefreshReg$(_c, \"DataBreachModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/customer/DataBreachModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/dataBreachService.ts":
/*!*******************************************************!*\
  !*** ./src/services/data-breach/dataBreachService.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataBreachCategory: () => (/* binding */ DataBreachCategory),\n/* harmony export */   DataBreachPriority: () => (/* binding */ DataBreachPriority),\n/* harmony export */   DataBreachSeverity: () => (/* binding */ DataBreachSeverity),\n/* harmony export */   DataBreachStatus: () => (/* binding */ DataBreachStatus),\n/* harmony export */   dataBreachService: () => (/* binding */ dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\n// Enums matching backend\nvar DataBreachCategory = /*#__PURE__*/ function(DataBreachCategory) {\n    DataBreachCategory[\"PERSONAL_DATA\"] = \"Personal Data\";\n    DataBreachCategory[\"FINANCIAL_DATA\"] = \"Financial Data\";\n    DataBreachCategory[\"HEALTH_DATA\"] = \"Health Data\";\n    DataBreachCategory[\"TECHNICAL_DATA\"] = \"Technical Data\";\n    DataBreachCategory[\"COMMUNICATION_DATA\"] = \"Communication Data\";\n    DataBreachCategory[\"OTHER\"] = \"Other\";\n    return DataBreachCategory;\n}({});\nvar DataBreachSeverity = /*#__PURE__*/ function(DataBreachSeverity) {\n    DataBreachSeverity[\"LOW\"] = \"low\";\n    DataBreachSeverity[\"MEDIUM\"] = \"medium\";\n    DataBreachSeverity[\"HIGH\"] = \"high\";\n    DataBreachSeverity[\"CRITICAL\"] = \"critical\";\n    return DataBreachSeverity;\n}({});\nvar DataBreachStatus = /*#__PURE__*/ function(DataBreachStatus) {\n    DataBreachStatus[\"SUBMITTED\"] = \"submitted\";\n    DataBreachStatus[\"UNDER_REVIEW\"] = \"under_review\";\n    DataBreachStatus[\"INVESTIGATING\"] = \"investigating\";\n    DataBreachStatus[\"RESOLVED\"] = \"resolved\";\n    DataBreachStatus[\"CLOSED\"] = \"closed\";\n    return DataBreachStatus;\n}({});\nvar DataBreachPriority = /*#__PURE__*/ function(DataBreachPriority) {\n    DataBreachPriority[\"LOW\"] = \"low\";\n    DataBreachPriority[\"MEDIUM\"] = \"medium\";\n    DataBreachPriority[\"HIGH\"] = \"high\";\n    DataBreachPriority[\"URGENT\"] = \"urgent\";\n    return DataBreachPriority;\n}({});\nconst dataBreachService = {\n    // Create new report\n    async createReport (data) {\n        try {\n            console.log('🔄 Creating data breach report:', {\n                title: data.title,\n                category: data.category,\n                severity: data.severity,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            const formData = new FormData();\n            formData.append('title', data.title);\n            formData.append('description', data.description);\n            formData.append('category', data.category);\n            formData.append('severity', data.severity);\n            formData.append('incident_date', data.incident_date);\n            formData.append('organization_involved', data.organization_involved);\n            if (data.priority) {\n                formData.append('priority', data.priority);\n            }\n            if (data.affected_data_types) {\n                formData.append('affected_data_types', data.affected_data_types);\n            }\n            if (data.contact_attempts) {\n                formData.append('contact_attempts', data.contact_attempts);\n            }\n            // Add attachments if provided\n            if (data.attachments && data.attachments.length > 0) {\n                data.attachments.forEach((file)=>{\n                    formData.append('attachments', file);\n                });\n            }\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/data-breach-reports', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Data breach report created successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error creating data breach report:', error);\n            throw error;\n        }\n    },\n    // Get report by ID\n    async getReportById (reportId) {\n        try {\n            console.log('🔄 Fetching data breach report by ID:', reportId);\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(reportId));\n            console.log('✅ Data breach report fetched successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error fetching data breach report:', error);\n            throw error;\n        }\n    },\n    // Update report status\n    async updateStatus (reportId, status, comment) {\n        try {\n            console.log('🔄 Updating data breach report status:', {\n                reportId,\n                status,\n                comment\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/status\"), {\n                status,\n                comment\n            });\n            console.log('✅ Data breach report status updated successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error updating data breach report status:', error);\n            throw error;\n        }\n    },\n    // Assign report to officer\n    async assignReport (reportId, assignedTo) {\n        try {\n            console.log('🔄 Assigning data breach report:', {\n                reportId,\n                assignedTo\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/assign\"), {\n                assigned_to: assignedTo\n            });\n            console.log('✅ Data breach report assigned successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error assigning data breach report:', error);\n            throw error;\n        }\n    },\n    // Get all reports with pagination\n    async getReports () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get report by ID\n    async getReport (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Update report\n    async updateReport (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Delete report\n    async deleteReport (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/data-breach-reports/\".concat(id));\n    },\n    // Update report status (for staff)\n    async updateReportStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Add attachment to report\n    async addAttachment (id, file) {\n        const formData = new FormData();\n        formData.append('files', file);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/data-breach-reports/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Remove attachment from report\n    async removeAttachment (reportId, attachmentId) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/data-breach-reports/\".concat(reportId, \"/attachments/\").concat(attachmentId));\n    },\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getSeverityColor (severity) {\n        switch(severity === null || severity === void 0 ? void 0 : severity.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'critical':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Personal Data',\n                label: 'Personal Data'\n            },\n            {\n                value: 'Financial Data',\n                label: 'Financial Data'\n            },\n            {\n                value: 'Health Data',\n                label: 'Health Data'\n            },\n            {\n                value: 'Technical Data',\n                label: 'Technical Data'\n            },\n            {\n                value: 'Communication Data',\n                label: 'Communication Data'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getSeverityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'critical',\n                label: 'Critical'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/index.ts":
/*!*******************************************!*\
  !*** ./src/services/data-breach/index.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataBreachCategory: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachCategory),\n/* harmony export */   DataBreachPriority: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachPriority),\n/* harmony export */   DataBreachSeverity: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachSeverity),\n/* harmony export */   DataBreachStatus: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachStatus),\n/* harmony export */   dataBreachService: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _dataBreachService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataBreachService */ \"(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhLWJyZWFjaC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0MiLCJzb3VyY2VzIjpbIkQ6XFxNZW1vcnkgQnVzaW5lc3MgU29sdXRpb2luc1xcUHJvamVjdHNcXE1BQ1JBXFxwcm9qZWN0XFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxzZXJ2aWNlc1xcZGF0YS1icmVhY2hcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZGF0YUJyZWFjaFNlcnZpY2UnO1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/index.ts\n"));

/***/ })

});