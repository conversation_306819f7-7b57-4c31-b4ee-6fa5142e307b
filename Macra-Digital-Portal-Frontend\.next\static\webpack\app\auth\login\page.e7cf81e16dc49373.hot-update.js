"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth */ \"(app-pages-browser)/./src/components/auth/index.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoginForm() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [fieldErrors, setFieldErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isCustomerPortal, setIsCustomerPortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [requires2FA, setRequires2FA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingMessage, setLoadingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [forgotPasswordLoading, setForgotPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dynamicMessages, setDynamicMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'Connecting to staff portal...'\n    ]);\n    const { login, isAuthenticated, loading: staffLoading, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Client-side initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginForm.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            // Check if we're on staff portal - fix the logic completely\n            const message = searchParams.get('message');\n            if (message) {\n                setSuccessMessage(message);\n            }\n            // Add timeout to prevent infinite loading - but don't redirect on timeout\n            const loadingTimeout = setTimeout({\n                \"LoginForm.useEffect.loadingTimeout\": ()=>{\n                    if (loading && !error) {\n                        setLoading(false);\n                    }\n                }\n            }[\"LoginForm.useEffect.loadingTimeout\"], 10000); // 10 second timeout\n            return ({\n                \"LoginForm.useEffect\": ()=>clearTimeout(loadingTimeout)\n            })[\"LoginForm.useEffect\"];\n        }\n    }[\"LoginForm.useEffect\"], [\n        searchParams,\n        loading,\n        isClient,\n        error,\n        isCustomerPortal,\n        router\n    ]);\n    // Redirect if already authenticated - only within staff portal, never to customer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            // Don't redirect in any of these conditions:\n            if (requires2FA) return;\n            if (error) return; // Explicit error check first\n            if (loading || staffLoading) return; // Don't redirect during loading\n            if (!isClient) return; // Wait for client-side hydration\n            if (isCustomerPortal) return; // Only redirect in staff portal\n            // Only redirect if user is authenticated and no errors\n            if (isAuthenticated && !error) {\n                router.replace('/dashboard');\n            }\n        }\n    }[\"LoginForm.useEffect\"], [\n        requires2FA,\n        isCustomerPortal,\n        isAuthenticated,\n        staffLoading,\n        loading,\n        router,\n        error,\n        isClient\n    ]);\n    const validateEmail = (email)=>{\n        if (!email.trim()) {\n            return 'Email address is required';\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return 'Please enter a valid email address';\n        }\n        return null;\n    };\n    const validatePassword = (password)=>{\n        if (!password) {\n            return 'Password is required';\n        }\n        if (password.length < 8) {\n            return 'Password must be at least 8 characters long';\n        }\n        return null;\n    };\n    const validateForm = ()=>{\n        const emailError = validateEmail(email);\n        const passwordError = validatePassword(password);\n        setFieldErrors({\n            email: emailError || undefined,\n            password: passwordError || undefined\n        });\n        if (emailError) return emailError;\n        if (passwordError) return passwordError;\n        return null;\n    };\n    const handleEmailChange = (e)=>{\n        const value = e.target.value;\n        setEmail(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.email) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    email: undefined\n                }));\n        }\n        // Clear general error when user starts typing (with slight delay to let user see the error)\n        if (error) {\n            setTimeout(()=>{\n                setError('');\n            }, 100);\n        }\n    };\n    const handlePasswordChange = (e)=>{\n        const value = e.target.value;\n        setPassword(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.password) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    password: undefined\n                }));\n        }\n        // Clear general error when user starts typing (with slight delay to let user see the error)\n        if (error) {\n            setTimeout(()=>{\n                setError('');\n            }, 100);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Clear previous messages\n        setError('');\n        setSuccessMessage('');\n        // Validate form before submission\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            setLoading(false);\n            return;\n        }\n        setLoading(true);\n        try {\n            // Always treat this as staff login since we're on staff portal\n            setDynamicMessages([\n                'Verifying your credentials...',\n                'Please wait...'\n            ]);\n            const response = await login(email.trim().toLowerCase(), password, rememberMe);\n            if (response) {\n                // Check if email verification is required (for unverified accounts)\n                const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n                if (requiresEmailVerification) {\n                    setRequires2FA(true);\n                    setSuccessMessage('Email verification required. Please check your email for the verification code.');\n                    setDynamicMessages([\n                        'Email verification required!',\n                        'Verification code sent...',\n                        'Redirecting to verification...'\n                    ]);\n                    setTimeout(()=>{\n                        router.replace('/auth/verify-2fa');\n                    }, 1500);\n                } else {\n                    // Check if 2FA is required for verified accounts\n                    const requires2FA = response.user.two_factor_enabled;\n                    if (requires2FA == true || requires2FA == 'true') {\n                        setRequires2FA(true);\n                        setSuccessMessage('Login successful! Two-factor authentication is enabled for your account. Please check your email for the verification code.');\n                        setDynamicMessages([\n                            'Login successful!',\n                            'Sending verification code...',\n                            'Redirecting to 2FA verification...'\n                        ]);\n                        setTimeout(()=>{\n                            router.replace('/auth/verify-2fa');\n                        }, 1500);\n                    } else {\n                        setTimeout(()=>{\n                            setSuccessMessage('Login successful! Redirecting to your dashboard...');\n                            setDynamicMessages([\n                                'Login successful!',\n                                'Setting up your session...',\n                                'Redirecting...'\n                            ]);\n                            console.log(\"============= =========\", user);\n                            router.replace('/dashboard');\n                        }, 3000);\n                    }\n                }\n            } else {\n                setError('Invalid user session. Please try again.');\n                setLoading(false);\n                return;\n            }\n        } catch (err) {\n            // AuthService now provides clean error messages, so we can use them directly\n            setLoading(false);\n            console.error('Staff login error:', err);\n            const errorMessage = (0,_lib__WEBPACK_IMPORTED_MODULE_5__.getErrorMessage)(err);\n            setError(errorMessage);\n            // Return early - stay on login page when authentication fails\n            return;\n        }\n    };\n    const handleForgotPasswordClick = (e)=>{\n        e.preventDefault();\n        setForgotPasswordLoading(true);\n        setLoadingMessage('Redirecting to forgot password...');\n        setTimeout(()=>{\n            router.push('/auth/forgot-password');\n        }, 3000);\n    };\n    // Show loading while client is initializing, checking authentication, or during login/redirect\n    if (!isClient || loading || forgotPasswordLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.PageTransition, {\n            isLoading: true,\n            loadingMessage: loadingMessage || (!isClient ? 'Loading...' : 'Signing in...'),\n            loadingSubmessage: \"Please wait while we process your request\",\n            dynamicMessages: loading ? dynamicMessages : undefined,\n            showProgress: loading,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.AuthLayout, {\n        title: \"Welcome Back!\",\n        subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                \"Staff Portal Access\",\n                ' ',\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-red-600 dark:text-red-400\",\n                    children: \"• Secure Dashboard Login\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 17\n                }, void 0)\n            ]\n        }, void 0, true),\n        isCustomerPortal: false,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.StatusMessage, {\n                type: \"error\",\n                message: error,\n                className: \"mb-4\",\n                dismissible: true,\n                onDismiss: ()=>setError('')\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 13\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_4__.StatusMessage, {\n                type: \"success\",\n                message: successMessage,\n                className: \"mb-4\",\n                dismissible: true,\n                onDismiss: ()=>setSuccessMessage('')\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-6 animate-fadeIn animate-delay-200\",\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Email address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"email\",\n                                    name: \"email\",\n                                    type: \"email\",\n                                    autoComplete: \"email\",\n                                    required: true,\n                                    value: email,\n                                    onChange: handleEmailChange,\n                                    className: \"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth \".concat(fieldErrors.email ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'),\n                                    placeholder: \"Enter your email address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this),\n                            fieldErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 19\n                                    }, this),\n                                    fieldErrors.email\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"password\",\n                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Password\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"password\",\n                                    name: \"password\",\n                                    type: \"password\",\n                                    autoComplete: \"current-password\",\n                                    required: true,\n                                    value: password,\n                                    onChange: handlePasswordChange,\n                                    className: \"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth \".concat(fieldErrors.password ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'),\n                                    placeholder: \"Enter your password\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            fieldErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this),\n                                    fieldErrors.password\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 13\n                    }, this),\n                    !isCustomerPortal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between animate-fadeIn animate-delay-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"remember-me\",\n                                        name: \"remember-me\",\n                                        type: \"checkbox\",\n                                        checked: rememberMe,\n                                        onChange: (e)=>setRememberMe(e.target.checked),\n                                        className: \"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"remember-me\",\n                                        className: \"ml-2 block text-sm text-gray-900 dark:text-gray-100\",\n                                        children: \"Remember me\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/auth/forgot-password\",\n                                    onClick: handleForgotPasswordClick,\n                                    className: \"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline cursor-pointer\",\n                                    children: \"Forgot your password?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slideInFromBottom animate-delay-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 21\n                                    }, this),\n                                    \"Signing in...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3v1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 21\n                                    }, this),\n                                    \"Sign in\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 232,\n        columnNumber: 11\n    }, this);\n}\n_s(LoginForm, \"G+yCezb3G+MzNyciTxKYfhgsaV4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = LoginForm;\nfunction LoginPage() {\n    console.log('LoginPage component rendering...');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"Loading login page...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 386,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginForm, {}, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoginPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"LoginForm\");\n$RefreshReg$(_c1, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXV0aC9sb2dpbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXNEO0FBQ087QUFDWjtBQUM2QjtBQUN0QztBQUd4QyxTQUFTVTs7SUFDUCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR1osK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDYSxVQUFVQyxZQUFZLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2UsWUFBWUMsY0FBYyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUIsU0FBU0MsV0FBVyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDbUIsT0FBT0MsU0FBUyxHQUFHcEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDcUIsZ0JBQWdCQyxrQkFBa0IsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3VCLGFBQWFDLGVBQWUsR0FBR3hCLCtDQUFRQSxDQUFzQyxDQUFDO0lBQ3JGLE1BQU0sQ0FBQ3lCLGtCQUFrQkMsb0JBQW9CLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMyQixVQUFVQyxZQUFZLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMrQixnQkFBZ0JDLGtCQUFrQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUMsdUJBQXVCQyx5QkFBeUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ21DLGlCQUFpQkMsbUJBQW1CLEdBQUdwQywrQ0FBUUEsQ0FBVztRQUFDO0tBQWdDO0lBQ2xHLE1BQU0sRUFBRXFDLEtBQUssRUFBRUMsZUFBZSxFQUFFckIsU0FBU3NCLFlBQVksRUFBRUMsSUFBSSxFQUFFLEdBQUduQyw4REFBT0E7SUFDdkUsTUFBTW9DLFNBQVN0QywwREFBU0E7SUFDeEIsTUFBTXVDLGVBQWV0QyxnRUFBZUE7SUFFcEMsNkJBQTZCO0lBQzdCSCxnREFBU0E7K0JBQUM7WUFDUjJCLFlBQVk7UUFDZDs4QkFBRyxFQUFFO0lBRUwzQixnREFBU0E7K0JBQUM7WUFDUiw0REFBNEQ7WUFFNUQsTUFBTTBDLFVBQVVELGFBQWFFLEdBQUcsQ0FBQztZQUNqQyxJQUFJRCxTQUFTO2dCQUNYckIsa0JBQWtCcUI7WUFDcEI7WUFFQSwwRUFBMEU7WUFDMUUsTUFBTUUsaUJBQWlCQztzREFBVztvQkFDaEMsSUFBSTdCLFdBQVcsQ0FBQ0UsT0FBTzt3QkFDckJELFdBQVc7b0JBQ2I7Z0JBQ0Y7cURBQUcsUUFBUSxvQkFBb0I7WUFFL0I7dUNBQU8sSUFBTTZCLGFBQWFGOztRQUM1Qjs4QkFBRztRQUFDSDtRQUFjekI7UUFBU1U7UUFBVVI7UUFBT007UUFBa0JnQjtLQUFPO0lBRXJFLGtGQUFrRjtJQUNsRnhDLGdEQUFTQTsrQkFBQztZQUNSLDZDQUE2QztZQUM3QyxJQUFJNEIsYUFBYTtZQUNqQixJQUFJVixPQUFPLFFBQVEsNkJBQTZCO1lBQ2hELElBQUlGLFdBQVdzQixjQUFjLFFBQVEsZ0NBQWdDO1lBQ3JFLElBQUksQ0FBQ1osVUFBVSxRQUFRLGlDQUFpQztZQUN4RCxJQUFJRixrQkFBa0IsUUFBUSxnQ0FBZ0M7WUFFOUQsdURBQXVEO1lBQ3ZELElBQUlhLG1CQUFtQixDQUFDbkIsT0FBTztnQkFDN0JzQixPQUFPTyxPQUFPLENBQUM7WUFDakI7UUFDRjs4QkFBRztRQUFDbkI7UUFBYUo7UUFBa0JhO1FBQWlCQztRQUFjdEI7UUFBU3dCO1FBQVF0QjtRQUFPUTtLQUFTO0lBRW5HLE1BQU1zQixnQkFBZ0IsQ0FBQ3RDO1FBQ3JCLElBQUksQ0FBQ0EsTUFBTXVDLElBQUksSUFBSTtZQUNqQixPQUFPO1FBQ1Q7UUFFQSxNQUFNQyxhQUFhO1FBQ25CLElBQUksQ0FBQ0EsV0FBV0MsSUFBSSxDQUFDekMsUUFBUTtZQUMzQixPQUFPO1FBQ1Q7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxNQUFNMEMsbUJBQW1CLENBQUN4QztRQUN4QixJQUFJLENBQUNBLFVBQVU7WUFDYixPQUFPO1FBQ1Q7UUFFQSxJQUFJQSxTQUFTeUMsTUFBTSxHQUFHLEdBQUc7WUFDdkIsT0FBTztRQUNUO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTUMsZUFBZTtRQUNuQixNQUFNQyxhQUFhUCxjQUFjdEM7UUFDakMsTUFBTThDLGdCQUFnQkosaUJBQWlCeEM7UUFFdkNXLGVBQWU7WUFDYmIsT0FBTzZDLGNBQWNFO1lBQ3JCN0MsVUFBVTRDLGlCQUFpQkM7UUFDN0I7UUFFQSxJQUFJRixZQUFZLE9BQU9BO1FBQ3ZCLElBQUlDLGVBQWUsT0FBT0E7UUFFMUIsT0FBTztJQUNUO0lBRUEsTUFBTUUsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU1DLFFBQVFELEVBQUVFLE1BQU0sQ0FBQ0QsS0FBSztRQUM1QmpELFNBQVNpRDtRQUVULDRDQUE0QztRQUM1QyxJQUFJdEMsWUFBWVosS0FBSyxFQUFFO1lBQ3JCYSxlQUFldUMsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFcEQsT0FBTytDO2dCQUFVO1FBQ3REO1FBRUEsNEZBQTRGO1FBQzVGLElBQUl2QyxPQUFPO1lBQ1QyQixXQUFXO2dCQUNUMUIsU0FBUztZQUNYLEdBQUc7UUFDTDtJQUNGO0lBRUEsTUFBTTRDLHVCQUF1QixDQUFDSjtRQUM1QixNQUFNQyxRQUFRRCxFQUFFRSxNQUFNLENBQUNELEtBQUs7UUFDNUIvQyxZQUFZK0M7UUFFWiw0Q0FBNEM7UUFDNUMsSUFBSXRDLFlBQVlWLFFBQVEsRUFBRTtZQUN4QlcsZUFBZXVDLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRWxELFVBQVU2QztnQkFBVTtRQUN6RDtRQUVBLDRGQUE0RjtRQUM1RixJQUFJdkMsT0FBTztZQUNUMkIsV0FBVztnQkFDVDFCLFNBQVM7WUFDWCxHQUFHO1FBQ0w7SUFDRjtJQUdBLE1BQU02QyxlQUFlLE9BQU9MO1FBQzFCQSxFQUFFTSxjQUFjO1FBRWhCLDBCQUEwQjtRQUMxQjlDLFNBQVM7UUFDVEUsa0JBQWtCO1FBRWxCLGtDQUFrQztRQUNsQyxNQUFNNkMsa0JBQWtCWjtRQUN4QixJQUFJWSxpQkFBaUI7WUFDbkIvQyxTQUFTK0M7WUFDVGpELFdBQVc7WUFDWDtRQUNGO1FBRUFBLFdBQVc7UUFDWCxJQUFJO1lBQ0YsK0RBQStEO1lBQy9Ea0IsbUJBQW1CO2dCQUFDO2dCQUFpQzthQUFpQjtZQUN0RSxNQUFNZ0MsV0FBVyxNQUFNL0IsTUFBTTFCLE1BQU11QyxJQUFJLEdBQUdtQixXQUFXLElBQUl4RCxVQUFVRTtZQUNuRSxJQUFJcUQsVUFBVTtnQkFDWixvRUFBb0U7Z0JBQ3BFLE1BQU1FLDRCQUE0QkYsU0FBU0csWUFBWSxJQUFJSCxTQUFTSSxNQUFNLEtBQUs7Z0JBRS9FLElBQUlGLDJCQUEyQjtvQkFDN0J4QyxlQUFlO29CQUNmUixrQkFBa0I7b0JBQ2xCYyxtQkFBbUI7d0JBQUM7d0JBQWdDO3dCQUE2QjtxQkFBaUM7b0JBQ2xIVSxXQUFXO3dCQUNUTCxPQUFPTyxPQUFPLENBQUM7b0JBQ2pCLEdBQUc7Z0JBQ0wsT0FBTztvQkFDTCxpREFBaUQ7b0JBQ2pELE1BQU1uQixjQUFjdUMsU0FBUzVCLElBQUksQ0FBQ2lDLGtCQUFrQjtvQkFDcEQsSUFBRzVDLGVBQWUsUUFBUUEsZUFBZSxRQUFPO3dCQUM5Q0MsZUFBZTt3QkFDZlIsa0JBQWtCO3dCQUNsQmMsbUJBQW1COzRCQUFDOzRCQUFxQjs0QkFBZ0M7eUJBQXFDO3dCQUM5R1UsV0FBVzs0QkFDVEwsT0FBT08sT0FBTyxDQUFDO3dCQUNqQixHQUFHO29CQUNMLE9BQU87d0JBQ0xGLFdBQVc7NEJBQ1R4QixrQkFBa0I7NEJBQ2xCYyxtQkFBbUI7Z0NBQUM7Z0NBQXFCO2dDQUE4Qjs2QkFBaUI7NEJBQ3hGc0MsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQm5DOzRCQUN2Q0MsT0FBT08sT0FBTyxDQUFDO3dCQUNqQixHQUFHO29CQUNMO2dCQUNGO1lBQ0YsT0FBTztnQkFDTDVCLFNBQVM7Z0JBQ1RGLFdBQVc7Z0JBQ1g7WUFDRjtRQUNGLEVBQUUsT0FBTzBELEtBQWM7WUFDckIsNkVBQTZFO1lBQzdFMUQsV0FBVztZQUNYd0QsUUFBUXZELEtBQUssQ0FBQyxzQkFBc0J5RDtZQUNwQyxNQUFNQyxlQUFlcEUscURBQWVBLENBQUNtRTtZQUNyQ3hELFNBQVN5RDtZQUNULDhEQUE4RDtZQUM5RDtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyw0QkFBNEIsQ0FBQ2xCO1FBQ2pDQSxFQUFFTSxjQUFjO1FBQ2hCaEMseUJBQXlCO1FBQ3pCRixrQkFBa0I7UUFDbEJjLFdBQVc7WUFDVEwsT0FBT3NDLElBQUksQ0FBQztRQUNkLEdBQUc7SUFDTDtJQUVBLCtGQUErRjtJQUMvRixJQUFJLENBQUNwRCxZQUFZVixXQUFXZ0IsdUJBQXVCO1FBRWpELHFCQUNFLDhEQUFDekIsNERBQWNBO1lBQ2J3RSxXQUFXO1lBQ1hqRCxnQkFBZ0JBLGtCQUFtQixFQUFDSixXQUFXLGVBQWUsZUFBYztZQUM1RXNELG1CQUFrQjtZQUNsQjlDLGlCQUFpQmxCLFVBQVVrQixrQkFBa0J1QjtZQUM3Q3dCLGNBQWNqRTtzQkFFZCw0RUFBQ2tFOzs7Ozs7Ozs7O0lBR1A7SUFFQSxxQkFDUSw4REFBQzdFLHdEQUFVQTtRQUNUOEUsT0FBTTtRQUNOQyx3QkFDRTs7Z0JBQUU7Z0JBQ29COzhCQUNwQiw4REFBQ0M7b0JBQUtDLFdBQVU7OEJBQWlDOzs7Ozs7OztRQUtyRDlELGtCQUFrQjs7WUFFbkJOLHVCQUNDLDhEQUFDWiwyREFBYUE7Z0JBQ1ppRixNQUFLO2dCQUNMN0MsU0FBU3hCO2dCQUNUb0UsV0FBVTtnQkFDVkUsYUFBYTtnQkFDYkMsV0FBVyxJQUFNdEUsU0FBUzs7Ozs7O1lBSTdCQyxnQ0FDQyw4REFBQ2QsMkRBQWFBO2dCQUNaaUYsTUFBSztnQkFDTDdDLFNBQVN0QjtnQkFDVGtFLFdBQVU7Z0JBQ1ZFLGFBQWE7Z0JBQ2JDLFdBQVcsSUFBTXBFLGtCQUFrQjs7Ozs7OzBCQUl2Qyw4REFBQ3FFO2dCQUFLSixXQUFVO2dCQUE2Q0ssVUFBVTNCOztrQ0FDckUsOERBQUNrQjt3QkFBSUksV0FBVTs7MENBQ2IsOERBQUNNO2dDQUFNQyxTQUFRO2dDQUFRUCxXQUFVOzBDQUE2RDs7Ozs7OzBDQUc5Riw4REFBQ0o7Z0NBQUlJLFdBQVU7MENBQ2IsNEVBQUNRO29DQUNDQyxJQUFHO29DQUNIQyxNQUFLO29DQUNMVCxNQUFLO29DQUNMVSxjQUFhO29DQUNiQyxRQUFRO29DQUNSdEMsT0FBT2xEO29DQUNQeUYsVUFBVXpDO29DQUNWNEIsV0FBVyx3U0FJVixPQUhDaEUsWUFBWVosS0FBSyxHQUNiLDZGQUNBO29DQUVOMEYsYUFBWTs7Ozs7Ozs7Ozs7NEJBR2Y5RSxZQUFZWixLQUFLLGtCQUNoQiw4REFBQzJGO2dDQUFFZixXQUFVOztrREFDWCw4REFBQ2dCO3dDQUFJaEIsV0FBVTt3Q0FBZWlCLE1BQUs7d0NBQWVDLFNBQVE7a0RBQ3hELDRFQUFDQzs0Q0FBS0MsVUFBUzs0Q0FBVUMsR0FBRTs0Q0FBb0hDLFVBQVM7Ozs7Ozs7Ozs7O29DQUV6SnRGLFlBQVlaLEtBQUs7Ozs7Ozs7Ozs7Ozs7a0NBS3hCLDhEQUFDd0U7d0JBQUlJLFdBQVU7OzBDQUNiLDhEQUFDTTtnQ0FBTUMsU0FBUTtnQ0FBV1AsV0FBVTswQ0FBNkQ7Ozs7OzswQ0FHakcsOERBQUNKO2dDQUFJSSxXQUFVOzBDQUNiLDRFQUFDUTtvQ0FDQ0MsSUFBRztvQ0FDSEMsTUFBSztvQ0FDTFQsTUFBSztvQ0FDTFUsY0FBYTtvQ0FDYkMsUUFBUTtvQ0FDUnRDLE9BQU9oRDtvQ0FDUHVGLFVBQVVwQztvQ0FDVnVCLFdBQVcsd1NBSVYsT0FIQ2hFLFlBQVlWLFFBQVEsR0FDaEIsNkZBQ0E7b0NBRU53RixhQUFZOzs7Ozs7Ozs7Ozs0QkFHZjlFLFlBQVlWLFFBQVEsa0JBQ25CLDhEQUFDeUY7Z0NBQUVmLFdBQVU7O2tEQUNYLDhEQUFDZ0I7d0NBQUloQixXQUFVO3dDQUFlaUIsTUFBSzt3Q0FBZUMsU0FBUTtrREFDeEQsNEVBQUNDOzRDQUFLQyxVQUFTOzRDQUFVQyxHQUFFOzRDQUFvSEMsVUFBUzs7Ozs7Ozs7Ozs7b0NBRXpKdEYsWUFBWVYsUUFBUTs7Ozs7Ozs7Ozs7OztvQkFNMUIsQ0FBQ1ksa0NBQ0EsOERBQUMwRDt3QkFBSUksV0FBVTs7MENBQ2IsOERBQUNKO2dDQUFJSSxXQUFVOztrREFDYiw4REFBQ1E7d0NBQ0NDLElBQUc7d0NBQ0hDLE1BQUs7d0NBQ0xULE1BQUs7d0NBQ0xzQixTQUFTL0Y7d0NBQ1RxRixVQUFVLENBQUN4QyxJQUFNNUMsY0FBYzRDLEVBQUVFLE1BQU0sQ0FBQ2dELE9BQU87d0NBQy9DdkIsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDTTt3Q0FBTUMsU0FBUTt3Q0FBY1AsV0FBVTtrREFBc0Q7Ozs7Ozs7Ozs7OzswQ0FLL0YsOERBQUNKO2dDQUFJSSxXQUFVOzBDQUNiLDRFQUFDd0I7b0NBQ0NDLE1BQUs7b0NBQ0xDLFNBQVNuQztvQ0FDVFMsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1AsOERBQUNKO3dCQUFJSSxXQUFVO2tDQUNiLDRFQUFDMkI7NEJBQ0MxQixNQUFLOzRCQUNMMkIsVUFBVWxHOzRCQUNWc0UsV0FBVTtzQ0FFVHRFLHdCQUNDOztrREFDRSw4REFBQ2tFO3dDQUFJSSxXQUFVOzs7Ozs7b0NBQXVFOzs2REFJeEY7O2tEQUNFLDhEQUFDZ0I7d0NBQUloQixXQUFVO3dDQUFlaUIsTUFBSzt3Q0FBT1ksUUFBTzt3Q0FBZVgsU0FBUTtrREFDdEUsNEVBQUNDOzRDQUFLVyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBWTs0Q0FBSVgsR0FBRTs7Ozs7Ozs7Ozs7b0NBQ2pFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUzFCO0dBbFhTbEc7O1FBY3lETCwwREFBT0E7UUFDeERGLHNEQUFTQTtRQUNIQyw0REFBZUE7OztLQWhCN0JNO0FBb1hNLFNBQVM4RztJQUN0QjlDLFFBQVFDLEdBQUcsQ0FBQztJQUNaLHFCQUNFLDhEQUFDekUsMkNBQVFBO1FBQUN1SCx3QkFDUiw4REFBQ3RDO1lBQUlJLFdBQVU7OzhCQUNiLDhEQUFDSjtvQkFBSUksV0FBVTs7Ozs7OzhCQUNmLDhEQUFDZTtvQkFBRWYsV0FBVTs4QkFBcUI7Ozs7Ozs7Ozs7OztrQkFHcEMsNEVBQUM3RTs7Ozs7Ozs7OztBQUdQO01BWndCOEciLCJzb3VyY2VzIjpbIkQ6XFxNZW1vcnkgQnVzaW5lc3MgU29sdXRpb2luc1xcUHJvamVjdHNcXE1BQ1JBXFxwcm9qZWN0XFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxhcHBcXGF1dGhcXGxvZ2luXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IEF1dGhMYXlvdXQsIFN0YXR1c01lc3NhZ2UsIFBhZ2VUcmFuc2l0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgnO1xyXG5pbXBvcnQgeyBnZXRFcnJvck1lc3NhZ2UgfSBmcm9tICdAL2xpYic7XHJcblxyXG5cclxuZnVuY3Rpb24gTG9naW5Gb3JtKCkge1xyXG4gIGNvbnN0IFtlbWFpbCwgc2V0RW1haWxdID0gdXNlU3RhdGUoJycpO1xyXG4gIGNvbnN0IFtwYXNzd29yZCwgc2V0UGFzc3dvcmRdID0gdXNlU3RhdGUoJycpO1xyXG4gIGNvbnN0IFtyZW1lbWJlck1lLCBzZXRSZW1lbWJlck1lXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW3N1Y2Nlc3NNZXNzYWdlLCBzZXRTdWNjZXNzTWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2ZpZWxkRXJyb3JzLCBzZXRGaWVsZEVycm9yc10gPSB1c2VTdGF0ZTx7ZW1haWw/OiBzdHJpbmc7IHBhc3N3b3JkPzogc3RyaW5nfT4oe30pO1xyXG4gIGNvbnN0IFtpc0N1c3RvbWVyUG9ydGFsLCBzZXRJc0N1c3RvbWVyUG9ydGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNDbGllbnQsIHNldElzQ2xpZW50XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcmVxdWlyZXMyRkEsIHNldFJlcXVpcmVzMkZBXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbbG9hZGluZ01lc3NhZ2UsIHNldExvYWRpbmdNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbZm9yZ290UGFzc3dvcmRMb2FkaW5nLCBzZXRGb3Jnb3RQYXNzd29yZExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtkeW5hbWljTWVzc2FnZXMsIHNldER5bmFtaWNNZXNzYWdlc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oWydDb25uZWN0aW5nIHRvIHN0YWZmIHBvcnRhbC4uLiddKTtcclxuICBjb25zdCB7IGxvZ2luLCBpc0F1dGhlbnRpY2F0ZWQsIGxvYWRpbmc6IHN0YWZmTG9hZGluZywgdXNlciB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xyXG5cclxuICAvLyBDbGllbnQtc2lkZSBpbml0aWFsaXphdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRJc0NsaWVudCh0cnVlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBDaGVjayBpZiB3ZSdyZSBvbiBzdGFmZiBwb3J0YWwgLSBmaXggdGhlIGxvZ2ljIGNvbXBsZXRlbHlcclxuXHJcbiAgICBjb25zdCBtZXNzYWdlID0gc2VhcmNoUGFyYW1zLmdldCgnbWVzc2FnZScpO1xyXG4gICAgaWYgKG1lc3NhZ2UpIHtcclxuICAgICAgc2V0U3VjY2Vzc01lc3NhZ2UobWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQWRkIHRpbWVvdXQgdG8gcHJldmVudCBpbmZpbml0ZSBsb2FkaW5nIC0gYnV0IGRvbid0IHJlZGlyZWN0IG9uIHRpbWVvdXRcclxuICAgIGNvbnN0IGxvYWRpbmdUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGlmIChsb2FkaW5nICYmICFlcnJvcikge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9LCAxMDAwMCk7IC8vIDEwIHNlY29uZCB0aW1lb3V0XHJcblxyXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dChsb2FkaW5nVGltZW91dCk7XHJcbiAgfSwgW3NlYXJjaFBhcmFtcywgbG9hZGluZywgaXNDbGllbnQsIGVycm9yLCBpc0N1c3RvbWVyUG9ydGFsLCByb3V0ZXJdKTtcclxuXHJcbiAgLy8gUmVkaXJlY3QgaWYgYWxyZWFkeSBhdXRoZW50aWNhdGVkIC0gb25seSB3aXRoaW4gc3RhZmYgcG9ydGFsLCBuZXZlciB0byBjdXN0b21lclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBEb24ndCByZWRpcmVjdCBpbiBhbnkgb2YgdGhlc2UgY29uZGl0aW9uczpcclxuICAgIGlmIChyZXF1aXJlczJGQSkgcmV0dXJuO1xyXG4gICAgaWYgKGVycm9yKSByZXR1cm47IC8vIEV4cGxpY2l0IGVycm9yIGNoZWNrIGZpcnN0XHJcbiAgICBpZiAobG9hZGluZyB8fCBzdGFmZkxvYWRpbmcpIHJldHVybjsgLy8gRG9uJ3QgcmVkaXJlY3QgZHVyaW5nIGxvYWRpbmdcclxuICAgIGlmICghaXNDbGllbnQpIHJldHVybjsgLy8gV2FpdCBmb3IgY2xpZW50LXNpZGUgaHlkcmF0aW9uXHJcbiAgICBpZiAoaXNDdXN0b21lclBvcnRhbCkgcmV0dXJuOyAvLyBPbmx5IHJlZGlyZWN0IGluIHN0YWZmIHBvcnRhbFxyXG5cclxuICAgIC8vIE9ubHkgcmVkaXJlY3QgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkIGFuZCBubyBlcnJvcnNcclxuICAgIGlmIChpc0F1dGhlbnRpY2F0ZWQgJiYgIWVycm9yKSB7XHJcbiAgICAgIHJvdXRlci5yZXBsYWNlKCcvZGFzaGJvYXJkJyk7XHJcbiAgICB9XHJcbiAgfSwgW3JlcXVpcmVzMkZBLCBpc0N1c3RvbWVyUG9ydGFsLCBpc0F1dGhlbnRpY2F0ZWQsIHN0YWZmTG9hZGluZywgbG9hZGluZywgcm91dGVyLCBlcnJvciwgaXNDbGllbnRdKTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVFbWFpbCA9IChlbWFpbDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCA9PiB7XHJcbiAgICBpZiAoIWVtYWlsLnRyaW0oKSkge1xyXG4gICAgICByZXR1cm4gJ0VtYWlsIGFkZHJlc3MgaXMgcmVxdWlyZWQnO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLztcclxuICAgIGlmICghZW1haWxSZWdleC50ZXN0KGVtYWlsKSkge1xyXG4gICAgICByZXR1cm4gJ1BsZWFzZSBlbnRlciBhIHZhbGlkIGVtYWlsIGFkZHJlc3MnO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVQYXNzd29yZCA9IChwYXNzd29yZDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCA9PiB7XHJcbiAgICBpZiAoIXBhc3N3b3JkKSB7XHJcbiAgICAgIHJldHVybiAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChwYXNzd29yZC5sZW5ndGggPCA4KSB7XHJcbiAgICAgIHJldHVybiAnUGFzc3dvcmQgbXVzdCBiZSBhdCBsZWFzdCA4IGNoYXJhY3RlcnMgbG9uZyc7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IHN0cmluZyB8IG51bGwgPT4ge1xyXG4gICAgY29uc3QgZW1haWxFcnJvciA9IHZhbGlkYXRlRW1haWwoZW1haWwpO1xyXG4gICAgY29uc3QgcGFzc3dvcmRFcnJvciA9IHZhbGlkYXRlUGFzc3dvcmQocGFzc3dvcmQpO1xyXG5cclxuICAgIHNldEZpZWxkRXJyb3JzKHtcclxuICAgICAgZW1haWw6IGVtYWlsRXJyb3IgfHwgdW5kZWZpbmVkLFxyXG4gICAgICBwYXNzd29yZDogcGFzc3dvcmRFcnJvciB8fCB1bmRlZmluZWQsXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoZW1haWxFcnJvcikgcmV0dXJuIGVtYWlsRXJyb3I7XHJcbiAgICBpZiAocGFzc3dvcmRFcnJvcikgcmV0dXJuIHBhc3N3b3JkRXJyb3I7XHJcblxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRW1haWxDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHZhbHVlID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICBzZXRFbWFpbCh2YWx1ZSk7XHJcblxyXG4gICAgLy8gQ2xlYXIgZmllbGQgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcclxuICAgIGlmIChmaWVsZEVycm9ycy5lbWFpbCkge1xyXG4gICAgICBzZXRGaWVsZEVycm9ycyhwcmV2ID0+ICh7IC4uLnByZXYsIGVtYWlsOiB1bmRlZmluZWQgfSkpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENsZWFyIGdlbmVyYWwgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmcgKHdpdGggc2xpZ2h0IGRlbGF5IHRvIGxldCB1c2VyIHNlZSB0aGUgZXJyb3IpXHJcbiAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgc2V0RXJyb3IoJycpO1xyXG4gICAgICB9LCAxMDApO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkQ2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XHJcbiAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xyXG4gICAgc2V0UGFzc3dvcmQodmFsdWUpO1xyXG5cclxuICAgIC8vIENsZWFyIGZpZWxkIGVycm9yIHdoZW4gdXNlciBzdGFydHMgdHlwaW5nXHJcbiAgICBpZiAoZmllbGRFcnJvcnMucGFzc3dvcmQpIHtcclxuICAgICAgc2V0RmllbGRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBwYXNzd29yZDogdW5kZWZpbmVkIH0pKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhciBnZW5lcmFsIGVycm9yIHdoZW4gdXNlciBzdGFydHMgdHlwaW5nICh3aXRoIHNsaWdodCBkZWxheSB0byBsZXQgdXNlciBzZWUgdGhlIGVycm9yKVxyXG4gICAgaWYgKGVycm9yKSB7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNldEVycm9yKCcnKTtcclxuICAgICAgfSwgMTAwKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG5cclxuICAgIC8vIENsZWFyIHByZXZpb3VzIG1lc3NhZ2VzXHJcbiAgICBzZXRFcnJvcignJyk7XHJcbiAgICBzZXRTdWNjZXNzTWVzc2FnZSgnJyk7XHJcblxyXG4gICAgLy8gVmFsaWRhdGUgZm9ybSBiZWZvcmUgc3VibWlzc2lvblxyXG4gICAgY29uc3QgdmFsaWRhdGlvbkVycm9yID0gdmFsaWRhdGVGb3JtKCk7XHJcbiAgICBpZiAodmFsaWRhdGlvbkVycm9yKSB7XHJcbiAgICAgIHNldEVycm9yKHZhbGlkYXRpb25FcnJvcik7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEFsd2F5cyB0cmVhdCB0aGlzIGFzIHN0YWZmIGxvZ2luIHNpbmNlIHdlJ3JlIG9uIHN0YWZmIHBvcnRhbFxyXG4gICAgICBzZXREeW5hbWljTWVzc2FnZXMoWydWZXJpZnlpbmcgeW91ciBjcmVkZW50aWFscy4uLicsICdQbGVhc2Ugd2FpdC4uLiddKTtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsb2dpbihlbWFpbC50cmltKCkudG9Mb3dlckNhc2UoKSwgcGFzc3dvcmQsIHJlbWVtYmVyTWUpO1xyXG4gICAgICBpZiAocmVzcG9uc2UpIHtcclxuICAgICAgICAvLyBDaGVjayBpZiBlbWFpbCB2ZXJpZmljYXRpb24gaXMgcmVxdWlyZWQgKGZvciB1bnZlcmlmaWVkIGFjY291bnRzKVxyXG4gICAgICAgIGNvbnN0IHJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24gPSByZXNwb25zZS5yZXF1aXJlc18yZmEgJiYgcmVzcG9uc2UuYWN0aW9uID09PSAncmVnaXN0ZXInO1xyXG5cclxuICAgICAgICBpZiAocmVxdWlyZXNFbWFpbFZlcmlmaWNhdGlvbikge1xyXG4gICAgICAgICAgc2V0UmVxdWlyZXMyRkEodHJ1ZSk7XHJcbiAgICAgICAgICBzZXRTdWNjZXNzTWVzc2FnZSgnRW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkLiBQbGVhc2UgY2hlY2sgeW91ciBlbWFpbCBmb3IgdGhlIHZlcmlmaWNhdGlvbiBjb2RlLicpO1xyXG4gICAgICAgICAgc2V0RHluYW1pY01lc3NhZ2VzKFsnRW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkIScsICdWZXJpZmljYXRpb24gY29kZSBzZW50Li4uJywgJ1JlZGlyZWN0aW5nIHRvIHZlcmlmaWNhdGlvbi4uLiddKTtcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2F1dGgvdmVyaWZ5LTJmYScpO1xyXG4gICAgICAgICAgfSwgMTUwMCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIENoZWNrIGlmIDJGQSBpcyByZXF1aXJlZCBmb3IgdmVyaWZpZWQgYWNjb3VudHNcclxuICAgICAgICAgIGNvbnN0IHJlcXVpcmVzMkZBID0gcmVzcG9uc2UudXNlci50d29fZmFjdG9yX2VuYWJsZWQ7XHJcbiAgICAgICAgICBpZihyZXF1aXJlczJGQSA9PSB0cnVlIHx8IHJlcXVpcmVzMkZBID09ICd0cnVlJyl7XHJcbiAgICAgICAgICAgIHNldFJlcXVpcmVzMkZBKHRydWUpO1xyXG4gICAgICAgICAgICBzZXRTdWNjZXNzTWVzc2FnZSgnTG9naW4gc3VjY2Vzc2Z1bCEgVHdvLWZhY3RvciBhdXRoZW50aWNhdGlvbiBpcyBlbmFibGVkIGZvciB5b3VyIGFjY291bnQuIFBsZWFzZSBjaGVjayB5b3VyIGVtYWlsIGZvciB0aGUgdmVyaWZpY2F0aW9uIGNvZGUuJyk7XHJcbiAgICAgICAgICAgIHNldER5bmFtaWNNZXNzYWdlcyhbJ0xvZ2luIHN1Y2Nlc3NmdWwhJywgJ1NlbmRpbmcgdmVyaWZpY2F0aW9uIGNvZGUuLi4nLCAnUmVkaXJlY3RpbmcgdG8gMkZBIHZlcmlmaWNhdGlvbi4uLiddKTtcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoJy9hdXRoL3ZlcmlmeS0yZmEnKTtcclxuICAgICAgICAgICAgfSwgMTUwMCk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICBzZXRTdWNjZXNzTWVzc2FnZSgnTG9naW4gc3VjY2Vzc2Z1bCEgUmVkaXJlY3RpbmcgdG8geW91ciBkYXNoYm9hcmQuLi4nKTtcclxuICAgICAgICAgICAgICBzZXREeW5hbWljTWVzc2FnZXMoWydMb2dpbiBzdWNjZXNzZnVsIScsICdTZXR0aW5nIHVwIHlvdXIgc2Vzc2lvbi4uLicsICdSZWRpcmVjdGluZy4uLiddKTtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIj09PT09PT09PT09PT0gPT09PT09PT09XCIsIHVzZXIpO1xyXG4gICAgICAgICAgICAgIHJvdXRlci5yZXBsYWNlKCcvZGFzaGJvYXJkJyk7XHJcbiAgICAgICAgICAgIH0sIDMwMDApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRFcnJvcignSW52YWxpZCB1c2VyIHNlc3Npb24uIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnI6IHVua25vd24pIHtcclxuICAgICAgLy8gQXV0aFNlcnZpY2Ugbm93IHByb3ZpZGVzIGNsZWFuIGVycm9yIG1lc3NhZ2VzLCBzbyB3ZSBjYW4gdXNlIHRoZW0gZGlyZWN0bHlcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0YWZmIGxvZ2luIGVycm9yOicsIGVycilcclxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZ2V0RXJyb3JNZXNzYWdlKGVycik7XHJcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XHJcbiAgICAgIC8vIFJldHVybiBlYXJseSAtIHN0YXkgb24gbG9naW4gcGFnZSB3aGVuIGF1dGhlbnRpY2F0aW9uIGZhaWxzXHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVGb3Jnb3RQYXNzd29yZENsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcclxuICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgIHNldEZvcmdvdFBhc3N3b3JkTG9hZGluZyh0cnVlKTtcclxuICAgIHNldExvYWRpbmdNZXNzYWdlKCdSZWRpcmVjdGluZyB0byBmb3Jnb3QgcGFzc3dvcmQuLi4nKTtcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICByb3V0ZXIucHVzaCgnL2F1dGgvZm9yZ290LXBhc3N3b3JkJyk7XHJcbiAgICB9LCAzMDAwKTtcclxuICB9O1xyXG5cclxuICAvLyBTaG93IGxvYWRpbmcgd2hpbGUgY2xpZW50IGlzIGluaXRpYWxpemluZywgY2hlY2tpbmcgYXV0aGVudGljYXRpb24sIG9yIGR1cmluZyBsb2dpbi9yZWRpcmVjdFxyXG4gIGlmICghaXNDbGllbnQgfHwgbG9hZGluZyB8fCBmb3Jnb3RQYXNzd29yZExvYWRpbmcpIHtcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8UGFnZVRyYW5zaXRpb25cclxuICAgICAgICBpc0xvYWRpbmc9e3RydWV9XHJcbiAgICAgICAgbG9hZGluZ01lc3NhZ2U9e2xvYWRpbmdNZXNzYWdlIHx8ICghaXNDbGllbnQgPyAnTG9hZGluZy4uLicgOiAnU2lnbmluZyBpbi4uLicpfVxyXG4gICAgICAgIGxvYWRpbmdTdWJtZXNzYWdlPVwiUGxlYXNlIHdhaXQgd2hpbGUgd2UgcHJvY2VzcyB5b3VyIHJlcXVlc3RcIlxyXG4gICAgICAgIGR5bmFtaWNNZXNzYWdlcz17bG9hZGluZyA/IGR5bmFtaWNNZXNzYWdlcyA6IHVuZGVmaW5lZH1cclxuICAgICAgICBzaG93UHJvZ3Jlc3M9e2xvYWRpbmd9XHJcbiAgICAgID5cclxuICAgICAgICA8ZGl2IC8+XHJcbiAgICAgIDwvUGFnZVRyYW5zaXRpb24+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgICAgICAgIDxBdXRoTGF5b3V0XHJcbiAgICAgICAgICAgIHRpdGxlPVwiV2VsY29tZSBCYWNrIVwiXHJcbiAgICAgICAgICAgIHN1YnRpdGxlPXsoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIFN0YWZmIFBvcnRhbCBBY2Nlc3N7JyAnfVxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIOKAoiBTZWN1cmUgRGFzaGJvYXJkIExvZ2luXHJcbiAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIGlzQ3VzdG9tZXJQb3J0YWw9e2ZhbHNlfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAge2Vycm9yICYmIChcclxuICAgICAgICAgICAgPFN0YXR1c01lc3NhZ2VcclxuICAgICAgICAgICAgICB0eXBlPVwiZXJyb3JcIlxyXG4gICAgICAgICAgICAgIG1lc3NhZ2U9e2Vycm9yfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTRcIlxyXG4gICAgICAgICAgICAgIGRpc21pc3NpYmxlPXt0cnVlfVxyXG4gICAgICAgICAgICAgIG9uRGlzbWlzcz17KCkgPT4gc2V0RXJyb3IoJycpfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7c3VjY2Vzc01lc3NhZ2UgJiYgKFxyXG4gICAgICAgICAgICA8U3RhdHVzTWVzc2FnZVxyXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWNjZXNzXCJcclxuICAgICAgICAgICAgICBtZXNzYWdlPXtzdWNjZXNzTWVzc2FnZX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi00XCJcclxuICAgICAgICAgICAgICBkaXNtaXNzaWJsZT17dHJ1ZX1cclxuICAgICAgICAgICAgICBvbkRpc21pc3M9eygpID0+IHNldFN1Y2Nlc3NNZXNzYWdlKCcnKX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgPGZvcm0gY2xhc3NOYW1lPVwic3BhY2UteS02IGFuaW1hdGUtZmFkZUluIGFuaW1hdGUtZGVsYXktMjAwXCIgb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zbGlkZUluRnJvbUJvdHRvbSBhbmltYXRlLWRlbGF5LTMwMFwiPlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZW1haWxcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XHJcbiAgICAgICAgICAgICAgICBFbWFpbCBhZGRyZXNzXHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTFcIj5cclxuICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICBpZD1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgbmFtZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgYXV0b0NvbXBsZXRlPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVFbWFpbENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYXBwZWFyYW5jZS1ub25lIGJsb2NrIHctZnVsbCBweC00IHB5LTMgYm9yZGVyLTIgcm91bmRlZC1tZCBzaGFkb3ctc20gcGxhY2Vob2xkZXItZ3JheS00MDAgZGFyazpwbGFjZWhvbGRlci1ncmF5LTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIHNtOnRleHQtc20gYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS02MDAgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDAgZmllbGQtZm9jdXMtcmluZyB0cmFuc2l0aW9uLXNtb290aCAke1xyXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkRXJyb3JzLmVtYWlsXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcmVkLTMwMCBkYXJrOmJvcmRlci1yZWQtNjAwIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCBhbmltYXRlLXNoYWtlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCdcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAge2ZpZWxkRXJyb3JzLmVtYWlsICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDAgZmxleCBpdGVtcy1jZW50ZXIgYW5pbWF0ZS1zbGlkZUluRnJvbVRvcFwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xOCAxMGE4IDggMCAxMS0xNiAwIDggOCAwIDAxMTYgMHptLTcgNGExIDEgMCAxMS0yIDAgMSAxIDAgMDEyIDB6bS0xLTlhMSAxIDAgMDAtMSAxdjRhMSAxIDAgMTAyIDBWNmExIDEgMCAwMC0xLTF6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgIHtmaWVsZEVycm9ycy5lbWFpbH1cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zbGlkZUluRnJvbUJvdHRvbSBhbmltYXRlLWRlbGF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicGFzc3dvcmRcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XHJcbiAgICAgICAgICAgICAgICBQYXNzd29yZFxyXG4gICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgaWQ9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cImN1cnJlbnQtcGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmR9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQYXNzd29yZENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYXBwZWFyYW5jZS1ub25lIGJsb2NrIHctZnVsbCBweC00IHB5LTMgYm9yZGVyLTIgcm91bmRlZC1tZCBzaGFkb3ctc20gcGxhY2Vob2xkZXItZ3JheS00MDAgZGFyazpwbGFjZWhvbGRlci1ncmF5LTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIHNtOnRleHQtc20gYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS02MDAgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDAgZmllbGQtZm9jdXMtcmluZyB0cmFuc2l0aW9uLXNtb290aCAke1xyXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkRXJyb3JzLnBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcmVkLTMwMCBkYXJrOmJvcmRlci1yZWQtNjAwIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCBhbmltYXRlLXNoYWtlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCdcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIHtmaWVsZEVycm9ycy5wYXNzd29yZCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwIGZsZXggaXRlbXMtY2VudGVyIGFuaW1hdGUtc2xpZGVJbkZyb21Ub3BcIj5cclxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTggMTBhOCA4IDAgMTEtMTYgMCA4IDggMCAwMTE2IDB6bS03IDRhMSAxIDAgMTEtMiAwIDEgMSAwIDAxMiAwem0tMS05YTEgMSAwIDAwLTEgMXY0YTEgMSAwIDEwMiAwVjZhMSAxIDAgMDAtMS0xelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICB7ZmllbGRFcnJvcnMucGFzc3dvcmR9XHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogU2hvdyBSZW1lbWJlciBNZSBhbmQgRm9yZ290IFBhc3N3b3JkIG9ubHkgZm9yIFN0YWZmIFBvcnRhbCAqL31cclxuICAgICAgICAgICAgeyFpc0N1c3RvbWVyUG9ydGFsICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBhbmltYXRlLWZhZGVJbiBhbmltYXRlLWRlbGF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICBpZD1cInJlbWVtYmVyLW1lXCJcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwicmVtZW1iZXItbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17cmVtZW1iZXJNZX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFJlbWVtYmVyTWUoZS50YXJnZXQuY2hlY2tlZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC02MDAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmVkLTUwMCBib3JkZXItMiBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIHRyYW5zaXRpb24tc21vb3RoXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJyZW1lbWJlci1tZVwiIGNsYXNzTmFtZT1cIm1sLTIgYmxvY2sgdGV4dC1zbSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIFJlbWVtYmVyIG1lXHJcbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgPGFcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2F1dGgvZm9yZ290LXBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVGb3Jnb3RQYXNzd29yZENsaWNrfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC01MDAgZGFyazp0ZXh0LXJlZC00MDAgZGFyazpob3Zlcjp0ZXh0LXJlZC0zMDAgdHJhbnNpdGlvbi1jb2xvcnMgaG92ZXI6dW5kZXJsaW5lIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEZvcmdvdCB5b3VyIHBhc3N3b3JkP1xyXG4gICAgICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zbGlkZUluRnJvbUJvdHRvbSBhbmltYXRlLWRlbGF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgcHktMyBweC00IGJvcmRlci0yIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIHNoYWRvdy1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLXJlZC01MDAgZGFyazpmb2N1czpyaW5nLW9mZnNldC1ncmF5LTgwMCBidXR0b24taG92ZXItbGlmdCB0cmFuc2l0aW9uLXNtb290aCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGUgbXItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ25pbmcgaW4uLi5cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZD1cIk0xMSAxNmwtNC00bTAgMGw0LTRtLTQgNGgxNG0tNSA0djFhMyAzIDAgMDEtMyAzSDZhMyAzIDAgMDEtMyAzdjFcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ24gaW5cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZm9ybT5cclxuICAgIDwvQXV0aExheW91dD5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dpblBhZ2UoKSB7XHJcbiAgY29uc29sZS5sb2coJ0xvZ2luUGFnZSBjb21wb25lbnQgcmVuZGVyaW5nLi4uJyk7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItcmVkLTYwMFwiPjwvZGl2PlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgbG9naW4gcGFnZS4uLjwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICB9PlxyXG4gICAgICA8TG9naW5Gb3JtIC8+XHJcbiAgICA8L1N1c3BlbnNlPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTdXNwZW5zZSIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZUF1dGgiLCJBdXRoTGF5b3V0IiwiU3RhdHVzTWVzc2FnZSIsIlBhZ2VUcmFuc2l0aW9uIiwiZ2V0RXJyb3JNZXNzYWdlIiwiTG9naW5Gb3JtIiwiZW1haWwiLCJzZXRFbWFpbCIsInBhc3N3b3JkIiwic2V0UGFzc3dvcmQiLCJyZW1lbWJlck1lIiwic2V0UmVtZW1iZXJNZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3NNZXNzYWdlIiwic2V0U3VjY2Vzc01lc3NhZ2UiLCJmaWVsZEVycm9ycyIsInNldEZpZWxkRXJyb3JzIiwiaXNDdXN0b21lclBvcnRhbCIsInNldElzQ3VzdG9tZXJQb3J0YWwiLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwicmVxdWlyZXMyRkEiLCJzZXRSZXF1aXJlczJGQSIsImxvYWRpbmdNZXNzYWdlIiwic2V0TG9hZGluZ01lc3NhZ2UiLCJmb3Jnb3RQYXNzd29yZExvYWRpbmciLCJzZXRGb3Jnb3RQYXNzd29yZExvYWRpbmciLCJkeW5hbWljTWVzc2FnZXMiLCJzZXREeW5hbWljTWVzc2FnZXMiLCJsb2dpbiIsImlzQXV0aGVudGljYXRlZCIsInN0YWZmTG9hZGluZyIsInVzZXIiLCJyb3V0ZXIiLCJzZWFyY2hQYXJhbXMiLCJtZXNzYWdlIiwiZ2V0IiwibG9hZGluZ1RpbWVvdXQiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwicmVwbGFjZSIsInZhbGlkYXRlRW1haWwiLCJ0cmltIiwiZW1haWxSZWdleCIsInRlc3QiLCJ2YWxpZGF0ZVBhc3N3b3JkIiwibGVuZ3RoIiwidmFsaWRhdGVGb3JtIiwiZW1haWxFcnJvciIsInBhc3N3b3JkRXJyb3IiLCJ1bmRlZmluZWQiLCJoYW5kbGVFbWFpbENoYW5nZSIsImUiLCJ2YWx1ZSIsInRhcmdldCIsInByZXYiLCJoYW5kbGVQYXNzd29yZENoYW5nZSIsImhhbmRsZVN1Ym1pdCIsInByZXZlbnREZWZhdWx0IiwidmFsaWRhdGlvbkVycm9yIiwicmVzcG9uc2UiLCJ0b0xvd2VyQ2FzZSIsInJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24iLCJyZXF1aXJlc18yZmEiLCJhY3Rpb24iLCJ0d29fZmFjdG9yX2VuYWJsZWQiLCJjb25zb2xlIiwibG9nIiwiZXJyIiwiZXJyb3JNZXNzYWdlIiwiaGFuZGxlRm9yZ290UGFzc3dvcmRDbGljayIsInB1c2giLCJpc0xvYWRpbmciLCJsb2FkaW5nU3VibWVzc2FnZSIsInNob3dQcm9ncmVzcyIsImRpdiIsInRpdGxlIiwic3VidGl0bGUiLCJzcGFuIiwiY2xhc3NOYW1lIiwidHlwZSIsImRpc21pc3NpYmxlIiwib25EaXNtaXNzIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJpZCIsIm5hbWUiLCJhdXRvQ29tcGxldGUiLCJyZXF1aXJlZCIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJwIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJjaGVja2VkIiwiYSIsImhyZWYiLCJvbkNsaWNrIiwiYnV0dG9uIiwiZGlzYWJsZWQiLCJzdHJva2UiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsIkxvZ2luUGFnZSIsImZhbGxiYWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ })

});