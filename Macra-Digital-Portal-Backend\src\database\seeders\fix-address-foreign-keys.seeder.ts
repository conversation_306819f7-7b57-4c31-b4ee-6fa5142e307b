import { DataSource } from 'typeorm';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class FixAddressForeignKeysSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    console.log('🔧 Fixing address foreign key constraints...');
    
    try {
      // First, let's check what users exist
      const userResult = await dataSource.query('SELECT user_id FROM users LIMIT 5');
      console.log('Available users:', userResult);

      // Check addresses with invalid updated_by references
      const invalidUpdatedByResult = await dataSource.query(`
        SELECT a.address_id, a.updated_by 
        FROM addresses a 
        LEFT JOIN users u ON a.updated_by = u.user_id 
        WHERE a.updated_by IS NOT NULL AND u.user_id IS NULL
        LIMIT 10
      `);
      
      console.log(`Found ${invalidUpdatedByResult.length} addresses with invalid updated_by references`);
      
      if (invalidUpdatedByResult.length > 0) {
        console.log('Sample invalid references:', invalidUpdatedByResult);
        
        // Option 1: Set invalid updated_by to NULL
        const updateResult = await dataSource.query(`
          UPDATE addresses a 
          LEFT JOIN users u ON a.updated_by = u.user_id 
          SET a.updated_by = NULL 
          WHERE a.updated_by IS NOT NULL AND u.user_id IS NULL
        `);
        
        console.log(`✅ Updated ${updateResult.affectedRows || 0} addresses - set invalid updated_by to NULL`);
      }

      // Check addresses with invalid created_by references
      const invalidCreatedByResult = await dataSource.query(`
        SELECT a.address_id, a.created_by 
        FROM addresses a 
        LEFT JOIN users u ON a.created_by = u.user_id 
        WHERE a.created_by IS NOT NULL AND u.user_id IS NULL
        LIMIT 10
      `);
      
      console.log(`Found ${invalidCreatedByResult.length} addresses with invalid created_by references`);
      
      if (invalidCreatedByResult.length > 0) {
        console.log('Sample invalid created_by references:', invalidCreatedByResult);
        
        // For created_by, we need a valid user. Let's get the first available user or create a system user
        let systemUserId = null;
        
        // Try to find an existing system/admin user
        const systemUserResult = await dataSource.query(`
          SELECT user_id FROM users 
          WHERE email LIKE '%admin%' OR email LIKE '%system%' 
          LIMIT 1
        `);
        
        if (systemUserResult.length > 0) {
          systemUserId = systemUserResult[0].user_id;
          console.log(`Using existing system user: ${systemUserId}`);
        } else {
          // Get any existing user
          const anyUserResult = await dataSource.query('SELECT user_id FROM users LIMIT 1');
          if (anyUserResult.length > 0) {
            systemUserId = anyUserResult[0].user_id;
            console.log(`Using first available user: ${systemUserId}`);
          }
        }
        
        if (systemUserId) {
          const updateCreatedByResult = await dataSource.query(`
            UPDATE addresses a 
            LEFT JOIN users u ON a.created_by = u.user_id 
            SET a.created_by = ? 
            WHERE a.created_by IS NOT NULL AND u.user_id IS NULL
          `, [systemUserId]);
          
          console.log(`✅ Updated ${updateCreatedByResult.affectedRows || 0} addresses - set invalid created_by to system user`);
        } else {
          console.log('⚠️ No users found to assign as created_by. You may need to seed users first.');
        }
      }

      console.log('✅ Address foreign key constraints fixed!');
      
    } catch (error) {
      console.error('❌ Error fixing address foreign keys:', error);
      throw error;
    }
  }
}