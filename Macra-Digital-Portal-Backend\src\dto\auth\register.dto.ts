import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Matches, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Optional } from '@nestjs/common';

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 8 characters, must contain uppercase, lowercase, and number/special character)',
    example: 'Password123!',
    minLength: 8,
    maxLength: 30,
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(30, { message: 'Password must not exceed 30 characters' })
  // @Matches(/^(?=(?:.*[A-Z]){2,})(?=(?:.*[a-z]){1,})(?=(?:.*\d){2,})(?=(?:.*[\W_]){2,}).{8,30}$/, {
  //   message: 'Password must contain at least 2 uppercase letters, 1 lowercase letter, 2 numbers, and 2 special characters',
  // })
  password: string;


  @ApiProperty({
    description: 'User first name',
    example: 'John',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  first_name: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  last_name: string;

  @ApiPropertyOptional({
    description: 'User middle name (optional)',
    example: 'Michael',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  middle_name?: string;

  @Optional()
  email_verified_at?: string;

  @ApiProperty({
    description: 'User phone number in international format',
    example: '+265123456789',
    pattern: '^\\+?\\d{10,20}$',
  })
  @IsString()
  @Matches(/^\+?\d{10,15}$/, {
    message: 'Phone number must be in international format with 10-15 digits',
  })
  phone: string;

  @ApiPropertyOptional({
    description: 'User organization (optional)',
    example: 'ACME Corporation',
  })
  @IsOptional()
  @IsString()
  organization?: string;
}

// Alias for user creation
export class CreateUserDto extends RegisterDto {}
