import { ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { DataBreachCategory, DataBreachSeverity, DataBreachStatus, DataBreachPriority } from 'src/data-breach/data-breachs-constants';
export declare class RespondentRequiredForInvestigatingConstraint implements ValidatorConstraintInterface {
    validate(respondentRegNumber: string, args: ValidationArguments): boolean;
    defaultMessage(): string;
}
export declare class CreateDataBreachReportDto {
    title: string;
    description: string;
    category: DataBreachCategory;
    severity: DataBreachSeverity;
    incident_date: string;
    organization_involved: string;
    affected_data_types?: string;
    contact_attempts?: string;
    priority?: DataBreachPriority;
}
declare const UpdateDataBreachReportDto_base: import("@nestjs/common").Type<Partial<CreateDataBreachReportDto>>;
export declare class UpdateDataBreachReportDto extends UpdateDataBreachReportDto_base {
    status?: DataBreachStatus;
    assigned_to?: string;
    respondent_reg_number?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
}
export declare class UpdateDataBreachReportStatusDto {
    status: DataBreachStatus;
    comment?: string;
}
export declare class DataBreachReportFilterDto {
    category?: DataBreachCategory;
    severity?: DataBreachSeverity;
    status?: DataBreachStatus;
    priority?: DataBreachPriority;
    reporter_id?: string;
    assigned_to?: string;
    from_date?: string;
    to_date?: string;
    incident_from_date?: string;
    incident_to_date?: string;
    search?: string;
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: 'ASC' | 'DESC';
}
export {};
