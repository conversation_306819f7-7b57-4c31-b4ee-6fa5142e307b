import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { Task } from '../entities/tasks.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Tasks')
@Controller('tasks')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Create a new task' })
  @ApiResponse({ status: 201, description: 'Task created successfully' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Create new task',
  })
  create(@Body() createTaskDto: CreateTaskDto, @Request() req: any) {
    return this.tasksService.create(createTaskDto, req.user.user_id);
  }

  @Get()
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get all tasks with pagination' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  findAll(@Paginate() query: PaginateQuery) {
    return this.tasksService.findAll(query);
  }

  @Get('unassigned')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get unassigned tasks' })
  @ApiResponse({ status: 200, description: 'Unassigned tasks retrieved successfully' })
  findUnassigned(@Paginate() query: PaginateQuery) {
    return this.tasksService.findUnassigned(query);
  }

  @Get('assigned')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get assigned tasks' })
  @ApiResponse({ status: 200, description: 'Assigned tasks retrieved successfully' })
  findAssigned(@Paginate() query: PaginateQuery) {
    return this.tasksService.findAssigned(query);
  }

  @Get('assigned/me')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get tasks assigned to current user' })
  @ApiResponse({ status: 200, description: 'User tasks retrieved successfully' })
  findMyTasks(@Paginate() query: PaginateQuery, @Request() req: any) {
    return this.tasksService.findAssignedToUser(req.user.user_id, query);
  }

  @Get('stats')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get task statistics' })
  @ApiResponse({ status: 200, description: 'Task statistics retrieved successfully' })
  getStats() {
    return this.tasksService.getTaskStats();
  }

  @Get(':id')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Get task by ID' })
  @ApiResponse({ status: 200, description: 'Task retrieved successfully' })
  findOne(@Param('id') id: string) {
    return this.tasksService.findOne(id);
  }

  @Get(':id/navigation')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({
    summary: 'Get task with navigation information',
    description: 'Returns task with basic navigation info. Frontend handles actual navigation logic.'
  })
  @ApiResponse({
    status: 200,
    description: 'Task navigation information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        task: { type: 'object' },
        canNavigateToEntity: { type: 'boolean' }
      }
    }
  })
  findOneWithNavigation(@Param('id') id: string) {
    return this.tasksService.findOneWithNavigationInfo(id);
  }

  @Patch(':id')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Update task' })
  @ApiResponse({ status: 200, description: 'Task updated successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Update task',
  })
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
    return this.tasksService.update(id, updateTaskDto);
  }

  @Put(':id/assign')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Assign task to user' })
  @ApiResponse({ status: 200, description: 'Task assigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Assign task to user',
  })
  assign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.assign(id, assignTaskDto, req.user.user_id);
  }

  @Put(':id/reassign')
  @Roles('administrator', 'officer','customer', 'finance', 'director general')
  @ApiOperation({ summary: 'Reassign task to another user' })
  @ApiResponse({ status: 200, description: 'Task reassigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Reassign task to another user',
  })
  reassign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.reassign(id, assignTaskDto, req.user.user_id);
  }

  @Put(':id/assign-or-reassign')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({
    summary: 'Assign or reassign task (unified endpoint)',
    description: 'Automatically determines whether to assign or reassign based on current task state. Supports due dates and priority updates.'
  })
  @ApiResponse({ status: 200, description: 'Task assigned/reassigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Assign or reassign task',
  })
  assignOrReassign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.assignOrReassign(id, assignTaskDto, req.user.user_id);
  }

  @Get('application/:applicationId')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({
    summary: 'Get existing task for application',
    description: 'Returns the existing task for an application if one exists'
  })
  @ApiResponse({ status: 200, description: 'Task found' })
  @ApiResponse({ status: 404, description: 'No task found for application' })
  findTaskForApplication(@Param('applicationId') applicationId: string) {
    return this.tasksService.findTaskForApplication(applicationId);
  }

  @Get('application/:applicationId/all')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Find all tasks for specific application' })
  @ApiResponse({ status: 200, description: 'Tasks found successfully' })
  findAllTasksForApplication(@Param('applicationId') applicationId: string) {
    return this.tasksService.findAllTasksForApplication(applicationId);
  }

  @Put('application/:applicationId/close-all')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({ summary: 'Close all open tasks for specific application' })
  @ApiResponse({ status: 200, description: 'Tasks closed successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Close all tasks for application',
  })
  closeAllTasksForApplication(
    @Param('applicationId') applicationId: string,
    @Body() body: { reason?: string },
    @Request() req: any
  ) {
    return this.tasksService.closeAllTasksForApplication(applicationId, req.user.userId, body.reason);
  }

  @Post('application/:applicationId/create-or-reassign')
  @Roles('administrator', 'officer','customer', 'finance')
  @ApiOperation({
    summary: 'Create task for application or reassign existing one',
    description: 'Checks if a task already exists for the application. If it does, reassigns it. If not, creates a new task and assigns it.'
  })
  @ApiResponse({ status: 200, description: 'Task created or reassigned successfully' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Create or reassign application task',
  })
  createOrReassignApplicationTask(
    @Param('applicationId') applicationId: string,
    @Body() body: { createTaskDto: CreateTaskDto; assignTaskDto: AssignTaskDto },
    @Request() req: any
  ) {
    return this.tasksService.createOrReassignApplicationTask(
      applicationId,
      body.createTaskDto,
      body.assignTaskDto,
      req.user.user_id
    );
  }

  @Delete(':id')
  @Roles('administrator')
  @ApiOperation({ summary: 'Delete task' })
  @ApiResponse({ status: 200, description: 'Task deleted successfully' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Delete task',
  })
  remove(@Param('id') id: string) {
    return this.tasksService.remove(id);
  }

  @Post(':id/test-assignment-email')
  @Roles('administrator', 'officer', 'finance', 'customer')
  @ApiOperation({ summary: 'Test task assignment email notification' })
  @ApiResponse({ status: 200, description: 'Test email sent successfully' })
  async testAssignmentEmail(@Param('id') id: string, @Request() req: any) {
    return this.tasksService.testAssignmentEmail(id, req.user.user_id);
  }
}