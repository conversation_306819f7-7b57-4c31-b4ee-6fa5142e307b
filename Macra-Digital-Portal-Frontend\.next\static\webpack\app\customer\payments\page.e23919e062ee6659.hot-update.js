"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/payments/page",{

/***/ "(app-pages-browser)/./src/components/customer/CustomerLayout.tsx":
/*!****************************************************!*\
  !*** ./src/components/customer/CustomerLayout.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _LogoutButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../LogoutButton */ \"(app-pages-browser)/./src/components/LogoutButton.tsx\");\n/* harmony import */ var _common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../common/NotificationBell */ \"(app-pages-browser)/./src/components/common/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CustomerLayout = (param)=>{\n    let { children, breadcrumbs } = param;\n    _s();\n    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showLoader } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading)();\n    // Memoize navigation items to prevent unnecessary re-renders\n    const navigationItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[navigationItems]\": ()=>[\n                {\n                    name: 'Dashboard',\n                    href: '/customer',\n                    icon: 'ri-dashboard-line',\n                    current: pathname === '/customer'\n                },\n                {\n                    name: 'My Licenses',\n                    href: '/customer/my-licenses',\n                    icon: 'ri-key-line',\n                    current: pathname === '/customer/my-licenses'\n                },\n                {\n                    name: 'My Applications',\n                    href: '/customer/applications',\n                    icon: 'ri-file-list-3-line',\n                    current: pathname === '/customer/applications'\n                },\n                {\n                    name: 'Invoices & Payments',\n                    href: '/customer/payments',\n                    icon: 'ri-bank-card-line',\n                    current: pathname === '/customer/payments'\n                },\n                {\n                    name: 'Documents',\n                    href: '/customer/documents',\n                    icon: 'ri-file-text-line',\n                    current: pathname === '/customer/documents'\n                },\n                {\n                    name: 'Procurement',\n                    href: '/customer/procurement',\n                    icon: 'ri-auction-line',\n                    current: pathname === '/customer/procurement'\n                },\n                {\n                    name: 'Request Resource',\n                    href: '/customer/resources',\n                    icon: 'ri-hand-heart-line',\n                    current: pathname === '/customer/resources'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[navigationItems]\"], [\n        pathname\n    ]);\n    const supportItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[supportItems]\": ()=>[\n                {\n                    name: 'Consumer Affairs',\n                    href: '/customer/consumer-affairs',\n                    icon: 'ri-customer-service-2-line'\n                },\n                {\n                    name: 'Data Breach',\n                    href: '/customer/data-protection',\n                    icon: 'ri-shield-keyhole-line'\n                },\n                {\n                    name: 'Help Center',\n                    href: '/customer/help',\n                    icon: 'ri-question-line'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[supportItems]\"], []);\n    // Prefetch customer pages on mount for faster navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerLayout.useEffect\": ()=>{\n            const prefetchPages = {\n                \"CustomerLayout.useEffect.prefetchPages\": ()=>{\n                    const customerPages = [\n                        '/customer',\n                        '/customer/applications',\n                        '/customer/applications/standards',\n                        '/customer/payments',\n                        '/customer/my-licenses',\n                        '/customer/procurement',\n                        '/customer/profile',\n                        '/customer/consumer-affairs',\n                        '/customer/data-protection',\n                        '/customer/resources',\n                        '/customer/help'\n                    ];\n                    customerPages.forEach({\n                        \"CustomerLayout.useEffect.prefetchPages\": (page)=>{\n                            router.prefetch(page);\n                        }\n                    }[\"CustomerLayout.useEffect.prefetchPages\"]);\n                }\n            }[\"CustomerLayout.useEffect.prefetchPages\"];\n            // Delay prefetching to not interfere with initial page load\n            const timer = setTimeout(prefetchPages, 1000);\n            return ({\n                \"CustomerLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"CustomerLayout.useEffect\"];\n        }\n    }[\"CustomerLayout.useEffect\"], [\n        router\n    ]);\n    const toggleMobileSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleMobileSidebar]\": ()=>{\n            setIsMobileSidebarOpen(!isMobileSidebarOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleMobileSidebar]\"], [\n        isMobileSidebarOpen\n    ]);\n    const handleNavClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavClick]\": (href, name)=>{\n            const pageMessages = {\n                '/customer': 'Loading Dashboard...',\n                '/customer/my-licenses': 'Loading My Licenses...',\n                '/customer/applications': 'Loading Applications...',\n                '/customer/applications/apply/': 'Loading Standards License Options...',\n                '/customer/payments': 'Loading Payments...',\n                '/customer/documents': 'Loading Documents...',\n                '/customer/procurement': 'Loading Procurement...',\n                '/customer/resources': 'Loading Resources...',\n                '/customer/data-protection': 'Loading Data Breach...',\n                '/customer/help': 'Loading Help Center...',\n                '/customer/profile': 'Loading Profile...',\n                '/customer/settings': 'Loading Settings...'\n            };\n            const message = pageMessages[href] || \"Loading \".concat(name, \"...\");\n            showLoader(message);\n            setIsMobileSidebarOpen(false);\n        }\n    }[\"CustomerLayout.useCallback[handleNavClick]\"], [\n        showLoader\n    ]);\n    const handleNavHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavHover]\": (href)=>{\n            // Prefetch on hover for instant navigation\n            router.prefetch(href);\n        }\n    }[\"CustomerLayout.useCallback[handleNavHover]\"], [\n        router\n    ]);\n    const toggleUserDropdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleUserDropdown]\": ()=>{\n            setIsUserDropdownOpen(!isUserDropdownOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleUserDropdown]\"], [\n        isUserDropdownOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            isMobileSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: ()=>setIsMobileSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\\n        \".concat(isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/images/macra-logo.png\",\n                                    alt: \"MACRA Logo\",\n                                    className: \"max-h-12 w-auto\",\n                                    onLoad: ()=>console.log('MACRA logo loaded successfully in customer portal'),\n                                    onError: (e)=>{\n                                        var _target_parentNode;\n                                        console.error('Error loading MACRA logo in customer portal:', e);\n                                        console.log('Attempting to load logo from:', e.currentTarget.src);\n                                        // Fallback to styled logo if image fails\n                                        const target = e.target;\n                                        target.style.display = 'none';\n                                        const textFallback = document.createElement('div');\n                                        textFallback.innerHTML = 'MACRA';\n                                        textFallback.className = 'text-primary font-bold text-lg bg-red-600 text-white px-3 py-2 rounded-full';\n                                        (_target_parentNode = target.parentNode) === null || _target_parentNode === void 0 ? void 0 : _target_parentNode.appendChild(textFallback);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-6 px-4 side-nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            onClick: ()=>handleNavClick(item.href, item.name),\n                                            onMouseEnter: ()=>handleNavHover(item.href),\n                                            className: \"\\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\\n                    \".concat(item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100', \"\\n                  \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center mr-3 \".concat(item.current ? 'text-red-600 dark:text-red-400' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 space-y-1\",\n                                            children: supportItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    onClick: ()=>handleNavClick(item.href, item.name),\n                                                    onMouseEnter: ()=>handleNavHover(item.href),\n                                                    className: \"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 flex items-center justify-center mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white dark:bg-gray-800 shadow-sm z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleMobileSidebar,\n                                    className: \"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\",\n                                    \"aria-label\": \"Open mobile menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-menu-line ri-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                children: [\n                                                    index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-arrow-right-s-line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: \"hover:text-primary\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-900 dark:text-gray-100\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleUserDropdown,\n                                                    className: \"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Open user menu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            className: \"h-8 w-8 rounded-full object-cover\",\n                                                            src: (user === null || user === void 0 ? void 0 : user.profile_image) || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\",\n                                                            alt: \"Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isUserDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/customer/profile\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                onClick: ()=>{\n                                                                    handleNavClick('/customer/profile', 'Profile');\n                                                                    setIsUserDropdownOpen(false);\n                                                                },\n                                                                children: \"Your Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LogoutButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"text\",\n                                                                size: \"sm\",\n                                                                className: \"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                showConfirmation: true,\n                                                                children: \"Sign out\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerLayout, \"Bvue6xx7qybDlJR/560gagzUyBI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading\n    ];\n});\n_c = CustomerLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerLayout);\nvar _c;\n$RefreshReg$(_c, \"CustomerLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});