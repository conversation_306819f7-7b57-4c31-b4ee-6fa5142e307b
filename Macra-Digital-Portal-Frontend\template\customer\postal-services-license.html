<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MACRA Postal Services License - Digital Portal</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
    rel="stylesheet"
  />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
  />
  <style>
    :where([class^="ri-"])::before { content: "\f3c2"; }
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }
    
    .license-document {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    .official-seal {
      background: radial-gradient(circle, #e02b20 0%, #b91c1c 100%);
      border: 3px solid #dc2626;
    }
    
    .watermark {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      z-index: 1;
      opacity: 0.05;
      pointer-events: none;
      user-select: none;
      font-size: 8rem;
      font-weight: bold;
      color: #e02b20;
      white-space: nowrap;
    }
    
    .watermark-logo {
      position: fixed;
      top: 30%;
      left: 30%;
      transform: translate(-50%, -50%) rotate(-30deg);
      z-index: 1;
      opacity: 0.03;
      pointer-events: none;
      user-select: none;
      width: 300px;
      height: 300px;
    }
    
    .watermark-logo-2 {
      position: fixed;
      top: 70%;
      right: 20%;
      transform: translate(50%, -50%) rotate(15deg);
      z-index: 1;
      opacity: 0.03;
      pointer-events: none;
      user-select: none;
      width: 250px;
      height: 250px;
    }
    
    .license-content {
      position: relative;
      z-index: 2;
    }
    
    .license-watermark {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      opacity: 0.08;
      pointer-events: none;
      user-select: none;
      width: 400px;
      height: 400px;
    }
    
    @media print {
      body { background-color: white; }
      .no-print { display: none; }
      .watermark, .watermark-logo, .watermark-logo-2 {
        opacity: 0.08 !important;
      }
      .license-watermark {
        opacity: 0.12 !important;
      }
    }
  </style>
</head>
<body class="bg-gray-50 font-sans min-h-screen">
  <!-- Watermarks -->
  <div class="watermark">MACRA</div>
  <div class="watermark-logo">
    <img src=".docs/images/macra-logo.png" alt="MACRA Watermark" class="w-full h-full object-contain">
  </div>
  <div class="watermark-logo-2">
    <img src=".docs/images/macra-logo.png" alt="MACRA Watermark" class="w-full h-full object-contain">
  </div>

  <!-- Action Buttons -->
  <div class="no-print fixed top-4 right-4 z-10 flex space-x-2">
    <button onclick="window.print()" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg shadow-md hover:bg-opacity-90 transition-all">
      <i class="ri-printer-line mr-2"></i>
      Print License
    </button>
    <button onclick="downloadPDF()" class="inline-flex items-center px-4 py-2 bg-secondary text-white rounded-lg shadow-md hover:bg-opacity-90 transition-all">
      <i class="ri-download-line mr-2"></i>
      Download PDF
    </button>
  </div>

  <div class="max-w-4xl mx-auto my-8 p-8 license-document rounded-xl license-content">
    <!-- License Watermark -->
    <div class="license-watermark">
      <img src=".docs/images/macra-logo.png" alt="MACRA Watermark" class="w-full h-full object-contain opacity-100">
    </div>

    <!-- Header -->
    <div class="text-center border-b-4 border-primary pb-6 mb-8 relative" style="z-index: 3;">
      <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-primary via-red-500 to-primary"></div>
      <img src=".docs/images/macra-logo.png" alt="MACRA Logo" class="mx-auto mb-6 h-20 w-auto">
      <h1 class="text-4xl font-bold text-primary mb-2">Malawi Communications Regulatory Authority (MACRA)</h1>
      <h2 class="text-2xl font-semibold text-gray-700 mb-2">Postal Services License</h2>
      <div class="inline-flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-full text-sm font-medium">
        <i class="ri-verified-badge-line mr-2"></i>
        Official License Document
      </div>
    </div>

    <!-- License Details -->
    <div class="bg-gray-50 rounded-xl p-6 mb-8 border border-gray-200 relative" style="z-index: 3;">
      <h3 class="text-xl font-bold text-primary mb-6 flex items-center">
        <i class="ri-file-text-line mr-2"></i>
        License Information
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">Licensee</span>
            <span class="text-lg font-semibold text-gray-900">Acme Corporation</span>
          </div>
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">Registered Address</span>
            <span class="text-lg font-semibold text-gray-900">Plot 45/2/1, Area 4, Lilongwe, Malawi</span>
          </div>
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">Date of Issue</span>
            <span class="text-lg font-semibold text-gray-900">10 March 2025</span>
          </div>
        </div>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">License Number</span>
            <span class="text-lg font-semibold text-primary">PSL-2025-005</span>
          </div>
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">Category</span>
            <span class="text-lg font-semibold text-gray-900">Postal Services License</span>
          </div>
          <div class="flex flex-col">
            <span class="text-sm font-medium text-gray-500 uppercase tracking-wide">Date of Expiry</span>
            <span class="text-lg font-semibold text-gray-900">09 March 2035</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Signature -->
    <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8 border border-gray-200 relative" style="z-index: 3;">
      <div class="max-w-md mx-auto text-center">
        <h4 class="text-lg font-bold text-primary mb-4 flex items-center justify-center">
          <i class="ri-quill-pen-line mr-2"></i>
          For MACRA
        </h4>
        <div class="space-y-3">
          <div class="border-t-2 border-primary pt-4">
            <div class="h-8 border-b border-gray-400 w-48 mb-2 mx-auto"></div>
            <p class="text-sm font-medium text-gray-700">Director General</p>
            <p class="text-sm text-gray-600">Date: 10 March 2025</p>
          </div>
        </div>
      </div>
    </div>

    <!-- QR Code -->
    <div class="text-center relative" style="z-index: 3;">
      <p class="font-semibold text-secondary mb-4">Verification QR Code</p>
      <div class="w-32 h-32 mx-auto my-4 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center p-2 relative">
        <canvas id="qrcode" class="w-full h-full"></canvas>
        <div id="qr-fallback" class="qr-fallback w-full h-full absolute inset-0 rounded bg-gray-100 flex items-center justify-center">
          <div class="text-center">
            <i class="ri-qr-code-line text-4xl text-gray-400 mb-2"></i>
            <p class="text-xs text-gray-500">QR Code</p>
          </div>
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">Scan to verify license authenticity</p>
      <p class="text-xs text-gray-400 mt-1">PSL-2025-005</p>
    </div>

    <!-- Footer -->
    <div class="text-center bg-gray-50 rounded-xl p-6 border-t-4 border-primary mt-8 relative" style="z-index: 3;">
      <div class="space-y-3">
        <p class="text-sm text-gray-700 font-medium">This license is issued under the Communications Act of Malawi and is subject to the terms and conditions set forth by MACRA.</p>
        <div class="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-600">
          <div class="flex items-center">
            <i class="ri-phone-line mr-2 text-primary"></i>
            <span>+265 1 770 100</span>
          </div>
          <div class="flex items-center">
            <i class="ri-mail-line mr-2 text-primary"></i>
            <span><EMAIL></span>
          </div>
          <div class="flex items-center">
            <i class="ri-global-line mr-2 text-primary"></i>
            <span>www.macra.mw</span>
          </div>
        </div>
        <p class="text-xs text-gray-500 mt-4">License Number: PSL-2025-005 | Generated on: 10 March 2025</p>
      </div>
    </div>
  </div>

  <!-- QR Code Library -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>

  <script>
    function downloadPDF() {
      // Hide action buttons before printing
      const buttons = document.querySelectorAll('.no-print');
      buttons.forEach(btn => btn.style.display = 'none');

      // Trigger print dialog (user can save as PDF)
      window.print();

      // Show buttons again after print dialog
      setTimeout(() => {
        buttons.forEach(btn => btn.style.display = 'flex');
      }, 1000);
    }

    // Generate QR Code
    function generateQRCode() {
      const qrCodeElement = document.getElementById('qrcode');
      const fallbackElement = document.getElementById('qr-fallback');

      if (!qrCodeElement) {
        console.error('QR Code element not found');
        return;
      }

      // Check if QRCode library is loaded
      if (typeof qrcode === 'undefined') {
        console.error('QRCode library not loaded - using fallback');
        return; // Keep fallback visible
      }

      try {
        console.log('Generating QR code for Postal Services License...');

        // Postal Services License verification data
        const qrString = 'MACRA-PSL-2025-005-POSTAL-SERVICES-CORP-POSTAL-LICENSE-2025-03-10-VERIFY-macra.mw';

        // Create QR code using qrcode-generator library
        const qr = qrcode(0, 'M'); // Type 0 (auto), Error correction level M
        qr.addData(qrString);
        qr.make();

        // Get the module count (size of the QR code)
        const moduleCount = qr.getModuleCount();
        const cellSize = 120 / moduleCount; // Fit into 120x120 canvas

        // Set canvas size
        qrCodeElement.width = 120;
        qrCodeElement.height = 120;

        // Get canvas context
        const ctx = qrCodeElement.getContext('2d');

        // Clear canvas
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, 120, 120);

        // Draw QR code
        ctx.fillStyle = '#000000';
        for (let row = 0; row < moduleCount; row++) {
          for (let col = 0; col < moduleCount; col++) {
            if (qr.isDark(row, col)) {
              ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
            }
          }
        }

        console.log('Postal Services License QR Code generated successfully');
        // Hide fallback since real QR code loaded
        if (fallbackElement) {
          fallbackElement.style.display = 'none';
        }

      } catch (error) {
        console.error('Error in QR code generation:', error);
        // Keep fallback visible
      }
    }

    // Try to generate QR code when page loads
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, attempting to generate Postal Services License QR code...');
      generateQRCode();
    });

    // Fallback: try again after a short delay
    window.addEventListener('load', function() {
      setTimeout(function() {
        const qrElement = document.getElementById('qrcode');
        if (qrElement && qrElement.width === 0) {
          console.log('Retrying Postal Services License QR code generation...');
          generateQRCode();
        }
      }, 1000);
    });
  </script>
</body>
</html>
