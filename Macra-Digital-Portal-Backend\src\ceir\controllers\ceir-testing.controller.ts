import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseUUIDPipe,
  ValidationPipe,
  ParseFloatPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Audit } from '../../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../../entities/audit-trail.entity';

// Import services
import { CeirEquipmentSpecificationsService } from '../services/ceir-equipment-specifications.service';
import { CeirTestReportsService } from '../services/ceir-test-reports.service';

// Import DTOs
import { CreateCeirEquipmentSpecificationDto, UpdateCeirEquipmentSpecificationDto } from '../dto/ceir-equipment-specifications';
import { CreateCeirTestReportDto, UpdateCeirTestReportDto } from '../dto/ceir-test-reports';

// Import entities
import { CeirEquipmentSpecifications } from '../entities/ceir-equipment-specifications.entity';
import { CeirTestReports } from '../entities/ceir-test-reports.entity';

@ApiTags('CEIR Testing & Specifications')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller('ceir/testing')
export class CeirTestingController {
  constructor(
    private readonly ceirEquipmentSpecificationsService: CeirEquipmentSpecificationsService,
    private readonly ceirTestReportsService: CeirTestReportsService,
  ) {}

  // ===== EQUIPMENT SPECIFICATIONS ENDPOINTS =====

  @Post('equipment-specifications')
  @ApiOperation({ summary: 'Create a new CEIR equipment specification' })
  @ApiResponse({
    status: 201,
    description: 'Equipment specification created successfully',
    type: CeirEquipmentSpecifications,
  })
  @ApiBody({ type: CreateCeirEquipmentSpecificationDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentSpecification',
    description: 'Created CEIR equipment specification',
  })
  async createEquipmentSpecification(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createDto: CreateCeirEquipmentSpecificationDto,
    @Request() req: any,
  ): Promise<CeirEquipmentSpecifications> {
    return this.ceirEquipmentSpecificationsService.create(createDto, req.user?.userId);
  }

  @Get('equipment-specifications')
  @ApiOperation({ summary: 'Get all CEIR equipment specifications' })
  @ApiResponse({
    status: 200,
    description: 'List of equipment specifications',
    type: [CeirEquipmentSpecifications],
  })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentSpecification',
    description: 'Viewed CEIR equipment specifications',
  })
  async findAllEquipmentSpecifications(@Query('active') active?: boolean): Promise<CeirEquipmentSpecifications[]> {
    if (active === true) {
      return this.ceirEquipmentSpecificationsService.findAllActive();
    }
    return this.ceirEquipmentSpecificationsService.findAll();
  }

  @Get('equipment-specifications/by-device/:deviceId')
  @ApiOperation({ summary: 'Get equipment specifications by device ID' })
  @ApiParam({ name: 'deviceId', description: 'Device UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specifications for device',
    type: [CeirEquipmentSpecifications],
  })
  async findSpecificationsByDevice(@Param('deviceId', ParseUUIDPipe) deviceId: string): Promise<CeirEquipmentSpecifications[]> {
    return this.ceirEquipmentSpecificationsService.findByDevice(deviceId);
  }

  @Get('equipment-specifications/by-category/:categoryId')
  @ApiOperation({ summary: 'Get equipment specifications by category ID' })
  @ApiParam({ name: 'categoryId', description: 'Equipment category UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specifications for category',
    type: [CeirEquipmentSpecifications],
  })
  async findSpecificationsByCategory(@Param('categoryId', ParseUUIDPipe) categoryId: string): Promise<CeirEquipmentSpecifications[]> {
    return this.ceirEquipmentSpecificationsService.findByCategory(categoryId);
  }

  @Get('equipment-specifications/by-frequency/:frequency')
  @ApiOperation({ summary: 'Get equipment specifications by frequency range' })
  @ApiParam({ name: 'frequency', description: 'Frequency in MHz' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specifications supporting frequency',
    type: [CeirEquipmentSpecifications],
  })
  async findSpecificationsByFrequency(@Param('frequency', ParseFloatPipe) frequency: number): Promise<CeirEquipmentSpecifications[]> {
    return this.ceirEquipmentSpecificationsService.findByFrequency(frequency);
  }

  @Get('equipment-specifications/:id')
  @ApiOperation({ summary: 'Get CEIR equipment specification by ID' })
  @ApiParam({ name: 'id', description: 'Equipment specification UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specification found',
    type: CeirEquipmentSpecifications,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentSpecification',
    description: 'Viewed CEIR equipment specification details',
  })
  async findOneEquipmentSpecification(@Param('id', ParseUUIDPipe) id: string): Promise<CeirEquipmentSpecifications> {
    return this.ceirEquipmentSpecificationsService.findOne(id);
  }

  @Patch('equipment-specifications/:id')
  @ApiOperation({ summary: 'Update CEIR equipment specification' })
  @ApiParam({ name: 'id', description: 'Equipment specification UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specification updated successfully',
    type: CeirEquipmentSpecifications,
  })
  @ApiBody({ type: UpdateCeirEquipmentSpecificationDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentSpecification',
    description: 'Updated CEIR equipment specification',
  })
  async updateEquipmentSpecification(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateDto: UpdateCeirEquipmentSpecificationDto,
    @Request() req: any,
  ): Promise<CeirEquipmentSpecifications> {
    return this.ceirEquipmentSpecificationsService.update(id, updateDto, req.user?.userId);
  }

  @Delete('equipment-specifications/:id')
  @ApiOperation({ summary: 'Delete CEIR equipment specification' })
  @ApiParam({ name: 'id', description: 'Equipment specification UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment specification deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentSpecification',
    description: 'Deleted CEIR equipment specification',
  })
  async removeEquipmentSpecification(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.ceirEquipmentSpecificationsService.remove(id);
  }

  // ===== TEST REPORTS ENDPOINTS =====

  @Post('test-reports')
  @ApiOperation({ summary: 'Create a new CEIR test report' })
  @ApiResponse({
    status: 201,
    description: 'Test report created successfully',
    type: CeirTestReports,
  })
  @ApiBody({ type: CreateCeirTestReportDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Created CEIR test report',
  })
  async createTestReport(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createDto: CreateCeirTestReportDto,
    @Request() req: any,
  ): Promise<CeirTestReports> {
    return this.ceirTestReportsService.create(createDto, req.user?.userId);
  }

  @Get('test-reports')
  @ApiOperation({ summary: 'Get all CEIR test reports' })
  @ApiResponse({
    status: 200,
    description: 'List of test reports',
    type: [CeirTestReports],
  })
  @ApiQuery({ name: 'valid', required: false, type: Boolean, description: 'Filter by valid status' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Viewed CEIR test reports',
  })
  async findAllTestReports(@Query('valid') valid?: boolean): Promise<CeirTestReports[]> {
    if (valid === true) {
      return this.ceirTestReportsService.findAllValid();
    }
    return this.ceirTestReportsService.findAll();
  }

  @Get('test-reports/statistics')
  @ApiOperation({ summary: 'Get CEIR test reports statistics' })
  @ApiResponse({
    status: 200,
    description: 'Test reports statistics',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Viewed test reports statistics',
  })
  async getTestReportsStatistics() {
    return this.ceirTestReportsService.getStatistics();
  }

  @Get('test-reports/by-device/:deviceId')
  @ApiOperation({ summary: 'Get test reports by device ID' })
  @ApiParam({ name: 'deviceId', description: 'Device UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test reports for device',
    type: [CeirTestReports],
  })
  async findTestReportsByDevice(@Param('deviceId', ParseUUIDPipe) deviceId: string): Promise<CeirTestReports[]> {
    return this.ceirTestReportsService.findByDevice(deviceId);
  }

  @Get('test-reports/by-certification-body/:bodyId')
  @ApiOperation({ summary: 'Get test reports by certification body ID' })
  @ApiParam({ name: 'bodyId', description: 'Certification body UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test reports by certification body',
    type: [CeirTestReports],
  })
  async findTestReportsByCertificationBody(@Param('bodyId', ParseUUIDPipe) bodyId: string): Promise<CeirTestReports[]> {
    return this.ceirTestReportsService.findByCertificationBody(bodyId);
  }

  @Get('test-reports/expiring')
  @ApiOperation({ summary: 'Get test reports expiring soon' })
  @ApiQuery({ name: 'days', required: false, type: Number, description: 'Number of days to look ahead (default: 30)' })
  @ApiResponse({
    status: 200,
    description: 'List of expiring test reports',
    type: [CeirTestReports],
  })
  async findExpiringTestReports(@Query('days') days?: number): Promise<CeirTestReports[]> {
    return this.ceirTestReportsService.findExpiring(days || 30);
  }

  @Get('test-reports/:id')
  @ApiOperation({ summary: 'Get CEIR test report by ID' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test report found',
    type: CeirTestReports,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Viewed CEIR test report details',
  })
  async findOneTestReport(@Param('id', ParseUUIDPipe) id: string): Promise<CeirTestReports> {
    return this.ceirTestReportsService.findOne(id);
  }

  @Patch('test-reports/:id')
  @ApiOperation({ summary: 'Update CEIR test report' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test report updated successfully',
    type: CeirTestReports,
  })
  @ApiBody({ type: UpdateCeirTestReportDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Updated CEIR test report',
  })
  async updateTestReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateDto: UpdateCeirTestReportDto,
    @Request() req: any,
  ): Promise<CeirTestReports> {
    return this.ceirTestReportsService.update(id, updateDto, req.user?.userId);
  }

  @Delete('test-reports/:id')
  @ApiOperation({ summary: 'Delete CEIR test report' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test report deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Deleted CEIR test report',
  })
  async removeTestReport(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.ceirTestReportsService.remove(id);
  }

  @Patch('test-reports/:id/approve')
  @ApiOperation({ summary: 'Approve test report' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiQuery({ name: 'approvedBy', description: 'Name of approver' })
  @ApiResponse({
    status: 200,
    description: 'Test report approved successfully',
    type: CeirTestReports,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Approved CEIR test report',
  })
  async approveTestReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('approvedBy') approvedBy: string,
    @Request() req: any,
  ): Promise<CeirTestReports> {
    return this.ceirTestReportsService.approve(id, req.user.userId, approvedBy);
  }

  @Patch('test-reports/:id/reject')
  @ApiOperation({ summary: 'Reject test report' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test report rejected successfully',
    type: CeirTestReports,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Rejected CEIR test report',
  })
  async rejectTestReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<CeirTestReports> {
    return this.ceirTestReportsService.reject(id, req.user.userId);
  }

  @Patch('test-reports/:id/validate')
  @ApiOperation({ summary: 'Validate test report' })
  @ApiParam({ name: 'id', description: 'Test report UUID' })
  @ApiResponse({
    status: 200,
    description: 'Test report validated successfully',
    type: CeirTestReports,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTestReport',
    description: 'Validated CEIR test report',
  })
  async validateTestReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<CeirTestReports> {
    return this.ceirTestReportsService.validate(id, req.user.userId);
  }
}
