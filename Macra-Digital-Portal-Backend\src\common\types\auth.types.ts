/**
 * Enhanced type definitions for authentication and authorization
 */

import { User } from '../../entities/user.entity';
import { TwoFactorAction } from '../constants/auth.constants';

export interface JwtPayload {
  email: string;
  sub: string;
  roles?: string[];
  iat?: number;
  exp?: number;
}

export interface AuthResponse {
  access_token: string;
  user: AuthUserInfo;
  requiresTwoFactor?: boolean;
  requiresRecovery?: boolean;
  message?: string;
  requires_2fa?: boolean;
  action?: string;
}

export interface AuthUserInfo {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  two_factor_enabled: boolean;
  roles?: string[];
  isStaff?: boolean;
  isCustomer?: boolean;
  isAdmin?: boolean;
  permissions?: string[];
  email_verified_at?: Date | null;
  requires_verification?: boolean;
}

export interface TwoFactorCodeResult {
  message: string;
  otpAuthUrl: string;
  hashedToken: string;
  secret: string;
}

export interface TwoFactorSetupResult {
  otpAuthUrl: string;
  qrCodeDataUrl?: string;
  secret: string;
  message?: string;
  hashedToken?: string;
}

export interface DeviceInfo {
  ip: string;
  country: string;
  city: string;
  userAgent: string;
}

export interface EmailContext {
  [key: string]: any;
  year?: number;
}

export interface TwoFactorEmailContext extends EmailContext {
  name: string;
  message: string;
  verifyUrl: string;
}

export interface LoginAlertEmailContext extends EmailContext {
  userName: string;
  loginUrl: string;
  ip: string;
  country: string;
  city: string;
  userAgent: string;
  message: string;
}

export interface PasswordResetEmailContext extends EmailContext {
  userName: string;
  loginUrl: string;
}

// Type guards
export function isValidTwoFactorAction(action: string): action is TwoFactorAction {
  return Object.values(TwoFactorAction).includes(action as TwoFactorAction);
}

export function isAuthResponse(obj: any): obj is AuthResponse {
  return obj && 
    typeof obj.access_token === 'string' &&
    obj.user &&
    typeof obj.user.user_id === 'string' &&
    typeof obj.user.email === 'string';
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUserId(userId: string): boolean {
  // UUID v4 validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(userId);
}

// Utility types
export type UserWithRoles = User & {
  roles: NonNullable<User['roles']>;
};

export type RequiredAuthFields = Required<Pick<User, 'user_id' | 'email' | 'first_name' | 'last_name' | 'two_factor_enabled'>>;

export type SafeUser = Omit<User, 'password' | 'two_factor_code' | 'two_factor_temp'>;

// Service method return types
export type LoginResult = Promise<AuthResponse>;
export type RegisterResult = Promise<AuthResponse>;
export type TwoFactorResult = Promise<TwoFactorCodeResult>;
export type TwoFactorSetupPromise = Promise<TwoFactorSetupResult>;
export type PasswordResetResult = Promise<{ message: string }>;

// Validation result types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface UserValidationResult extends ValidationResult {
  user?: User;
}

// Enhanced error types
export interface AuthError extends Error {
  code: string;
  context?: Record<string, any>;
}

export class AuthenticationError extends Error implements AuthError {
  code = 'AUTH_ERROR';
  context?: Record<string, any>;

  constructor(message: string, context?: Record<string, any>) {
    super(message);
    this.name = 'AuthenticationError';
    this.context = context;
  }
}

export class TwoFactorError extends Error implements AuthError {
  code = 'TWO_FACTOR_ERROR';
  context?: Record<string, any>;

  constructor(message: string, context?: Record<string, any>) {
    super(message);
    this.name = 'TwoFactorError';
    this.context = context;
  }
}
