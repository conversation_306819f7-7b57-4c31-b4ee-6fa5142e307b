import { PartialType } from '@nestjs/swagger';
import { CreateCeirEquipmentSpecificationDto } from './create-ceir-equipment-specification.dto';
import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCeirEquipmentSpecificationDto extends PartialType(CreateCeirEquipmentSpecificationDto) {
  @ApiProperty({
    description: 'User ID who is updating this specification',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID' })
  updated_by?: string;
}
