'use client';

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '../../contexts/ToastContext';
import DataTable from '../../components/common/DataTable';
import Select from '../../components/common/Select';
import { useTaskNavigation } from '../../hooks/useTaskNavigation';
import { PaginateQuery, Task, TaskFilters, TaskPriority, TaskStatus, User } from '@/types';
import TaskModal from '../../components/tasks/TaskModal';
import { taskService } from '@/services/task-assignment';
import ConfirmationModal from '@/components/common/ConfirmationModal';
import ReassignTaskModal from '@/components/tasks/ReassignTaskModal';
import { formatStatus, getStatusColor } from '@/utils/formatters';
import { useAuth } from '@/contexts/AuthContext';

export default function TasksPage() {
  const { showSuccess } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [tasksData, setTasksData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [taskToReassign, setTaskToReassign] = useState<Task | null>(null);
  const [filters, setFilters] = useState<TaskFilters>({
    status: TaskStatus.PENDING // Default to showing pending tasks
  });
  const [users, setUsers] = useState<User[]>([]);
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });
  const {user} = useAuth();

  // Add task navigation hook
  const { openTaskView, isLoading: isNavigating } = useTaskNavigation();


  const handleCreateTask = () => {
    setEditingTask(null);
    setIsModalOpen(true);
  };

  const canViewTask =(task: Task) => {
    console.log( 'canViewTask:', task, user);
    if(task.assigned_at && task.assigned_to == user?.user_id) return true;
    return false;
  }

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingTask(null);
  };

  const handleTaskSaved = () => {
    if (editingTask) {
      showSuccess('Task updated successfully!');
    } else {
      showSuccess('Task created successfully!');
    }
    // Reload tasks to show updated data
    loadTasks(currentQuery);
  };

  const handleFilterChange = (key: keyof TaskFilters, value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      [key]: value === '' ? undefined : value
    }));
  };

  const handleDeleteTask = (task: Task) => {
    setTaskToDelete(task);
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setTaskToDelete(null);
  };

  const handleReassignTask = (task: Task) => {
    setTaskToReassign(task);
    setShowReassignModal(true);
  };

  const handleCancelReassign = () => {
    setShowReassignModal(false);
    setTaskToReassign(null);
  };

  const handleReassignSuccess = () => {
    setShowReassignModal(false);
    setTaskToReassign(null);
    // Reload tasks to show updated assignment
    loadTasks(currentQuery);
  };

  const handleConfirmDelete = async () => {
    if (!taskToDelete) return;

    setIsDeleting(true);
    try {
      await taskService.deleteTask(taskToDelete.task_id);
      setShowDeleteModal(false);
      setTaskToDelete(null);
      // Reload tasks
      loadTasks(currentQuery);
    } catch (err) {
      console.error('Error deleting task:', err);
      setError('Failed to delete task');
    } finally {
      setIsDeleting(false);
    }
  };

  const loadTasks = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);

      // Combine query with current filters
      const params = {
        ...query,
        ...filters
      };

      const response = await taskService.getTasks(params);
      setTasksData(response);
    } catch (err: any) {
      console.error('Error loading tasks:', err);
      let errorMessage = 'Failed to load tasks. Please try again.';

      if (err && typeof err === 'object') {
        if ('response' in err && err.response && typeof err.response === 'object') {
          if ('status' in err.response) {
            const status = err.response.status;
            if (status === 401) {
              errorMessage = 'Authentication required. Please log in again.';
            } else if (status === 403) {
              errorMessage = 'You do not have permission to view tasks.';
            } else if (status === 500) {
              errorMessage = 'Server error. Please try again later.';
            } else if ('data' in err.response &&
                      err.response.data &&
                      typeof err.response.data === 'object' &&
                      'message' in err.response.data &&
                      typeof err.response.data.message === 'string') {
              errorMessage = err.response.data.message;
            }
          }
        } else if ('message' in err && typeof err.message === 'string') {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setTasksData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load tasks when component mounts or filters change
  useEffect(() => {
    console.log('🔄 Filters changed, loading tasks:', filters);
    loadTasks({ page: 1, limit: 10 });
  }, [filters, loadTasks]);

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, []);

  // Handler for DataTable query changes (pagination, search, sorting)
  const handleQueryChange = useCallback((query: PaginateQuery) => {
    loadTasks(query);
  }, [loadTasks]);

  const loadUsers = async () => {
    try {
      const usersResponse = await taskService.getOfficers();
      setUsers(usersResponse.data);
    } catch (err) {
      console.error('Error loading users:', err);
      setUsers([]);
    }
  };



  const taskColumns = [
    {
      key: 'task_number',
      label: 'Task Number',
      sortable: true,
      render: (value: unknown, task: Task & Record<string, unknown>) => (
        <div className={
          canViewTask(task) ? 
          "text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline"
          : "text-sm font-medium text-gray-900 dark:text-gray-100"
        }
          onClick={() => canViewTask(task) ? openTaskView(task.task_id): null}
          title="Click to view task"
        >
          {String(value)}
        </div>
      ),
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value: unknown, task: Task & Record<string, unknown>) => (
        <div>
          <div
            className={
              canViewTask(task) ? 
              "text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline"
              : "text-sm font-medium text-gray-900 dark:text-gray-100"
            }
            onClick={() =>  canViewTask(task) ? openTaskView(task.task_id): null}
            title="Click to view task"
          >
            {String(value)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
            {task.description}
          </div>
        </div>
      ),
    },
    {
      key: 'task_type',
      label: 'Type',
      render: (value: unknown) => (
        <span className="text-sm text-gray-900 dark:text-gray-100 capitalize">
          {String(value).replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(value)}`}>
          {formatStatus(value as string)}
        </span>
      ),
    },
    {
      key: 'assignee',
      label: 'Assigned To',
      render: (_: unknown, task: Task & Record<string, unknown>) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {task.assignee ? `${task.assignee.first_name} ${task.assignee.last_name}` : 'Unassigned'}
        </span>
      ),
    },
    {
      key: 'due_date',
      label: 'Due Date',
      sortable: true,
      render: (value: unknown) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(String(value)).toLocaleDateString() : 'No due date'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, task: Task) => (
        <div className="flex items-center space-x-2">
          {canViewTask(task)? (
          <button
            onClick={() => openTaskView(task.task_id)}
            disabled={isNavigating}
            className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50"
            title="Open in new tab"
          >
            <i className="ri-external-link-line"></i> View
          </button>
          ): null}
          {/* Hide reassign/assign button for completed tasks */}
          {task.status !== TaskStatus.COMPLETED ? (
            <button
              onClick={() => handleReassignTask(task)}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900"
              title="Reassign task"
            >
              <i className="ri-user-shared-line"></i>
              {task.assignee ? 'Reassign' : 'Assign'}
            </button>
          ) : (
            <span className="text-sm text-gray-500 dark:text-gray-400 italic flex items-center">
              <i className="ri-check-circle-line mr-1 text-green-600 dark:text-green-400"></i>
              Completed
            </span>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Task Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and track tasks across your organization
        </p>
      </div>

      {/* Action header */}
      <div className="mb-6 flex justify-end">
        <button type="button"
          onClick={handleCreateTask}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900"
        >
          <i className="ri-add-line w-5 h-5 mr-2"></i>
          Add Task
        </button>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Filters Section */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Status Filter */}
          <Select
            label="Status"
            value={filters.status || ''}
            onChange={(value) => handleFilterChange('status', value)}
            options={[
              { value: '', label: 'All Statuses' },
              { value: TaskStatus.PENDING, label: 'Pending' },
              { value: TaskStatus.COMPLETED, label: 'Completed' },
              { value: TaskStatus.CANCELLED, label: 'Cancelled' },
              { value: TaskStatus.ON_HOLD, label: 'On Hold' },
            ]}
          />

          {/* Priority Filter */}
          <Select
            label="Priority"
            value={filters.priority || ''}
            onChange={(value) => handleFilterChange('priority', value)}
            options={[
              { value: '', label: 'All Priorities' },
              { value: TaskPriority.LOW, label: 'Low' },
              { value: TaskPriority.MEDIUM, label: 'Medium' },
              { value: TaskPriority.HIGH, label: 'High' },
              { value: TaskPriority.URGENT, label: 'Urgent' },
            ]}
          />

          {/* Assignment Status Filter */}
          <Select
            label="Assignment Status"
            value={filters.assigned_to || ''}
            onChange={(value) => handleFilterChange('assignment_status', value)}
            options={[
              { value: '', label: 'All Tasks' },
              { value: 'assigned', label: 'Assigned' },
              { value: 'unassigned', label: 'Unassigned' },
            ]}
          />
        </div>
      </div>

      <DataTable<Task & Record<string, unknown>>
        columns={taskColumns}
        data={tasksData}
        loading={loading}
        onQueryChange={handleQueryChange}
        searchPlaceholder="Search tasks by title, description, or task number..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Task"
        message={
          taskToDelete ? (
            <div>
              <p className="mb-2">
                Are you sure you want to delete task <strong>{taskToDelete.task_number}</strong>?
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This action cannot be undone. All data associated with this task will be permanently removed.
              </p>
            </div>
          ) : (
            'Are you sure you want to delete this task?'
          )
        }
        confirmText="Yes, Delete Task"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={isDeleting}
      />

      {/* Reassign Task Modal */}
      <ReassignTaskModal
        isOpen={showReassignModal}
        onClose={handleCancelReassign}
        task={taskToReassign}
        onReassignSuccess={handleReassignSuccess}
      />

      {/* Task Modal */}
      <TaskModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleTaskSaved}
        task={editingTask}
      />
    </div>
  );
}