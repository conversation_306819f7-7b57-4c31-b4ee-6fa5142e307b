import { User } from './user.entity';
import { Documents } from './documents.entity';
export declare class ConsumerAffairsComplaint {
    complaint_id: string;
    complaint_number: string;
    complainant_id: string;
    complainee_reg_number?: string;
    title: string;
    description: string;
    category: string;
    status: string;
    priority: string;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
    created_at: Date;
    updated_at?: Date;
    deleted_at?: Date;
    created_by: string;
    updated_by?: string;
    complainant: User;
    assignee?: User;
    creator?: User;
    updater?: User;
    attachments: Documents[];
    status_history: ConsumerAffairsComplaintStatusHistory[];
    generateId(): void;
}
export declare class ConsumerAffairsComplaintStatusHistory {
    history_id: string;
    complaint_id: string;
    status: string;
    comment?: string;
    created_at: Date;
    created_by: string;
    complaint: ConsumerAffairsComplaint;
    creator: User;
    generateId(): void;
}
