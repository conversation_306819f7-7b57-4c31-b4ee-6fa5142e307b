{"version": 3, "file": "assign-task.dto.js", "sourceRoot": "", "sources": ["../../../src/dto/tasks/assign-task.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAiG;AACjG,6CAAmE;AACnE,8DAA2D;AAE3D,MAAa,aAAa;CA8BzB;AA9BD,sCA8BC;AAzBC;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mEAAmE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClH,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,IAAI,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC;IACpE,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;iDACO;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACI;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IAC/E,IAAA,8BAAY,GAAE;IACd,IAAA,4BAAU,GAAE;;+CACK;AASlB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,eAAe;QAC5B,IAAI,EAAE,2BAAY;QAClB,OAAO,EAAE,2BAAY,CAAC,MAAM;KAC7B,CAAC;IACD,IAAA,wBAAM,EAAC,2BAAY,CAAC;IACpB,IAAA,4BAAU,GAAE;;+CACW;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IACnE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;uDACa"}