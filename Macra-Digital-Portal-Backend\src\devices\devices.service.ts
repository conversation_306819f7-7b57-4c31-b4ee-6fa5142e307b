import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Device } from 'src/entities/device.entity';
import { CreateDeviceDto } from 'src/dto/devices/create-device.dto';
import { UpdateDeviceDto } from 'src/dto/devices/update-device.dto';
import { DeviceApprovalStatus } from 'src/dto/devices/device-enums';
import { BatchValidationResult } from 'src/dto/devices/batch-validate-imei.dto';

@Injectable()
export class DevicesService {
  constructor(
    @InjectRepository(Device)
    private readonly deviceRepo: Repository<Device>,
  ) {}

  // Device methods
  async createDevice(dto: CreateDeviceDto, userId?: string): Promise<Device> {
    try {
      // Note: IMEI and device serial number validation removed as these fields are no longer required

      // Prepare device data with defaults
      const deviceData = {
        ...dto,
        approval_status: dto.approval_status || DeviceApprovalStatus.PENDING,
        created_by: dto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      };

      // Note: created_by is now optional and can be null

      const entity = this.deviceRepo.create(deviceData);
      return this.deviceRepo.save(entity);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new ConflictException('Failed to create device. Please check for duplicate IMEI or serial number.');
    }
  }

  async findAllDevices(): Promise<Device[]> {
    return this.deviceRepo.find({
      relations: ['creator', 'updater', 'application'],
      order: { created_at: 'DESC' }
    });
  }

  async findOneDevice(id: string): Promise<Device> {
    const entity = await this.deviceRepo.findOne({
      where: { device_id: id },
      relations: ['creator', 'updater', 'application']
    });
    if (!entity) throw new NotFoundException(`Device with id ${id} not found`);
    return entity;
  }

  async findDevicesByApplication(applicationId: string): Promise<Device[]> {
    return this.deviceRepo.find({
      where: { application_id: applicationId },
      relations: ['creator', 'updater', 'application'],
      order: { created_at: 'DESC' }
    });
  }

  // Note: findDeviceByImei method removed as IMEI field is no longer available

  async updateDevice(id: string, dto: UpdateDeviceDto, userId?: string): Promise<Device> {
    const entity = await this.findOneDevice(id);

    // Note: IMEI and device serial number validation removed as these fields are no longer required

    // Prepare update data with optional updated_by
    const updateData = {
      ...dto,
    };

    const updated = this.deviceRepo.merge(entity, updateData);
    return this.deviceRepo.save(updated);
  }

  async removeDevice(id: string): Promise<void> {
    const entity = await this.findOneDevice(id);
    await this.deviceRepo.softRemove(entity);
  }

  async batchValidateImeis(imeis: string[]): Promise<BatchValidationResult[]> {
    const results: BatchValidationResult[] = [];

    for (const imei of imeis) {
      // Since IMEI field is removed, just perform basic format validation
      const cleanImei = imei.replace(/\D/g, '');
      const isValidFormat = cleanImei.length === 15;

      results.push({
        imei: cleanImei,
        isValid: isValidFormat,
        status: isValidFormat ? 'valid' : 'invalid',
        message: isValidFormat ? 'IMEI format is valid' : 'Invalid IMEI format',
        validatedAt: new Date().toISOString()
      });
    }

    return results;
  }
}
