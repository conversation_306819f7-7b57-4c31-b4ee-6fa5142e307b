"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplateService = void 0;
const common_1 = require("@nestjs/common");
const formatters_1 = require("../common/utils/formatters");
let EmailTemplateService = class EmailTemplateService {
    generateApplicationSubmittedTemplate(data) {
        const subject = `Application ${data.applicationNumber} Successfully Submitted - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Submitted</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">MACRA - Malawi Communications Regulatory Authority</h1>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Successfully Submitted</h2>
              
              <p>Dear ${data.applicantName},</p>
              
              <p>We are pleased to confirm that your license application has been successfully submitted to MACRA.</p>
              
              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Submission Date:</td>
                    <td style="padding: 8px 0;">${data.submissionDate}</td>
                  </tr>
                </table>
              </div>
              
              <h3 style="color: #d32f2f;">What Happens Next?</h3>
              <ol style="padding-left: 20px;">
                <li>Your application will be reviewed by our technical team</li>
                <li>We will conduct a preliminary assessment within 5-7 business days</li>
                <li>You will receive updates on the progress via email and SMS</li>
                <li>If additional information is required, we will contact you directly</li>
              </ol>
              
              <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Important:</strong> Please keep your application number <strong>${data.applicationNumber}</strong> for future reference. You can track your application status by logging into your MACRA portal account.</p>
              </div>
              
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Status</a>
              </div>
              ` : ''}
              
              <p>If you have any questions or concerns, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>
              
              <p>Thank you for choosing MACRA for your licensing needs.</p>
              
              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateTaskAssignedTemplate(data) {
        const subject = `New Task Assigned: ${data.taskTitle} - MACRA`;
        const priorityColor = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }[data.priority.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Task Assignment</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">MACRA - Task Assignment</h1>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">New Task Assigned</h2>
              
              <p>Dear ${data.assigneeName},</p>
              
              <p>You have been assigned a new task that requires your attention.</p>
              
              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Task Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Task:</td>
                    <td style="padding: 8px 0;">${data.taskTitle}</td>
                  </tr>

                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Priority:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.priority}
                      </span>
                    </td>
                  </tr>
                  ${data.dueDate ? `
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Due Date:</td>
                    <td style="padding: 8px 0;">${data.dueDate}</td>
                  </tr>
                  ` : ''}
                </table>
              </div>
              
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #856404;">Task Description</h4>
                <p style="margin-bottom: 0;">${data.taskDescription}</p>
              </div>
              
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Task Details</a>
              </div>
              ` : ''}
              
              <p>Please log into the MACRA portal to view the complete task details and begin your review.</p>
              
              <p>If you have any questions about this task, please contact your supervisor or the task assigner.</p>
              
              <p>Best regards,<br>
              <strong>MACRA System</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateTaskCompletedTemplate(data) {
        const subject = `Application Update: ${data.applicationNumber} - MACRA`;
        const outcomeColor = {
            'approved': '#28a745',
            'rejected': '#dc3545',
            'pending': '#ffc107',
            'completed': '#dc3545'
        }[data.outcome.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">MACRA - Application Update</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Review Update</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are writing to inform you that a review task for your application has been completed.</p>

              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Review Task:</td>
                    <td style="padding: 8px 0;">${data.taskTitle}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Completion Date:</td>
                    <td style="padding: 8px 0;">${data.completionDate}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Status:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${outcomeColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.outcome}
                      </span>
                    </td>
                  </tr>
                </table>
              </div>

              ${data.comments ? `
              <div style="background-color: #e9ecef; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">Reviewer Comments</h4>
                <p style="margin-bottom: 0;">${data.comments}</p>
              </div>
              ` : ''}

              ${data.nextSteps ? `
              <h3 style="color: #d32f2f;">Next Steps</h3>
              <p>${data.nextSteps}</p>
              ` : ''}

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Status</a>
              </div>
              ` : ''}

              <p>If you have any questions or need further assistance, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for your patience during the review process.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateApplicationStatusChangeTemplate(data) {
        const subject = `Application Status Update: ${data.applicationNumber} - MACRA`;
        const statusColor = {
            'submitted': '#dc3545',
            'under_review': '#ffc107',
            'evaluation': '#fd7e14',
            'approved': '#28a745',
            'rejected': '#dc3545',
            'withdrawn': '#6c757d'
        }[data.newStatus.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Status Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">MACRA - Status Update</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Status Update</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are writing to inform you that the status of your application has been updated.</p>

              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Previous Status:</td>
                    <td style="padding: 8px 0; text-transform: capitalize;">${data.oldStatus.replace('_', ' ')}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">New Status:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.newStatus.replace('_', ' ')}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Update Date:</td>
                    <td style="padding: 8px 0;">${data.changeDate}</td>
                  </tr>
                </table>
              </div>

              ${data.comments ? `
              <div style="background-color: #e9ecef; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">Additional Information</h4>
                <p style="margin-bottom: 0;">${data.comments}</p>
              </div>
              ` : ''}

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Details</a>
              </div>
              ` : ''}

              <p>You can track your application progress by logging into your MACRA portal account at any time.</p>

              <p>If you have any questions, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for your continued patience.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateLicenseApprovedTemplate(data) {
        const subject = `License Approved: ${data.licenseNumber} - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>License Approved</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">🎉 MACRA - License Approved!</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #28a745; margin-bottom: 20px;">Congratulations! Your License Has Been Approved</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are pleased to inform you that your license application has been <strong>approved</strong>!</p>

              <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #155724;">License Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">License Number:</td>
                    <td style="padding: 8px 0; font-weight: bold; color: #28a745;">${data.licenseNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Approval Date:</td>
                    <td style="padding: 8px 0;">${data.approvalDate}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Expiry Date:</td>
                    <td style="padding: 8px 0;">${data.expiryDate}</td>
                  </tr>
                </table>
              </div>

              <h3 style="color: #28a745;">Next Steps</h3>
              <ol style="padding-left: 20px;">
                <li>Download your official license certificate from the portal</li>
                <li>Review the terms and conditions of your license</li>
                <li>Ensure compliance with all regulatory requirements</li>
                <li>Set up renewal reminders before the expiry date</li>
              </ol>

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">Download License Certificate</a>
              </div>
              ` : ''}

              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Important:</strong> Please keep your license number <strong>${data.licenseNumber}</strong> safe and ensure you comply with all license conditions. Renewal will be required before ${data.expiryDate}.</p>
              </div>

              <p>If you have any questions about your license or need assistance, please contact us:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for choosing MACRA. We look forward to working with you.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateActivityNoteTemplate(data) {
        const subject = `Application Update: ${data.applicationNumber} - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">Application Update</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <p>Dear ${data.applicantName},</p>

              <p>We have an update regarding your application with MACRA:</p>

              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #d32f2f;">Application Details</h3>
                <p style="margin: 5px 0;"><strong>Application Number:</strong> ${data.applicationNumber}</p>
                <p style="margin: 5px 0;"><strong>License Type:</strong> ${data.licenseType}</p>
                ${data.step ? `<p style="margin: 5px 0;"><strong>Step:</strong> ${data.step}</p>` : ''}
                <p style="margin: 5px 0;"><strong>Update Type:</strong> ${data.category}</p>
                <p style="margin: 5px 0;"><strong>Date:</strong> ${data.createdDate}</p>
              </div>

              <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 20px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #dc3545;">Update Details</h4>
                <p style="margin: 0; white-space: pre-wrap;">${data.noteContent}</p>
              </div>

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application</a>
              </div>
              ` : ''}

              <p>If you have any questions about this update, please don't hesitate to contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for choosing MACRA for your licensing needs.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateInvoiceGeneratedTemplate(data) {
        const subject = `Invoice Generated: ${data.invoiceNumber} - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invoice Generated</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #f8f9fa; color: #333; padding: 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px; color: #333;">💳 MACRA - Invoice Generated</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #dc3545; margin-bottom: 20px;">Dear ${data.applicantName},</h2>

              <p>An invoice has been generated for your license application. Please find the details below:</p>

              <!-- Invoice Details Card -->
              <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #dc3545; margin-top: 0; margin-bottom: 15px;">📋 Invoice Details</h3>

                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">Invoice Number:</td>
                    <td style="padding: 8px 0; color: #1f2937;">${data.invoiceNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">Application Number:</td>
                    <td style="padding: 8px 0; color: #1f2937;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">License Type:</td>
                    <td style="padding: 8px 0; color: #1f2937;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">Description:</td>
                    <td style="padding: 8px 0; color: #1f2937;">${data.description}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">Amount:</td>
                    <td style="padding: 8px 0; color: #1f2937; font-size: 18px; font-weight: bold;">${(0, formatters_1.formatCurrency)(data.amount)}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #374151;">Due Date:</td>
                    <td style="padding: 8px 0; color: #dc2626; font-weight: bold;">${data.dueDate}</td>
                  </tr>
                </table>
              </div>

              <!-- Payment Instructions -->
              <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #92400e; margin-top: 0; margin-bottom: 15px;">💰 Payment Instructions</h3>
                <p style="margin-bottom: 10px; color: #92400e;">To complete your license application, please make payment using one of the following methods:</p>

                <div style="margin: 15px 0;">
                  <h4 style="color: #92400e; margin-bottom: 8px;">🏦 Bank Payment</h4>
                  <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li>Visit any bank branch or authorized agent</li>
                    <li>Use Invoice Number: <strong>${data.invoiceNumber}</strong></li>
                    <li>Reference: Application ${data.applicationNumber}</li>
                  </ul>
                </div>

                <div style="margin: 15px 0;">
                  <h4 style="color: #92400e; margin-bottom: 8px;">📱 Mobile Money</h4>
                  <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li>Use MACRA payment shortcode</li>
                    <li>Enter Invoice Number: <strong>${data.invoiceNumber}</strong></li>
                  </ul>
                </div>
              </div>

              <!-- Important Notice -->
              <div style="background-color: #fef2f2; border: 1px solid #fca5a5; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #dc2626; font-weight: bold;">⚠️ Important Notice:</p>
                <p style="margin: 5px 0 0 0; color: #dc2626;">Your application status has been updated to "Pending Payment". Please make payment by the due date to avoid delays in processing.</p>
              </div>

              <!-- Action Button -->
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  View Application Status
                </a>
              </div>
              ` : ''}

              <p>Once payment is confirmed, your application will proceed to the next stage of processing.</p>

              <p>If you have any questions about this invoice or need assistance with payment, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for choosing MACRA services.</p>

              <p>Best regards,<br>
              <strong>MACRA Billing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateDataBreachReportSubmittedTemplate(data) {
        const subject = `Data Breach Report ${data.reportNumber} Successfully Submitted - MACRA Data Protection`;
        const severityColor = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'critical': '#dc3545'
        }[data.severity.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Data Breach Report Submitted - MACRA</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

            <!-- Header -->
            <div style="background-color: #f8f9fa; padding: 30px 20px; text-align: center; border-bottom: 3px solid #dc3545;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 60px; margin-bottom: 15px;">
              <h1 style="color: #333; margin: 0; font-size: 24px; font-weight: bold;">Data Breach Report Submitted</h1>
              <p style="color: #666; margin: 10px 0 0 0;">Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Content -->
            <div style="padding: 30px 20px;">
              <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Dear ${data.reporterName},</p>

              <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 25px;">
                Thank you for submitting your data breach report to MACRA. We have received your report and it is now being processed by our Data Protection team.
              </p>

              <!-- Report Details -->
              <div style="background-color: #f8f9fa; border-left: 4px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 4px;">
                <h3 style="color: #dc3545; margin: 0 0 15px 0; font-size: 18px;">Report Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #333; width: 40%;">Report Number:</td>
                    <td style="padding: 8px 0; color: #666;">${data.reportNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #333;">Title:</td>
                    <td style="padding: 8px 0; color: #666;">${data.reportTitle}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #333;">Category:</td>
                    <td style="padding: 8px 0; color: #666;">${data.category}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #333;">Severity:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${severityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                        ${data.severity}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #333;">Submitted:</td>
                    <td style="padding: 8px 0; color: #666;">${data.submissionDate}</td>
                  </tr>
                </table>
              </div>

              <!-- Next Steps -->
              <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 20px; margin: 20px 0; border-radius: 4px;">
                <h4 style="color: #0066cc; margin: 0 0 10px 0;">What happens next?</h4>
                <ul style="color: #333; margin: 0; padding-left: 20px; line-height: 1.6;">
                  <li>Our Data Protection team will review your report within 24-48 hours</li>
                  <li>You will receive updates on the investigation progress</li>
                  <li>We may contact you for additional information if needed</li>
                  <li>You can track your report status through the customer portal</li>
                </ul>
              </div>

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                  Track Your Report
                </a>
              </div>
              ` : ''}

              <p style="font-size: 14px; color: #666; line-height: 1.6; margin-top: 25px;">
                If you have any questions about your report or need immediate assistance, please contact our Data Protection team:
              </p>

              <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p style="font-size: 14px; color: #666; line-height: 1.6;">
                Thank you for helping us protect personal data in Malawi.
              </p>

              <p style="font-size: 14px; color: #333; margin-top: 25px;">
                Best regards,<br>
                <strong>MACRA Data Protection Team</strong><br>
                Malawi Communications Regulatory Authority
              </p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateComplaintSubmittedTemplate(data) {
        const subject = `Complaint ${data.complaintNumber} Successfully Submitted - MACRA Consumer Affairs`;
        const priorityColor = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }[data.priority.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Complaint Submitted - MACRA</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

            <!-- Header -->
            <div style="background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); padding: 30px 20px; text-align: center;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 60px; margin-bottom: 15px;">
              <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: bold;">Complaint Submitted Successfully</h1>
              <p style="color: #e0e7ff; margin: 10px 0 0 0; font-size: 16px;">Your complaint has been received and is being processed</p>
            </div>

            <!-- Content -->
            <div style="padding: 30px 20px;">
              <p style="font-size: 16px; color: #374151; margin-bottom: 20px;">Dear ${data.complainantName},</p>

              <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 25px;">
                Thank you for submitting your complaint to MACRA Consumer Affairs. We have successfully received your complaint and it has been assigned the following reference number:
              </p>

              <!-- Complaint Details Card -->
              <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                <h3 style="color: #dc3545; margin: 0 0 15px 0; font-size: 18px;">Complaint Details</h3>

                <div style="margin-bottom: 12px;">
                  <strong style="color: #374151;">Complaint Number:</strong>
                  <span style="color: #1e40af; font-weight: bold; font-size: 16px;">${data.complaintNumber}</span>
                </div>

                <div style="margin-bottom: 12px;">
                  <strong style="color: #374151;">Title:</strong>
                  <span style="color: #6b7280;">${data.complaintTitle}</span>
                </div>

                <div style="margin-bottom: 12px;">
                  <strong style="color: #374151;">Category:</strong>
                  <span style="color: #6b7280;">${data.category}</span>
                </div>

                <div style="margin-bottom: 12px;">
                  <strong style="color: #374151;">Priority:</strong>
                  <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">${data.priority}</span>
                </div>

                <div style="margin-bottom: 0;">
                  <strong style="color: #374151;">Submission Date:</strong>
                  <span style="color: #6b7280;">${data.submissionDate}</span>
                </div>
              </div>

              <h3 style="color: #dc3545; margin: 25px 0 15px 0; font-size: 18px;">What Happens Next?</h3>

              <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 20px;">
                <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                  <li style="margin-bottom: 8px;">Your complaint will be reviewed by our Consumer Affairs team</li>
                  <li style="margin-bottom: 8px;">We will investigate the matter and contact relevant parties if necessary</li>
                  <li style="margin-bottom: 8px;">You will receive updates on the progress of your complaint</li>
                  <li style="margin-bottom: 0;">We aim to resolve complaints within 30 working days</li>
                </ul>
              </div>

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 25px 0;">
                <a href="${data.portalUrl}" style="background-color: #dc3545; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Track Your Complaint
                </a>
              </div>
              ` : ''}

              <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
                You can track the progress of your complaint using the complaint number provided above. If you have any additional information or questions, please contact our Consumer Affairs team.
              </p>

              <p style="font-size: 14px; color: #6b7280; margin-bottom: 20px;">
                If you have any questions, please contact our Consumer Affairs team:
              </p>
              <ul style="font-size: 14px; color: #6b7280; margin-bottom: 20px;">
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p style="font-size: 16px; color: #374151; margin-bottom: 0;">
                Thank you for bringing this matter to our attention.
              </p>

              <p style="font-size: 16px; color: #374151; margin-top: 20px;">
                Best regards,<br>
                <strong>MACRA Consumer Affairs Team</strong><br>
                Malawi Communications Regulatory Authority
              </p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generatePaymentConfirmationTemplate(data) {
        const subject = `Payment Confirmed: Invoice ${data.invoiceNumber} - MACRA`;
        const isFullyPaid = data.paidAmount >= data.invoiceAmount;
        const remainingAmount = data.invoiceAmount - data.paidAmount;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Payment Confirmed</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px;">✅ Payment Confirmed!</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <p>Dear ${data.clientName},</p>

              <p>We are pleased to confirm that your payment has been successfully processed and approved.</p>

              <!-- Payment Details -->
              <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0; color: #28a745;">💰 Payment Details</h3>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                  <span style="font-weight: bold;">Invoice Number:</span>
                  <span>${data.invoiceNumber}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                  <span style="font-weight: bold;">Payment Amount:</span>
                  <span style="color: #28a745; font-weight: bold;"> ${(0, formatters_1.formatCurrency)(data.paidAmount)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                  <span style="font-weight: bold;">Invoice Total:</span>
                  <span> ${(0, formatters_1.formatCurrency)(data.invoiceAmount)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                  <span style="font-weight: bold;">Payment Date:</span>
                  <span>${data.paymentDate}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                  <span style="font-weight: bold;">Processed By:</span>
                  <span>${data.processedBy}</span>
                </div>
                ${!isFullyPaid ? `
                <div style="display: flex; justify-content: space-between; margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                  <span style="font-weight: bold; color: #ffc107;">Remaining Balance:</span>
                  <span style="color: #ffc107; font-weight: bold;">${(0, formatters_1.formatCurrency)(remainingAmount)}</span>
                </div>
                ` : ''}
              </div>

              <!-- Status Notice -->
              ${isFullyPaid ? `
              <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #155724; font-weight: bold;">🎉 Invoice Fully Paid!</p>
                <p style="margin: 5px 0 0 0; color: #155724;">Your invoice has been fully paid and marked as complete. Thank you for your prompt payment!</p>
              </div>
              ` : `
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #856404; font-weight: bold;">📊 Partial Payment Received</p>
                <p style="margin: 5px 0 0 0; color: #856404;">This payment has been applied to your invoice. A remaining balance of ${(0, formatters_1.formatCurrency)(remainingAmount)} is still outstanding.</p>
              </div>
              `}

              <!-- Action Button -->
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  View Invoice Details
                </a>
              </div>
              ` : ''}

              <p>This payment confirmation serves as your receipt. Please keep this email for your records.</p>

              ${!isFullyPaid ? `
              <p><strong>Next Steps:</strong> If you need to make an additional payment for the remaining balance, please log into your customer portal or contact our billing team.</p>
              ` : ''}

              <p>If you have any questions about this payment or need assistance, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for your business and for choosing MACRA services.</p>

              <p>Best regards,<br>
              <strong>MACRA Billing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generate2FATemplate(data) {
        const subject = 'Verify OTP - MACRA Digital Portal';
        const html = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <title>Two-Factor Authentication - MACRA Digital Portal</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        </head>
        <body style="font-family: 'Segoe UI', Roboto, sans-serif; background-color: #f4f4f7; margin: 0; padding: 0; color: #333;">
          <div style="max-width: 600px; margin: 40px auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);">

            <!-- Header -->
            <div style="background-color: #d32f2f; padding: 20px; text-align: center;">
              <img width="50" height="50" src="cid:logo@macra" alt="MACRA Logo" />
            </div>

            <!-- Content -->
            <div style="padding: 30px 40px; text-align: center;">
              <h1 style="color: #2e2e2e; font-size: 24px; margin-bottom: 20px;">
                Hello <strong>${data.name}</strong>,
              </h1>
              <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 20px;">
                ${data.message}
                <br><br>
                <span style="font-size: x-large; font-weight: bold; color: #d32f2f;">${data.otp}</span><br><br><br>
                <span style="font-size: smaller; color: #999;">This one-time code is valid for 5 minutes. Please do not share it with anyone.</span>
              </p>
              <a href="${data.verifyUrl}" style="display: inline-block; margin-top: 25px; padding: 12px 24px; background-color: #d32f2f; color: #ffffff !important; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                Verify OTP
              </a>
            </div>

            <!-- Footer -->
            <div style="font-size: 13px; text-align: center; padding: 20px; color: #999;">
              &copy; ${new Date().getFullYear()} MACRA. All rights reserved.
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generatePasswordResetTemplate(data) {
        const subject = 'Password Reset - MACRA Digital Portal';
        const html = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <title>Password Reset - MACRA Digital Portal</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <style>
            .content {
              padding: 30px 40px;
              text-align: center;
            }
            .button {
              display: inline-block;
              margin-top: 25px;
              padding: 12px 24px;
              background-color: #28a745;
              color: #ffffff !important;
              text-decoration: none;
              border-radius: 6px;
              font-weight: bold;
              font-size: 16px;
            }
            .footer {
              font-size: 13px;
              text-align: center;
              padding: 20px;
              color: #999;
            }
          </style>
        </head>
        <body style="font-family: 'Segoe UI', Roboto, sans-serif; background-color: #f4f4f7; margin: 0; padding: 0; color: #333;">
          <div style="max-width: 600px; margin: 40px auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);">

            <!-- Header -->
            <div style="background-color: #28a745; padding: 20px; text-align: center;">
              <img width="50" height="50" src="cid:logo@macra" alt="MACRA Logo" />
            </div>

            <!-- Content -->
            <div class="content">
              <h1 style="color: #28a745; font-size: 24px; margin-bottom: 20px;">Password Reset Successful</h1>
              <p>Hello ${data.userName},</p>
              <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 20px;">
                Your password has been successfully reset. You can now log in using your new credentials.
              </p>
              <a href="${data.loginUrl}" class="button">Login to Your Account</a>
            </div>

            <!-- Footer -->
            <div class="footer">
              &copy; ${new Date().getFullYear()} MACRA. All rights reserved.
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateLoginAlertTemplate(data) {
        const subject = 'Login Alert - MACRA Digital Portal';
        const html = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <title>Login Alert - MACRA Digital Portal</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        </head>
        <body style="font-family: 'Segoe UI', Roboto, sans-serif; background-color: #f4f4f7; margin: 0; padding: 0; color: #333;">
          <div style="max-width: 600px; margin: 40px auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);">

            <!-- Header -->
            <div style="background-color: #ffc107; padding: 20px; text-align: center;">
              <img width="50" height="50" src="cid:logo@macra" alt="MACRA Logo" />
            </div>

            <!-- Content -->
            <div style="padding: 30px 40px; text-align: center;">
              <h1 style="color: #2e2e2e; font-size: 24px; margin-bottom: 20px;">
                Hello <strong>${data.userName}</strong>,
              </h1>
              <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 20px;">
                ${data.message}
              </p>

              <!-- Login Details -->
              <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                <h3 style="color: #495057; margin-top: 0;">Login Details:</h3>
                <p style="margin: 5px 0;"><strong>IP Address:</strong> ${data.ip}</p>
                <p style="margin: 5px 0;"><strong>Location:</strong> ${data.city}, ${data.country}</p>
                <p style="margin: 5px 0;"><strong>Device:</strong> ${data.userAgent}</p>
                <p style="margin: 5px 0;"><strong>Time:</strong> ${new Date().toLocaleString()}</p>
              </div>

              <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 20px;">
                If you didn't sign in, please secure your account immediately by changing your password.
              </p>

              <a href="${data.loginUrl}" style="display: inline-block; margin-top: 25px; padding: 12px 24px; background-color: #d32f2f; color: #ffffff !important; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                Access Your Account
              </a>
            </div>

            <!-- Footer -->
            <div style="font-size: 13px; text-align: center; padding: 20px; color: #999;">
              &copy; ${new Date().getFullYear()} MACRA. All rights reserved.
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateDocumentApprovalTemplate(data) {
        const subject = `Document Approved - ${data.documentName}`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Document Approved</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px;">✅ Document Approved!</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #28a745; margin-bottom: 20px;">Document Approval Notification</h2>

              <p>Dear ${data.userName},</p>

              <p>We are pleased to inform you that your document has been <strong>approved</strong>!</p>

              <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #155724; margin-top: 0;">Document Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Document Name:</td>
                    <td style="padding: 8px 0;">${data.documentName}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Approval Date:</td>
                    <td style="padding: 8px 0;">${data.approvalDate}</td>
                  </tr>
                  ${data.comment ? `
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Comment:</td>
                    <td style="padding: 8px 0;">${data.comment}</td>
                  </tr>
                  ` : ''}
                </table>
              </div>

              <p>Your document has been successfully processed and is now approved in our system.</p>

              <p>If you have any questions, please don't hesitate to contact our support team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Best regards,<br>
              <strong>MACRA Digital Portal Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateDocumentRejectionTemplate(data) {
        const subject = `Document Rejected - ${data.documentName}`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Document Rejected</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #dc3545; color: white; padding: 20px; text-align: center;">
              <img src="cid:logo@macra" alt="MACRA Logo" style="height: 50px; margin-bottom: 10px;">
              <h1 style="margin: 0; font-size: 24px;">❌ Document Rejected</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #dc3545; margin-bottom: 20px;">Document Rejection Notification</h2>

              <p>Dear ${data.userName},</p>

              <p>We regret to inform you that your document has been <strong>rejected</strong>.</p>

              <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #721c24; margin-top: 0;">Document Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Document Name:</td>
                    <td style="padding: 8px 0;">${data.documentName}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Rejection Date:</td>
                    <td style="padding: 8px 0;">${data.rejectionDate}</td>
                  </tr>
                  ${data.comment ? `
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Reason:</td>
                    <td style="padding: 8px 0;">${data.comment}</td>
                  </tr>
                  ` : ''}
                </table>
              </div>

              <h3 style="color: #dc3545;">Next Steps</h3>
              <ol style="padding-left: 20px;">
                <li>Review the feedback provided above</li>
                <li>Make the necessary corrections to your document</li>
                <li>Resubmit your document through the portal</li>
                <li>Contact support if you need clarification</li>
              </ol>

              <p>Please review the feedback provided and resubmit your document with the necessary corrections.</p>

              <p>If you have any questions about this decision, please contact our support team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Best regards,<br>
              <strong>MACRA Digital Portal Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
};
exports.EmailTemplateService = EmailTemplateService;
exports.EmailTemplateService = EmailTemplateService = __decorate([
    (0, common_1.Injectable)()
], EmailTemplateService);
//# sourceMappingURL=email-template.service.js.map