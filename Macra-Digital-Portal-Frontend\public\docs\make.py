import os
import pypandoc

# Folder containing .md files
input_folder = "../docs"
output_file = "./output.docx"
files = [
    "authentication.md", "user-management.md", 
    "license-applications.md", "data-breach.md", 
    "document-management.md", "notifications.md", 
    "application-tracking.md"]

# Collect all markdown content in sorted order
all_content = ""
for filename in files:
    if filename.lower().endswith(".md"):
        file_path = os.path.join(input_folder, filename)
        with open(file_path, "r", encoding="utf-8") as f:
            all_content += f.read() + "\n\n"  # Add spacing between files
# for filename in sorted(os.listdir(input_folder)):
#     if filename.lower().endswith(".md"):
#         file_path = os.path.join(input_folder, filename)
#         with open(file_path, "r", encoding="utf-8") as f:
#             all_content += f.read() + "\n\n"  # Add spacing between files

# Convert combined markdown text to one DOCX
pypandoc.convert_text(
    all_content,
    to='docx',
    format='md',
    outputfile=output_file,
    extra_args=['--standalone']
)

print(f"✅ Combined DOCX created: {output_file}")
