
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Management - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dark body {
        background-color: #111827;
      }
      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }
      .tab-button {
        position: relative;
        z-index: 1;
      }
      .tab-button.active {
        color: #e02b20;
        font-weight: 500;
      }
      .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e02b20;
      }
      .tab-content {
        display: block;
      }
      .tab-content.hidden {
        display: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <!-- Theme Toggle Script -->
    <script>
      // Theme management
      function initTheme() {
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }

      function toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        if (isDark) {
          document.documentElement.classList.remove('dark');
          localStorage.setItem('theme', 'light');
        } else {
          document.documentElement.classList.add('dark');
          localStorage.setItem('theme', 'dark');
        }
      }

      // Initialize theme on page load
      initTheme();
    </script>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white dark:bg-gray-800 shadow-lg flex-shrink-0 hidden md:block">
       <div class="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between w-full">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
            <button
              onclick="toggleTheme()"
              class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Toggle theme"
            >
              <i class="ri-moon-line dark:hidden"></i>
              <i class="ri-sun-line hidden dark:block"></i>
            </button>
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class=" flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50 "
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="../license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>

              </div>

              Spectrum Management
            </a>
          <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
</svg>

              </div>

              Financial Transactions
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="user-management.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                    placeholder="Search for licenses, users, or transactions..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="../account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">User Profile</h1>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Manage your personal information and preferences.
              </p>
            </div>

            <!-- Profile content -->
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Profile Information</h3>
                  <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Personal details and contact information.</p>
                </div>
                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-gray-800">
                  Edit Profile
                </button>
              </div>

              <div class="border-t border-gray-200 dark:border-gray-700">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex items-center space-x-5">
                    <div class="flex-shrink-0">
                      <img class="h-24 w-24 rounded-full" src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile">
                    </div>
                    <div>
                      <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">Emily Banda</h2>
                      <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Administrator</p>
                      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Member since January 2023</p>
                    </div>
                  </div>

                  <div class="mt-8 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Full name</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">Emily Banda</dd>
                    </div>

                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email address</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><EMAIL></dd>
                    </div>

                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone number</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">+****************</dd>
                    </div>

                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Role</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">Administrator</dd>
                    </div>

                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Department</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">IT Operations</dd>
                    </div>

                    <div class="sm:col-span-3">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">New York, NY</dd>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Security section -->
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Security Settings</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Manage your password and security preferences.</p>
              </div>

              <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div class="space-y-6">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Password</h4>
                    <div class="mt-2 flex items-center justify-between">
                      <p class="text-sm text-gray-500 dark:text-gray-400">Last changed 3 months ago</p>
                      <button type="button" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-gray-800">
                        Change password
                      </button>
                    </div>
                  </div>

                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Two-factor authentication</h4>
                    <div class="mt-2 flex items-center justify-between">
                      <p class="text-sm text-gray-500 dark:text-gray-400">Enabled</p>
                      <button type="button" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-gray-800">
                        Manage
                      </button>
                    </div>
                  </div>

                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Active sessions</h4>
                    <div class="mt-2">
                      <p class="text-sm text-gray-500 dark:text-gray-400">You're currently signed in on 2 devices</p>
                      <button type="button" class="mt-2 inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-gray-800">
                        View all sessions
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Preferences section -->
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Preferences</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Manage your notification and display preferences.</p>
              </div>

              <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div class="space-y-6">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Email notifications</h4>
                    <div class="mt-2">
                      <div class="flex items-start">
                        <div class="flex items-center h-5">
                          <input id="email-license" name="email-license" type="checkbox" class="focus:ring-primary h-4 w-4 text-primary border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" checked>
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="email-license" class="font-medium text-gray-700 dark:text-gray-300">License updates</label>
                          <p class="text-gray-500 dark:text-gray-400">Receive notifications about license status changes.</p>
                        </div>
                      </div>
                      <div class="mt-4 flex items-start">
                        <div class="flex items-center h-5">
                          <input id="email-user" name="email-user" type="checkbox" class="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded" checked>
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="email-user" class="font-medium text-gray-700">User activity</label>
                          <p class="text-gray-500">Receive notifications about new user registrations and activities.</p>
                        </div>
                      </div>
                      <div class="mt-4 flex items-start">
                        <div class="flex items-center h-5">
                          <input id="email-transaction" name="email-transaction" type="checkbox" class="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="email-transaction" class="font-medium text-gray-700">Transaction updates</label>
                          <p class="text-gray-500">Receive notifications about financial transactions.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 class="text-sm font-medium text-gray-900">Display preferences</h4>
                    <div class="mt-2">
                      <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-500">Theme</p>
                        <select class="mt-1 block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                          <option>Light</option>
                          <option>Dark</option>
                          <option>System</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <script>
      // Tab switching function
      function showTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });

        // Show the selected tab content
        const selectedContent = document.getElementById(`${tabId}-content`);
        if (selectedContent) {
          selectedContent.classList.remove('hidden');
          selectedContent.classList.add('active');
        }

        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.remove('active');
          button.classList.add('text-gray-500');
        });

        const activeTab = document.getElementById(`${tabId}-tab`);
        if (activeTab) {
          activeTab.classList.add('active');
          activeTab.classList.remove('text-gray-500');
        }
      }

      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.showTab = showTab;
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>
