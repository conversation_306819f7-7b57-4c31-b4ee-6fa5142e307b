import {
  <PERSON>,
  Get,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { VerificationService, PublicLicenseInfo } from '../common/services/verification.service';

@ApiTags('Public Verification')
@Controller('public')
export class PublicController {
  constructor(private readonly verificationService: VerificationService) {}

  @Get('verify/:licenseNumber')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Verify license authenticity (Public endpoint - no authentication required)',
    description: 'Verify the authenticity of a license using its license number. This endpoint is publicly accessible and does not require authentication.'
  })
  @ApiParam({ 
    name: 'licenseNumber', 
    description: 'License number to verify',
    example: 'LIC-2024-01-001'
  })
  @ApiQuery({ 
    name: 'code', 
    required: false, 
    description: 'Optional verification code for enhanced security',
    example: 'ABC123DEF456'
  })
  @ApiResponse({
    status: 200,
    description: 'License verification result',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            licenseNumber: { type: 'string' },
            licenseType: { type: 'string' },
            status: { type: 'string' },
            issueDate: { type: 'string', format: 'date' },
            expiryDate: { type: 'string', format: 'date' },
            organizationName: { type: 'string' },
            isValid: { type: 'boolean' },
            verifiedAt: { type: 'string', format: 'date-time' },
          },
        },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'License not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'License not found' },
        error: { type: 'string', example: 'LICENSE_NOT_FOUND' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid license number format',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Invalid license number format' },
        error: { type: 'string', example: 'INVALID_FORMAT' },
      },
    },
  })
  async verifyLicense(
    @Param('licenseNumber') licenseNumber: string,
    @Query('code') verificationCode?: string,
  ): Promise<{
    success: boolean;
    data?: PublicLicenseInfo;
    message: string;
    error?: string;
  }> {
    // Validate license number format
    if (!this.isValidLicenseNumberFormat(licenseNumber)) {
      throw new BadRequestException({
        success: false,
        message: 'Invalid license number format. Expected format: LIC-YYYY-MM-NNN',
        error: 'INVALID_FORMAT',
      });
    }

    // Validate verification code format if provided
    if (verificationCode && !this.verificationService.isValidVerificationCode(verificationCode)) {
      throw new BadRequestException({
        success: false,
        message: 'Invalid verification code format',
        error: 'INVALID_VERIFICATION_CODE',
      });
    }

    try {
      const licenseInfo = await this.verificationService.verifyLicense(licenseNumber, verificationCode);

      if (!licenseInfo) {
        throw new NotFoundException({
          success: false,
          message: 'License not found or verification failed',
          error: 'LICENSE_NOT_FOUND',
        });
      }

      return {
        success: true,
        data: licenseInfo,
        message: 'License verified successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        success: false,
        message: 'Verification failed due to system error',
        error: 'VERIFICATION_ERROR',
      });
    }
  }

  @Get('verify-status/:licenseNumber')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Check if license exists and is verifiable (Public endpoint)',
    description: 'Check if a license exists in the system and can be verified. This is a lightweight endpoint for quick status checks.'
  })
  @ApiParam({ 
    name: 'licenseNumber', 
    description: 'License number to check',
    example: 'LIC-2024-01-001'
  })
  @ApiResponse({
    status: 200,
    description: 'License status check result',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        exists: { type: 'boolean' },
        verifiable: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async checkLicenseStatus(
    @Param('licenseNumber') licenseNumber: string,
  ): Promise<{
    success: boolean;
    exists: boolean;
    verifiable: boolean;
    message: string;
  }> {
    // Validate license number format
    if (!this.isValidLicenseNumberFormat(licenseNumber)) {
      throw new BadRequestException({
        success: false,
        exists: false,
        verifiable: false,
        message: 'Invalid license number format',
      });
    }

    try {
      const isVerifiable = await this.verificationService.isLicenseVerifiable(licenseNumber);

      return {
        success: true,
        exists: isVerifiable,
        verifiable: isVerifiable,
        message: isVerifiable 
          ? 'License exists and is verifiable' 
          : 'License not found or not verifiable',
      };
    } catch (error) {
      return {
        success: false,
        exists: false,
        verifiable: false,
        message: 'Error checking license status',
      };
    }
  }

  @Get('verification-stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get public verification statistics (Public endpoint)',
    description: 'Get general statistics about licenses in the system. No sensitive information is exposed.'
  })
  @ApiResponse({
    status: 200,
    description: 'Verification statistics',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalLicenses: { type: 'number' },
            activeLicenses: { type: 'number' },
            expiredLicenses: { type: 'number' },
            suspendedLicenses: { type: 'number' },
          },
        },
        message: { type: 'string' },
      },
    },
  })
  async getVerificationStats(): Promise<{
    success: boolean;
    data: any;
    message: string;
  }> {
    try {
      const stats = await this.verificationService.getVerificationStats();

      return {
        success: true,
        data: stats,
        message: 'Verification statistics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: {
          totalLicenses: 0,
          activeLicenses: 0,
          expiredLicenses: 0,
          suspendedLicenses: 0,
        },
        message: 'Error retrieving verification statistics',
      };
    }
  }

  /**
   * Validate license number format
   * Expected format: LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001)
   */
  private isValidLicenseNumberFormat(licenseNumber: string): boolean {
    const pattern = /^LIC-\d{4}-\d{2}-\d{3}$/;
    return pattern.test(licenseNumber);
  }
}
