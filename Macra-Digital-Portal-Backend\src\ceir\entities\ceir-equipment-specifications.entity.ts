import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsUUID, IsString, IsOptional, IsBoolean, IsNumber, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';

import { CeirEquipmentTypeCategories } from './ceir-equipment-type-categories.entity';

// Network technology constants for validation
export const NETWORK_TECHNOLOGIES = [
  'gsm',
  'gprs',
  'edge',
  'umts',
  'hspa',
  'lte',
  'lte_advanced',
  '5g',
  'wifi',
  'bluetooth',
  'nfc',
  'satellite',
] as const;

// Antenna type constants for validation
export const ANTENNA_TYPES = [
  'internal',
  'external',
  'diversity',
  'mimo',
  'omnidirectional',
  'directional',
] as const;

@Entity('ceir_equipment_specifications')
export class CeirEquipmentSpecifications {
  @ApiProperty({
    description: 'Unique identifier for the equipment specification',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID()
  specification_id: string;

  @ApiProperty({
    description: 'Device ID this specification belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  device_id: string;

  @ApiProperty({
    description: 'Equipment category ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  equipment_category_id: string;

  @ApiProperty({
    description: 'Hardware version',
    example: 'HW v2.1'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  hardware_version?: string;

  @ApiProperty({
    description: 'Software version',
    example: 'SW v1.5.2'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  software_version?: string;

  @ApiProperty({
    description: 'Firmware version',
    example: 'FW v3.2.1'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  firmware_version?: string;

  @ApiProperty({
    description: 'Supported network technologies',
    type: 'array',
    example: ['gsm', 'umts', 'lte']
  })
  @Column({ type: 'simple-array', nullable: true })
  supported_network_technologies?: string[];

  @ApiProperty({
    description: 'Operating frequency bands',
    example: ['GSM 900 (880-915 MHz)', 'GSM 1800 (1710-1785 MHz)', 'UMTS 2100 (1920-1980 MHz)']
  })
  @Column({ type: 'simple-array', nullable: true })
  operating_frequency_bands?: string[];

  @ApiProperty({
    description: 'Maximum transmit power in dBm',
    example: 33.0
  })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  max_transmit_power_dbm?: number;

  @ApiProperty({
    description: 'Receiver sensitivity in dBm',
    example: -102.0
  })
  @Column({ type: 'decimal', precision: 6, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  receiver_sensitivity_dbm?: number;

  @ApiProperty({
    description: 'Antenna type',
    example: 'internal'
  })
  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @IsString()
  antenna_type?: string;

  @ApiProperty({
    description: 'Antenna gain in dBi',
    example: 2.15
  })
  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  antenna_gain_dbi?: number;

  @ApiProperty({
    description: 'SAR value (head) in W/kg',
    example: 0.98
  })
  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  sar_head_wkg?: number;

  @ApiProperty({
    description: 'SAR value (body) in W/kg',
    example: 1.15
  })
  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  sar_body_wkg?: number;

  @ApiProperty({
    description: 'Operating temperature range',
    example: '-10°C to +55°C'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  operating_temperature_range?: string;

  @ApiProperty({
    description: 'Storage temperature range',
    example: '-20°C to +70°C'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  storage_temperature_range?: string;

  @ApiProperty({
    description: 'Operating humidity range',
    example: '5% to 95% RH (non-condensing)'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  operating_humidity_range?: string;

  @ApiProperty({
    description: 'Power supply voltage',
    example: '3.7V DC (Li-ion battery)'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  power_supply_voltage?: string;

  @ApiProperty({
    description: 'Power consumption in watts',
    example: 2.5
  })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  power_consumption_watts?: number;

  @ApiProperty({
    description: 'Battery capacity in mAh',
    example: 4000
  })
  @Column({ type: 'integer', nullable: true })
  @IsOptional()
  @IsNumber()
  battery_capacity_mah?: number;

  @ApiProperty({
    description: 'Physical dimensions (L x W x H)',
    example: '158.2 x 73.7 x 7.9 mm'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  physical_dimensions?: string;

  @ApiProperty({
    description: 'Weight in grams',
    example: 169
  })
  @Column({ type: 'decimal', precision: 6, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  weight_grams?: number;

  @ApiProperty({
    description: 'IP rating for water/dust protection',
    example: 'IP68'
  })
  @Column({ type: 'varchar', length: 20, nullable: true })
  @IsOptional()
  @IsString()
  ip_rating?: string;

  @ApiProperty({
    description: 'Supported SIM card types',
    example: ['Nano-SIM', 'eSIM']
  })
  @Column({ type: 'simple-array', nullable: true })
  supported_sim_types?: string[];

  @ApiProperty({
    description: 'Memory specifications',
    example: '8GB RAM, 128GB Storage, microSD up to 1TB'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  memory_specifications?: string;

  @ApiProperty({
    description: 'Display specifications',
    example: '6.1" Super AMOLED, 2400x1080, 421 PPI'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  display_specifications?: string;

  @ApiProperty({
    description: 'Camera specifications',
    example: 'Triple rear: 64MP main + 12MP ultra-wide + 5MP macro, 32MP front'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  camera_specifications?: string;

  @ApiProperty({
    description: 'Connectivity features',
    example: ['Wi-Fi 6', 'Bluetooth 5.2', 'NFC', 'USB-C', '3.5mm headphone jack']
  })
  @Column({ type: 'simple-array', nullable: true })
  connectivity_features?: string[];

  @ApiProperty({
    description: 'Security features',
    example: ['Fingerprint scanner', 'Face recognition', 'Knox security platform']
  })
  @Column({ type: 'simple-array', nullable: true })
  security_features?: string[];

  @ApiProperty({
    description: 'Compliance certifications',
    example: ['CE', 'FCC', 'IC', 'RoHS', 'REACH']
  })
  @Column({ type: 'simple-array', nullable: true })
  compliance_certifications?: string[];

  @ApiProperty({
    description: 'Date when specifications were last updated',
    example: '2024-01-15'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  specification_date?: Date;

  @ApiProperty({
    description: 'Additional technical notes',
    example: 'Supports carrier aggregation, VoLTE, and Wi-Fi calling'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  technical_notes?: string;

  @ApiProperty({
    description: 'Whether these specifications are currently active',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne('Device', { nullable: false })
  @JoinColumn({ name: 'device_id' })
  device: any;

  @ManyToOne(() => CeirEquipmentTypeCategories, { nullable: false })
  @JoinColumn({ name: 'equipment_category_id' })
  equipment_category: CeirEquipmentTypeCategories;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.specification_id) {
      this.specification_id = uuidv4();
    }
  }
}
