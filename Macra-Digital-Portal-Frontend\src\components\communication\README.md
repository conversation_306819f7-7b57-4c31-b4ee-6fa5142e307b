# Communication Center Components

A comprehensive set of React components for handling messaging and communication within the MACRA Digital Portal.

## Components Overview

### 1. SendMessageComponent
The main component for composing and sending messages with full features including attachments, priority settings, and multiple recipients.

### 2. QuickSendMessage
A simplified version of the send message component for quick integration and basic messaging needs.

### 3. CommunicationCenter
A complete communication dashboard with tabs for sending messages, viewing inbox, statistics, and settings.

## Installation & Setup

1. Ensure you have the required dependencies:
```bash
npm install @heroicons/react @headlessui/react
```

2. Import the components in your project:
```typescript
import SendMessageComponent from '@/components/communication/SendMessageComponent';
import QuickSendMessage from '@/components/communication/QuickSendMessage';
import CommunicationCenter from '@/components/communication/CommunicationCenter';
```

## Usage Examples

### Basic SendMessageComponent

```typescript
import React from 'react';
import SendMessageComponent from '@/components/communication/SendMessageComponent';
import { useSendMessage } from '@/hooks/useCommunication';

const MyComponent = () => {
  const { sendMessage, recipients, loading } = useSendMessage();

  const handleSendMessage = async (messageData) => {
    try {
      await sendMessage(messageData);
      console.log('Message sent successfully!');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  return (
    <SendMessageComponent
      onSendMessage={handleSendMessage}
      availableRecipients={recipients}
      messageType="email"
      allowAttachments={true}
      allowPriority={true}
      allowMultipleRecipients={true}
      disabled={loading}
    />
  );
};
```

### QuickSendMessage for Simple Use Cases

```typescript
import React from 'react';
import QuickSendMessage from '@/components/communication/QuickSendMessage';

const QuickMessageExample = () => {
  const recipients = [
    { id: '1', name: 'John Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' }
  ];

  const handleSendMessage = async (data) => {
    // Your API call here
    console.log('Sending message:', data);
  };

  return (
    <QuickSendMessage
      onSendMessage={handleSendMessage}
      availableRecipients={recipients}
      showSubject={true}
      showPriority={true}
      allowMultipleRecipients={true}
      placeholder="Type your quick message here..."
    />
  );
};
```

### Email Applicant Integration

```typescript
import React, { useState } from 'react';
import SendMessageComponent from '@/components/communication/SendMessageComponent';

const EmailApplicantModal = ({ applicant, isOpen, onClose }) => {
  const [recipients] = useState([
    {
      id: applicant.user_id,
      name: applicant.full_name,
      email: applicant.email,
      role: 'Applicant'
    }
  ]);

  const handleSendEmail = async (messageData) => {
    // Send email to applicant
    await fetch('/api/communication/send-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...messageData,
        applicantId: applicant.id
      })
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
        <h2 className="text-lg font-semibold mb-4">
          Email Applicant: {applicant.full_name}
        </h2>
        
        <SendMessageComponent
          onSendMessage={handleSendEmail}
          availableRecipients={recipients}
          defaultRecipients={recipients}
          messageType="email"
          allowAttachments={true}
          allowPriority={true}
          allowMultipleRecipients={false}
          placeholder="Type your message to the applicant..."
        />
        
        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};
```

### Task Assignment Notification

```typescript
import React from 'react';
import QuickSendMessage from '@/components/communication/QuickSendMessage';

const TaskAssignmentNotification = ({ task, assignee }) => {
  const handleSendNotification = async (data) => {
    await fetch('/api/tasks/send-notification', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...data,
        taskId: task.id,
        notificationType: 'task_assignment'
      })
    });
  };

  return (
    <QuickSendMessage
      onSendMessage={handleSendNotification}
      availableRecipients={[assignee]}
      defaultRecipients={[assignee]}
      defaultSubject={`Task Assigned: ${task.title}`}
      showSubject={false}
      showPriority={true}
      allowMultipleRecipients={false}
      placeholder="Add a note about this task assignment..."
    />
  );
};
```

## Component Props

### SendMessageComponent Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onSendMessage` | `(data: SendMessageData) => Promise<void>` | Required | Callback function when message is sent |
| `availableRecipients` | `MessageRecipient[]` | Required | List of available recipients |
| `defaultRecipients` | `MessageRecipient[]` | `[]` | Pre-selected recipients |
| `defaultSubject` | `string` | `''` | Default subject text |
| `defaultMessage` | `string` | `''` | Default message content |
| `messageType` | `'email' \| 'notification' \| 'announcement'` | `'email'` | Type of message |
| `allowAttachments` | `boolean` | `true` | Enable file attachments |
| `allowPriority` | `boolean` | `true` | Show priority selector |
| `allowMultipleRecipients` | `boolean` | `true` | Allow multiple recipients |
| `placeholder` | `string` | `'Type your message here...'` | Message textarea placeholder |
| `className` | `string` | `''` | Additional CSS classes |
| `disabled` | `boolean` | `false` | Disable the component |

### QuickSendMessage Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onSendMessage` | `(data: QuickMessageData) => Promise<void>` | Required | Callback function when message is sent |
| `availableRecipients` | `QuickMessageRecipient[]` | Required | List of available recipients |
| `defaultRecipients` | `QuickMessageRecipient[]` | `[]` | Pre-selected recipients |
| `defaultSubject` | `string` | `''` | Default subject text |
| `placeholder` | `string` | `'Type your message here...'` | Message textarea placeholder |
| `showSubject` | `boolean` | `true` | Show subject field |
| `showPriority` | `boolean` | `true` | Show priority selector |
| `allowMultipleRecipients` | `boolean` | `true` | Allow multiple recipients |
| `className` | `string` | `''` | Additional CSS classes |
| `disabled` | `boolean` | `false` | Disable the component |

## Data Types

### SendMessageData
```typescript
interface SendMessageData {
  recipients: string[];
  subject: string;
  message: string;
  attachments: File[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  messageType: 'email' | 'notification' | 'announcement';
}
```

### MessageRecipient
```typescript
interface MessageRecipient {
  id: string;
  name: string;
  email: string;
  role?: string;
  avatar?: string;
}
```

## Styling

The components use Tailwind CSS classes. You can customize the appearance by:

1. **Overriding with className prop:**
```typescript
<SendMessageComponent
  className="custom-message-component"
  // ... other props
/>
```

2. **Custom CSS:**
```css
.custom-message-component {
  /* Your custom styles */
}
```

## Integration with Backend

### API Endpoints Expected

The components expect these API endpoints:

- `POST /communication/messages` - Send message
- `GET /communication/recipients` - Get available recipients
- `GET /communication/templates` - Get message templates
- `POST /communication/messages/bulk` - Bulk operations

### Example Backend Integration

```typescript
// In your page or component
const handleSendMessage = async (messageData: SendMessageData) => {
  const formData = new FormData();
  
  formData.append('recipients', JSON.stringify(messageData.recipients));
  formData.append('subject', messageData.subject);
  formData.append('message', messageData.message);
  formData.append('priority', messageData.priority);
  formData.append('messageType', messageData.messageType);
  
  messageData.attachments.forEach((file) => {
    formData.append('attachments', file);
  });
  
  const response = await fetch('/api/communication/messages', {
    method: 'POST',
    body: formData
  });
  
  if (!response.ok) {
    throw new Error('Failed to send message');
  }
  
  return response.json();
};
```

## Features

### ✅ Core Features
- ✅ Multiple recipient selection with search
- ✅ Priority levels (Low, Normal, High, Urgent)
- ✅ File attachments with drag & drop
- ✅ Message templates
- ✅ Real-time validation
- ✅ Success/error notifications
- ✅ Responsive design
- ✅ Accessibility support

### ✅ Message Types
- ✅ Email messages
- ✅ System notifications
- ✅ Announcements
- ✅ Task assignments
- ✅ Alerts and reminders

### ✅ Advanced Features
- ✅ Message scheduling
- ✅ Delivery reports
- ✅ Read receipts
- ✅ Message threading
- ✅ Bulk operations
- ✅ Search and filtering

## Best Practices

1. **Always handle errors gracefully**
2. **Validate recipient data before sending**
3. **Use appropriate message types for different scenarios**
4. **Implement proper loading states**
5. **Provide clear user feedback**
6. **Follow accessibility guidelines**

## Troubleshooting

### Common Issues

1. **Recipients not loading:** Check API endpoint and network connectivity
2. **Attachments not uploading:** Verify file size limits and allowed types
3. **Messages not sending:** Check form validation and API response
4. **Styling issues:** Ensure Tailwind CSS is properly configured

### Debug Mode

Enable debug logging by setting:
```typescript
localStorage.setItem('communication-debug', 'true');
```
