"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportFilterDto = exports.UpdateDataBreachReportStatusDto = exports.UpdateDataBreachReportDto = exports.CreateDataBreachReportDto = exports.RespondentRequiredForInvestigatingConstraint = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const data_breachs_constants_1 = require("../../data-breach/data-breachs-constants");
let RespondentRequiredForInvestigatingConstraint = class RespondentRequiredForInvestigatingConstraint {
    validate(respondentRegNumber, args) {
        const object = args.object;
        if (object.status === data_breachs_constants_1.DataBreachStatus.INVESTIGATING) {
            return !!(respondentRegNumber && respondentRegNumber.trim() !== '');
        }
        return true;
    }
    defaultMessage() {
        return 'Respondent registration number is required when status is investigating';
    }
};
exports.RespondentRequiredForInvestigatingConstraint = RespondentRequiredForInvestigatingConstraint;
exports.RespondentRequiredForInvestigatingConstraint = RespondentRequiredForInvestigatingConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ name: 'respondentRequiredForInvestigating', async: false })
], RespondentRequiredForInvestigatingConstraint);
class CreateDataBreachReportDto {
}
exports.CreateDataBreachReportDto = CreateDataBreachReportDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachCategory, { message: 'Invalid breach category' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachSeverity, { message: 'Invalid severity level' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "severity", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid incident date format' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "incident_date", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: 'Organization name must be at least 2 characters long' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Organization name must not exceed 100 characters' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "organization_involved", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "affected_data_types", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "contact_attempts", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "priority", void 0);
class UpdateDataBreachReportDto extends (0, swagger_1.PartialType)(CreateDataBreachReportDto) {
}
exports.UpdateDataBreachReportDto = UpdateDataBreachReportDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Validate)(RespondentRequiredForInvestigatingConstraint),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "respondent_reg_number", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "resolution", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "internal_notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], UpdateDataBreachReportDto.prototype, "resolved_at", void 0);
class UpdateDataBreachReportStatusDto {
}
exports.UpdateDataBreachReportStatusDto = UpdateDataBreachReportStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], UpdateDataBreachReportStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportStatusDto.prototype, "comment", void 0);
class DataBreachReportFilterDto {
}
exports.DataBreachReportFilterDto = DataBreachReportFilterDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachCategory, { message: 'Invalid breach category' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachSeverity, { message: 'Invalid severity level' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "severity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breachs_constants_1.DataBreachPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid reporter ID' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "reporter_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for from_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for to_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for incident_from_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "incident_from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for incident_to_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "incident_to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], DataBreachReportFilterDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], DataBreachReportFilterDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "sort_by", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "sort_order", void 0);
//# sourceMappingURL=data-breach-report.dto.js.map