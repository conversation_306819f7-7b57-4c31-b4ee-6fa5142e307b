import { Injectable, BadRequestException } from '@nestjs/common';
import { isValidEmail, isValidUserId, hasRequiredUserFields } from '../types/user.types';
import { isValidTwoFactorAction } from '../types/auth.types';
import { TwoFactorAction } from '../constants/auth.constants';

export interface ValidationRule {
  field: string;
  value: any;
  rules: string[];
  customMessage?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  field?: string;
}

@Injectable()
export class ValidationService {
  /**
   * Validate email format
   */
  validateEmail(email: string, fieldName: string = 'email'): ValidationResult {
    if (!email) {
      return {
        isValid: false,
        errors: [`${fieldName} is required`],
        field: fieldName,
      };
    }

    if (!isValidEmail(email)) {
      return {
        isValid: false,
        errors: [`${fieldName} must be a valid email address`],
        field: fieldName,
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Validate user ID format
   */
  validateUserId(userId: string, fieldName: string = 'userId'): ValidationResult {
    if (!userId) {
      return {
        isValid: false,
        errors: [`${fieldName} is required`],
        field: fieldName,
      };
    }

    if (!isValidUserId(userId)) {
      return {
        isValid: false,
        errors: [`${fieldName} must be a valid UUID`],
        field: fieldName,
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string, fieldName: string = 'password'): ValidationResult {
    const errors: string[] = [];

    if (!password) {
      return {
        isValid: false,
        errors: [`${fieldName} is required`],
        field: fieldName,
      };
    }

    if (password.length < 8) {
      errors.push(`${fieldName} must be at least 8 characters long`);
    }

    if (password.length > 128) {
      errors.push(`${fieldName} must not exceed 128 characters`);
    }

    if (!/[A-Z]/.test(password)) {
      errors.push(`${fieldName} must contain at least one uppercase letter`);
    }

    if (!/[a-z]/.test(password)) {
      errors.push(`${fieldName} must contain at least one lowercase letter`);
    }

    if (!/\d/.test(password)) {
      errors.push(`${fieldName} must contain at least one number`);
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push(`${fieldName} must contain at least one special character`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      field: fieldName,
    };
  }

  /**
   * Validate two-factor action
   */
  validateTwoFactorAction(action: string): ValidationResult {
    if (!action) {
      return {
        isValid: false,
        errors: ['Two-factor action is required'],
        field: 'action',
      };
    }

    if (!isValidTwoFactorAction(action)) {
      return {
        isValid: false,
        errors: [`Invalid two-factor action. Must be one of: ${Object.values(TwoFactorAction).join(', ')}`],
        field: 'action',
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Validate user creation data
   */
  validateUserCreationData(data: any): ValidationResult {
    const errors: string[] = [];

    if (!hasRequiredUserFields(data)) {
      errors.push('Missing required user fields: email, password, first_name, last_name, phone');
    }

    if (data.email) {
      const emailValidation = this.validateEmail(data.email);
      if (!emailValidation.isValid) {
        errors.push(...emailValidation.errors);
      }
    }

    if (data.password) {
      const passwordValidation = this.validatePassword(data.password);
      if (!passwordValidation.isValid) {
        errors.push(...passwordValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate multiple fields at once
   */
  validateFields(rules: ValidationRule[]): ValidationResult {
    const allErrors: string[] = [];

    for (const rule of rules) {
      const fieldErrors = this.validateField(rule);
      allErrors.push(...fieldErrors);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
    };
  }

  /**
   * Validate a single field based on rules
   */
  private validateField(rule: ValidationRule): string[] {
    const errors: string[] = [];
    const { field, value, rules: validationRules, customMessage } = rule;

    for (const validationRule of validationRules) {
      switch (validationRule) {
        case 'required':
          if (!value || (typeof value === 'string' && value.trim() === '')) {
            errors.push(customMessage || `${field} is required`);
          }
          break;

        case 'email':
          if (value && !isValidEmail(value)) {
            errors.push(customMessage || `${field} must be a valid email address`);
          }
          break;

        case 'uuid':
          if (value && !isValidUserId(value)) {
            errors.push(customMessage || `${field} must be a valid UUID`);
          }
          break;

        case 'string':
          if (value && typeof value !== 'string') {
            errors.push(customMessage || `${field} must be a string`);
          }
          break;

        case 'number':
          if (value && typeof value !== 'number') {
            errors.push(customMessage || `${field} must be a number`);
          }
          break;

        default:
          // Handle custom validation rules like minLength:8, maxLength:100
          if (validationRule.includes(':')) {
            const [ruleName, ruleValue] = validationRule.split(':');
            const numericValue = parseInt(ruleValue, 10);

            switch (ruleName) {
              case 'minLength':
                if (value && value.length < numericValue) {
                  errors.push(customMessage || `${field} must be at least ${numericValue} characters long`);
                }
                break;

              case 'maxLength':
                if (value && value.length > numericValue) {
                  errors.push(customMessage || `${field} must not exceed ${numericValue} characters`);
                }
                break;

              case 'min':
                if (value && value < numericValue) {
                  errors.push(customMessage || `${field} must be at least ${numericValue}`);
                }
                break;

              case 'max':
                if (value && value > numericValue) {
                  errors.push(customMessage || `${field} must not exceed ${numericValue}`);
                }
                break;
            }
          }
          break;
      }
    }

    return errors;
  }

  /**
   * Throw validation error if validation fails
   */
  validateAndThrow(validationResult: ValidationResult): void {
    if (!validationResult.isValid) {
      throw new BadRequestException(validationResult.errors.join('; '));
    }
  }

  /**
   * Validate and throw for multiple validation results
   */
  validateAllAndThrow(validationResults: ValidationResult[]): void {
    const allErrors: string[] = [];
    
    for (const result of validationResults) {
      if (!result.isValid) {
        allErrors.push(...result.errors);
      }
    }

    if (allErrors.length > 0) {
      throw new BadRequestException(allErrors.join('; '));
    }
  }
}
