import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConsumerAffairsComplaintController } from './consumer-affairs-complaint.controller';
import { ConsumerAffairsComplaintService } from './consumer-affairs-complaint.service';
import {
  ConsumerAffairsComplaint,
  ConsumerAffairsComplaintStatusHistory
} from 'src/entities/consumer-affairs-complaint.entity';
import { NotificationsModule } from 'src/notifications/notifications.module';
import { DocumentsModule } from 'src/documents/documents.module';
import { User } from 'src/entities/user.entity';
import { AuditTrailService } from 'src/audit-trail/audit-trail.service';
import { AuditTrail } from 'src/entities/audit-trail.entity';
import { AdminAlert } from 'src/entities/admin_alerts.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConsumerAffairsComplaint,
      ConsumerAffairsComplaintStatusHistory,
      User,
      AuditTrail,
      AdminAlert,
    ]),
    MulterModule.register({
      storage: require('multer').memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
    NotificationsModule,
    DocumentsModule,
  ],
  controllers: [ConsumerAffairsComplaintController],
  providers: [
    ConsumerAffairsComplaintService,
    AuditTrailService,
  ],
  exports: [ConsumerAffairsComplaintService],
})
export class ConsumerAffairsModule {}
