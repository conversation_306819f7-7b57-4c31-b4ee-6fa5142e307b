import { Body, Controller, Get, HttpCode, Param, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AppService } from './app.service';
import { PostalCode } from './entities/postal-code.entity';
import { SearchPostalCodeDTO } from './dto/postal-code/search.dto';
import { GetAllPostalCodesDTO } from './dto/postal-code/get-all.dto';
import { StandardResponse } from './common/interceptors/response.interceptor';

@ApiTags('postal-codes')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'MACRA Digital Portal API',
      version: '1.0.0',
    };
  }


  @Post('/postal-codes/search')
  @UsePipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
    forbidUnknownValues: true
  }))
  @HttpCode(200)
  searchPostalCodes(@Body() searchCode: SearchPostalCodeDTO) {
    return this.appService.searchPostalCodes(searchCode);
  }

  @Get('/postal-codes/all')
  @ApiOperation({
    summary: 'Get all postal codes with pagination',
    description: 'Retrieve all postal codes with optional pagination parameters for frontend prefetch'
  })
  @ApiResponse({
    status: 200,
    description: 'Postal codes retrieved successfully',
    type: [PostalCode]
  })
  @UsePipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true
  }))
  async getAllPostalCodes(
    @Query() query: GetAllPostalCodesDTO
  ): Promise<StandardResponse<PostalCode[]>> {
    return this.appService.getAllPostalCodes({
      page: query.page || 1,
      limit: query.limit || 1000
    });
  }

  @Get('/postal-codes/search/:postal_code')
  @ApiOperation({
    summary: 'Get postal code by postal code',
    description: 'Retrieve postal code by postal code'
  })
  @ApiResponse({
    status: 200,
    description: 'Postal code retrieved successfully',
    type: PostalCode
  })
  @UsePipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true
  }))
  async getPostalCodeByCode(
    @Param('postal_code') postalCode: string
  ): Promise<PostalCode> {
    return this.appService.getPostalCodeByCode(postalCode);
  }
}
