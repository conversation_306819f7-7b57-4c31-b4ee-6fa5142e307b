import { PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsBoolean, IsInt } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationStatus } from '../../entities/notifications.entity';
import { CreateNotificationDto } from './create-notification.dto';

export class UpdateNotificationDto extends PartialType(CreateNotificationDto) {
  @ApiPropertyOptional({
    description: 'Notification status',
    enum: NotificationStatus
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiPropertyOptional({
    description: 'External provider message ID'
  })
  @IsOptional()
  @IsString()
  external_id?: string;

  @ApiPropertyOptional({
    description: 'Error message if notification failed'
  })
  @IsOptional()
  @IsString()
  error_message?: string;

  @ApiPropertyOptional({
    description: 'Number of retry attempts'
  })
  @IsOptional()
  @IsInt()
  retry_count?: number;

  @ApiPropertyOptional({
    description: 'Whether notification has been read'
  })
  @IsOptional()
  @IsBoolean()
  is_read?: boolean;

  @ApiPropertyOptional({
    description: 'When notification was sent'
  })
  @IsOptional()
  sent_at?: Date;

  @ApiPropertyOptional({
    description: 'When notification was delivered'
  })
  @IsOptional()
  delivered_at?: Date;

  @ApiPropertyOptional({
    description: 'When notification was read'
  })
  @IsOptional()
  read_at?: Date;
}
