<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ************ - MACRA Digital Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#e02b20",
              secondary: "#6366f1"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
            fontFamily: {
              'sans': ['Inter', 'system-ui', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style>
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .invoice-container {
        background: white;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-radius: 12px;
        overflow: hidden;
      }

      .logo-space {
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .logo-space h2 {
        margin-left: 1rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #e02b20;
      }

      .enhanced-table {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e5e7eb;
      }

      .enhanced-table th {
        background-color: #f9fafb;
        color: #374151;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        padding: 12px 16px;
        border-bottom: 1px solid #e5e7eb;
      }

      .enhanced-table td {
        padding: 12px 16px;
        border-bottom: 1px solid #f3f4f6;
        color: #1f2937;
      }

      .enhanced-table tr:last-child td {
        border-bottom: none;
      }

      .total-row {
        background-color: #fef2f2;
        font-weight: 600;
      }

      .total-row td {
        color: #dc2626;
        font-size: 1.1rem;
      }

      .info-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
      }

      .payment-card {
        background: linear-gradient(135deg, #e02b20 0%, #dc2626 100%);
        color: white;
        border-radius: 8px;
        padding: 20px;
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .status-partial {
        background-color: #fef3c7;
        color: #92400e;
      }

      .qr-fallback {
        background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSI0IiB5PSI0IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjIwIiB5PSI0IiB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSIyOCIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iMzYiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjQ0IiB5PSI0IiB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSI1MiIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iNjAiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjY4IiB5PSI0IiB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSI3NiIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgZmlsbD0iYmxhY2siLz4KPC9zdmc+');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      @media print {
        body {
          background: white;
        }
        .invoice-container {
          box-shadow: none;
          border-radius: 0;
        }
      }
    </style>
</head>
<body class="min-h-screen bg-gray-50 py-8 px-4">
    <div class="max-w-4xl mx-auto">
        <div class="invoice-container">
            <!-- Header with Logo Space -->
            <div class="bg-white border-b border-gray-200">
                <div class="px-8 py-6">
                    <div class="flex items-start justify-between">
                        <!-- Logo and MACRA Text -->
                        <div class="flex flex-col items-start">
                            <div class="logo-space mb-2">
                                <div class="w-20 h-20 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                                    <img src=".docs/images/macra-logo.png" alt="MACRA Logo" class="max-h-16 w-auto" onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\'text-xs text-gray-500 text-center\'>MACRA<br>LOGO</div>'" />
                                </div>
                            </div>
                            <div class="text-left">
                                <h2 class="text-sm font-semibold text-primary leading-tight">Malawi Communications</h2>
                                <h2 class="text-sm font-semibold text-primary leading-tight">Regulatory Authority</h2>
                            </div>
                        </div>

                        <!-- Header Text -->
                        <div class="text-right">
                            <h1 class="text-2xl font-bold text-gray-900 mb-1">The Republic of Malawi</h1>
                            <h3 class="text-lg text-gray-600">Government Bill</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Details Section -->
            <div class="px-8 py-6">
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Invoice Details</h4>
                    <div class="info-card">
                        <table class="enhanced-table w-full">
                            <tbody>
                                <tr>
                                    <td class="font-medium text-gray-700 w-1/3">Invoice Number</td>
                                    <td class="font-semibold text-gray-900">************</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700">Payment Option</td>
                                    <td>
                                        <span class="status-badge status-partial">PARTIAL</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700">Applicant Name</td>
                                    <td class="font-semibold text-gray-900">DAWN FM</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700">Phone Number</td>
                                    <td class="text-gray-900">+265 884 868 478</td>
                                </tr>
                                <tr>
                                    <td class="font-medium text-gray-700">License Category</td>
                                    <td class="font-medium text-gray-900">RADIO BROADCASTING LICENSE</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- License Categories Section -->
                <div class="mb-8">
                    <table class="enhanced-table w-full">
                        <thead>
                            <tr>
                                <th class="text-left">Items</th>
                                <th class="text-center">Quantity</th>
                                <th class="text-right">Amount (MWK)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-gray-900">VAT</td>
                                <td class="text-center font-medium">1</td>
                                <td class="text-right font-medium">565,000.00</td>
                            </tr>
                            <tr>
                                <td class="text-gray-900">Radio Broadcasting License</td>
                                <td class="text-center font-medium">1</td>
                                <td class="text-right font-medium">6,000,000.00</td>
                            </tr>
                            <tr class="total-row">
                                <td class="font-bold" colspan="2">Total Billed Amount</td>
                                <td class="text-right font-bold text-lg">6,565,000.00</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">
                            <span class="text-gray-600">Amount in Words:</span>
                            <span class="text-gray-900 font-semibold">SIX MILLION FIVE HUNDRED SIXTY FIVE THOUSAND MALAWI KWACHA</span>
                        </p>
                    </div>
                </div>
                <!-- Invoice Metadata Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Invoice Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="info-card">
                            <table class="enhanced-table w-full">
                                <tbody>
                                    <tr>
                                        <td class="font-medium text-gray-700">Date Issued</td>
                                        <td class="font-semibold text-gray-900">22 MAY 2025</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-gray-700">Expires On</td>
                                        <td class="font-semibold text-gray-900">31 MAR 2026</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="info-card">
                            <table class="enhanced-table w-full">
                                <tbody>
                                    <tr>
                                        <td class="font-medium text-gray-700">Printed By</td>
                                        <td class="font-semibold text-gray-900">MACRA SYSTEM ADMIN</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-gray-700">Printed On</td>
                                        <td class="font-semibold text-gray-900">MAY 26, 2025, 8:42:59 AM</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="info-card flex flex-col items-center justify-center">
                            <h5 class="text-sm font-medium text-gray-700 mb-3">QR Code</h5>
                            <div class="w-24 h-24 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center p-1 relative">
                                <canvas id="qrcode" class="w-full h-full"></canvas>
                                <div id="qr-fallback" class="qr-fallback w-full h-full absolute inset-0 rounded"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 text-center">Scan for verification</p>
                            <p class="text-xs text-gray-400 mt-1 text-center">INV-************</p>
                        </div>
                    </div>
                </div>

                <!-- Payment Instructions Section -->
                <div class="mb-8">
                    <div class="payment-card">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="ri-bank-line text-2xl text-white mr-4"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-white mb-2">How to Pay</h4>
                                <div class="space-y-2">
                                    <p class="text-white text-sm">
                                        <span class="font-medium">Via Bank:</span> Visit any Branch or Bank agent
                                    </p>
                                    <p class="text-white text-sm">
                                        <span class="font-medium">Invoice Number:</span>
                                        <span class="bg-white bg-opacity-20 px-2 py-1 rounded font-mono">************</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 border-t border-gray-200 px-8 py-6">
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        <span class="font-semibold text-primary">MACRA DIGITAL PORTAL SYSTEM</span>
                        © 2025 All Rights Reserved.
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                        Malawi Communications Regulatory Authority
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Generate QR Code
        function generateQRCode() {
            const qrCodeElement = document.getElementById('qrcode');
            const fallbackElement = document.getElementById('qr-fallback');

            if (!qrCodeElement) {
                console.error('QR Code element not found');
                return;
            }

            // Check if QRCode library is loaded
            if (typeof qrcode === 'undefined') {
                console.error('QRCode library not loaded - using fallback');
                return; // Keep fallback visible
            }

            try {
                console.log('Generating QR code with qrcode-generator library...');

                // Simple QR Code data for better compatibility
                const qrString = 'MACRA-INV-************-DAWN-FM-6565000-MWK-2025-05-22';

                // Create QR code using qrcode-generator library
                const qr = qrcode(0, 'M'); // Type 0 (auto), Error correction level M
                qr.addData(qrString);
                qr.make();

                // Get the module count (size of the QR code)
                const moduleCount = qr.getModuleCount();
                const cellSize = 88 / moduleCount; // Fit into 88x88 canvas

                // Set canvas size
                qrCodeElement.width = 88;
                qrCodeElement.height = 88;

                // Get canvas context
                const ctx = qrCodeElement.getContext('2d');

                // Clear canvas
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, 88, 88);

                // Draw QR code
                ctx.fillStyle = '#000000';
                for (let row = 0; row < moduleCount; row++) {
                    for (let col = 0; col < moduleCount; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                console.log('QR Code generated successfully');
                // Hide fallback since real QR code loaded
                if (fallbackElement) {
                    fallbackElement.style.display = 'none';
                }

            } catch (error) {
                console.error('Error in QR code generation:', error);
                // Keep fallback visible
            }
        }

        // Try to generate QR code when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, attempting to generate QR code...');
            generateQRCode();
        });

        // Fallback: try again after a short delay
        window.addEventListener('load', function() {
            setTimeout(function() {
                const qrElement = document.getElementById('qrcode');
                if (qrElement && !qrElement.querySelector('canvas')) {
                    console.log('Retrying QR code generation...');
                    generateQRCode();
                }
            }, 1000);
        });
    </script>
</body>
</html>