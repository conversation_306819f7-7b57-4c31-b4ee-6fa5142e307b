<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Time Analytics Report - MACRA</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metric-card.fast { border-left: 4px solid #28a745; }
        .metric-card.average { border-left: 4px solid #ffc107; }
        .metric-card.slow { border-left: 4px solid #dc3545; }
        .metric-card.info { border-left: 4px solid #17a2b8; }
        
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-card.fast .metric-value { color: #28a745; }
        .metric-card.average .metric-value { color: #ffc107; }
        .metric-card.slow .metric-value { color: #dc3545; }
        .metric-card.info .metric-value { color: #17a2b8; }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            position: relative;
        }

        canvas {
            height: 250px !important;
        }

        .chart-container h3 {
            margin-top: 15px;
            color: #333;
            text-align: center;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .performance-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .performance-excellent { background: #d4edda; color: #155724; }
        .performance-good { background: #d1ecf1; color: #0c5460; }
        .performance-average { background: #fff3cd; color: #856404; }
        .performance-poor { background: #f8d7da; color: #721c24; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            canvas { max-width: 100%; height: auto; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Processing Time Analytics Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> August 11, 2025, 04:23 PM CAT</div>
                <div><strong>Period:</strong> Q3 2025 (Jul-Sep)</div>
                <div><strong>Generated By:</strong> Analytics System</div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-grid">
            <div class="metric-card average">
                <div class="metric-value">18.5</div>
                <div class="metric-label">Average Processing Time</div>
            </div>
            <div class="metric-card fast">
                <div class="metric-value">7</div>
                <div class="metric-label">Fastest Processing</div>
            </div>
            <div class="metric-card slow">
                <div class="metric-value">45</div>
                <div class="metric-label">Longest Processing</div>
            </div>
            <div class="metric-card info">
                <div class="metric-value">82%</div>
                <div class="metric-label">Within SLA</div>
            </div>
            <div class="metric-card info">
                <div class="metric-value">15.2</div>
                <div class="metric-label">Median Days</div>
            </div>
        </div>

        <!-- Processing Time by License Type -->
        <div class="section">
            <h2 class="section-title">Processing Time by License Type</h2>
            <div class="chart-container">
                <canvas id="licenseTypeChart"></canvas>
                <h3>Average Processing Time vs SLA Targets</h3>
                <!-- Summary Stats -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #007bff;">Telecommunications</div>
                        <div style="font-size: 14px;">22.3 days (Target: 30)</div>
                        <div style="color: #28a745; font-size: 12px;">✓ Within SLA</div>
                    </div>
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #28a745;">Broadcasting</div>
                        <div style="font-size: 14px;">16.8 days (Target: 21)</div>
                        <div style="color: #28a745; font-size: 12px;">✓ Excellent</div>
                    </div>
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #ffc107;">Postal Services</div>
                        <div style="font-size: 14px;">14.2 days (Target: 15)</div>
                        <div style="color: #28a745; font-size: 12px;">✓ Excellent</div>
                    </div>
                    <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #dc3545;">Standards</div>
                        <div style="font-size: 14px;">19.5 days (Target: 20)</div>
                        <div style="color: #ffc107; font-size: 12px;">⚠ Average</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Stages Breakdown -->
        <div class="section">
            <h2 class="section-title">Processing Stage Time Distribution</h2>
            <div class="chart-container">
                <canvas id="stageBreakdownChart"></canvas>
                <h3>Processing Stage Breakdown</h3>
                <!-- Stage Details Table -->
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; margin-top: 20px;">
                    <div style="background: #dc3545; color: white; padding: 10px; font-weight: bold; text-align: center;">
                        Processing Stage Details
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0;">
                        <div style="padding: 15px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="width: 20px; height: 20px; background: #dc3545; border-radius: 50%; margin: 0 auto 8px;"></div>
                            <div style="font-weight: bold; color: #dc3545;">Technical evaluation</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">8.2 days</div>
                            <div style="font-size: 12px; color: #666;">44.3% of total time</div>
                            <div style="font-size: 12px; color: #ffc107;">⚠ Bottleneck Risk</div>
                        </div>
                        <div style="padding: 15px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="width: 20px; height: 20px; background: #ffc107; border-radius: 50%; margin: 0 auto 8px;"></div>
                            <div style="font-weight: bold; color: #ffc107;">Legal review</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">4.1 days</div>
                            <div style="font-size: 12px; color: #666;">22.2% of total time</div>
                            <div style="font-size: 12px; color: #28a745;">✓ Normal</div>
                        </div>
                        <div style="padding: 15px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="width: 20px; height: 20px; background: #28a745; border-radius: 50%; margin: 0 auto 8px;"></div>
                            <div style="font-weight: bold; color: #28a745;">Final approval</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">2.8 days</div>
                            <div style="font-size: 12px; color: #666;">15.1% of total time</div>
                            <div style="font-size: 12px; color: #28a745;">✓ Efficient</div>
                        </div>
                        <div style="padding: 15px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="width: 20px; height: 20px; background: #007bff; border-radius: 50%; margin: 0 auto 8px;"></div>
                            <div style="font-weight: bold; color: #007bff;">Initial review</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">2.5 days</div>
                            <div style="font-size: 12px; color: #666;">13.5% of total time</div>
                            <div style="font-size: 12px; color: #28a745;">✓ Fast</div>
                        </div>
                        <div style="padding: 15px; text-align: center;">
                            <div style="width: 20px; height: 20px; background: #6c757d; border-radius: 50%; margin: 0 auto 8px;"></div>
                            <div style="font-weight: bold; color: #6c757d;">Document generation</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">0.9 days</div>
                            <div style="font-size: 12px; color: #666;">4.9% of total time</div>
                            <div style="font-size: 12px; color: #28a745;">✓ Automated</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trends -->
        <div class="section">
            <h2 class="section-title">Monthly Processing Time Trends</h2>
            <div class="chart-container">
                <canvas id="monthlyTrendsChart"></canvas>
                <h3>Processing Time Improvement Trend</h3>
                <!-- Improvement Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="text-align: center; padding: 15px; background: white; border: 1px solid #e9ecef; border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;">↓ 7.3%</div>
                        <div style="font-size: 12px; color: #666;">Quarterly Improvement</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border: 1px solid #e9ecef; border-radius: 8px; border-left: 4px solid #007bff;">
                        <div style="font-size: 24px; font-weight: bold; color: #007bff;">2.4 days</div>
                        <div style="font-size: 12px; color: #666;">Total Reduction</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border: 1px solid #e9ecef; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <div style="font-size: 24px; font-weight: bold; color: #ffc107;">16.8 days</div>
                        <div style="font-size: 12px; color: #666;">Current Average</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border: 1px solid #e9ecef; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <div style="font-size: 24px; font-weight: bold; color: #dc3545;">15 days</div>
                        <div style="font-size: 12px; color: #666;">Target Goal</div>
                    </div>
                </div>
                <!-- Monthly Details -->
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; margin-top: 20px;">
                    <div style="background: #28a745; color: white; padding: 10px; font-weight: bold; text-align: center;">
                        Monthly Processing Time Details
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 0;">
                        <div style="padding: 12px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="font-weight: bold; color: #dc3545;">September</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">19.2 days</div>
                            <div style="font-size: 12px; color: #666;">Baseline</div>
                        </div>
                        <div style="padding: 12px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="font-weight: bold; color: #ffc107;">October</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">18.5 days</div>
                            <div style="font-size: 12px; color: #28a745;">↓ 3.6%</div>
                        </div>
                        <div style="padding: 12px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="font-weight: bold; color: #17a2b8;">November</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">17.8 days</div>
                            <div style="font-size: 12px; color: #28a745;">↓ 3.8%</div>
                        </div>
                        <div style="padding: 12px; border-right: 1px solid #e9ecef; text-align: center;">
                            <div style="font-weight: bold; color: #28a745;">December</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">17.2 days</div>
                            <div style="font-size: 12px; color: #28a745;">↓ 3.4%</div>
                        </div>
                        <div style="padding: 12px; text-align: center;">
                            <div style="font-weight: bold; color: #007bff;">January</div>
                            <div style="font-size: 18px; font-weight: bold; margin: 5px 0;">16.8 days</div>
                            <div style="font-size: 12px; color: #28a745;">↓ 2.3%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This report is confidential and intended for authorized personnel only.</p>
        </div>
    </div>

    <script>
        // Chart.js configuration for Processing Time by License Type (Bar Chart)
        const licenseTypeChart = new Chart(document.getElementById('licenseTypeChart'), {
            type: 'bar',
            data: {
                labels: ['Telecommunications', 'Broadcasting', 'Postal Services', 'Standards'],
                datasets: [
                    {
                        label: 'Average Processing Time',
                        data: [22.3, 16.8, 14.2, 19.5],
                        backgroundColor: 'rgba(40, 167, 69, 0.6)',
                        borderColor: '#28a745',
                        borderWidth: 1
                    },
                    {
                        label: 'Target Goal',
                        data: [30, 21, 15, 20],
                        backgroundColor: 'rgba(220, 53, 69, 0.3)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Days'
                        },
                        ticks: {
                            stepSize: 5
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'License Type'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw} days`;
                            }
                        }
                    }
                }
            }
        });

        // Chart.js configuration for Processing Stage Time Distribution (Bar Chart)
        const stageBreakdownChart = new Chart(document.getElementById('stageBreakdownChart'), {
            type: 'bar',
            data: {
                labels: ['Initial review', 'Technical evaluation', 'Legal review', 'Final approval', 'Document generation'],
                datasets: [{
                    label: 'Processing Time',
                    data: [2.5, 8.2, 4.1, 2.8, 0.9],
                    backgroundColor: ['#007bff', '#dc3545', '#ffc107', '#28a745', '#6c757d'],
                    borderColor: ['#007bff', '#dc3545', '#ffc107', '#28a745', '#6c757d'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Days'
                        },
                        ticks: {
                            stepSize: 2
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Processing Stage',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const percentages = [13.5, 44.3, 22.2, 15.1, 4.9];
                                return `${context.dataset.label}: ${context.raw} days (${percentages[context.dataIndex]}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Chart.js configuration for Monthly Processing Time Trends (Line Chart)
        const monthlyTrendsChart = new Chart(document.getElementById('monthlyTrendsChart'), {
            type: 'line',
            data: {
                labels: ['Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
                datasets: [
                    {
                        label: 'Average Processing Time',
                        data: [19.2, 18.5, 17.8, 17.2, 16.8],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 6,
                        pointBackgroundColor: '#28a745'
                    },
                    {
                        label: 'Target Goal',
                        data: [15, 15, 15, 15, 15],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.2)',
                        fill: false,
                        tension: 0,
                        pointRadius: 0,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 12,
                        max: 22,
                        title: {
                            display: true,
                            text: 'Days'
                        },
                        ticks: {
                            stepSize: 2
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw} days`;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>