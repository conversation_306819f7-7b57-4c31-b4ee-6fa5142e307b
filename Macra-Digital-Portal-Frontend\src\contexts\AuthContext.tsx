'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService } from '../services/auth.service';
import Cookies from 'js-cookie';
import { startTokenValidationTimer, isTokenExpired } from '../lib/authUtils';
import { AuthResponse, User, RegisterData, UpdateUserData } from '@/types';
import { userService } from '@/services/userService';

interface AuthContextType {
  user: User | null;
  token: string | null;
  twoFa: User | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<AuthResponse>;
  completeTwoFactorLogin: (token: string, userData: User, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<AuthResponse>;
  logout: () => void;
  updateUser: (user: UpdateUserData) => void;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [twoFa, setTwoFa] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  // Set mounted to true after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Start token validation timer when mounted and authenticated
  useEffect(() => {
    if (!mounted || !user || !token) return;
    // Start periodic token validation (check every 5 minutes instead of 1 minute)
    const validationTimer = startTokenValidationTimer(300000);
    return () => {
      clearInterval(validationTimer);
    };
  }, [mounted, user, token]);

  useEffect(() => {
    if (!mounted) return;
    // Execute immediately instead of waiting for next render cycle
    initAuth();
  }, [mounted]);

  // Check for existing token on mount with validation
  const initAuth = async () => {
    // Get saved data from cookies
    const savedToken = Cookies.get('auth_token');
    const savedUser = Cookies.get('auth_user');
    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser);
        // Validate token is not expired
        if (!isTokenExpired(savedToken)) {
          setToken(savedToken);
          setUser(user);
          authService.setAuthToken(savedToken);
        } else {
          authService.clearAuthToken();
        }
      } catch (error) {
        authService.clearAuthToken();
        console.error('AuthContext: User data parse failed', error);
      }

      try{
          const saved2faUser = Cookies.get('2fa_login_user');
          const twoFaUser = saved2faUser? JSON.parse(saved2faUser ): null;
          setTwoFa(twoFaUser);
      } catch(e) {
        console.error('AuthContext: 2FA user parse failed', e);
      }

    }
    setLoading(false);
  }

  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<AuthResponse> => {
    try {
      const response: AuthResponse = await authService.login({ email, password });
      // Validate response structure for normal login
      if (!response || !response.user) {
        throw new Error('Invalid response from authentication service');
      }

      // Store remember me preference for 2FA completion
      if (rememberMe) {
        sessionStorage.setItem('remember_me', 'true');
      } else {
        sessionStorage.removeItem('remember_me');
      }

      // Check if 2FA is required (backend returns empty access_token when 2FA is needed)
      const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');

      // Check if email verification is required (new field from backend)
      const requiresEmailVerification = response.requires_2fa && response.action === 'register';

      if (!requires2FA && !requiresEmailVerification) {
        // No 2FA or email verification required, set auth context immediately
        Cookies.set('auth_user', JSON.stringify(response.user));
        Cookies.set('auth_token', response.access_token || '');
        if(response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true'){
          await authService.generateTwoFactorCode(response.user.user_id, 'login');
          Cookies.set('2fa_login_user',  JSON.stringify(response.user));
        }
        await initAuth();
      } else if (requiresEmailVerification) {
        // Email verification required - store user data for verification page
        Cookies.set('2fa_login_user', JSON.stringify(response.user));
        console.log('AuthContext: Email verification required for user:', response.user.email);
      }

      return response;
    } catch (error) {
      console.error('AuthContext: Login failed', error);
      throw error;
    }
  };

  const completeTwoFactorLogin = async (token: string, userData: User, rememberMe: boolean = false): Promise<void> => {
    try {
      // Ensure roles is an array
      const roles: string[] = Array.isArray(userData.roles) ? userData.roles : [];

      // Add computed isAdmin property for backward compatibility
      const user = {
        ...userData,
        roles,
        isAdmin: roles.includes('administrator'),
        isCustomer: roles.includes('customer'),
      };

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthContext: 2FA login successful, setting user', user);
      }
      
      setToken(token);
      setUser(user);

      // Set cookies with appropriate expiration based on rememberMe
      const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
      Cookies.set('auth_token', token, { expires: cookieExpiration });
      Cookies.set('auth_user', JSON.stringify(user), { expires: cookieExpiration });
      authService.setAuthToken(token);
    } catch (error) {
      console.error('AuthContext: 2FA login completion failed', error);
      throw error;
    }
  };

  const register = async (userData: RegisterData): Promise<AuthResponse> => {
    const result = await authService.register(userData);
    if(result.user) {
      login(userData.email, userData.password);
    }
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Registration successful - user should login manually');
    }

    // Don't automatically log in the user after registration
    // User should be redirected to login page to manually log in
    // This follows the requirement that after account creation,
    // users should be redirected to login page, not dashboard

    return result;
  };

  const logout = (): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logging out user');
    }
    // Clear state
    setUser(null);
    setToken(null);
    setTwoFa(null);
    // Clear auth service token
    authService.clearAuthToken();
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logout complete');
    }
  };

  const updateUser = (updatedUser: UpdateUserData): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Updating user', updatedUser);
    }

    // Convert roles to string array if they're objects
    let roles: string[] = [];
    if (updatedUser.roles) {
      roles = updatedUser.roles.map((role: string | { name?: string; role_name?: string }) =>
        typeof role === 'string' ? role : role.name || role.role_name || 'unknown'
      );
    }

    userService.getUserById(updatedUser.user_id).then(user => {
      user.isCustomer =  roles.includes('customer')
      if (process.env.NODE_ENV === 'development') {
      }
      setUser(user);
      // Update cookies with new user data - use existing token expiration
      const existingToken = Cookies.get('auth_token');
      if (existingToken) {
        // Try to determine original expiration from token or use default
        const cookieExpiration = 1; // Default to 1 day for user updates
        Cookies.set('auth_user', JSON.stringify(updatedUser), { expires: cookieExpiration });
      }
    })
    // Ensure isAdmin property is set for backward compatibility

  };

  const value: AuthContextType = {
    user,
    token,
    login,
    twoFa,
    completeTwoFactorLogin,
    register,
    logout,
    updateUser,
    loading: loading || !mounted,
    isAuthenticated: mounted && !!user && !!token,
    
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
