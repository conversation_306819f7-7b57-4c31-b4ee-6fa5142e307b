import { Repository } from 'typeorm';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ConsumerAffairsComplaint, ConsumerAffairsComplaintStatusHistory } from 'src/entities/consumer-affairs-complaint.entity';
import { CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintStatusDto, UpdateConsumerAffairsComplaineeDto } from 'src/dto/consumer-affairs/consumer-affairs-complaint.dto';
import { ConsumerAffairsComplaintResponseDto } from './consumer-affairs-constants';
import { EmailTemplateService } from 'src/notifications/email-template.service';
import { NotificationHelperService } from 'src/notifications/notification-helper.service';
import { DocumentsService } from 'src/documents/documents.service';
import { User } from 'src/entities/user.entity';
export declare class ConsumerAffairsComplaintService {
    private complaintRepository;
    private statusHistoryRepository;
    private userRepository;
    private emailTemplateService;
    private notificationHelperService;
    private documentsService;
    private readonly logger;
    constructor(complaintRepository: Repository<ConsumerAffairsComplaint>, statusHistoryRepository: Repository<ConsumerAffairsComplaintStatusHistory>, userRepository: Repository<User>, emailTemplateService: EmailTemplateService, notificationHelperService: NotificationHelperService, documentsService: DocumentsService);
    create(createDto: CreateConsumerAffairsComplaintDto, complainantId: string): Promise<ConsumerAffairsComplaintResponseDto>;
    findAll(query: PaginateQuery, userId: string, isStaff?: boolean): Promise<Paginated<ConsumerAffairsComplaint>>;
    findOne(complaintId: string, userId: string, isStaff?: boolean): Promise<ConsumerAffairsComplaintResponseDto>;
    update(complaintId: string, updateDto: UpdateConsumerAffairsComplaintDto, userId: string, isStaff?: boolean): Promise<ConsumerAffairsComplaintResponseDto>;
    delete(complaintId: string, userId: string, isStaff?: boolean): Promise<void>;
    uploadAttachments(complaintId: string, files: Express.Multer.File[], userId: string): Promise<any[]>;
    getAttachments(complaintId: string, userId: string, isStaff: boolean): Promise<any[]>;
    deleteAttachment(documentId: string, userId: string, isStaff: boolean): Promise<void>;
    getAttachmentDownloadUrl(documentId: string, userId: string, isStaff: boolean, expirySeconds?: number): Promise<{
        downloadUrl: string;
        document: any;
    }>;
    updateStatus(complaintId: string, statusDto: UpdateConsumerAffairsComplaintStatusDto, userId: string): Promise<ConsumerAffairsComplaintResponseDto>;
    updateComplainee(complaintId: string, complaineeDto: UpdateConsumerAffairsComplaineeDto, userId: string): Promise<ConsumerAffairsComplaintResponseDto>;
    private sendComplaintSubmissionNotification;
    private sendComplaintStatusUpdateNotification;
    private createStatusHistory;
    private createQueryBuilder;
    private applyFilters;
    private mapToResponseDto;
}
