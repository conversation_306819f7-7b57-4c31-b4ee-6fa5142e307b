import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Documents, DocumentType } from '../entities/documents.entity';
import { User } from '../entities/user.entity';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { MinioService, UploadOptions } from 'src/common/services/minio.service';
import { ActivityNotesService } from '../services/activity-notes.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { EmailTemplateService } from '../notifications/email-template.service';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Documents)
    private documentsRepository: Repository<Documents>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private readonly minioService: MinioService,
    @Inject(forwardRef(() => ActivityNotesService))
    private readonly activityNotesService: ActivityNotesService,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelperService: NotificationHelperService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Documents> = {
    sortableColumns: ['created_at', 'updated_at', 'file_name', 'document_type'],
    searchableColumns: ['file_name', 'document_type', 'entity_type'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['creator', 'updater'],
  };

  async create(createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Documents> {
    const document = this.documentsRepository.create({
      ...createDocumentDto,
      is_required: createDocumentDto.is_required || false,
      created_by: createdBy,
    });

    return this.documentsRepository.save(document);
  }

  async uploadFile(
    file: Express.Multer.File,
    createDocumentDto: CreateDocumentDto,
    createdBy: string
  ): Promise<Documents> {
    // Define upload options based on document type
    const uploadOptions: UploadOptions = {
      folder: this.getFolderByDocumentType(createDocumentDto.document_type),
      allowedMimeTypes: this.getAllowedMimeTypes(),
      maxSize: 10 * 1024 * 1024, // 10MB
    };

    // Upload file to MinIO
    const uploadResult = await this.minioService.uploadFile(file, uploadOptions);

    // Create document record with MinIO file path
    const document = this.documentsRepository.create({
      ...createDocumentDto,
      file_name: createDocumentDto.file_name || file.originalname,
      file_path: uploadResult.fileName, // Store MinIO object name
      file_size: uploadResult.size,
      mime_type: uploadResult.mimeType,
      is_required: createDocumentDto.is_required || false,
      created_by: createdBy,
    });

    return this.documentsRepository.save(document);
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Documents>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their uploaded documents only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.documentsRepository, this.paginateConfig);
    }

    // For staff users, show all documents
    return paginate(query, this.documentsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Documents> {
    const document = await this.documentsRepository.findOne({
      where: { document_id: id },
      relations: ['creator', 'updater'],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async findByApplication(applicationId: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { entity_type: 'application', entity_id: applicationId },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByEntity(entityType: string, entityId: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { entity_type: entityType, entity_id: entityId },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByDocumentType(documentType: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { document_type: documentType },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findRequiredDocuments(): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { is_required: true },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateDocumentDto: UpdateDocumentDto, updatedBy: string): Promise<Documents> {
    const document = await this.findOne(id);

    Object.assign(document, updateDocumentDto, { updated_by: updatedBy });
    return this.documentsRepository.save(document);
  }

  async remove(id: string): Promise<void> {
    const document = await this.findOne(id);
    await this.documentsRepository.softDelete(document.document_id);
  }

  async getDocumentStats(): Promise<any> {
    const stats = await this.documentsRepository
      .createQueryBuilder('document')
      .select('document.document_type', 'document_type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('document.document_type')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.document_type] = parseInt(stat.count);
      return acc;
    }, {});
  }

  async getDocumentsByMimeType(mimeType: string): Promise<Documents[]> {
    return this.documentsRepository.find({
      where: { mime_type: mimeType },
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async getTotalFileSize(): Promise<number> {
    const result = await this.documentsRepository
      .createQueryBuilder('document')
      .select('SUM(document.file_size)', 'total_size')
      .getRawOne();

    return parseInt(result.total_size) || 0;
  }

  async getFileStream(filePath: string) {
    try {
      // Get file from MinIO
      return await this.minioService.getFile(filePath);
    } catch (error) {
      throw new NotFoundException(`File not found: ${filePath}`);
    }
  }

  async getFileUrl(filePath: string, expiry: number = 24 * 60 * 60): Promise<string> {
    try {
      return await this.minioService.getFileUrl(filePath, expiry);
    } catch (error) {
      throw new NotFoundException(`File not found: ${filePath}`);
    }
  }

  async checkMinioConnection(): Promise<boolean> {
    try {
      // Try to list objects in the bucket to test connection
      await this.minioService.listFiles('', 1);
      return true;
    } catch (error) {
      console.error('MinIO connection check failed:', error);
      return false;
    }
  }

  async approveDocument(documentId: string, approvedBy: string, comment?: string): Promise<Documents> {
    // First, verify that the approvedBy user exists
    const approver = await this.usersRepository.findOne({
      where: { user_id: approvedBy }
    });

    if (!approver) {
      throw new NotFoundException(`User with ID ${approvedBy} not found`);
    }

    const document = await this.documentsRepository.findOne({
      where: { document_id: documentId },
      relations: ['creator']
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    // Update approval fields
    document.approved_by = approvedBy;
    document.approved_at = new Date();
    document.comment = comment || undefined;
    document.status = 'approved';
    document.updated_by = approvedBy;

    const savedDocument = await this.documentsRepository.save(document);

    // Create activity note (non-blocking)
    setImmediate(async () => {
      try {
        await this.activityNotesService.create({
          entity_type: 'document',
          entity_id: documentId,
          note: `Document "${document.file_name}" has been approved${comment ? `. Comment: ${comment}` : ''}`,
          note_type: 'approval',
          category: 'document_approval',
          is_internal: false,
        }, approvedBy);
      } catch (error) {
        console.error('Failed to create activity note for document approval:', error);
      }
    });

    // Send email notification if document has a creator with email (non-blocking)
    setImmediate(async () => {
      try {
        if (document.creator?.email) {
          await this.notificationHelperService.sendEmailNotification({
            recipientId: document.created_by,
            recipientEmail: document.creator.email,
            recipientName: document.creator.first_name || 'User',
            subject: `Document Approved - ${document.file_name}`,
            message: `Your document "${document.file_name}" has been approved.`,
            htmlContent: this.emailTemplateService.generateDocumentApprovalTemplate({
              userName: document.creator.first_name || 'User',
              documentName: document.file_name,
              comment: comment,
              approvalDate: new Date().toLocaleDateString(),
            }).html,
            entityType: 'document',
            entityId: documentId,
            createdBy: approvedBy,
          });
        }
      } catch (error) {
        console.error('Failed to send document approval email:', error);
      }
    });

    return savedDocument;
  }

  async rejectDocument(documentId: string, rejectedBy: string, comment?: string): Promise<Documents> {
    // First, verify that the rejectedBy user exists
    const rejector = await this.usersRepository.findOne({
      where: { user_id: rejectedBy }
    });

    if (!rejector) {
      throw new NotFoundException(`User with ID ${rejectedBy} not found`);
    }

    const document = await this.documentsRepository.findOne({
      where: { document_id: documentId },
      relations: ['creator']
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    // Update rejection fields
    document.approved_by = undefined;
    document.approved_at = undefined;
    document.comment = comment || undefined;
    document.status = 'rejected';
    document.updated_by = rejectedBy;

    const savedDocument = await this.documentsRepository.save(document);

    // Create activity note (non-blocking)
    setImmediate(async () => {
      try {
        await this.activityNotesService.create({
          entity_type: 'document',
          entity_id: documentId,
          note: `Document "${document.file_name}" has been rejected${comment ? `. Comment: ${comment}` : ''}`,
          note_type: 'rejection',
          category: 'document_approval',
          is_internal: false,
        }, rejectedBy);
      } catch (error) {
        console.error('Failed to create activity note for document rejection:', error);
      }
    });

    // Send email notification if document has a creator with email (non-blocking)
    setImmediate(async () => {
      try {
        if (document.creator?.email) {
          await this.notificationHelperService.sendEmailNotification({
            recipientId: document.created_by,
            recipientEmail: document.creator.email,
            recipientName: document.creator.first_name || 'User',
            subject: `Document Rejected - ${document.file_name}`,
            message: `Your document "${document.file_name}" has been rejected.`,
            htmlContent: this.emailTemplateService.generateDocumentRejectionTemplate({
              userName: document.creator.first_name || 'User',
              documentName: document.file_name,
              comment: comment,
              rejectionDate: new Date().toLocaleDateString(),
            }).html,
            entityType: 'document',
            entityId: documentId,
            createdBy: rejectedBy,
          });
        }
      } catch (error) {
        console.error('Failed to send document rejection email:', error);
      }
    });

    return savedDocument;
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      await this.minioService.deleteFile(filePath);
    } catch (error) {
      throw new NotFoundException(`File not found: ${filePath}`);
    }
  }

  private getFolderByDocumentType(documentType: string): string {
    const folderMap: Record<string, string> = {
      [DocumentType.CERTIFICATE_INCORPORATION]: 'certificates',
      [DocumentType.MEMORANDUM_ASSOCIATION]: 'legal-documents',
      [DocumentType.BUSINESS_PLAN]: 'business-plans',
      [DocumentType.FINANCIAL_STATEMENTS]: 'financial',
      [DocumentType.TECHNICAL_PROPOSAL]: 'technical',
      [DocumentType.PROOF_OF_PAYMENT]: 'payments',
      [DocumentType.CV_DOCUMENT]: 'cvs',
      [DocumentType.OTHER]: 'other',
    };

    return folderMap[documentType] || 'documents';
  }

  private getAllowedMimeTypes(): string[] {
    return [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
    ];
  }


}
