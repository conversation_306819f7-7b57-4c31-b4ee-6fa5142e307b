import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DataBreachReportService } from './data-breachs.service';
import {
  CreateDataBreachReportDto,
  UpdateDataBreachReportDto,
  DataBreachReportFilterDto,
  UpdateDataBreachReportStatusDto,
} from 'src/dto/data-breach/data-breach-report.dto';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('data-breach-reports')
@ApiTags('Data Breach Reports')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
export class DataBreachReportController {
  constructor(
    private readonly reportService: DataBreachReportService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new data breach report' })
  @ApiResponse({ status: 201, description: 'Report created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(
    @Body(ValidationPipe) createDto: CreateDataBreachReportDto,
    @Request() req: any,
  ) {
    const report = await this.reportService.create(createDto, req.user.user_id);

    // Attachments are now handled separately via the polymorphic Documents API
    // Use POST /documents/upload with entity_type: 'data-breach' and entity_id: report.report_id

    return report;
  }

  @Get()
  @ApiOperation({ summary: 'Get all data breach reports with pagination' })
  @ApiResponse({ status: 200, description: 'Reports retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Request() req: any,
  ) {
    return this.reportService.findAll(
      query,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific data breach report by ID' })
  @ApiResponse({ status: 200, description: 'Report retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Report not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    return this.reportService.findOne(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Put(':id')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateDataBreachReportDto,
    @Request() req: any,
  ) {
    const report = await this.reportService.update(
      id,
      updateDto,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach report updated successfully',
      data: report,
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    await this.reportService.delete(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach report deleted successfully',
    };
  }

  // Staff-only endpoints
  @Put(':id/status')
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) statusDto: UpdateDataBreachReportStatusDto,
    @Request() req: any,
  ) {
    const report = await this.reportService.updateStatus(
      id,
      statusDto,
      req.user.user_id
    );

    return {
      success: true,
      message: 'Report status updated successfully',
      data: report,
    };
  }

  @Put(':id/assign')
  async assignReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('assigned_to', ParseUUIDPipe) assignedTo: string,
    @Request() req: any,
  ) {
    const report = await this.reportService.update(
      id,
      { assigned_to: assignedTo },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Report assigned successfully',
      data: report,
    };
  }

  @Get('stats/summary')
  async getStatsSummary(@Request() req: any) {
    // TODO: Implement statistics summary
    // This would return counts by status, category, severity, etc.
    return {
      success: true,
      message: 'Statistics retrieved successfully',
      data: {
        total: 0,
        by_status: {},
        by_category: {},
        by_severity: {},
        by_priority: {},
      },
    };
  }

  @Get('export/csv')
  async exportToCsv(
    @Query(ValidationPipe) filterDto: DataBreachReportFilterDto,
    @Request() req: any,
  ) {
    // TODO: Implement CSV export functionality
    return {
      success: true,
      message: 'Export functionality not yet implemented',
    };
  }

  // Attachment management is now handled by the polymorphic Documents API
  // Use the following endpoints instead:
  // POST /documents/upload - Upload attachments with entity_type: 'data-breach' and entity_id: report_id
  // GET /documents/entity/data-breach/:reportId - Get attachments for a report
  // DELETE /documents/:documentId - Delete an attachment

  // Special endpoint for urgent/critical reports
  @Get('urgent/alerts')
  async getUrgentAlerts(@Request() req: any) {
    const urgentReports = await this.reportService.findAll(
      {
        path: '/data-breach-reports/urgent/alerts',
        filter: {
          severity: 'critical',
          status: 'submitted'
        },
        limit: 10
      },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Urgent data breach alerts retrieved successfully',
      data: urgentReports.data,
    };
  }
}
