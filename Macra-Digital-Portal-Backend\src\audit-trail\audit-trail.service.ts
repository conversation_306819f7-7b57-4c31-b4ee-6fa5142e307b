import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, Like } from 'typeorm';
import { AuditTrail, AuditAction, AuditModule, AuditStatus } from '../entities/audit-trail.entity';
import { CreateAuditTrailDto } from '../dto/audit-trail/create-audit-trail.dto';
import { AuditTrailQueryDto } from '../dto/audit-trail/audit-trail-query.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { join } from 'path';
import { assetsDir } from 'src/app.module';
import { User } from 'src/entities';
import { MailerService } from '@nestjs-modules/mailer';
import { AdminAlert } from 'src/entities/admin_alerts.entity';

@Injectable()
export class AuditTrailService {
  private readonly logger = new Logger(AuditTrailService.name);

  private recentFailures: number[] = []; // timestamps of failures

  private readonly FAILURE_WINDOW = 10 * 60 * 1000; // 10 minutes
  private readonly FAILURE_THRESHOLD = 10;
  private lastAlertSentAt = 0;
  private readonly ALERT_COOLDOWN = 15 * 60 * 1000; // 15 min cooldown

  constructor(
    @InjectRepository(AuditTrail)
    private auditTrailRepository: Repository<AuditTrail>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(AdminAlert)
    private adminAlertRepository: Repository<AdminAlert>,
    private mailerService: MailerService,
  ) { }

  async create(createAuditTrailDto: CreateAuditTrailDto): Promise<AuditTrail> {
    try {
      // Validate required fields
      if (!createAuditTrailDto.action || !createAuditTrailDto.module || !createAuditTrailDto.status) {
        throw new BadRequestException('Action, module, and status are required fields');
      }

      const auditTrail = this.auditTrailRepository.create({
        ...createAuditTrailDto,
        resource_type: createAuditTrailDto.resourceType
      });
      const savedAuditTrail = await this.auditTrailRepository.save(auditTrail);

      return savedAuditTrail;
    } catch (error) {
      throw error;
    }
  }

  async findAll(query: PaginateQuery, filters?: AuditTrailQueryDto): Promise<PaginatedResult<AuditTrail>> {
    try {

      const config: PaginateConfig<AuditTrail> = {
        sortableColumns: ['created_at', 'action', 'module', 'status', 'user_id'],
        searchableColumns: ['description', 'resource_type', 'ip_address'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        filterableColumns: {
          action: true,
          module: true,
          status: true,
          user_id: true,
          resource_type: true,
          ip_address: true,
        },
        relations: ['user'],
      };

      // Build the query builder for custom filtering
      const queryBuilder = this.auditTrailRepository
        .createQueryBuilder('audit_trail')
        .leftJoinAndSelect('audit_trail.user', 'user');

      // Apply custom filters
      if (filters) {
        this.applyFilters(queryBuilder, filters);
      }

      const result = await paginate(query, queryBuilder, config);
      const transformedResult = PaginationTransformer.transform<AuditTrail>(result);

      return transformedResult;
    } catch (error) {
      throw error;
    }
  }

  private applyFilters(queryBuilder: any, filters: AuditTrailQueryDto): void {
    if (filters.dateFrom && filters.dateTo) {
      queryBuilder.andWhere('audit_trail.created_at BETWEEN :dateFrom AND :dateTo', {
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
      });
    } else if (filters.dateFrom) {
      queryBuilder.andWhere('audit_trail.created_at >= :dateFrom', {
        dateFrom: filters.dateFrom,
      });
    } else if (filters.dateTo) {
      queryBuilder.andWhere('audit_trail.created_at <= :dateTo', {
        dateTo: filters.dateTo,
      });
    }

    if (filters.userId) {
      queryBuilder.andWhere('audit_trail.user_id = :userId', {
        userId: filters.userId,
      });
    }

    if (filters.action) {
      queryBuilder.andWhere('audit_trail.action = :action', {
        action: filters.action,
      });
    }

    if (filters.module) {
      queryBuilder.andWhere('audit_trail.module = :module', {
        module: filters.module,
      });
    }

    if (filters.status) {
      queryBuilder.andWhere('audit_trail.status = :status', {
        status: filters.status,
      });
    }

    if (filters.ipAddress) {
      queryBuilder.andWhere('audit_trail.ip_address LIKE :ipAddress', {
        ipAddress: `%${filters.ipAddress}%`,
      });
    }

    if (filters.resourceType) {
      queryBuilder.andWhere('audit_trail.resource_type = :resourceType', {
        resourceType: filters.resourceType,
      });
    }

    if (filters.resourceId) {
      queryBuilder.andWhere('audit_trail.resource_id = :resourceId', {
        resourceId: filters.resourceId,
      });
    }
  }

  async findOne(id: string): Promise<AuditTrail | null> {
    try {
      return await this.auditTrailRepository.findOne({
        where: { audit_id: id },
        relations: ['user'],
      });
    } catch (error) {
      throw error;
    }
  }

  // Helper method to log user actions with proper type safety
  async logUserAction(
    action: AuditAction,
    module: AuditModule,
    resourceType: string,
    userId?: string,
    resourceId?: string,
    description?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    metadata?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string,
    sessionId?: string,
    status: AuditStatus = AuditStatus.SUCCESS,
    errorMessage?: string,
  ): Promise<AuditTrail> {
    try {
      const auditTrailDto: CreateAuditTrailDto = {
        action,
        module,
        status,
        resourceType,
        resourceId,
        description,
        oldValues,
        newValues,
        metadata,
        ipAddress,
        userAgent,
        sessionId,
        errorMessage,
        userId,
      };

      return await this.create(auditTrailDto);
    } catch (error) {
      this.logger.error('Failed to log user action', error);
      throw error;
    }
  }

  // Convenience method for logging authentication events
  async logAuthEvent(
    action: AuditAction,
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    sessionId?: string,
    status: AuditStatus = AuditStatus.SUCCESS,
    errorMessage?: string,
    metadata?: Record<string, any>,
  ): Promise<AuditTrail> {
    return this.logUserAction(
      action,
      AuditModule.AUTHENTICATION,
      'Authentication',
      userId,
      undefined,
      `User ${action} attempt`,
      undefined,
      undefined,
      metadata,
      ipAddress,
      userAgent,
      sessionId,
      status,
      errorMessage,
    );
  }


  private async notifyAdmins(failureCount: number): Promise<void> {
    // Implement notification logic here
    // For example, sending an email or logging to a separate monitoring system
    this.logger.warn(`Audit failure threshold reached: ${failureCount} failures in the last ${this.FAILURE_WINDOW / 60000} minutes`);
    const users = await this.usersRepository.find({
      where: { roles: { name: 'administrator' } },
    });
    users.forEach(async user => {
      this.mailerService.sendMail({
            to: user.email,
            subject: 'Too Many Audit Failures - MACRA Digital Portal',
            template: 'audit-failure',
            context: {
              userName: user.first_name,
              year: new Date().getFullYear(),
              loginUrl: `${process.env.FRONTEND_URL}/auth/login`,
              message: `There have been ${failureCount} audit failures in the last ${this.FAILURE_WINDOW / 60000} minutes. Please investigate.`
            },
            attachments: [
              {
                filename: 'macra-logo.png',
                path: join(assetsDir, 'macra-logo.png'),
                cid: 'logo@macra'
              }
            ]
          } as any).catch((error) => {
            this.logger.error('An error occurred while mailing admins on audit failure', error);
          });
        });
  }
}
