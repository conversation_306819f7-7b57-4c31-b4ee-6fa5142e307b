# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.idea/
.vscode/

# production
/build
/dist

./*.js

*.md
uploads/*
# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint
.eslintcache

# Optional stylelint
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rpt2_output/

# Optional REPL history
.node_repl_history
src/migrations/*.ts

### VisualStudioCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# env files (can opt-in for committing if needed)
.env
.env.development.local
.env.test.local
.env.production.local
.env.local
.env.backup
.env.production