'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { formatAmount, formatDate } from '@/utils/formatters';

interface InvoiceItem {
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
}

interface PaymentRecord {
  payment_id: string;
  amount: number;
  payment_date: string;
  payment_method: string;
  transaction_reference: string;
  status: string;
  notes?: string;
}

interface Invoice {
  invoice_id: string;
  invoice_number: string;
  amount: number;
  due_date: string;
  status: string;
  description: string;
  created_at: string;
  balance?: number;
  tax_amount?: number;
  subtotal?: number;
  items?: InvoiceItem[];
  payment_records?: PaymentRecord[];
  billing_address?: {
    name: string;
    address: string;
    city: string;
    country: string;
  };
  company_details?: {
    name: string;
    address: string;
    phone: string;
    email: string;
  };
}

interface ViewInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoiceId: string | null;
  onUploadProof?: (invoice: Invoice) => void;
}

const ViewInvoiceModal: React.FC<ViewInvoiceModalProps> = ({
  isOpen,
  onClose,
  invoiceId,
  onUploadProof
}) => {
  const { showError } = useToast();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'payments' | 'history'>('details');

  useEffect(() => {
    if (isOpen && invoiceId) {
      fetchInvoiceDetails();
    }
  }, [isOpen, invoiceId]);

  const fetchInvoiceDetails = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const invoiceData = await invoiceService.getInvoiceById(invoiceId);
      
      // Mock data for demonstration
      const mockInvoice: Invoice = {
        invoice_id: invoiceId || '',
        invoice_number: 'INV-2024-001',
        amount: 150000,
        due_date: '2024-02-15T00:00:00Z',
        status: 'pending',
        description: 'Type Approval Application Fee - Smartphone Model XYZ',
        created_at: '2024-01-15T10:30:00Z',
        balance: 150000,
        tax_amount: 22500,
        subtotal: 127500,
        items: [
          {
            description: 'Type Approval Application Fee',
            quantity: 1,
            unit_price: 100000,
            total: 100000
          },
          {
            description: 'Testing and Certification Fee',
            quantity: 1,
            unit_price: 27500,
            total: 27500
          }
        ],
        payment_records: [
          {
            payment_id: 'pay-001',
            amount: 50000,
            payment_date: '2024-01-20T10:00:00Z',
            payment_method: 'bank_transfer',
            transaction_reference: 'TXN-*********',
            status: 'verified',
            notes: 'Partial payment received'
          }
        ],
        billing_address: {
          name: 'TechCorp Solutions Ltd',
          address: '123 Business Street',
          city: 'Lilongwe',
          country: 'Malawi'
        },
        company_details: {
          name: 'Malawi Communications Regulatory Authority',
          address: 'MACRA House, Private Bag 261, Lilongwe 3',
          phone: '+265 1 770 100',
          email: '<EMAIL>'
        }
      };
      
      setInvoice(mockInvoice);
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      showError('Failed to load invoice details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'verified':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending_verification':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // TODO: Implement PDF download
    console.log('Download PDF functionality to be implemented');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl">
        {/* Header - Fixed */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <i className="ri-file-text-line text-white text-lg"></i>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Invoice Details
              </h3>
              {invoice && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {invoice.invoice_number} • {formatDate(invoice.created_at)}
                </p>
              )}
            </div>
          </div>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading invoice details...</span>
            </div>
          ) : invoice ? (
            <>
              {/* Invoice Header */}
              <div className="p-6 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                      {invoice.invoice_number}
                    </h2>
                    <div className="flex items-center gap-3">
                      <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getStatusColor(invoice.status)}`}>
                        {invoice.status.toUpperCase()}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Due: {formatDate(invoice.due_date)}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                      {formatAmount(invoice.amount, 'MWK')}
                    </div>
                    {invoice.balance !== invoice.amount && (
                      <div className="text-lg text-orange-600 dark:text-orange-400">
                        Balance: {formatAmount(invoice.balance || 0, 'MWK')}
                      </div>
                    )}
                  </div>
                </div>

                {/* Company and Billing Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">From:</h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <p className="font-medium">{invoice.company_details?.name}</p>
                      <p>{invoice.company_details?.address}</p>
                      <p>{invoice.company_details?.phone}</p>
                      <p>{invoice.company_details?.email}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Bill To:</h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <p className="font-medium">{invoice.billing_address?.name}</p>
                      <p>{invoice.billing_address?.address}</p>
                      <p>{invoice.billing_address?.city}, {invoice.billing_address?.country}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tabs */}
              <div className="border-b border-gray-200 dark:border-gray-700">
                <nav className="flex space-x-8 px-6">
                  {[
                    { key: 'details', label: 'Invoice Details', icon: 'ri-file-text-line' },
                    { key: 'payments', label: 'Payment History', icon: 'ri-money-dollar-circle-line' },
                    { key: 'history', label: 'Activity History', icon: 'ri-time-line' }
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key as any)}
                      className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                        activeTab === tab.key
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                    >
                      <i className={tab.icon}></i>
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'details' && (
                  <div className="space-y-6">
                    {/* Invoice Items */}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Invoice Items</h4>
                      <div className="overflow-x-auto">
                        <table className="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                          <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Description
                              </th>
                              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Qty
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Unit Price
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {invoice.items?.map((item, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {item.description}
                                </td>
                                <td className="px-4 py-3 text-sm text-center text-gray-900 dark:text-gray-100">
                                  {item.quantity}
                                </td>
                                <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-gray-100">
                                  {formatAmount(item.unit_price, 'MWK')}
                                </td>
                                <td className="px-4 py-3 text-sm text-right font-medium text-gray-900 dark:text-gray-100">
                                  {formatAmount(item.total, 'MWK')}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Invoice Summary */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                          <span className="text-gray-900 dark:text-gray-100">{formatAmount(invoice.subtotal || 0, 'MWK')}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Tax (17.5%):</span>
                          <span className="text-gray-900 dark:text-gray-100">{formatAmount(invoice.tax_amount || 0, 'MWK')}</span>
                        </div>
                        <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                          <div className="flex justify-between text-lg font-semibold">
                            <span className="text-gray-900 dark:text-gray-100">Total:</span>
                            <span className="text-gray-900 dark:text-gray-100">{formatAmount(invoice.amount, 'MWK')}</span>
                          </div>
                        </div>
                        {invoice.balance !== invoice.amount && (
                          <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                            <div className="flex justify-between text-lg font-semibold">
                              <span className="text-orange-600 dark:text-orange-400">Outstanding Balance:</span>
                              <span className="text-orange-600 dark:text-orange-400">{formatAmount(invoice.balance || 0, 'MWK')}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'payments' && (
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Payment History</h4>
                    {invoice.payment_records && invoice.payment_records.length > 0 ? (
                      <div className="space-y-3">
                        {invoice.payment_records.map((payment) => (
                          <div key={payment.payment_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-medium text-gray-900 dark:text-gray-100">
                                    {formatAmount(payment.amount, 'MWK')}
                                  </span>
                                  <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(payment.status)}`}>
                                    {payment.status.toUpperCase()}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                  <p>Method: {payment.payment_method.replace('_', ' ').toUpperCase()}</p>
                                  <p>Reference: {payment.transaction_reference}</p>
                                  <p>Date: {formatDate(payment.payment_date)}</p>
                                  {payment.notes && <p>Notes: {payment.notes}</p>}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        <i className="ri-money-dollar-circle-line text-4xl mb-3"></i>
                        <p>No payment records found</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'history' && (
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Activity History</h4>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            Invoice Created
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(invoice.created_at)}
                          </p>
                        </div>
                      </div>
                      {invoice.payment_records?.map((payment, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              Payment Received - {formatAmount(payment.amount, 'MWK')}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(payment.payment_date)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <i className="ri-file-text-line text-4xl mb-3"></i>
                <p>Invoice not found</p>
              </div>
            </div>
          )}
        </div>

        {/* Footer - Fixed */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handlePrint}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <i className="ri-printer-line mr-2"></i>
              Print
            </button>
            <button
              type="button"
              onClick={handleDownloadPDF}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <i className="ri-download-line mr-2"></i>
              Download PDF
            </button>
          </div>
          <div className="flex space-x-3">
            {invoice && invoice.status !== 'paid' && onUploadProof && (
              <button
                type="button"
                onClick={() => onUploadProof(invoice)}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <i className="ri-upload-line mr-2"></i>
                Upload Proof
              </button>
            )}
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewInvoiceModal;
