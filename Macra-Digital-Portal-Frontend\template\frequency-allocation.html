<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Frequency Allocation - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      .side-nav {
        scrollbar-width: none;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="user-management/user-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-user-line"></i>
              </div>
              User Management
            </a>
            <a
              href="license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Spectrum Management
            </a>
            <a
              href="financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-exchange-dollar-line"></i>
              </div>
              Transaction Management
            </a>
            <a
              href="reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-chart-line"></i>
              </div>
              Reports
            </a>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Spectrum Tools
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="Frequency-Allocation.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-radar-line"></i>
                </div>
                Frequency Allocation
              </a>
              <a
                href="spectrum-monitoring.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-pulse-line"></i>
                </div>
                Spectrum Monitoring
              </a>
              <a
                href="interference-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-error-warning-line"></i>
                </div>
                Interference Management
              </a>
              <a
                href="spectrum-license-application.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-file-list-3-line"></i>
                </div>
                License Application
              </a>
            </div>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="user-management/user-profile.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-user-settings-line"></i>
                </div>
                My Profile
              </a>
              <a
                href="system-settings.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-settings-3-line"></i>
                </div>
                Settings
              </a>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm z-10">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex">
                <div class="flex-shrink-0 flex items-center md:hidden">
                  <img src=".docs/images/macra-logo.png" alt="Logo" class="h-8 w-auto">
                </div>
                <button
                  type="button"
                  class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <span class="sr-only">Open main menu</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-menu-line ri-lg"></i>
                  </div>
                </button>
              </div>
              <div
                class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
              >
                <div class="max-w-lg w-full">
                  <label for="search" class="sr-only">Search</label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <div
                        class="w-5 h-5 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-search-line"></i>
                      </div>
                    </div>
                    <input
                      id="search"
                      name="search"
                      class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                      placeholder="Search for frequencies or bands..."
                      type="search"
                    />
                  </div>
                </div>
              </div>
              <div class="flex items-center">
                <div class="flex-shrink-0 relative ml-4">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">View notifications</span>
                      <div
                        class="w-6 h-6 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-notification-3-line"></i>
                      </div>
                    </button>
                  </div>
                </div>
                <div class="ml-4 relative flex-shrink-0">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">Open user menu</span>
                      <img
                        class="h-8 w-8 rounded-full"
                        src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt=""
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Frequency Allocation</h1>
                <div>
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-add-line"></i>
                    </div>
                    New Allocation
                  </button>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Manage and allocate frequency bands for different services.
              </p>
            </div>

            <!-- Allocation form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Allocate Frequency Bands
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Fill out the form below to allocate a new frequency band.
                </p>
              </div>
              <div class="px-4 py-5 sm:p-6">
                <form>
                  <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                      <label for="frequency-band" class="block text-sm font-medium text-gray-700">
                        Frequency Band
                      </label>
                      <div class="mt-1">
                        <input type="text" name="frequency-band" id="frequency-band" placeholder="e.g., 2.4 GHz - 2.4835 GHz" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="service-type" class="block text-sm font-medium text-gray-700">
                        Service Type
                      </label>
                      <div class="mt-1">
                        <select id="service-type" name="service-type" aria-label="Service Type" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                          <option>Mobile</option>
                          <option>Broadcasting</option>
                          <option>Satellite</option>
                          <option>Fixed Wireless</option>
                          <option>Amateur Radio</option>
                          <option>Other</option>
                        </select>
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="licensee" class="block text-sm font-medium text-gray-700">
                        Licensee
                      </label>
                      <div class="mt-1">
                        <input type="text" name="licensee" id="licensee" placeholder="Enter licensee name" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="location" class="block text-sm font-medium text-gray-700">
                        Geographic Location
                      </label>
                      <div class="mt-1">
                        <input type="text" name="location" id="location" placeholder="e.g., Lilongwe Region" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="start-date" class="block text-sm font-medium text-gray-700">
                        Start Date
                      </label>
                      <div class="mt-1">
                        <input type="date" name="start-date" id="start-date" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="end-date" class="block text-sm font-medium text-gray-700">
                        End Date
                      </label>
                      <div class="mt-1">
                        <input type="date" name="end-date" id="end-date" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>
                  </div>
                  <div class="mt-6 flex justify-end">
                    <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      Cancel
                    </button>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      Allocate
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Current allocations -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Current Allocations
                </h3>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Band
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Licensee
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expiry Date
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        2.4 GHz - 2.4835 GHz
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Mobile
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Global Technologies Inc.
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Nationwide
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        2025-12-31
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Active
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                        <a href="#" class="text-primary hover:text-primary mr-3">Edit</a>
                        <a href="#" class="text-red-600 hover:text-red-900">Revoke</a>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        87.5 - 108 MHz
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Broadcasting
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Capital Radio Ltd
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Lilongwe Region
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        2024-06-30
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Active
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                        <a href="#" class="text-primary hover:text-primary mr-3">Edit</a>
                        <a href="#" class="text-red-600 hover:text-red-900">Revoke</a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                  <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                  </a>
                  <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                  </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Showing <span class="font-medium">1</span> to <span class="font-medium">2</span> of <span class="font-medium">15</span> allocations
                    </p>
                  </div>
                  <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="ri-arrow-left-s-line"></i>
                      </a>
                      <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                      </a>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        2
                      </a>
                      <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="ri-arrow-right-s-line"></i>
                      </a>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Mobile menu toggle
      document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuButton = document.querySelector('button[aria-label="Open main menu"]');
        if (mobileMenuButton) {
          mobileMenuButton.addEventListener('click', function() {
            const sidebar = document.querySelector('aside');
            sidebar.classList.toggle('hidden');
          });
        }
      });
    </script>
  </body>
</html>
</body>
</html>