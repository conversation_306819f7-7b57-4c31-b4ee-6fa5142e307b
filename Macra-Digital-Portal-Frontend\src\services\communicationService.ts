import { apiClient } from '@/lib/api-client';
import {
  SendMessageData,
  SendMessageResponse,
  Message,
  MessageListResponse,
  MessageRecipient,
  RecipientListResponse,
  MessageFilter,
  MessageSearchResult,
  BulkMessageOperation,
  MessageDeliveryReport,
  MessageTemplate,
  MessageDraft,
  CommunicationStats,
  MessageAnalytics,
  NotificationSettings
} from '@/types/communication';

const processApiResponse = <T>(response: any): T => {
  if (response.data) {
    return response.data;
  }
  return response;
};

export const communicationService = {
  // Message sending
  sendMessage: async (messageData: SendMessageData): Promise<SendMessageResponse> => {
    const formData = new FormData();
    
    // Add basic message data
    formData.append('recipients', JSON.stringify(messageData.recipients));
    formData.append('subject', messageData.subject);
    formData.append('message', messageData.message);
    formData.append('priority', messageData.priority);
    formData.append('messageType', messageData.messageType);
    
    // Add scheduled date if provided
    if (messageData.scheduledAt) {
      formData.append('scheduledAt', messageData.scheduledAt.toISOString());
    }
    
    if (messageData.expiresAt) {
      formData.append('expiresAt', messageData.expiresAt.toISOString());
    }
    
    // Add attachments
    messageData.attachments.forEach((file, index) => {
      formData.append(`attachments`, file);
    });
    
    const response = await apiClient.post('/communication/messages', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return processApiResponse<SendMessageResponse>(response);
  },

  // Message retrieval
  getMessages: async (filter?: MessageFilter, page = 1, limit = 20): Promise<MessageListResponse> => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    if (filter) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (value instanceof Date) {
            params.append(key, value.toISOString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }
    
    const response = await apiClient.get(`/communication/messages?${params.toString()}`);
    return processApiResponse<MessageListResponse>(response);
  },

  getMessage: async (messageId: string): Promise<Message> => {
    const response = await apiClient.get(`/communication/messages/${messageId}`);
    return processApiResponse<Message>(response);
  },

  getInboxMessages: async (page = 1, limit = 20): Promise<MessageListResponse> => {
    const response = await apiClient.get(`/communication/messages/inbox?page=${page}&limit=${limit}`);
    return processApiResponse<MessageListResponse>(response);
  },

  getSentMessages: async (page = 1, limit = 20): Promise<MessageListResponse> => {
    const response = await apiClient.get(`/communication/messages/sent?page=${page}&limit=${limit}`);
    return processApiResponse<MessageListResponse>(response);
  },

  getDraftMessages: async (page = 1, limit = 20): Promise<MessageListResponse> => {
    const response = await apiClient.get(`/communication/messages/drafts?page=${page}&limit=${limit}`);
    return processApiResponse<MessageListResponse>(response);
  },

  // Message actions
  markAsRead: async (messageId: string): Promise<void> => {
    await apiClient.patch(`/communication/messages/${messageId}/read`);
  },

  markAsUnread: async (messageId: string): Promise<void> => {
    await apiClient.patch(`/communication/messages/${messageId}/unread`);
  },

  archiveMessage: async (messageId: string): Promise<void> => {
    await apiClient.patch(`/communication/messages/${messageId}/archive`);
  },

  deleteMessage: async (messageId: string): Promise<void> => {
    await apiClient.delete(`/communication/messages/${messageId}`);
  },

  // Bulk operations
  bulkOperation: async (operation: BulkMessageOperation): Promise<void> => {
    await apiClient.post('/communication/messages/bulk', operation);
  },

  // Recipients
  getRecipients: async (search?: string, page = 1, limit = 50): Promise<RecipientListResponse> => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await apiClient.get(`/communication/recipients?${params.toString()}`);
    return processApiResponse<RecipientListResponse>(response);
  },

  getAvailableRecipients: async (): Promise<MessageRecipient[]> => {
    const response = await apiClient.get('/communication/recipients/available');
    return processApiResponse<MessageRecipient[]>(response);
  },

  // Search
  searchMessages: async (query: string, filter?: MessageFilter): Promise<MessageSearchResult> => {
    const params = new URLSearchParams();
    params.append('q', query);
    
    if (filter) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (value instanceof Date) {
            params.append(key, value.toISOString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }
    
    const response = await apiClient.get(`/communication/messages/search?${params.toString()}`);
    return processApiResponse<MessageSearchResult>(response);
  },

  // Delivery reports
  getDeliveryReport: async (messageId: string): Promise<MessageDeliveryReport> => {
    const response = await apiClient.get(`/communication/messages/${messageId}/delivery-report`);
    return processApiResponse<MessageDeliveryReport>(response);
  },

  // Templates
  getTemplates: async (): Promise<MessageTemplate[]> => {
    const response = await apiClient.get('/communication/templates');
    return processApiResponse<MessageTemplate[]>(response);
  },

  createTemplate: async (template: Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<MessageTemplate> => {
    const response = await apiClient.post('/communication/templates', template);
    return processApiResponse<MessageTemplate>(response);
  },

  updateTemplate: async (templateId: string, template: Partial<MessageTemplate>): Promise<MessageTemplate> => {
    const response = await apiClient.patch(`/communication/templates/${templateId}`, template);
    return processApiResponse<MessageTemplate>(response);
  },

  deleteTemplate: async (templateId: string): Promise<void> => {
    await apiClient.delete(`/communication/templates/${templateId}`);
  },

  // Drafts
  saveDraft: async (draft: Omit<MessageDraft, 'id' | 'lastSavedAt'>): Promise<MessageDraft> => {
    const response = await apiClient.post('/communication/drafts', draft);
    return processApiResponse<MessageDraft>(response);
  },

  updateDraft: async (draftId: string, draft: Partial<MessageDraft>): Promise<MessageDraft> => {
    const response = await apiClient.patch(`/communication/drafts/${draftId}`, draft);
    return processApiResponse<MessageDraft>(response);
  },

  deleteDraft: async (draftId: string): Promise<void> => {
    await apiClient.delete(`/communication/drafts/${draftId}`);
  },

  // Statistics and Analytics
  getStats: async (): Promise<CommunicationStats> => {
    const response = await apiClient.get('/communication/stats');
    return processApiResponse<CommunicationStats>(response);
  },

  getAnalytics: async (period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<MessageAnalytics> => {
    const response = await apiClient.get(`/communication/analytics?period=${period}`);
    return processApiResponse<MessageAnalytics>(response);
  },

  // Notification settings
  getNotificationSettings: async (): Promise<NotificationSettings> => {
    const response = await apiClient.get('/communication/settings/notifications');
    return processApiResponse<NotificationSettings>(response);
  },

  updateNotificationSettings: async (settings: NotificationSettings): Promise<NotificationSettings> => {
    const response = await apiClient.patch('/communication/settings/notifications', settings);
    return processApiResponse<NotificationSettings>(response);
  },

  // File attachments
  downloadAttachment: async (messageId: string, attachmentId: string): Promise<Blob> => {
    const response = await apiClient.get(
      `/communication/messages/${messageId}/attachments/${attachmentId}/download`,
      { responseType: 'blob' }
    );
    return response.data;
  },

  // Real-time features
  markAllAsRead: async (): Promise<void> => {
    await apiClient.post('/communication/messages/mark-all-read');
  },

  getUnreadCount: async (): Promise<number> => {
    const response = await apiClient.get('/communication/messages/unread-count');
    return processApiResponse<{ count: number }>(response).count;
  },

  // Message threading
  getMessageThread: async (threadId: string): Promise<Message[]> => {
    const response = await apiClient.get(`/communication/threads/${threadId}/messages`);
    return processApiResponse<Message[]>(response);
  },

  replyToMessage: async (messageId: string, replyData: SendMessageData): Promise<SendMessageResponse> => {
    const formData = new FormData();
    
    formData.append('message', replyData.message);
    formData.append('priority', replyData.priority);
    
    replyData.attachments.forEach((file) => {
      formData.append('attachments', file);
    });
    
    const response = await apiClient.post(`/communication/messages/${messageId}/reply`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return processApiResponse<SendMessageResponse>(response);
  },

  forwardMessage: async (messageId: string, forwardData: SendMessageData): Promise<SendMessageResponse> => {
    const formData = new FormData();
    
    formData.append('recipients', JSON.stringify(forwardData.recipients));
    formData.append('message', forwardData.message);
    formData.append('priority', forwardData.priority);
    
    forwardData.attachments.forEach((file) => {
      formData.append('attachments', file);
    });
    
    const response = await apiClient.post(`/communication/messages/${messageId}/forward`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return processApiResponse<SendMessageResponse>(response);
  }
};

export default communicationService;
