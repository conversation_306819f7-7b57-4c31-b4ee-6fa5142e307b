<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Two-Factor Authentication - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      .verification-input {
        width: 3rem;
        height: 3rem;
        font-size: 1.5rem;
        text-align: center;
        border-radius: 0.5rem;
        border: 1px solid #d1d5db;
      }
      .verification-input:focus {
        outline: none;
        border-color: #e02b20;
        box-shadow: 0 0 0 1px #e02b20;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <img src=".docs/images/macra-logo.png" alt="Logo" class="mx-auto h-16 w-auto">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Two-Factor Authentication
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Enter the 6-digit code from your authenticator app
        </p>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form class="space-y-6" action="index.html" method="GET">
            <div>
              <label for="code" class="sr-only">Verification code</label>
              <div class="flex justify-between items-center space-x-2">
                <input type="text" maxlength="1" class="verification-input" autofocus>
                <input type="text" maxlength="1" class="verification-input">
                <input type="text" maxlength="1" class="verification-input">
                <input type="text" maxlength="1" class="verification-input">
                <input type="text" maxlength="1" class="verification-input">
                <input type="text" maxlength="1" class="verification-input">
              </div>
            </div>

            <div>
              <button type="submit"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Verify
              </button>
            </div>
          </form>

          <div class="mt-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">
                  Or
                </span>
              </div>
            </div>

            <div class="mt-6 text-center">
              <p class="text-sm text-gray-500 mb-2">
                Didn't receive the code?
              </p>
              <button type="button" class="font-medium text-primary hover:text-primary">
                Resend code
              </button>
              <p class="mt-4 text-sm">
                <a href="login.html" class="font-medium text-primary hover:text-primary">
                  Try another method
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Auto-focus next input when a digit is entered
      document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('.verification-input');

        inputs.forEach((input, index) => {
          input.addEventListener('input', function() {
            if (this.value.length === 1 && index < inputs.length - 1) {
              inputs[index + 1].focus();
            }
          });

          input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value.length === 0 && index > 0) {
              inputs[index - 1].focus();
            }
          });
        });
      });
    </script>
  </body>
</html>
