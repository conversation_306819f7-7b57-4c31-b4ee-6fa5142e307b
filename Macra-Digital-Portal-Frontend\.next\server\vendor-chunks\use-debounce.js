"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-debounce";
exports.ids = ["vendor-chunks/use-debounce"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-debounce/dist/index.module.js":
/*!********************************************************!*\
  !*** ./node_modules/use-debounce/dist/index.module.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDebounce: () => (/* binding */ a),\n/* harmony export */   useDebouncedCallback: () => (/* binding */ c),\n/* harmony export */   useThrottledCallback: () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction c(e,u,c,i){var a=this,o=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),f=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0),l=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0),v=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),m=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]),d=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(),g=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(),p=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(e),w=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!0);p.current=e;var s=\"undefined\"!=typeof window,x=!u&&0!==u&&s;if(\"function\"!=typeof e)throw new TypeError(\"Expected a function\");u=+u||0;var h=!!(c=c||{}).leading,y=!(\"trailing\"in c)||!!c.trailing,F=\"maxWait\"in c,A=\"debounceOnServer\"in c&&!!c.debounceOnServer,D=F?Math.max(+c.maxWait||0,u):null;(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function(){return w.current=!0,function(){w.current=!1}},[]);var T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function(){var r=function(r){var n=m.current,t=d.current;return m.current=d.current=null,f.current=r,l.current=l.current||r,g.current=p.current.apply(t,n)},n=function(r,n){x&&cancelAnimationFrame(v.current),v.current=x?requestAnimationFrame(r):setTimeout(r,n)},t=function(r){if(!w.current)return!1;var n=r-o.current;return!o.current||n>=u||n<0||F&&r-f.current>=D},e=function(n){return v.current=null,y&&m.current?r(n):(m.current=d.current=null,g.current)},c=function r(){var c=Date.now();if(h&&l.current===f.current&&T(),t(c))return e(c);if(w.current){var i=u-(c-o.current),a=F?Math.min(i,D-(c-f.current)):i;n(r,a)}},T=function(){i&&i({})},W=function(){if(s||A){var e=Date.now(),i=t(e);if(m.current=[].slice.call(arguments),d.current=a,o.current=e,i){if(!v.current&&w.current)return f.current=o.current,n(c,u),h?r(o.current):g.current;if(F)return n(c,u),r(o.current)}return v.current||n(c,u),g.current}};return W.cancel=function(){v.current&&(x?cancelAnimationFrame(v.current):clearTimeout(v.current)),f.current=0,m.current=o.current=d.current=v.current=null},W.isPending=function(){return!!v.current},W.flush=function(){return v.current?e(Date.now()):g.current},W},[h,F,u,D,y,x,s,A,i]);return T}function i(r,n){return r===n}function a(n,t,a){var o=a&&a.equalityFn||i,f=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(n),l=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({})[1],v=c((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(r){f.current=r,l({})},[l]),t,a,l),m=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(n);return o(m.current,n)||(v(n),m.current=n),[f.current,v]}function o(r,n,t){var e=void 0===t?{}:t,u=e.leading,i=e.trailing;return c(r,n,{maxWait:n,leading:void 0===u||u,trailing:void 0===i||i})}\n//# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWRlYm91bmNlL2Rpc3QvaW5kZXgubW9kdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBGLG9CQUFvQixhQUFhLDZDQUFDLFNBQVMsNkNBQUMsTUFBTSw2Q0FBQyxNQUFNLDZDQUFDLFNBQVMsNkNBQUMsT0FBTyw2Q0FBQyxLQUFLLDZDQUFDLEtBQUssNkNBQUMsTUFBTSw2Q0FBQyxLQUFLLFlBQVksZ0RBQWdELG1FQUFtRSxRQUFRLGdCQUFnQiw4SUFBOEksZ0RBQUMsWUFBWSwrQkFBK0IsY0FBYyxLQUFLLE1BQU0sOENBQUMsWUFBWSxrQkFBa0IsNEJBQTRCLGtHQUFrRyxpQkFBaUIsd0ZBQXdGLGVBQWUsdUJBQXVCLGtCQUFrQiwrQ0FBK0MsZUFBZSw2RUFBNkUsZ0JBQWdCLGlCQUFpQixrREFBa0QsY0FBYyx3REFBd0QsUUFBUSxjQUFjLE9BQU8sRUFBRSxjQUFjLFNBQVMsd0JBQXdCLGlFQUFpRSxvRkFBb0YsZ0NBQWdDLHFDQUFxQywyQkFBMkIsZ0lBQWdJLHdCQUF3QixrQkFBa0Isb0JBQW9CLHlDQUF5QyxHQUFHLHNCQUFzQixTQUFTLGdCQUFnQixhQUFhLGtCQUFrQiwyQkFBMkIsNkNBQUMsTUFBTSwrQ0FBQyxHQUFHLFNBQVMsa0RBQUMsYUFBYSxnQkFBZ0IsRUFBRSxlQUFlLDZDQUFDLElBQUksd0RBQXdELGtCQUFrQixtQkFBbUIsNEJBQTRCLGNBQWMsdURBQXVELEVBQStFO0FBQ3JuRSIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxub2RlX21vZHVsZXNcXHVzZS1kZWJvdW5jZVxcZGlzdFxcaW5kZXgubW9kdWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VSZWYgYXMgcix1c2VFZmZlY3QgYXMgbix1c2VNZW1vIGFzIHQsdXNlU3RhdGUgYXMgZSx1c2VDYWxsYmFjayBhcyB1fWZyb21cInJlYWN0XCI7ZnVuY3Rpb24gYyhlLHUsYyxpKXt2YXIgYT10aGlzLG89cihudWxsKSxmPXIoMCksbD1yKDApLHY9cihudWxsKSxtPXIoW10pLGQ9cigpLGc9cigpLHA9cihlKSx3PXIoITApO3AuY3VycmVudD1lO3ZhciBzPVwidW5kZWZpbmVkXCIhPXR5cGVvZiB3aW5kb3cseD0hdSYmMCE9PXUmJnM7aWYoXCJmdW5jdGlvblwiIT10eXBlb2YgZSl0aHJvdyBuZXcgVHlwZUVycm9yKFwiRXhwZWN0ZWQgYSBmdW5jdGlvblwiKTt1PSt1fHwwO3ZhciBoPSEhKGM9Y3x8e30pLmxlYWRpbmcseT0hKFwidHJhaWxpbmdcImluIGMpfHwhIWMudHJhaWxpbmcsRj1cIm1heFdhaXRcImluIGMsQT1cImRlYm91bmNlT25TZXJ2ZXJcImluIGMmJiEhYy5kZWJvdW5jZU9uU2VydmVyLEQ9Rj9NYXRoLm1heCgrYy5tYXhXYWl0fHwwLHUpOm51bGw7bihmdW5jdGlvbigpe3JldHVybiB3LmN1cnJlbnQ9ITAsZnVuY3Rpb24oKXt3LmN1cnJlbnQ9ITF9fSxbXSk7dmFyIFQ9dChmdW5jdGlvbigpe3ZhciByPWZ1bmN0aW9uKHIpe3ZhciBuPW0uY3VycmVudCx0PWQuY3VycmVudDtyZXR1cm4gbS5jdXJyZW50PWQuY3VycmVudD1udWxsLGYuY3VycmVudD1yLGwuY3VycmVudD1sLmN1cnJlbnR8fHIsZy5jdXJyZW50PXAuY3VycmVudC5hcHBseSh0LG4pfSxuPWZ1bmN0aW9uKHIsbil7eCYmY2FuY2VsQW5pbWF0aW9uRnJhbWUodi5jdXJyZW50KSx2LmN1cnJlbnQ9eD9yZXF1ZXN0QW5pbWF0aW9uRnJhbWUocik6c2V0VGltZW91dChyLG4pfSx0PWZ1bmN0aW9uKHIpe2lmKCF3LmN1cnJlbnQpcmV0dXJuITE7dmFyIG49ci1vLmN1cnJlbnQ7cmV0dXJuIW8uY3VycmVudHx8bj49dXx8bjwwfHxGJiZyLWYuY3VycmVudD49RH0sZT1mdW5jdGlvbihuKXtyZXR1cm4gdi5jdXJyZW50PW51bGwseSYmbS5jdXJyZW50P3Iobik6KG0uY3VycmVudD1kLmN1cnJlbnQ9bnVsbCxnLmN1cnJlbnQpfSxjPWZ1bmN0aW9uIHIoKXt2YXIgYz1EYXRlLm5vdygpO2lmKGgmJmwuY3VycmVudD09PWYuY3VycmVudCYmVCgpLHQoYykpcmV0dXJuIGUoYyk7aWYody5jdXJyZW50KXt2YXIgaT11LShjLW8uY3VycmVudCksYT1GP01hdGgubWluKGksRC0oYy1mLmN1cnJlbnQpKTppO24ocixhKX19LFQ9ZnVuY3Rpb24oKXtpJiZpKHt9KX0sVz1mdW5jdGlvbigpe2lmKHN8fEEpe3ZhciBlPURhdGUubm93KCksaT10KGUpO2lmKG0uY3VycmVudD1bXS5zbGljZS5jYWxsKGFyZ3VtZW50cyksZC5jdXJyZW50PWEsby5jdXJyZW50PWUsaSl7aWYoIXYuY3VycmVudCYmdy5jdXJyZW50KXJldHVybiBmLmN1cnJlbnQ9by5jdXJyZW50LG4oYyx1KSxoP3Ioby5jdXJyZW50KTpnLmN1cnJlbnQ7aWYoRilyZXR1cm4gbihjLHUpLHIoby5jdXJyZW50KX1yZXR1cm4gdi5jdXJyZW50fHxuKGMsdSksZy5jdXJyZW50fX07cmV0dXJuIFcuY2FuY2VsPWZ1bmN0aW9uKCl7di5jdXJyZW50JiYoeD9jYW5jZWxBbmltYXRpb25GcmFtZSh2LmN1cnJlbnQpOmNsZWFyVGltZW91dCh2LmN1cnJlbnQpKSxmLmN1cnJlbnQ9MCxtLmN1cnJlbnQ9by5jdXJyZW50PWQuY3VycmVudD12LmN1cnJlbnQ9bnVsbH0sVy5pc1BlbmRpbmc9ZnVuY3Rpb24oKXtyZXR1cm4hIXYuY3VycmVudH0sVy5mbHVzaD1mdW5jdGlvbigpe3JldHVybiB2LmN1cnJlbnQ/ZShEYXRlLm5vdygpKTpnLmN1cnJlbnR9LFd9LFtoLEYsdSxELHkseCxzLEEsaV0pO3JldHVybiBUfWZ1bmN0aW9uIGkocixuKXtyZXR1cm4gcj09PW59ZnVuY3Rpb24gYShuLHQsYSl7dmFyIG89YSYmYS5lcXVhbGl0eUZufHxpLGY9cihuKSxsPWUoe30pWzFdLHY9Yyh1KGZ1bmN0aW9uKHIpe2YuY3VycmVudD1yLGwoe30pfSxbbF0pLHQsYSxsKSxtPXIobik7cmV0dXJuIG8obS5jdXJyZW50LG4pfHwodihuKSxtLmN1cnJlbnQ9biksW2YuY3VycmVudCx2XX1mdW5jdGlvbiBvKHIsbix0KXt2YXIgZT12b2lkIDA9PT10P3t9OnQsdT1lLmxlYWRpbmcsaT1lLnRyYWlsaW5nO3JldHVybiBjKHIsbix7bWF4V2FpdDpuLGxlYWRpbmc6dm9pZCAwPT09dXx8dSx0cmFpbGluZzp2b2lkIDA9PT1pfHxpfSl9ZXhwb3J0e2EgYXMgdXNlRGVib3VuY2UsYyBhcyB1c2VEZWJvdW5jZWRDYWxsYmFjayxvIGFzIHVzZVRocm90dGxlZENhbGxiYWNrfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1vZHVsZS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-debounce/dist/index.module.js\n");

/***/ })

};
;