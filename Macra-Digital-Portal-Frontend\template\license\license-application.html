<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Application - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }
      .tab-button {
        position: relative;
        z-index: 1;
      }
      .tab-button.active {
        color: #e02b20;
        font-weight: 500;
      }
      .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e02b20;
      }
      .tab-content {
        display: block;
      }
      .tab-content.hidden {
        display: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
       <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
    </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>

              </div>

              Spectrum Management
            </a>
          <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
</svg>

              </div>

              Financial Transactions
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="../user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                    placeholder="Search for licenses..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="../user-management/user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="../account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-semibold text-gray-900">License Application</h1>
                  <p class="mt-1 text-sm text-gray-500">
                    Complete the form below to apply for a new license.
                  </p>
                </div>
                <div class="relative">
                  <a
                    href="license-management.html"
                    role="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-arrow-left-line"></i>
                    </div>
                    Back to License Management
                  </a>
                </div>
              </div>
            </div>

            <!-- Application form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <form id="licenseApplicationForm" class="space-y-8">
                  <!-- Applicant Information -->
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Applicant Information</h3>
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      
                      <!-- Client Name -->
                      <div class="col-span-1">
                        <label for="clientName" class="block text-sm font-medium text-gray-700">Client Name</label>
                        <input type="text" id="clientName" name="clientName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" required>
                      </div>

                      <!-- Email -->
                      <div class="col-span-1">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" id="email" name="email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" required>
                      </div>

                      <!-- Phone -->
                      <div class="col-span-1">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" required>
                      </div>

                      <!-- Organization -->
                      <div class="col-span-1 sm:col-span-2 lg:col-span-1">
                        <label for="organization" class="block text-sm font-medium text-gray-700">Organization/Company Name</label>
                        <input type="text" id="organization" name="organization" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" required>
                      </div>

                      <!-- Address -->
                      <div class="col-span-1 sm:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700">Business Address</label>
                        <textarea id="address" name="address" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" required></textarea>
                      </div>

                    </div>
                  </div>
                  <!-- License Details -->
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 border-b pb-2 mb-4">License Details</h3>
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                      <div class="sm:col-span-3">
                        <label for="licenseType" class="block text-sm font-medium text-gray-700">License Type</label>
                        <div class="mt-1">
                          <select id="licenseType" name="licenseType" class="enhanced-select mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-md" required>
                            <option value="">Select License Type</option>
                            <option value="broadcasting">Broadcasting</option>
                            <option value="telecommunications">Telecommunications</option>
                            <option value="internet">Internet Service Provider</option>
                            <option value="postal">Postal/Courier Services</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                      </div>
                      <div class="sm:col-span-3">
                        <label for="duration" class="block text-sm font-medium text-gray-700">License Duration</label>
                        <div class="mt-1">
                          <select id="duration" name="duration" class="enhanced-select mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-md" required>
                            <option value="">Select Duration</option>
                            <option value="1">1 Year</option>
                            <option value="2">2 Years</option>
                            <option value="5">5 Years</option>
                            <option value="10">10 Years</option>
                          </select>
                        </div>
                      </div>
                      <div class="sm:col-span-3">
                        <label for="region" class="block text-sm font-medium text-gray-700">Geographic Region</label>
                        <div class="mt-1">
                          <select id="region" name="region" class="enhanced-select mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-md" required>
                            <option value="">Select Region</option>
                            <option value="northern">Northern Region</option>
                            <option value="central">Central Region</option>
                            <option value="southern">Southern Region</option>
                            <option value="nationwide">Nationwide</option>
                          </select>
                        </div>
                      </div>

                      <div class="sm:col-span-6">
                        <label for="purpose" class="block text-sm font-medium text-gray-700">Purpose of License</label>
                        <div class="mt-1">
                          <textarea id="purpose" name="purpose" rows="3" class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Briefly describe how you intend to use this license.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Supporting Documents -->
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Supporting Documents</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      
                      <!-- Business Registration -->
                      <div>
                        <label for="businessRegistration" class="block text-sm font-medium text-gray-700 mb-1">Business Registration Certificate</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md h-full">
                          <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex flex-col sm:flex-row justify-center text-sm text-gray-600">
                              <label for="file-upload-1" class="cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary">
                                <span>Upload a file</span>
                                <input id="file-upload-1" name="file-upload-1" type="file" class="sr-only" required>
                              </label>
                              <p class="pl-1 sm:pl-2 mt-1 sm:mt-0">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, JPG, PNG up to 10MB</p>
                          </div>
                        </div>
                      </div>

                      <!-- Technical Plan -->
                      <div>
                        <label for="technicalPlan" class="block text-sm font-medium text-gray-700 mb-1">Technical Plan/Proposal</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md h-full">
                          <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex flex-col sm:flex-row justify-center text-sm text-gray-600">
                              <label for="file-upload-2" class="cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary">
                                <span>Upload a file</span>
                                <input id="file-upload-2" name="file-upload-2" type="file" class="sr-only" required>
                              </label>
                              <p class="pl-1 sm:pl-2 mt-1 sm:mt-0">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, DOC, DOCX up to 10MB</p>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>


                  <!-- Terms and Declaration -->
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Terms and Declaration</h3>
                    <div class="space-y-4">
                      <div class="flex items-start">
                        <div class="flex items-center h-5">
                          <input id="terms" name="terms" type="checkbox" class="enhanced-checkbox" required>
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="terms" class="font-medium text-gray-700">I agree to the terms and conditions</label>
                          <p class="text-gray-500">By submitting this application, I confirm that all information provided is accurate and complete.</p>
                        </div>
                      </div>
                      <div class="flex items-start">
                        <div class="flex items-center h-5">
                          <input id="declaration" name="declaration" type="checkbox" class="enhanced-checkbox" required>
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="declaration" class="font-medium text-gray-700">Declaration</label>
                          <p class="text-gray-500">I understand that providing false information may result in the rejection of my application or revocation of any license granted.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Form Actions -->
                  <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 sm:space-y-0 mt-6">
                    <a href="license-management.html" class="px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all text-center">
                      Cancel
                    </a>
                    <button type="submit" class="px-4 py-3 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                      Submit Application
                    </button>
                  </div>

                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;

      // Form submission handler
      document.getElementById('licenseApplicationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Your license application has been submitted successfully!');
        window.location.href = 'license-management.html';
      });
    </script>
  </body>
</html>
