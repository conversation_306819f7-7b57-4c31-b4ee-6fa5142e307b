"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-to-js";
exports.ids = ["vendor-chunks/style-to-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-to-js/cjs/index.js":
/*!***********************************************!*\
  !*** ./node_modules/style-to-js/cjs/index.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar style_to_object_1 = __importDefault(__webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/style-to-object/cjs/index.js\"));\nvar utilities_1 = __webpack_require__(/*! ./utilities */ \"(ssr)/./node_modules/style-to-js/cjs/utilities.js\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== 'string') {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function (property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3R5bGUtdG8tanMvY2pzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSx3Q0FBd0MsbUJBQU8sQ0FBQywwRUFBaUI7QUFDakUsa0JBQWtCLG1CQUFPLENBQUMsc0VBQWE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxub2RlX21vZHVsZXNcXHN0eWxlLXRvLWpzXFxjanNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xudmFyIHN0eWxlX3RvX29iamVjdF8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJzdHlsZS10by1vYmplY3RcIikpO1xudmFyIHV0aWxpdGllc18xID0gcmVxdWlyZShcIi4vdXRpbGl0aWVzXCIpO1xuLyoqXG4gKiBQYXJzZXMgQ1NTIGlubGluZSBzdHlsZSB0byBKYXZhU2NyaXB0IG9iamVjdCAoY2FtZWxDYXNlZCkuXG4gKi9cbmZ1bmN0aW9uIFN0eWxlVG9KUyhzdHlsZSwgb3B0aW9ucykge1xuICAgIHZhciBvdXRwdXQgPSB7fTtcbiAgICBpZiAoIXN0eWxlIHx8IHR5cGVvZiBzdHlsZSAhPT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIG91dHB1dDtcbiAgICB9XG4gICAgKDAsIHN0eWxlX3RvX29iamVjdF8xLmRlZmF1bHQpKHN0eWxlLCBmdW5jdGlvbiAocHJvcGVydHksIHZhbHVlKSB7XG4gICAgICAgIC8vIHNraXAgQ1NTIGNvbW1lbnRcbiAgICAgICAgaWYgKHByb3BlcnR5ICYmIHZhbHVlKSB7XG4gICAgICAgICAgICBvdXRwdXRbKDAsIHV0aWxpdGllc18xLmNhbWVsQ2FzZSkocHJvcGVydHksIG9wdGlvbnMpXSA9IHZhbHVlO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIG91dHB1dDtcbn1cblN0eWxlVG9KUy5kZWZhdWx0ID0gU3R5bGVUb0pTO1xubW9kdWxlLmV4cG9ydHMgPSBTdHlsZVRvSlM7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/style-to-js/cjs/utilities.js":
/*!***************************************************!*\
  !*** ./node_modules/style-to-js/cjs/utilities.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n    return !property ||\n        NO_HYPHEN_REGEX.test(property) ||\n        CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) { return \"\".concat(prefix, \"-\"); };\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n    if (options === void 0) { options = {}; }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;\n//# sourceMappingURL=utilities.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/utilities.js\n");

/***/ })

};
;