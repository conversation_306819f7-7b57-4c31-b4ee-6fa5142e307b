import {
  <PERSON><PERSON><PERSON>,
  
  <PERSON><PERSON>n,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
 BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { UserIdentification } from './user-identification.entity';

@Entity('identification_types')
export class IdentificationType {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  identification_type_id: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
  })
  name: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinC<PERSON>umn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany(() => UserIdentification, (userIdentification) => userIdentification.identification_type)
  user_identifications: UserIdentification[];

  @BeforeInsert()
  generateId() {
    if (!this.identification_type_id) {
      this.identification_type_id = uuidv4();
    }
  }
}
