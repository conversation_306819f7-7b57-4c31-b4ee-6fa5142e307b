<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Network Service License Evaluation - MACRA Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Score input styles */
      .score-input {
        @apply w-20 text-center border-2 border-gray-300 rounded-md px-2 py-1 text-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
      }

      .score-bar {
        @apply h-4 rounded-full transition-all duration-300;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .evaluation-criteria {
        @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6;
      }

      .criteria-item {
        @apply flex justify-between items-center py-2 border-b border-blue-200 last:border-b-0;
      }

      .pass-fail-indicator {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .pass-indicator {
        @apply bg-green-100 text-green-800;
      }

      .fail-indicator {
        @apply bg-red-100 text-red-800;
      }

      .pending-indicator {
        @apply bg-yellow-100 text-yellow-800;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="evaluation-template.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-check-line"></i>
              </div>
              License Evaluation
            </a>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>

              </div>

              Spectrum Management
            </a>
          <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
</svg>

              </div>

              Financial Transactions
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="../user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm border-b border-gray-200">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex items-center">
                <button
                  id="mobileSidebarToggle"
                  class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <i class="ri-menu-line text-xl"></i>
                </button>
                <div class="ml-4 md:ml-0">
                  <nav class="flex space-x-8">
                    <a
                      href="evaluation-template.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      All Evaluations
                    </a>
                    <a
                      href="individual-license-evaluation.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Individual License
                    </a>
                    <a
                      href="network-service-evaluation.html"
                      class="text-primary border-b-2 border-primary px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Network Service
                    </a>
                    <a
                      href="broadcasting-evaluation.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Broadcasting
                    </a>
                  </nav>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <div class="relative">
                  <button
                    id="notificationDropdown"
                    class="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-full"
                  >
                    <i class="ri-notification-line text-xl"></i>
                    <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                  </button>
                </div>
                <div class="relative">
                  <button
                    id="userDropdown"
                    class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdownContent"
                    class="dropdown-content absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5"
                  >
                    <a
                      href="../user-management/user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Your Profile
                    </a>
                    <a
                      href="../help-support.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Settings
                    </a>
                    <a
                      href="../login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign out
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Page content -->
        <main class="flex-1 overflow-y-auto">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-semibold text-gray-900">Network Service License Evaluation</h1>
                  <p class="mt-1 text-sm text-gray-500">
                    Evaluate and score network service license applications based on technical and operational criteria.
                  </p>
                </div>
                <div class="relative">
                  <a
                    href="license-management.html"
                    role="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <i class="ri-arrow-left-line mr-2"></i>
                    Back to License Management
                  </a>
                </div>
              </div>
            </div>

            <!-- Application Selection -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Select Application to Evaluate
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="showEvaluationForm('NSL-2024-001')">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">TechNet Solutions Ltd</h4>
                        <p class="text-xs text-gray-500">Application ID: NSL-2024-001</p>
                        <p class="text-xs text-gray-500">Type: Internet Service Provider</p>
                        <p class="text-xs text-gray-500">Submitted: 2024-01-15</p>
                      </div>
                      <span class="pending-indicator">Pending</span>
                    </div>
                  </div>
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="showEvaluationForm('NSL-2024-002')">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">ConnectMW Networks</h4>
                        <p class="text-xs text-gray-500">Application ID: NSL-2024-002</p>
                        <p class="text-xs text-gray-500">Type: Data Network Services</p>
                        <p class="text-xs text-gray-500">Submitted: 2024-01-12</p>
                      </div>
                      <span class="pending-indicator">Pending</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Evaluation Criteria -->
            <div class="evaluation-criteria mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="ri-information-line mr-2"></i>
                Network Service License Evaluation Criteria
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Technical Infrastructure</span>
                  <span class="text-sm font-bold text-primary">35%</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Financial Capacity</span>
                  <span class="text-sm font-bold text-primary">25%</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Operational Experience</span>
                  <span class="text-sm font-bold text-primary">25%</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Service Coverage Plan</span>
                  <span class="text-sm font-bold text-primary">15%</span>
                </div>
              </div>
              <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">
                  <i class="ri-alert-line mr-1"></i>
                  <strong>Minimum Score:</strong> 75% required for network service license approval. Technical infrastructure must score at least 70%.
                </p>
              </div>
            </div>

            <!-- Evaluation Form -->
            <div id="evaluationForm" class="bg-white shadow overflow-hidden sm:rounded-lg" style="display: none;">
              <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-6">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">Network Service License Evaluation</h3>
                    <p class="text-sm text-gray-500" id="selectedApplication">Evaluating: NSL-2024-001 - TechNet Solutions Ltd</p>
                  </div>
                  <div class="flex items-center space-x-4">
                    <div class="text-right">
                      <p class="text-sm text-gray-500">Total Score</p>
                      <p class="text-2xl font-bold text-primary" id="totalScore">0%</p>
                    </div>
                  </div>
                </div>

                <form class="space-y-8">
                  <!-- Technical Infrastructure (35%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Technical Infrastructure</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 35%</span>
                        <span class="text-sm font-medium text-primary" id="technicalScore">0/35</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Network Architecture & Design</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Equipment & Technology Standards</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Network Security & Redundancy</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Scalability & Future Expansion</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Financial Capacity (25%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Financial Capacity</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 25%</span>
                        <span class="text-sm font-medium text-primary" id="financialScore">0/25</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Capital Investment Plan</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="8" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Revenue Projections & Sustainability</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="8" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Financial Statements & Credit Rating</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="9" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Operational Experience (25%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Operational Experience</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 25%</span>
                        <span class="text-sm font-medium text-primary" id="operationalScore">0/25</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Management Team Experience</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="8" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Technical Staff Qualifications</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="8" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Previous Telecommunications Experience</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="9" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Service Coverage Plan (15%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Service Coverage Plan</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 15%</span>
                        <span class="text-sm font-medium text-primary" id="coverageScore">0/15</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Geographic Coverage Strategy</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Service Quality Commitments</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700">Implementation Timeline</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8">0</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Evaluation Summary -->
                  <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Evaluation Summary</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Score Breakdown</h5>
                        <div class="space-y-2">
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Technical Infrastructure (35%)</span>
                            <span class="text-sm font-medium" id="finalTechnical">0/35</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Financial Capacity (25%)</span>
                            <span class="text-sm font-medium" id="finalFinancial">0/25</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Operational Experience (25%)</span>
                            <span class="text-sm font-medium" id="finalOperational">0/25</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Service Coverage Plan (15%)</span>
                            <span class="text-sm font-medium" id="finalCoverage">0/15</span>
                          </div>
                          <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center">
                              <span class="text-sm font-medium text-gray-900">Total Score</span>
                              <span class="text-lg font-bold text-primary" id="finalTotal">0%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Recommendation</h5>
                        <div id="recommendationBox" class="p-4 rounded-lg border">
                          <div id="recommendationContent">
                            <p class="text-sm text-gray-600">Complete the evaluation to see recommendation.</p>
                          </div>
                        </div>
                        <div class="mt-4">
                          <label class="block text-sm font-medium text-gray-700">Evaluator Notes</label>
                          <textarea rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add overall evaluation notes and recommendations..."></textarea>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>
                    <div class="flex space-x-3">
                      <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <i class="ri-close-line mr-2"></i>
                        Reject Application
                      </button>
                      <button type="button" class="enhanced-button">
                        <i class="ri-check-line mr-2"></i>
                        Approve Application
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      function showEvaluationForm(applicationId) {
        document.getElementById('evaluationForm').style.display = 'block';
        document.getElementById('evaluationForm').scrollIntoView({ behavior: 'smooth' });
      }

      function updateScores() {
        // This would contain the scoring logic
        console.log('Updating scores...');
      }

      // Toggle dropdown functionality
      document.getElementById('userDropdown').addEventListener('click', function() {
        const dropdown = document.getElementById('userDropdownContent');
        dropdown.classList.toggle('show');
      });

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('#userDropdown') && !event.target.closest('#userDropdown')) {
          const dropdown = document.getElementById('userDropdownContent');
          dropdown.classList.remove('show');
        }
      });
    </script>
  </body>
</html>
