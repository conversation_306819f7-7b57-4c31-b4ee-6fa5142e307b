'use client';

import React, { useState } from 'react';
import {
  PaperAirplaneIcon,
  InboxIcon,
  PaperClipIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import SendMessageComponent from './SendMessageComponent';
// TODO: Uncomment when communication hooks are ready
// import { useSendMessage, useMessages, useCommunicationStats } from '@/hooks/useCommunication';
import { SendMessageData } from '@/types/communication';

const CommunicationCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Send Message');

  // Mock data and functions (replace with actual hooks when available)
  // TODO: Replace with real hooks when backend is ready:
  // const { sendMessage, recipients, templates, isSending, loading } = useSendMessage();
  // const { messages, loading: messagesLoading, refresh: refreshMessages } = useMessages();
  // const { stats, loading: statsLoading } = useCommunicationStats();

  const [isSending, setIsSending] = useState(false);
  const loading = false;
  const messagesLoading = false;
  const statsLoading = false;

  const recipients = [
    { id: '1', name: '<PERSON>e', email: '<EMAIL>', role: 'Officer' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'Administrator' },
  ];

  const messages: any[] = [];
  const stats = {
    totalMessages: 0,
    sentMessages: 0,
    unreadMessages: 0,
    responseRate: 0
  };

  const sendMessage = async (messageData: SendMessageData) => {
    setIsSending(true);
    // Mock API call - replace with real API when backend is ready
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('Message sent:', messageData);
    setIsSending(false);
  };

  const refreshMessages = () => {
    console.log('Refreshing messages...');
  };

  const tabs = [
    { name: 'Send Message', icon: PaperAirplaneIcon },
    { name: 'Inbox', icon: InboxIcon },
    { name: 'Statistics', icon: ChartBarIcon },
    { name: 'Settings', icon: Cog6ToothIcon },
  ];



  const handleSendMessage = async (messageData: SendMessageData) => {
    try {
      await sendMessage(messageData);
      // Refresh messages after sending
      refreshMessages();
    } catch (error) {
      // Error is handled in the hook
      throw error;
    }
  };

  const renderSendMessageTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Compose New Message</h2>
        
        {/* Email Message */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-700 mb-3">📧 Email Message</h3>
          <SendMessageComponent
            onSendMessage={handleSendMessage}
            availableRecipients={recipients}
            messageType="email"
            allowAttachments={true}
            allowPriority={true}
            allowMultipleRecipients={true}
            placeholder="Type your email message here..."
            disabled={isSending || loading}
          />
        </div>

        {/* System Notification */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-700 mb-3">🔔 System Notification</h3>
          <SendMessageComponent
            onSendMessage={handleSendMessage}
            availableRecipients={recipients}
            messageType="notification"
            allowAttachments={false}
            allowPriority={true}
            allowMultipleRecipients={true}
            placeholder="Type your notification message here..."
            disabled={isSending || loading}
          />
        </div>

        {/* Announcement */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-700 mb-3">📢 Announcement</h3>
          <SendMessageComponent
            onSendMessage={handleSendMessage}
            availableRecipients={recipients}
            messageType="announcement"
            allowAttachments={true}
            allowPriority={true}
            allowMultipleRecipients={true}
            placeholder="Type your announcement here..."
            disabled={isSending || loading}
          />
        </div>
      </div>
    </div>
  );

  const renderInboxTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Message Inbox</h2>
        
        {messagesLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <InboxIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No messages found</p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-gray-900">
                          {message.senderName}
                        </span>
                        <span className="text-sm text-gray-500">
                          {message.senderEmail}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          message.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                          message.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                          message.priority === 'normal' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {message.priority}
                        </span>
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1">
                        {message.subject}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {message.content}
                      </p>
                      {message.attachments.length > 0 && (
                        <div className="flex items-center gap-1 mt-2 text-sm text-gray-500">
                          <PaperClipIcon className="h-4 w-4" />
                          <span>{message.attachments.length} attachment(s)</span>
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">
                        {new Date(message.sentAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        {new Date(message.sentAt).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );

  const renderStatsTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Communication Statistics</h2>
        
        {statsLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : stats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalMessages}</div>
              <div className="text-sm text-blue-600">Total Messages</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.sentMessages}</div>
              <div className="text-sm text-green-600">Sent Messages</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{stats.unreadMessages}</div>
              <div className="text-sm text-orange-600">Unread Messages</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(stats.responseRate * 100)}%
              </div>
              <div className="text-sm text-purple-600">Response Rate</div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <ChartBarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No statistics available</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Communication Settings</h2>
        <div className="text-center py-8 text-gray-500">
          <Cog6ToothIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Settings panel coming soon...</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Communication Center</h1>
        <p className="text-gray-600 mt-2">
          Send messages, manage communications, and view statistics
        </p>
      </div>

      {/* Custom Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <div className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.name}
              onClick={() => setActiveTab(tab.name)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.name
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              {tab.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'Send Message' && renderSendMessageTab()}
        {activeTab === 'Inbox' && renderInboxTab()}
        {activeTab === 'Statistics' && renderStatsTab()}
        {activeTab === 'Settings' && renderSettingsTab()}
      </div>
    </div>
  );
};

export default CommunicationCenter;
