'use client';

import React, { useState, useEffect } from 'react';
import { activityNotesService, ActivityNote } from '@/services/activityNotesService';
import { useToast } from '@/contexts/ToastContext';
import { DocumentIcon, CalendarIcon, UserIcon } from '@heroicons/react/24/outline';

interface DataBreachActivityNotesProps {
  reportId: string;
  className?: string;
  refreshTrigger?: number; // Used to trigger refresh from parent
}

const DataBreachActivityNotes: React.FC<DataBreachActivityNotesProps> = ({
  reportId,
  className = '',
  refreshTrigger = 0
}) => {
  const { showError } = useToast();
  const [notes, setNotes] = useState<ActivityNote[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (reportId) {
      fetchActivityNotes();
    }
  }, [reportId, refreshTrigger]);

  const fetchActivityNotes = async () => {
    setLoading(true);
    try {
      const response = await activityNotesService.getDataBreachNotes(reportId);
      // Sort by creation date, newest first
      const sortedNotes = (response || []).sort((a: ActivityNote, b: ActivityNote) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setNotes(sortedNotes);
    } catch (error) {
      console.error('Error fetching activity notes:', error);
      showError('Failed to load activity notes');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNoteTypeIcon = (noteType: string) => {
    switch (noteType) {
      case 'status_update':
        return 'ri-refresh-line';
      case 'information_request':
        return 'ri-question-line';
      case 'data_breach_action':
        return 'ri-shield-keyhole-line';
      case 'general_note':
        return 'ri-file-text-line';
      default:
        return 'ri-chat-3-line';
    }
  };

  const getNoteTypeColor = (noteType: string) => {
    switch (noteType) {
      case 'status_update':
        return 'text-blue-600 bg-blue-50';
      case 'information_request':
        return 'text-orange-600 bg-orange-50';
      case 'data_breach_action':
        return 'text-red-600 bg-red-50';
      case 'general_note':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'normal':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'low':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Activity Notes</h3>
        </div>
        <div className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 dark:text-gray-400 mt-2">Loading activity notes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Activity Notes ({notes.length})
        </h3>
      </div>
      
      <div className="p-4">
        {notes.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <i className="ri-chat-3-line text-3xl mb-3"></i>
            <p>No activity notes found</p>
            <p className="text-sm">Activity notes will appear here when actions are taken on this report</p>
          </div>
        ) : (
          <div className="space-y-4">
            {notes.map((note) => (
              <div key={note.note_id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${getNoteTypeColor(note.note_type || 'general_note')}`}>
                      <i className={`${getNoteTypeIcon(note.note_type || 'general_note')} text-lg`}></i>
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {note.note_type?.replace('_', ' ').toUpperCase() || 'GENERAL NOTE'}
                        </span>
                        {note.priority && (
                          <span className={`px-2 py-1 text-xs rounded-full border ${getPriorityColor(note.priority)}`}>
                            {note.priority.toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <div className="flex items-center gap-1">
                          <CalendarIcon className="h-4 w-4" />
                          <span>{formatDate(note.created_at)}</span>
                        </div>
                        {note.created_by_name && (
                          <div className="flex items-center gap-1">
                            <UserIcon className="h-4 w-4" />
                            <span>{note.created_by_name}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="ml-14">
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    {note.note}
                  </p>

                  {/* Metadata */}
                  {note.metadata && (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Details</h5>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {note.metadata.action && (
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Action:</span>
                            <span className="ml-2 text-gray-900 dark:text-gray-100">
                              {note.metadata.action.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                        )}
                        {note.metadata.original_status && (
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Previous Status:</span>
                            <span className="ml-2 text-gray-900 dark:text-gray-100">
                              {note.metadata.original_status.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                        )}
                        {note.metadata.severity && (
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Severity:</span>
                            <span className="ml-2 text-gray-900 dark:text-gray-100">
                              {note.metadata.severity.toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Attachments */}
                  {note.metadata?.attachments && note.metadata.attachments.length > 0 && (
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Attachments ({note.metadata.attachments.length})
                      </h5>
                      <div className="space-y-2">
                        {note.metadata.attachments.map((attachment: any, index: number) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <DocumentIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-700 dark:text-gray-300">
                              {attachment.file_name || `Attachment ${index + 1}`}
                            </span>
                            {attachment.file_size && (
                              <span className="text-gray-500 dark:text-gray-400">
                                ({Math.round(attachment.file_size / 1024)} KB)
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DataBreachActivityNotes;
