"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/data-protection/page",{

/***/ "(app-pages-browser)/./src/app/customer/data-protection/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/customer/data-protection/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/customer/CustomerLayout */ \"(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\");\n/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loader */ \"(app-pages-browser)/./src/components/Loader.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_customer_ConsumerAffairsModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/customer/ConsumerAffairsModal */ \"(app-pages-browser)/./src/components/customer/ConsumerAffairsModal.tsx\");\n/* harmony import */ var _components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/customer/DataBreachModal */ \"(app-pages-browser)/./src/components/customer/DataBreachModal.tsx\");\n/* harmony import */ var _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/customer/ComplaintStatusBar */ \"(app-pages-browser)/./src/components/customer/ComplaintStatusBar.tsx\");\n/* harmony import */ var _services_consumer_affairs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/consumer-affairs */ \"(app-pages-browser)/./src/services/consumer-affairs/index.ts\");\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DataProtectionPage = ()=>{\n    _s();\n    const { isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [complaints, setComplaints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [showConsumerAffairsModal, setShowConsumerAffairsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDataBreachModal, setShowDataBreachModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect to customer login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            if (!authLoading && !isAuthenticated) {\n                router.push('/customer/auth/login');\n            }\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Fetch data function\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataProtectionPage.useCallback[fetchData]\": async ()=>{\n            if (!isAuthenticated) {\n                console.log('❌ User not authenticated, skipping data fetch');\n                return;\n            }\n            console.log('✅ User authenticated, fetching data...');\n            try {\n                setIsLoading(true);\n                setError('');\n                // Fetch both consumer affairs complaints and data breach reports\n                const [consumerAffairsResponse, dataBreachResponse] = await Promise.all([\n                    _services_consumer_affairs__WEBPACK_IMPORTED_MODULE_9__.consumerAffairsService.getComplaints({\n                        limit: 100\n                    }),\n                    _services_data_breach__WEBPACK_IMPORTED_MODULE_10__.dataBreachService.getReports({\n                        limit: 100\n                    })\n                ]);\n                console.log('🔍 Consumer Affairs Response:', consumerAffairsResponse);\n                console.log('🔍 Consumer Affairs Response.data type:', typeof consumerAffairsResponse.data);\n                console.log('🔍 Consumer Affairs Response.data:', consumerAffairsResponse.data);\n                console.log('🔍 Data Breach Response:', dataBreachResponse);\n                console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);\n                console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);\n                // Ensure data is an array (services return data directly)\n                const consumerAffairsData = Array.isArray(consumerAffairsResponse.data) ? consumerAffairsResponse.data : [];\n                const dataBreachData = Array.isArray(dataBreachResponse.data) ? dataBreachResponse.data : [];\n                console.log('🔍 Consumer Affairs Data Array:', consumerAffairsData);\n                console.log('🔍 Data Breach Data Array:', dataBreachData);\n                // Combine and transform the data\n                const combinedComplaints = [\n                    ...consumerAffairsData.map({\n                        \"DataProtectionPage.useCallback[fetchData]\": (complaint)=>{\n                            var _complaint_assignee, _complaint_assignee1;\n                            return {\n                                id: complaint.complaint_id,\n                                title: complaint.title,\n                                description: complaint.description,\n                                category: complaint.category,\n                                type: 'consumer_affairs',\n                                priority: complaint.priority,\n                                status: complaint.status,\n                                submittedAt: complaint.created_at,\n                                updatedAt: complaint.updated_at,\n                                assignedTo: ((_complaint_assignee = complaint.assignee) === null || _complaint_assignee === void 0 ? void 0 : _complaint_assignee.first_name) && ((_complaint_assignee1 = complaint.assignee) === null || _complaint_assignee1 === void 0 ? void 0 : _complaint_assignee1.last_name) ? \"\".concat(complaint.assignee.first_name, \" \").concat(complaint.assignee.last_name) : undefined,\n                                resolution: complaint.resolution,\n                                number: complaint.complaint_number\n                            };\n                        }\n                    }[\"DataProtectionPage.useCallback[fetchData]\"]),\n                    ...dataBreachData.map({\n                        \"DataProtectionPage.useCallback[fetchData]\": (report)=>{\n                            var _report_assignee, _report_assignee1;\n                            return {\n                                id: report.report_id,\n                                title: report.title,\n                                description: report.description,\n                                category: report.category,\n                                type: 'data_breach',\n                                priority: report.priority,\n                                status: report.status,\n                                submittedAt: report.created_at,\n                                updatedAt: report.updated_at,\n                                assignedTo: ((_report_assignee = report.assignee) === null || _report_assignee === void 0 ? void 0 : _report_assignee.first_name) && ((_report_assignee1 = report.assignee) === null || _report_assignee1 === void 0 ? void 0 : _report_assignee1.last_name) ? \"\".concat(report.assignee.first_name, \" \").concat(report.assignee.last_name) : undefined,\n                                resolution: report.resolution,\n                                number: report.report_number\n                            };\n                        }\n                    }[\"DataProtectionPage.useCallback[fetchData]\"])\n                ];\n                // Sort by creation date (newest first)\n                combinedComplaints.sort({\n                    \"DataProtectionPage.useCallback[fetchData]\": (a, b)=>new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()\n                }[\"DataProtectionPage.useCallback[fetchData]\"]);\n                setComplaints(combinedComplaints);\n            } catch (err) {\n                var _axiosError_response, _axiosError_response1;\n                console.error('Error fetching complaints:', err);\n                const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n                const isAxiosError = err && typeof err === 'object' && 'response' in err;\n                const axiosError = isAxiosError ? err : null;\n                const status = axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status;\n                console.error('Error details:', {\n                    message: errorMessage,\n                    response: axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.data,\n                    status: status\n                });\n                if (status === 401) {\n                    setError('Authentication required. Please log in again.');\n                } else if (status === 404) {\n                    setError('API endpoints not found. Please check if the backend is running.');\n                } else {\n                    setError(\"Failed to load complaints: \".concat(errorMessage));\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"DataProtectionPage.useCallback[fetchData]\"], [\n        isAuthenticated\n    ]);\n    // Fetch data on mount and when authentication changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        fetchData\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800';\n            case 'resolved':\n                return 'bg-green-100 text-green-800';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'low':\n                return 'bg-gray-100 text-gray-800';\n            case 'medium':\n                return 'bg-blue-100 text-blue-800';\n            case 'high':\n                return 'bg-orange-100 text-orange-800';\n            case 'urgent':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const consumerAffairsComplaints = complaints.filter((c)=>c.type === 'consumer_affairs');\n    const dataBreachComplaints = complaints.filter((c)=>c.type === 'data_breach');\n    // Calculate complaint statistics\n    const totalComplaints = complaints.length;\n    const pendingComplaints = complaints.filter((c)=>c.status === 'submitted' || c.status === 'under_review').length;\n    const investigatingComplaints = complaints.filter((c)=>c.status === 'investigating').length;\n    const resolvedComplaints = complaints.filter((c)=>c.status === 'resolved' || c.status === 'closed').length;\n    if (authLoading || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    message: \"Loading Data Protection...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-2 text-sm underline hover:no-underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                                    children: \"Data Protection\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Submit and track consumer affairs complaints and data breach reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setShowDataBreachModal(true),\n                            className: \"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-shield-keyhole-line mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Report Data Breach\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200 dark:border-gray-700 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            {\n                                key: 'overview',\n                                label: 'Overview',\n                                icon: 'ri-dashboard-line'\n                            },\n                            {\n                                key: 'track',\n                                label: 'Track Complaints',\n                                icon: 'ri-search-eye-line',\n                                count: complaints.length\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab(tab.key),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center \".concat(activeTab === tab.key ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"\".concat(tab.icon, \" mr-2\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tab.label,\n                                    tab.count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\",\n                                        children: tab.count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, tab.key, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-file-list-3-line text-2xl text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Total Complaints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: totalComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-time-line text-2xl text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: pendingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-search-eye-line text-2xl text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Investigating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: investigatingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-check-double-line text-2xl text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Resolved\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: resolvedComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-shield-keyhole-line text-3xl text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                    children: \"Data Breach Reporting\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"Report unauthorized access, misuse, or breach of your personal data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"What to Report:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Unauthorized data access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Data misuse or sharing\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Privacy violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Identity theft concerns\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, undefined),\n                activeTab === 'track' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: complaints.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-file-search-line text-4xl text-gray-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                children: \"No complaints found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 dark:text-gray-400 mb-4\",\n                                children: \"You haven't submitted any complaints yet.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\",\n                                children: \"Submit Your First Complaint\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: complaints.map((complaint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3\",\n                                                            children: complaint.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(complaint.status)),\n                                                            children: complaint.status.replace('_', ' ').toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(complaint.priority)),\n                                                            children: complaint.priority.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'),\n                                                            children: complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        complaint.id,\n                                                        \" | Category: \",\n                                                        complaint.category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300 mb-3\",\n                                                    children: complaint.description.length > 150 ? \"\".concat(complaint.description.substring(0, 150), \"...\") : complaint.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        currentStage: (0,_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_8__.getStageIndexFromStatus)(complaint.status),\n                                                        stages: complaint.type === 'consumer_affairs' ? _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_8__.COMPLAINT_STAGES.CONSUMER_AFFAIRS : _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_8__.COMPLAINT_STAGES.DATA_BREACH,\n                                                        status: complaint.status,\n                                                        size: \"sm\",\n                                                        variant: \"horizontal\",\n                                                        showPercentage: false,\n                                                        showStageNames: true,\n                                                        className: \"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Submitted: \",\n                                                                formatDate(complaint.submittedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mx-2\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated: \",\n                                                                formatDate(complaint.updatedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        complaint.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-2\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Assigned to: \",\n                                                                        complaint.assignedTo\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: \"View Details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, complaint.id, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 19\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-900 dark:text-red-100 mb-4\",\n                            children: \"Need Help with Data Protection?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Contact Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-phone-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"+265 1 770 100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-mail-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-time-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mon-Fri, 8:00 AM - 5:00 PM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Data Breach Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Unauthorized Access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Misuse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Privacy Violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Identity Theft\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Sharing Issues\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, undefined),\n                showConsumerAffairsModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_ConsumerAffairsModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onClose: ()=>setShowConsumerAffairsModal(false),\n                    onSubmit: (data)=>{\n                        console.log('Consumer Affairs complaint submitted:', data);\n                        setShowConsumerAffairsModal(false);\n                        // Refresh complaints list without full page reload\n                        fetchData();\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, undefined),\n                showDataBreachModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    onClose: ()=>setShowDataBreachModal(false),\n                    onSubmit: (data)=>{\n                        console.log('Data breach report submitted:', data);\n                        setShowDataBreachModal(false);\n                        // Refresh complaints list without full page reload\n                        fetchData();\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataProtectionPage, \"KdantqLGbwL02PWe4Z/tk6MHNGo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DataProtectionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataProtectionPage);\nvar _c;\n$RefreshReg$(_c, \"DataProtectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customer/data-protection/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/customer/ConsumerAffairsModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/customer/ConsumerAffairsModal.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/forms/TextInput */ \"(app-pages-browser)/./src/components/forms/TextInput.tsx\");\n/* harmony import */ var _components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/forms/TextArea */ \"(app-pages-browser)/./src/components/forms/TextArea.tsx\");\n/* harmony import */ var _components_forms_Select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/forms/Select */ \"(app-pages-browser)/./src/components/forms/Select.tsx\");\n/* harmony import */ var _services_consumer_affairs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/consumer-affairs */ \"(app-pages-browser)/./src/services/consumer-affairs/index.ts\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst complaintCategories = [\n    'Billing & Charges',\n    'Service Quality',\n    'Network Issues',\n    'Customer Service',\n    'Contract Disputes',\n    'Accessibility',\n    'Fraud & Scams',\n    'Other'\n];\nconst ConsumerAffairsModal = (param)=>{\n    let { onClose, onSubmit } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        category: ''\n    });\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (formErrors[name]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files) {\n            const newFiles = Array.from(e.target.files);\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n    };\n    const removeAttachment = (index)=>{\n        setAttachments((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const validateForm = ()=>{\n        const errors = {};\n        if (!formData.title.trim()) {\n            errors.title = 'Title is required';\n        }\n        if (!formData.description.trim()) {\n            errors.description = 'Description is required';\n        } else if (formData.description.trim().length < 20) {\n            errors.description = 'Description must be at least 20 characters';\n        }\n        if (!formData.category) {\n            errors.category = 'Category is required';\n        }\n        setFormErrors(errors);\n        return Object.keys(errors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const complaintData = {\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                attachments: attachments\n            };\n            const response = await _services_consumer_affairs__WEBPACK_IMPORTED_MODULE_5__.consumerAffairsService.createComplaint(complaintData);\n            // Show success message\n            showSuccess(\"Your complaint has been submitted successfully! Reference ID: \".concat(response.complaint_id || 'N/A'), 6000);\n            onSubmit(response);\n            // Reset form\n            setFormData({\n                title: '',\n                description: '',\n                category: ''\n            });\n            setAttachments([]);\n        } catch (error) {\n            console.error('Error submitting complaint:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Failed to submit complaint. Please try again.';\n            showError(errorMessage);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                            children: \"Lodge Consumer Affairs Complaint\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            \"aria-label\": \"Close modal\",\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: \"Submit your complaint about telecommunications services, billing issues, or other consumer concerns. Our team will investigate and work to resolve your issue.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextInput__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    label: \"Complaint Title *\",\n                                    id: \"complaint-title\",\n                                    name: \"title\",\n                                    value: formData.title,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Brief summary of your complaint\",\n                                    error: formErrors.title,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    label: \"Category\",\n                                    name: \"category\",\n                                    value: formData.category,\n                                    onChange: handleInputChange,\n                                    error: formErrors.category,\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select a category\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        complaintCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_TextArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        label: \"Detailed Description *\",\n                                        id: \"complaint-description\",\n                                        name: \"description\",\n                                        value: formData.description,\n                                        onChange: handleInputChange,\n                                        rows: 6,\n                                        placeholder: \"Please provide a detailed description of your complaint, including dates, times, and any relevant information...\",\n                                        error: formErrors.description,\n                                        helperText: \"Minimum 20 characters required\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"complaint-attachments\",\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Supporting Documents (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"complaint-attachments\",\n                                            type: \"file\",\n                                            multiple: true,\n                                            accept: \".pdf,.doc,.docx,.jpg,.jpeg,.png\",\n                                            onChange: handleFileChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                            children: \"Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB per file)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-2\",\n                                            children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                    className: \"ri-file-line text-gray-400 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                    children: file.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400 ml-2\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        formatFileSize(file.size),\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeAttachment(index),\n                                                            className: \"text-red-500 hover:text-red-700\",\n                                                            \"aria-label\": \"Remove \".concat(file.name),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"ri-close-line\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\",\n                                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-loader-4-line animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Submitting...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-send-plane-line mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Submit Complaint\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\ConsumerAffairsModal.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConsumerAffairsModal, \"1rQQTG1ZHEMSHR3BPIp3GRFfKbI=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = ConsumerAffairsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConsumerAffairsModal);\nvar _c;\n$RefreshReg$(_c, \"ConsumerAffairsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/customer/ConsumerAffairsModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/customer/CustomerLayout.tsx":
/*!****************************************************!*\
  !*** ./src/components/customer/CustomerLayout.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LoadingContext */ \"(app-pages-browser)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _LogoutButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../LogoutButton */ \"(app-pages-browser)/./src/components/LogoutButton.tsx\");\n/* harmony import */ var _common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../common/NotificationBell */ \"(app-pages-browser)/./src/components/common/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CustomerLayout = (param)=>{\n    let { children, breadcrumbs } = param;\n    _s();\n    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showLoader } = (0,_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading)();\n    // Memoize navigation items to prevent unnecessary re-renders\n    const navigationItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[navigationItems]\": ()=>[\n                {\n                    name: 'Dashboard',\n                    href: '/customer',\n                    icon: 'ri-dashboard-line',\n                    current: pathname === '/customer'\n                },\n                {\n                    name: 'My Licenses',\n                    href: '/customer/my-licenses',\n                    icon: 'ri-key-line',\n                    current: pathname === '/customer/my-licenses'\n                },\n                {\n                    name: 'My Applications',\n                    href: '/customer/applications',\n                    icon: 'ri-file-list-3-line',\n                    current: pathname === '/customer/applications'\n                },\n                {\n                    name: 'Invoices & Payments',\n                    href: '/customer/payments',\n                    icon: 'ri-bank-card-line',\n                    current: pathname === '/customer/payments'\n                },\n                {\n                    name: 'Documents',\n                    href: '/customer/documents',\n                    icon: 'ri-file-text-line',\n                    current: pathname === '/customer/documents'\n                },\n                {\n                    name: 'Procurement',\n                    href: '/customer/procurement',\n                    icon: 'ri-auction-line',\n                    current: pathname === '/customer/procurement'\n                },\n                {\n                    name: 'Request Resource',\n                    href: '/customer/resources',\n                    icon: 'ri-hand-heart-line',\n                    current: pathname === '/customer/resources'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[navigationItems]\"], [\n        pathname\n    ]);\n    const supportItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerLayout.useMemo[supportItems]\": ()=>[\n                {\n                    name: 'Consumer Affairs',\n                    href: '/customer/consumer-affairs',\n                    icon: 'ri-customer-service-2-line'\n                },\n                {\n                    name: 'Data Protection',\n                    href: '/customer/data-protection',\n                    icon: 'ri-shield-keyhole-line'\n                },\n                {\n                    name: 'Help Center',\n                    href: '/customer/help',\n                    icon: 'ri-question-line'\n                }\n            ]\n    }[\"CustomerLayout.useMemo[supportItems]\"], []);\n    // Prefetch customer pages on mount for faster navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerLayout.useEffect\": ()=>{\n            const prefetchPages = {\n                \"CustomerLayout.useEffect.prefetchPages\": ()=>{\n                    const customerPages = [\n                        '/customer',\n                        '/customer/applications',\n                        '/customer/applications/standards',\n                        '/customer/payments',\n                        '/customer/my-licenses',\n                        '/customer/procurement',\n                        '/customer/profile',\n                        '/customer/consumer-affairs',\n                        '/customer/data-protection',\n                        '/customer/resources',\n                        '/customer/help'\n                    ];\n                    customerPages.forEach({\n                        \"CustomerLayout.useEffect.prefetchPages\": (page)=>{\n                            router.prefetch(page);\n                        }\n                    }[\"CustomerLayout.useEffect.prefetchPages\"]);\n                }\n            }[\"CustomerLayout.useEffect.prefetchPages\"];\n            // Delay prefetching to not interfere with initial page load\n            const timer = setTimeout(prefetchPages, 1000);\n            return ({\n                \"CustomerLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"CustomerLayout.useEffect\"];\n        }\n    }[\"CustomerLayout.useEffect\"], [\n        router\n    ]);\n    const toggleMobileSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleMobileSidebar]\": ()=>{\n            setIsMobileSidebarOpen(!isMobileSidebarOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleMobileSidebar]\"], [\n        isMobileSidebarOpen\n    ]);\n    const handleNavClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavClick]\": (href, name)=>{\n            const pageMessages = {\n                '/customer': 'Loading Dashboard...',\n                '/customer/my-licenses': 'Loading My Licenses...',\n                '/customer/applications': 'Loading Applications...',\n                '/customer/applications/apply/': 'Loading Standards License Options...',\n                '/customer/payments': 'Loading Payments...',\n                '/customer/documents': 'Loading Documents...',\n                '/customer/procurement': 'Loading Procurement...',\n                '/customer/resources': 'Loading Resources...',\n                '/customer/data-protection': 'Loading Data Protection...',\n                '/customer/help': 'Loading Help Center...',\n                '/customer/profile': 'Loading Profile...',\n                '/customer/settings': 'Loading Settings...'\n            };\n            const message = pageMessages[href] || \"Loading \".concat(name, \"...\");\n            showLoader(message);\n            setIsMobileSidebarOpen(false);\n        }\n    }[\"CustomerLayout.useCallback[handleNavClick]\"], [\n        showLoader\n    ]);\n    const handleNavHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[handleNavHover]\": (href)=>{\n            // Prefetch on hover for instant navigation\n            router.prefetch(href);\n        }\n    }[\"CustomerLayout.useCallback[handleNavHover]\"], [\n        router\n    ]);\n    const toggleUserDropdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CustomerLayout.useCallback[toggleUserDropdown]\": ()=>{\n            setIsUserDropdownOpen(!isUserDropdownOpen);\n        }\n    }[\"CustomerLayout.useCallback[toggleUserDropdown]\"], [\n        isUserDropdownOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            isMobileSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: ()=>setIsMobileSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\\n        \".concat(isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/images/macra-logo.png\",\n                                    alt: \"MACRA Logo\",\n                                    className: \"max-h-12 w-auto\",\n                                    onLoad: ()=>console.log('MACRA logo loaded successfully in customer portal'),\n                                    onError: (e)=>{\n                                        var _target_parentNode;\n                                        console.error('Error loading MACRA logo in customer portal:', e);\n                                        console.log('Attempting to load logo from:', e.currentTarget.src);\n                                        // Fallback to styled logo if image fails\n                                        const target = e.target;\n                                        target.style.display = 'none';\n                                        const textFallback = document.createElement('div');\n                                        textFallback.innerHTML = 'MACRA';\n                                        textFallback.className = 'text-primary font-bold text-lg bg-red-600 text-white px-3 py-2 rounded-full';\n                                        (_target_parentNode = target.parentNode) === null || _target_parentNode === void 0 ? void 0 : _target_parentNode.appendChild(textFallback);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-6 px-4 side-nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            onClick: ()=>handleNavClick(item.href, item.name),\n                                            onMouseEnter: ()=>handleNavHover(item.href),\n                                            className: \"\\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\\n                    \".concat(item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100', \"\\n                  \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center mr-3 \".concat(item.current ? 'text-red-600 dark:text-red-400' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 space-y-1\",\n                                            children: supportItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    onClick: ()=>handleNavClick(item.href, item.name),\n                                                    onMouseEnter: ()=>handleNavHover(item.href),\n                                                    className: \"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 flex items-center justify-center mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white dark:bg-gray-800 shadow-sm z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleMobileSidebar,\n                                    className: \"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\",\n                                    \"aria-label\": \"Open mobile menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-menu-line ri-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                children: [\n                                                    index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-arrow-right-s-line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: \"hover:text-primary\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-900 dark:text-gray-100\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_NotificationBell__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleUserDropdown,\n                                                    className: \"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Open user menu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            className: \"h-8 w-8 rounded-full object-cover\",\n                                                            src: (user === null || user === void 0 ? void 0 : user.profile_image) || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\",\n                                                            alt: \"Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isUserDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/customer/profile\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                onClick: ()=>{\n                                                                    handleNavClick('/customer/profile', 'Profile');\n                                                                    setIsUserDropdownOpen(false);\n                                                                },\n                                                                children: \"Your Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LogoutButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"text\",\n                                                                size: \"sm\",\n                                                                className: \"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                                showConfirmation: true,\n                                                                children: \"Sign out\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\customer\\\\CustomerLayout.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerLayout, \"Bvue6xx7qybDlJR/560gagzUyBI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading\n    ];\n});\n_c = CustomerLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerLayout);\nvar _c;\n$RefreshReg$(_c, \"CustomerLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbWVyL0N1c3RvbWVyTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXlFO0FBQzVDO0FBQzRCO0FBQ1I7QUFDTTtBQUNaO0FBQ2U7QUFZMUQsTUFBTVksaUJBQWdEO1FBQUMsRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUU7O0lBQzlFLE1BQU0sQ0FBQ0MscUJBQXFCQyx1QkFBdUIsR0FBR2YsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDZ0Isb0JBQW9CQyxzQkFBc0IsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU1rQixXQUFXYiw0REFBV0E7SUFDNUIsTUFBTWMsU0FBU2IsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRWMsSUFBSSxFQUFFLEdBQUdiLDhEQUFPQTtJQUN4QixNQUFNLEVBQUVjLFVBQVUsRUFBRSxHQUFHYixvRUFBVUE7SUFFakMsNkRBQTZEO0lBQzdELE1BQU1jLGtCQUFrQnBCLDhDQUFPQTttREFBQyxJQUFNO2dCQUNwQztvQkFDRXFCLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLFNBQVNSLGFBQWE7Z0JBQ3hCO2dCQUNBO29CQUNFSyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxTQUFTUixhQUFhO2dCQUN4QjtnQkFDQTtvQkFDRUssTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsU0FBU1IsYUFBYTtnQkFDeEI7Z0JBQ0E7b0JBQ0VLLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLFNBQVNSLGFBQWE7Z0JBQ3hCO2dCQUNBO29CQUNFSyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxTQUFTUixhQUFhO2dCQUN4QjtnQkFDQTtvQkFDRUssTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsU0FBU1IsYUFBYTtnQkFDeEI7Z0JBQ0E7b0JBQ0VLLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLFNBQVNSLGFBQWE7Z0JBQ3hCO2FBQ0Q7a0RBQUU7UUFBQ0E7S0FBUztJQUViLE1BQU1TLGVBQWV6Qiw4Q0FBT0E7Z0RBQUMsSUFBTTtnQkFDakM7b0JBQ0VxQixNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxNQUFNO2dCQUNSO2dCQUNBO29CQUNFRixNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxNQUFNO2dCQUNSO2dCQUNBO29CQUNFRixNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxNQUFNO2dCQUNSO2FBQ0Q7K0NBQUUsRUFBRTtJQUVMLHlEQUF5RDtJQUN6RHhCLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU0yQjswREFBZ0I7b0JBQ3BCLE1BQU1DLGdCQUFnQjt3QkFDcEI7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7cUJBQ0Q7b0JBRURBLGNBQWNDLE9BQU87a0VBQUNDLENBQUFBOzRCQUNwQlosT0FBT2EsUUFBUSxDQUFDRDt3QkFDbEI7O2dCQUNGOztZQUVBLDREQUE0RDtZQUM1RCxNQUFNRSxRQUFRQyxXQUFXTixlQUFlO1lBQ3hDOzRDQUFPLElBQU1PLGFBQWFGOztRQUM1QjttQ0FBRztRQUFDZDtLQUFPO0lBRVgsTUFBTWlCLHNCQUFzQmpDLGtEQUFXQTsyREFBQztZQUN0Q1ksdUJBQXVCLENBQUNEO1FBQzFCOzBEQUFHO1FBQUNBO0tBQW9CO0lBRXhCLE1BQU11QixpQkFBaUJsQyxrREFBV0E7c0RBQUMsQ0FBQ3FCLE1BQWNEO1lBQ2hELE1BQU1lLGVBQTBDO2dCQUM5QyxhQUFhO2dCQUNiLHlCQUF5QjtnQkFDekIsMEJBQTBCO2dCQUMxQixpQ0FBaUM7Z0JBQ2pDLHNCQUFzQjtnQkFDdEIsdUJBQXVCO2dCQUN2Qix5QkFBeUI7Z0JBQ3pCLHVCQUF1QjtnQkFDdkIsNkJBQTZCO2dCQUM3QixrQkFBa0I7Z0JBQ2xCLHFCQUFxQjtnQkFDckIsc0JBQXNCO1lBQ3hCO1lBRUEsTUFBTUMsVUFBVUQsWUFBWSxDQUFDZCxLQUFLLElBQUksV0FBZ0IsT0FBTEQsTUFBSztZQUN0REYsV0FBV2tCO1lBQ1h4Qix1QkFBdUI7UUFDekI7cURBQUc7UUFBQ007S0FBVztJQUVmLE1BQU1tQixpQkFBaUJyQyxrREFBV0E7c0RBQUMsQ0FBQ3FCO1lBQ2xDLDJDQUEyQztZQUMzQ0wsT0FBT2EsUUFBUSxDQUFDUjtRQUNsQjtxREFBRztRQUFDTDtLQUFPO0lBRVgsTUFBTXNCLHFCQUFxQnRDLGtEQUFXQTswREFBQztZQUNyQ2Msc0JBQXNCLENBQUNEO1FBQ3pCO3lEQUFHO1FBQUNBO0tBQW1CO0lBRXZCLHFCQUNFLDhEQUFDMEI7UUFBSUMsV0FBVTs7WUFFWjdCLHFDQUNDLDhEQUFDNEI7Z0JBQ0NDLFdBQVU7Z0JBQ1ZDLFNBQVMsSUFBTTdCLHVCQUF1Qjs7Ozs7OzBCQUsxQyw4REFBQzhCO2dCQUFNRixXQUFXLDZLQUUrRCxPQUE3RTdCLHNCQUFzQixrQkFBa0Isc0NBQXFDOzBCQUUvRSw0RUFBQzRCO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRztvQ0FDQ0MsS0FBSTtvQ0FDSkMsS0FBSTtvQ0FDSkwsV0FBVTtvQ0FDVk0sUUFBUSxJQUFNQyxRQUFRQyxHQUFHLENBQUM7b0NBQzFCQyxTQUFTLENBQUNDOzRDQVNSQzt3Q0FSQUosUUFBUUssS0FBSyxDQUFDLGdEQUFnREY7d0NBQzlESCxRQUFRQyxHQUFHLENBQUMsaUNBQWlDRSxFQUFFRyxhQUFhLENBQUNULEdBQUc7d0NBQ2hFLHlDQUF5Qzt3Q0FDekMsTUFBTU8sU0FBU0QsRUFBRUMsTUFBTTt3Q0FDdkJBLE9BQU9HLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO3dDQUN2QixNQUFNQyxlQUFlQyxTQUFTQyxhQUFhLENBQUM7d0NBQzVDRixhQUFhRyxTQUFTLEdBQUc7d0NBQ3pCSCxhQUFhaEIsU0FBUyxHQUFHO3lDQUN6QlcscUJBQUFBLE9BQU9TLFVBQVUsY0FBakJULHlDQUFBQSxtQkFBbUJVLFdBQVcsQ0FBQ0w7b0NBQ2pDOzs7Ozs7Ozs7Ozs7Ozs7O3NDQU1OLDhEQUFDTTs0QkFBSXRCLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnJCLGdCQUFnQjRDLEdBQUcsQ0FBQyxDQUFDQyxxQkFDcEIsOERBQUMvRCxrREFBSUE7NENBRUhvQixNQUFNMkMsS0FBSzNDLElBQUk7NENBQ2ZvQixTQUFTLElBQU1QLGVBQWU4QixLQUFLM0MsSUFBSSxFQUFFMkMsS0FBSzVDLElBQUk7NENBQ2xENkMsY0FBYyxJQUFNNUIsZUFBZTJCLEtBQUszQyxJQUFJOzRDQUM1Q21CLFdBQVcsMEpBS1IsT0FIQ3dCLEtBQUt6QyxPQUFPLEdBQ1YsOEdBQ0EseUhBQ0g7OzhEQUdILDhEQUFDZ0I7b0RBQUlDLFdBQVcsaURBQXNHLE9BQXJEd0IsS0FBS3pDLE9BQU8sR0FBRyxtQ0FBbUM7OERBQ2pILDRFQUFDMkM7d0RBQUUxQixXQUFXd0IsS0FBSzFDLElBQUk7Ozs7Ozs7Ozs7O2dEQUV4QjBDLEtBQUs1QyxJQUFJOzsyQ0FmTDRDLEtBQUs1QyxJQUFJOzs7Ozs7Ozs7OzhDQXFCcEIsOERBQUNtQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMyQjs0Q0FBRzNCLFdBQVU7c0RBQXVGOzs7Ozs7c0RBR3JHLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWmhCLGFBQWF1QyxHQUFHLENBQUMsQ0FBQ0MscUJBQ2pCLDhEQUFDL0Qsa0RBQUlBO29EQUVIb0IsTUFBTTJDLEtBQUszQyxJQUFJO29EQUNmb0IsU0FBUyxJQUFNUCxlQUFlOEIsS0FBSzNDLElBQUksRUFBRTJDLEtBQUs1QyxJQUFJO29EQUNsRDZDLGNBQWMsSUFBTTVCLGVBQWUyQixLQUFLM0MsSUFBSTtvREFDNUNtQixXQUFVOztzRUFFViw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUMwQjtnRUFBRTFCLFdBQVd3QixLQUFLMUMsSUFBSTs7Ozs7Ozs7Ozs7d0RBRXhCMEMsS0FBSzVDLElBQUk7O21EQVRMNEMsS0FBSzVDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFxQjVCLDhEQUFDbUI7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDNEI7d0JBQU81QixXQUFVO2tDQUNoQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNkI7b0NBQ0NDLE1BQUs7b0NBQ0w3QixTQUFTUjtvQ0FDVE8sV0FBVTtvQ0FDVitCLGNBQVc7OENBRVgsNEVBQUNoQzt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzBCOzRDQUFFMUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJakIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaOUIsZUFBZUEsWUFBWThELE1BQU0sR0FBRyxtQkFDbkMsOERBQUNWO3dDQUFJdEIsV0FBVTtrREFDWjlCLFlBQVlxRCxHQUFHLENBQUMsQ0FBQ0MsTUFBTVMsc0JBQ3RCLDhEQUFDN0UsdURBQWM7O29EQUNaNkUsUUFBUSxtQkFBSyw4REFBQ1A7d0RBQUUxQixXQUFVOzs7Ozs7b0RBQzFCd0IsS0FBSzNDLElBQUksaUJBQ1IsOERBQUNwQixrREFBSUE7d0RBQUNvQixNQUFNMkMsS0FBSzNDLElBQUk7d0RBQUVtQixXQUFVO2tFQUM5QndCLEtBQUtXLEtBQUs7Ozs7O2tGQUdiLDhEQUFDQzt3REFBS3BDLFdBQVU7a0VBQW9Dd0IsS0FBS1csS0FBSzs7Ozs7OzsrQ0FQN0NGOzs7Ozs7Ozs7Ozs7Ozs7OENBZTdCLDhEQUFDbEM7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDakMsZ0VBQWdCQTs0Q0FBQ2lDLFdBQVU7Ozs7OztzREFFNUIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzZCO29EQUNDQyxNQUFLO29EQUNMN0IsU0FBU0g7b0RBQ1RFLFdBQVU7O3NFQUVWLDhEQUFDb0M7NERBQUtwQyxXQUFVO3NFQUFVOzs7Ozs7c0VBQzFCLDhEQUFDRzs0REFDQ0gsV0FBVTs0REFDVkksS0FBSzNCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRELGFBQWEsS0FBSTs0REFDNUJoQyxLQUFJOzs7Ozs7Ozs7Ozs7Z0RBSVBoQyxvQ0FDQyw4REFBQzBCO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN2QyxrREFBSUE7Z0VBQ0hvQixNQUFLO2dFQUNMbUIsV0FBVTtnRUFDVkMsU0FBUztvRUFDUFAsZUFBZSxxQkFBcUI7b0VBQ3BDcEIsc0JBQXNCO2dFQUN4QjswRUFDRDs7Ozs7OzBFQUlELDhEQUFDUixxREFBWUE7Z0VBQ1h3RSxTQUFRO2dFQUNSQyxNQUFLO2dFQUNMdkMsV0FBVTtnRUFDVndDLGtCQUFrQjswRUFDbkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBWWYsOERBQUNDO3dCQUFLekMsV0FBVTtrQ0FDZCw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOzBDQUNaL0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPZjtHQS9UTUQ7O1FBR2FOLHdEQUFXQTtRQUNiQyxzREFBU0E7UUFDUEMsMERBQU9BO1FBQ0RDLGdFQUFVQTs7O0tBTjdCRztBQWlVTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGN1c3RvbWVyXFxDdXN0b21lckxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xyXG5pbXBvcnQgeyB1c2VQYXRobmFtZSwgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xyXG5pbXBvcnQgeyB1c2VMb2FkaW5nIH0gZnJvbSAnQC9jb250ZXh0cy9Mb2FkaW5nQ29udGV4dCc7XHJcbmltcG9ydCBMb2dvdXRCdXR0b24gZnJvbSAnLi4vTG9nb3V0QnV0dG9uJztcclxuaW1wb3J0IE5vdGlmaWNhdGlvbkJlbGwgZnJvbSAnLi4vY29tbW9uL05vdGlmaWNhdGlvbkJlbGwnO1xyXG5cclxuaW50ZXJmYWNlIEJyZWFkY3J1bWJJdGVtIHtcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIGhyZWY/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBDdXN0b21lckxheW91dFByb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGJyZWFkY3J1bWJzPzogQnJlYWRjcnVtYkl0ZW1bXTtcclxufVxyXG5cclxuY29uc3QgQ3VzdG9tZXJMYXlvdXQ6IFJlYWN0LkZDPEN1c3RvbWVyTGF5b3V0UHJvcHM+ID0gKHsgY2hpbGRyZW4sIGJyZWFkY3J1bWJzIH0pID0+IHtcclxuICBjb25zdCBbaXNNb2JpbGVTaWRlYmFyT3Blbiwgc2V0SXNNb2JpbGVTaWRlYmFyT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzVXNlckRyb3Bkb3duT3Blbiwgc2V0SXNVc2VyRHJvcGRvd25PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgeyBzaG93TG9hZGVyIH0gPSB1c2VMb2FkaW5nKCk7XHJcblxyXG4gIC8vIE1lbW9pemUgbmF2aWdhdGlvbiBpdGVtcyB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcclxuICBjb25zdCBuYXZpZ2F0aW9uSXRlbXMgPSB1c2VNZW1vKCgpID0+IFtcclxuICAgIHtcclxuICAgICAgbmFtZTogJ0Rhc2hib2FyZCcsXHJcbiAgICAgIGhyZWY6ICcvY3VzdG9tZXInLFxyXG4gICAgICBpY29uOiAncmktZGFzaGJvYXJkLWxpbmUnLFxyXG4gICAgICBjdXJyZW50OiBwYXRobmFtZSA9PT0gJy9jdXN0b21lcidcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICdNeSBMaWNlbnNlcycsXHJcbiAgICAgIGhyZWY6ICcvY3VzdG9tZXIvbXktbGljZW5zZXMnLFxyXG4gICAgICBpY29uOiAncmkta2V5LWxpbmUnLFxyXG4gICAgICBjdXJyZW50OiBwYXRobmFtZSA9PT0gJy9jdXN0b21lci9teS1saWNlbnNlcydcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICdNeSBBcHBsaWNhdGlvbnMnLFxyXG4gICAgICBocmVmOiAnL2N1c3RvbWVyL2FwcGxpY2F0aW9ucycsXHJcbiAgICAgIGljb246ICdyaS1maWxlLWxpc3QtMy1saW5lJyxcclxuICAgICAgY3VycmVudDogcGF0aG5hbWUgPT09ICcvY3VzdG9tZXIvYXBwbGljYXRpb25zJ1xyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ0ludm9pY2VzICYgUGF5bWVudHMnLFxyXG4gICAgICBocmVmOiAnL2N1c3RvbWVyL3BheW1lbnRzJyxcclxuICAgICAgaWNvbjogJ3JpLWJhbmstY2FyZC1saW5lJyxcclxuICAgICAgY3VycmVudDogcGF0aG5hbWUgPT09ICcvY3VzdG9tZXIvcGF5bWVudHMnXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBuYW1lOiAnRG9jdW1lbnRzJyxcclxuICAgICAgaHJlZjogJy9jdXN0b21lci9kb2N1bWVudHMnLFxyXG4gICAgICBpY29uOiAncmktZmlsZS10ZXh0LWxpbmUnLFxyXG4gICAgICBjdXJyZW50OiBwYXRobmFtZSA9PT0gJy9jdXN0b21lci9kb2N1bWVudHMnXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBuYW1lOiAnUHJvY3VyZW1lbnQnLFxyXG4gICAgICBocmVmOiAnL2N1c3RvbWVyL3Byb2N1cmVtZW50JyxcclxuICAgICAgaWNvbjogJ3JpLWF1Y3Rpb24tbGluZScsXHJcbiAgICAgIGN1cnJlbnQ6IHBhdGhuYW1lID09PSAnL2N1c3RvbWVyL3Byb2N1cmVtZW50J1xyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ1JlcXVlc3QgUmVzb3VyY2UnLFxyXG4gICAgICBocmVmOiAnL2N1c3RvbWVyL3Jlc291cmNlcycsXHJcbiAgICAgIGljb246ICdyaS1oYW5kLWhlYXJ0LWxpbmUnLFxyXG4gICAgICBjdXJyZW50OiBwYXRobmFtZSA9PT0gJy9jdXN0b21lci9yZXNvdXJjZXMnXHJcbiAgICB9XHJcbiAgXSwgW3BhdGhuYW1lXSk7XHJcblxyXG4gIGNvbnN0IHN1cHBvcnRJdGVtcyA9IHVzZU1lbW8oKCkgPT4gW1xyXG4gICAge1xyXG4gICAgICBuYW1lOiAnQ29uc3VtZXIgQWZmYWlycycsXHJcbiAgICAgIGhyZWY6ICcvY3VzdG9tZXIvY29uc3VtZXItYWZmYWlycycsXHJcbiAgICAgIGljb246ICdyaS1jdXN0b21lci1zZXJ2aWNlLTItbGluZSdcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICdEYXRhIFByb3RlY3Rpb24nLFxyXG4gICAgICBocmVmOiAnL2N1c3RvbWVyL2RhdGEtcHJvdGVjdGlvbicsXHJcbiAgICAgIGljb246ICdyaS1zaGllbGQta2V5aG9sZS1saW5lJ1xyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ0hlbHAgQ2VudGVyJyxcclxuICAgICAgaHJlZjogJy9jdXN0b21lci9oZWxwJyxcclxuICAgICAgaWNvbjogJ3JpLXF1ZXN0aW9uLWxpbmUnXHJcbiAgICB9XHJcbiAgXSwgW10pO1xyXG5cclxuICAvLyBQcmVmZXRjaCBjdXN0b21lciBwYWdlcyBvbiBtb3VudCBmb3IgZmFzdGVyIG5hdmlnYXRpb25cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgcHJlZmV0Y2hQYWdlcyA9ICgpID0+IHtcclxuICAgICAgY29uc3QgY3VzdG9tZXJQYWdlcyA9IFtcclxuICAgICAgICAnL2N1c3RvbWVyJyxcclxuICAgICAgICAnL2N1c3RvbWVyL2FwcGxpY2F0aW9ucycsXHJcbiAgICAgICAgJy9jdXN0b21lci9hcHBsaWNhdGlvbnMvc3RhbmRhcmRzJyxcclxuICAgICAgICAnL2N1c3RvbWVyL3BheW1lbnRzJyxcclxuICAgICAgICAnL2N1c3RvbWVyL215LWxpY2Vuc2VzJyxcclxuICAgICAgICAnL2N1c3RvbWVyL3Byb2N1cmVtZW50JyxcclxuICAgICAgICAnL2N1c3RvbWVyL3Byb2ZpbGUnLFxyXG4gICAgICAgICcvY3VzdG9tZXIvY29uc3VtZXItYWZmYWlycycsXHJcbiAgICAgICAgJy9jdXN0b21lci9kYXRhLXByb3RlY3Rpb24nLFxyXG4gICAgICAgICcvY3VzdG9tZXIvcmVzb3VyY2VzJyxcclxuICAgICAgICAnL2N1c3RvbWVyL2hlbHAnXHJcbiAgICAgIF07XHJcblxyXG4gICAgICBjdXN0b21lclBhZ2VzLmZvckVhY2gocGFnZSA9PiB7XHJcbiAgICAgICAgcm91dGVyLnByZWZldGNoKHBhZ2UpO1xyXG4gICAgICB9KTtcclxuICAgIH07XHJcblxyXG4gICAgLy8gRGVsYXkgcHJlZmV0Y2hpbmcgdG8gbm90IGludGVyZmVyZSB3aXRoIGluaXRpYWwgcGFnZSBsb2FkXHJcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQocHJlZmV0Y2hQYWdlcywgMTAwMCk7XHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcclxuICB9LCBbcm91dGVyXSk7XHJcblxyXG4gIGNvbnN0IHRvZ2dsZU1vYmlsZVNpZGViYXIgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBzZXRJc01vYmlsZVNpZGViYXJPcGVuKCFpc01vYmlsZVNpZGViYXJPcGVuKTtcclxuICB9LCBbaXNNb2JpbGVTaWRlYmFyT3Blbl0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVOYXZDbGljayA9IHVzZUNhbGxiYWNrKChocmVmOiBzdHJpbmcsIG5hbWU6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgcGFnZU1lc3NhZ2VzOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xyXG4gICAgICAnL2N1c3RvbWVyJzogJ0xvYWRpbmcgRGFzaGJvYXJkLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9teS1saWNlbnNlcyc6ICdMb2FkaW5nIE15IExpY2Vuc2VzLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9hcHBsaWNhdGlvbnMnOiAnTG9hZGluZyBBcHBsaWNhdGlvbnMuLi4nLFxyXG4gICAgICAnL2N1c3RvbWVyL2FwcGxpY2F0aW9ucy9hcHBseS8nOiAnTG9hZGluZyBTdGFuZGFyZHMgTGljZW5zZSBPcHRpb25zLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9wYXltZW50cyc6ICdMb2FkaW5nIFBheW1lbnRzLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9kb2N1bWVudHMnOiAnTG9hZGluZyBEb2N1bWVudHMuLi4nLFxyXG4gICAgICAnL2N1c3RvbWVyL3Byb2N1cmVtZW50JzogJ0xvYWRpbmcgUHJvY3VyZW1lbnQuLi4nLFxyXG4gICAgICAnL2N1c3RvbWVyL3Jlc291cmNlcyc6ICdMb2FkaW5nIFJlc291cmNlcy4uLicsXHJcbiAgICAgICcvY3VzdG9tZXIvZGF0YS1wcm90ZWN0aW9uJzogJ0xvYWRpbmcgRGF0YSBQcm90ZWN0aW9uLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9oZWxwJzogJ0xvYWRpbmcgSGVscCBDZW50ZXIuLi4nLFxyXG4gICAgICAnL2N1c3RvbWVyL3Byb2ZpbGUnOiAnTG9hZGluZyBQcm9maWxlLi4uJyxcclxuICAgICAgJy9jdXN0b21lci9zZXR0aW5ncyc6ICdMb2FkaW5nIFNldHRpbmdzLi4uJ1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBtZXNzYWdlID0gcGFnZU1lc3NhZ2VzW2hyZWZdIHx8IGBMb2FkaW5nICR7bmFtZX0uLi5gO1xyXG4gICAgc2hvd0xvYWRlcihtZXNzYWdlKTtcclxuICAgIHNldElzTW9iaWxlU2lkZWJhck9wZW4oZmFsc2UpO1xyXG4gIH0sIFtzaG93TG9hZGVyXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZU5hdkhvdmVyID0gdXNlQ2FsbGJhY2soKGhyZWY6IHN0cmluZykgPT4ge1xyXG4gICAgLy8gUHJlZmV0Y2ggb24gaG92ZXIgZm9yIGluc3RhbnQgbmF2aWdhdGlvblxyXG4gICAgcm91dGVyLnByZWZldGNoKGhyZWYpO1xyXG4gIH0sIFtyb3V0ZXJdKTtcclxuXHJcbiAgY29uc3QgdG9nZ2xlVXNlckRyb3Bkb3duID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgc2V0SXNVc2VyRHJvcGRvd25PcGVuKCFpc1VzZXJEcm9wZG93bk9wZW4pO1xyXG4gIH0sIFtpc1VzZXJEcm9wZG93bk9wZW5dKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgey8qIE1vYmlsZSBzaWRlYmFyIG92ZXJsYXkgKi99XHJcbiAgICAgIHtpc01vYmlsZVNpZGViYXJPcGVuICYmIChcclxuICAgICAgICA8ZGl2IFxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIHotNDAgbWQ6aGlkZGVuXCJcclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlU2lkZWJhck9wZW4oZmFsc2UpfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogU2lkZWJhciAqL31cclxuICAgICAgPGFzaWRlIGNsYXNzTmFtZT17YFxyXG4gICAgICAgIGZpeGVkIGxnOnN0YXRpYyBpbnNldC15LTAgbGVmdC0wIHotNTAgdy02NCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHNoYWRvdy1sZyB0cmFuc2Zvcm0gdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGdwdS1hY2NlbGVyYXRlZFxyXG4gICAgICAgICR7aXNNb2JpbGVTaWRlYmFyT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCBsZzp0cmFuc2xhdGUteC0wJ31cclxuICAgICAgYH0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxyXG4gICAgICAgICAgey8qIExvZ28vSGVhZGVyICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTE2IGZsZXggaXRlbXMtY2VudGVyIHB4LTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL21hY3JhLWxvZ28ucG5nXCJcclxuICAgICAgICAgICAgICAgIGFsdD1cIk1BQ1JBIExvZ29cIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LWgtMTIgdy1hdXRvXCJcclxuICAgICAgICAgICAgICAgIG9uTG9hZD17KCkgPT4gY29uc29sZS5sb2coJ01BQ1JBIGxvZ28gbG9hZGVkIHN1Y2Nlc3NmdWxseSBpbiBjdXN0b21lciBwb3J0YWwnKX1cclxuICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgTUFDUkEgbG9nbyBpbiBjdXN0b21lciBwb3J0YWw6JywgZSk7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIHRvIGxvYWQgbG9nbyBmcm9tOicsIGUuY3VycmVudFRhcmdldC5zcmMpO1xyXG4gICAgICAgICAgICAgICAgICAvLyBGYWxsYmFjayB0byBzdHlsZWQgbG9nbyBpZiBpbWFnZSBmYWlsc1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xyXG4gICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuZGlzcGxheSA9ICdub25lJztcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdGV4dEZhbGxiYWNrID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XHJcbiAgICAgICAgICAgICAgICAgIHRleHRGYWxsYmFjay5pbm5lckhUTUwgPSAnTUFDUkEnO1xyXG4gICAgICAgICAgICAgICAgICB0ZXh0RmFsbGJhY2suY2xhc3NOYW1lID0gJ3RleHQtcHJpbWFyeSBmb250LWJvbGQgdGV4dC1sZyBiZy1yZWQtNjAwIHRleHQtd2hpdGUgcHgtMyBweS0yIHJvdW5kZWQtZnVsbCc7XHJcbiAgICAgICAgICAgICAgICAgIHRhcmdldC5wYXJlbnROb2RlPy5hcHBlbmRDaGlsZCh0ZXh0RmFsbGJhY2spO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxyXG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJtdC02IHB4LTQgc2lkZS1uYXZcIj5cclxuICAgICAgICAgICAgey8qIE1haW4gTmF2aWdhdGlvbiAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cclxuICAgICAgICAgICAgICB7bmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTmF2Q2xpY2soaXRlbS5ocmVmLCBpdGVtLm5hbWUpfVxyXG4gICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IGhhbmRsZU5hdkhvdmVyKGl0ZW0uaHJlZil9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxyXG4gICAgICAgICAgICAgICAgICAgIGZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMi41IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgZ3B1LWFjY2VsZXJhdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgJHtpdGVtLmN1cnJlbnRcclxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC01MCBkYXJrOmJnLXJlZC05MDAvMjAgdGV4dC1yZWQtNzAwIGRhcms6dGV4dC1yZWQtMzAwIGJvcmRlci1yLTIgYm9yZGVyLXJlZC02MDAgZGFyazpib3JkZXItcmVkLTQwMCdcclxuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTEwMCdcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGB9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy01IGgtNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zICR7aXRlbS5jdXJyZW50ID8gJ3RleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMCcgOiAnJ31gfT5cclxuICAgICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9e2l0ZW0uaWNvbn0+PC9pPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogU3VwcG9ydCBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LThcIj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwicHgtNCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XHJcbiAgICAgICAgICAgICAgICBTdXBwb3J0XHJcbiAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICB7c3VwcG9ydEl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOYXZDbGljayhpdGVtLmhyZWYsIGl0ZW0ubmFtZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBoYW5kbGVOYXZIb3ZlcihpdGVtLmhyZWYpfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMi41IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgZ3B1LWFjY2VsZXJhdGVkIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNSBoLTUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPXtpdGVtLmljb259PjwvaT5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L25hdj5cclxuXHJcblxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2FzaWRlPlxyXG5cclxuICAgICAgey8qIE1haW4gY29udGVudCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICB7LyogVG9wIGhlYWRlciAqL31cclxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgc2hhZG93LXNtIHotMTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTYgcHgtNCBzbTpweC02XCI+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVNb2JpbGVTaWRlYmFyfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmhpZGRlbiB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktNDAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTMwMCBmb2N1czpvdXRsaW5lLW5vbmVcIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJPcGVuIG1vYmlsZSBtZW51XCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktbWVudS1saW5lIHJpLWxnXCI+PC9pPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAge2JyZWFkY3J1bWJzICYmIGJyZWFkY3J1bWJzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICB7YnJlYWRjcnVtYnMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxSZWFjdC5GcmFnbWVudCBrZXk9e2luZGV4fT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA+IDAgJiYgPGkgY2xhc3NOYW1lPVwicmktYXJyb3ctcmlnaHQtcy1saW5lXCI+PC9pPn1cclxuICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmhyZWYgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2l0ZW0uaHJlZn0gY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1wcmltYXJ5XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwXCI+e2l0ZW0ubGFiZWx9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L1JlYWN0LkZyYWdtZW50PlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvbmF2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxOb3RpZmljYXRpb25CZWxsIGNsYXNzTmFtZT1cIm1yLTRcIiAvPlxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVVzZXJEcm9wZG93bn1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWF4LXcteHMgdGV4dC1zbSByb3VuZGVkLWZ1bGwgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5PcGVuIHVzZXIgbWVudTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZC1mdWxsIG9iamVjdC1jb3ZlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3JjPXt1c2VyPy5wcm9maWxlX2ltYWdlIHx8IFwiaHR0cHM6Ly9iYW5uZXIyLmNsZWFucG5nLmNvbS8yMDE4MDcyOC9zYWMvOTEyOGUzMTk1MjJmNjZhYmE5ZWRmOTUxODBhODZlN2Uud2VicFwifVxyXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIlByb2ZpbGVcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHtpc1VzZXJEcm9wZG93bk9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgbXQtMiB3LTQ4IHJvdW5kZWQtbWQgc2hhZG93LWxnIGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcmluZy0xIHJpbmctYmxhY2sgcmluZy1vcGFjaXR5LTUgZGFyazpyaW5nLWdyYXktNzAwIHotNTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvY3VzdG9tZXIvcHJvZmlsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlTmF2Q2xpY2soJy9jdXN0b21lci9wcm9maWxlJywgJ1Byb2ZpbGUnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJc1VzZXJEcm9wZG93bk9wZW4oZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBZb3VyIFByb2ZpbGVcclxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgPExvZ291dEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0IHRleHQtbGVmdCB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2hvd0NvbmZpcm1hdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgU2lnbiBvdXRcclxuICAgICAgICAgICAgICAgICAgICAgIDwvTG9nb3V0QnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9oZWFkZXI+XHJcblxyXG4gICAgICAgIHsvKiBNYWluIGNvbnRlbnQgYXJlYSAqL31cclxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9tYWluPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDdXN0b21lckxheW91dDsiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZU1lbW8iLCJ1c2VDYWxsYmFjayIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJ1c2VMb2FkaW5nIiwiTG9nb3V0QnV0dG9uIiwiTm90aWZpY2F0aW9uQmVsbCIsIkN1c3RvbWVyTGF5b3V0IiwiY2hpbGRyZW4iLCJicmVhZGNydW1icyIsImlzTW9iaWxlU2lkZWJhck9wZW4iLCJzZXRJc01vYmlsZVNpZGViYXJPcGVuIiwiaXNVc2VyRHJvcGRvd25PcGVuIiwic2V0SXNVc2VyRHJvcGRvd25PcGVuIiwicGF0aG5hbWUiLCJyb3V0ZXIiLCJ1c2VyIiwic2hvd0xvYWRlciIsIm5hdmlnYXRpb25JdGVtcyIsIm5hbWUiLCJocmVmIiwiaWNvbiIsImN1cnJlbnQiLCJzdXBwb3J0SXRlbXMiLCJwcmVmZXRjaFBhZ2VzIiwiY3VzdG9tZXJQYWdlcyIsImZvckVhY2giLCJwYWdlIiwicHJlZmV0Y2giLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJ0b2dnbGVNb2JpbGVTaWRlYmFyIiwiaGFuZGxlTmF2Q2xpY2siLCJwYWdlTWVzc2FnZXMiLCJtZXNzYWdlIiwiaGFuZGxlTmF2SG92ZXIiLCJ0b2dnbGVVc2VyRHJvcGRvd24iLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwiYXNpZGUiLCJpbWciLCJzcmMiLCJhbHQiLCJvbkxvYWQiLCJjb25zb2xlIiwibG9nIiwib25FcnJvciIsImUiLCJ0YXJnZXQiLCJlcnJvciIsImN1cnJlbnRUYXJnZXQiLCJzdHlsZSIsImRpc3BsYXkiLCJ0ZXh0RmFsbGJhY2siLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJpbm5lckhUTUwiLCJwYXJlbnROb2RlIiwiYXBwZW5kQ2hpbGQiLCJuYXYiLCJtYXAiLCJpdGVtIiwib25Nb3VzZUVudGVyIiwiaSIsImgzIiwiaGVhZGVyIiwiYnV0dG9uIiwidHlwZSIsImFyaWEtbGFiZWwiLCJsZW5ndGgiLCJpbmRleCIsIkZyYWdtZW50IiwibGFiZWwiLCJzcGFuIiwicHJvZmlsZV9pbWFnZSIsInZhcmlhbnQiLCJzaXplIiwic2hvd0NvbmZpcm1hdGlvbiIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFeUY7QUFDbEM7QUFDdkI7QUFDNkM7QUFFeEI7QUFlckQsTUFBTVUsNEJBQWNULG9EQUFhQSxDQUE4QlU7QUFFeEQsTUFBTUMsVUFBVTs7SUFDckIsTUFBTUMsVUFBVVgsaURBQVVBLENBQUNRO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0dBTldEO0FBWU4sTUFBTUcsZUFBNEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHZiwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNnQixPQUFPQyxTQUFTLEdBQUdqQiwrQ0FBUUEsQ0FBYztJQUNoRCxNQUFNLENBQUNrQixPQUFPQyxTQUFTLEdBQUduQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDb0IsU0FBU0MsV0FBVyxHQUFHckIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDc0IsU0FBU0MsV0FBVyxHQUFHdkIsK0NBQVFBLENBQUM7SUFDdkMsc0NBQXNDO0lBQ3RDQyxnREFBU0E7a0NBQUM7WUFDUnNCLFdBQVc7UUFDYjtpQ0FBRyxFQUFFO0lBRUwsOERBQThEO0lBQzlEdEIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSSxDQUFDcUIsV0FBVyxDQUFDUixRQUFRLENBQUNJLE9BQU87WUFDakMsOEVBQThFO1lBQzlFLE1BQU1NLGtCQUFrQnBCLHlFQUF5QkEsQ0FBQztZQUNsRDswQ0FBTztvQkFDTHFCLGNBQWNEO2dCQUNoQjs7UUFDRjtpQ0FBRztRQUFDRjtRQUFTUjtRQUFNSTtLQUFNO0lBRXpCakIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSSxDQUFDcUIsU0FBUztZQUNkLCtEQUErRDtZQUMvREk7UUFDRjtpQ0FBRztRQUFDSjtLQUFRO0lBRVosb0RBQW9EO0lBQ3BELE1BQU1JLFdBQVc7UUFDZiw4QkFBOEI7UUFDOUIsTUFBTUMsYUFBYXhCLGlEQUFPQSxDQUFDeUIsR0FBRyxDQUFDO1FBQy9CLE1BQU1DLFlBQVkxQixpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztRQUM5QkUsUUFBUUMsR0FBRyxDQUFDLElBQUk7UUFDaEJELFFBQVFDLEdBQUcsQ0FBQyxJQUFJLFlBQVlKO1FBQzVCRyxRQUFRQyxHQUFHLENBQUMsSUFBSSxZQUFZRjtRQUM1QixJQUFJRixjQUFjRSxXQUFXO1lBQzNCLElBQUk7Z0JBQ0YsTUFBTWYsT0FBT2tCLEtBQUtDLEtBQUssQ0FBQ0o7Z0JBQ3hCLGdDQUFnQztnQkFDaEMsSUFBSSxDQUFDeEIsOERBQWNBLENBQUNzQixhQUFhO29CQUMvQlIsU0FBU1E7b0JBQ1RaLFFBQVFEO29CQUNSWiwrREFBV0EsQ0FBQ2dDLFlBQVksQ0FBQ1A7Z0JBQzNCLE9BQU87b0JBQ0x6QiwrREFBV0EsQ0FBQ2lDLGNBQWM7Z0JBQzVCO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkbEMsK0RBQVdBLENBQUNpQyxjQUFjO2dCQUMxQkwsUUFBUU0sS0FBSyxDQUFDLHVDQUF1Q0E7WUFDdkQ7WUFDQSxJQUFHO2dCQUNDLE1BQU1DLGVBQWVsQyxpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztnQkFDakMsTUFBTVUsWUFBWU4sS0FBS0MsS0FBSyxDQUFDSSxnQkFBZ0I7Z0JBQzdDcEIsU0FBU3FCO1lBQ2IsRUFBRSxPQUFNQyxHQUFHO2dCQUNUVCxRQUFRTSxLQUFLLENBQUMsc0NBQXNDRztZQUN0RDtRQUVGO1FBQ0FsQixXQUFXO0lBQ2I7SUFFQSxNQUFNbUIsUUFBUSxlQUFPQyxPQUFlQztZQUFrQkMsOEVBQXNCO1FBQzFFLElBQUk7WUFDRixNQUFNQyxXQUF5QixNQUFNMUMsK0RBQVdBLENBQUNzQyxLQUFLLENBQUM7Z0JBQUVDO2dCQUFPQztZQUFTO1lBQ3pFLCtDQUErQztZQUMvQyxJQUFJLENBQUNFLFlBQVksQ0FBQ0EsU0FBUzlCLElBQUksRUFBRTtnQkFDL0IsTUFBTSxJQUFJSCxNQUFNO1lBQ2xCO1lBRUEsa0RBQWtEO1lBQ2xELElBQUlnQyxZQUFZO2dCQUNkRSxlQUFlQyxPQUFPLENBQUMsZUFBZTtZQUN4QyxPQUFPO2dCQUNMRCxlQUFlRSxVQUFVLENBQUM7WUFDNUI7WUFFQSxtRkFBbUY7WUFDbkYsTUFBTUMsY0FBY0osU0FBUzlCLElBQUksQ0FBQ21DLGtCQUFrQixJQUFLLEVBQUNMLFNBQVNNLFlBQVksSUFBSU4sU0FBU00sWUFBWSxLQUFLLEVBQUM7WUFFOUcsbUVBQW1FO1lBQ25FLE1BQU1DLDRCQUE0QlAsU0FBU1EsWUFBWSxJQUFJUixTQUFTUyxNQUFNLEtBQUs7WUFFL0UsSUFBSSxDQUFDTCxlQUFlLENBQUNHLDJCQUEyQjtnQkFDOUMsc0VBQXNFO2dCQUN0RWhELGlEQUFPQSxDQUFDbUQsR0FBRyxDQUFDLGFBQWF0QixLQUFLdUIsU0FBUyxDQUFDWCxTQUFTOUIsSUFBSTtnQkFDckRYLGlEQUFPQSxDQUFDbUQsR0FBRyxDQUFDLGNBQWNWLFNBQVNNLFlBQVksSUFBSTtnQkFDbkQsSUFBR04sU0FBUzlCLElBQUksQ0FBQ21DLGtCQUFrQixJQUFJLFFBQVFMLFNBQVM5QixJQUFJLENBQUNtQyxrQkFBa0IsSUFBSSxRQUFPO29CQUN4RixNQUFNL0MsK0RBQVdBLENBQUNzRCxxQkFBcUIsQ0FBQ1osU0FBUzlCLElBQUksQ0FBQzJDLE9BQU8sRUFBRTtvQkFDL0R0RCxpREFBT0EsQ0FBQ21ELEdBQUcsQ0FBQyxrQkFBbUJ0QixLQUFLdUIsU0FBUyxDQUFDWCxTQUFTOUIsSUFBSTtnQkFDN0Q7Z0JBQ0EsTUFBTVk7WUFDUixPQUFPLElBQUl5QiwyQkFBMkI7Z0JBQ3BDLHNFQUFzRTtnQkFDdEVoRCxpREFBT0EsQ0FBQ21ELEdBQUcsQ0FBQyxrQkFBa0J0QixLQUFLdUIsU0FBUyxDQUFDWCxTQUFTOUIsSUFBSTtnQkFDMURnQixRQUFRQyxHQUFHLENBQUMsc0RBQXNEYSxTQUFTOUIsSUFBSSxDQUFDMkIsS0FBSztZQUN2RjtZQUVBLE9BQU9HO1FBQ1QsRUFBRSxPQUFPUixPQUFPO1lBQ2ROLFFBQVFNLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1zQix5QkFBeUIsZUFBT3hDLE9BQWV5QztZQUFnQmhCLDhFQUFzQjtRQUN6RixJQUFJO1lBQ0YsMkJBQTJCO1lBQzNCLE1BQU1pQixRQUFrQkMsTUFBTUMsT0FBTyxDQUFDSCxTQUFTQyxLQUFLLElBQUlELFNBQVNDLEtBQUssR0FBRyxFQUFFO1lBRTNFLDJEQUEyRDtZQUMzRCxNQUFNOUMsT0FBTztnQkFDWCxHQUFHNkMsUUFBUTtnQkFDWEM7Z0JBQ0FHLFNBQVNILE1BQU1JLFFBQVEsQ0FBQztnQkFDeEJDLFlBQVlMLE1BQU1JLFFBQVEsQ0FBQztZQUM3QjtZQUVBLElBQUlFLElBQXNDLEVBQUU7Z0JBQzFDcEMsUUFBUUMsR0FBRyxDQUFDLG1EQUFtRGpCO1lBQ2pFO1lBRUFLLFNBQVNEO1lBQ1RILFFBQVFEO1lBRVIsOERBQThEO1lBQzlELE1BQU1xRCxtQkFBbUJ4QixhQUFhLEtBQUssR0FBRywwQ0FBMEM7WUFDeEZ4QyxpREFBT0EsQ0FBQ21ELEdBQUcsQ0FBQyxjQUFjcEMsT0FBTztnQkFBRWtELFNBQVNEO1lBQWlCO1lBQzdEaEUsaURBQU9BLENBQUNtRCxHQUFHLENBQUMsYUFBYXRCLEtBQUt1QixTQUFTLENBQUN6QyxPQUFPO2dCQUFFc0QsU0FBU0Q7WUFBaUI7WUFDM0VqRSwrREFBV0EsQ0FBQ2dDLFlBQVksQ0FBQ2hCO1FBQzNCLEVBQUUsT0FBT2tCLE9BQU87WUFDZE4sUUFBUU0sS0FBSyxDQUFDLDRDQUE0Q0E7WUFDMUQsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBTWlDLFdBQVcsT0FBT1Y7UUFDdEIsTUFBTVcsU0FBUyxNQUFNcEUsK0RBQVdBLENBQUNtRSxRQUFRLENBQUNWO1FBQzFDLElBQUdXLE9BQU94RCxJQUFJLEVBQUU7WUFDZDBCLE1BQU1tQixTQUFTbEIsS0FBSyxFQUFFa0IsU0FBU2pCLFFBQVE7UUFDekM7UUFDQSxJQUFJd0IsSUFBc0MsRUFBRTtZQUMxQ3BDLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBRUEseURBQXlEO1FBQ3pELDZEQUE2RDtRQUM3RCw0REFBNEQ7UUFDNUQsMERBQTBEO1FBRTFELE9BQU91QztJQUNUO0lBRUEsTUFBTUMsU0FBUztRQUNiLElBQUlMLElBQXNDLEVBQUU7WUFDMUNwQyxRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUNBLGNBQWM7UUFDZGhCLFFBQVE7UUFDUkksU0FBUztRQUNURixTQUFTO1FBQ1QsMkJBQTJCO1FBQzNCZiwrREFBV0EsQ0FBQ2lDLGNBQWM7UUFDMUIsSUFBSStCLElBQXNDLEVBQUU7WUFDMUNwQyxRQUFRQyxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTXlDLGFBQWEsQ0FBQ0M7UUFDbEIsSUFBSVAsSUFBc0MsRUFBRTtZQUMxQ3BDLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEIwQztRQUM1QztRQUVBLG1EQUFtRDtRQUNuRCxJQUFJYixRQUFrQixFQUFFO1FBQ3hCLElBQUlhLFlBQVliLEtBQUssRUFBRTtZQUNyQkEsUUFBUWEsWUFBWWIsS0FBSyxDQUFDYyxHQUFHLENBQUMsQ0FBQ0MsT0FDN0IsT0FBT0EsU0FBUyxXQUFXQSxPQUFPQSxLQUFLQyxJQUFJLElBQUlELEtBQUtFLFNBQVMsSUFBSTtRQUVyRTtRQUVBdkUsOERBQVdBLENBQUN3RSxXQUFXLENBQUNMLFlBQVloQixPQUFPLEVBQUVzQixJQUFJLENBQUNqRSxDQUFBQTtZQUNoREEsS0FBS21ELFVBQVUsR0FBSUwsTUFBTUksUUFBUSxDQUFDO1lBQ2xDLElBQUlFLElBQXNDLEVBQUUsQ0FDNUM7WUFDQW5ELFFBQVFEO1lBQ1Isb0VBQW9FO1lBQ3BFLE1BQU1rRSxnQkFBZ0I3RSxpREFBT0EsQ0FBQ3lCLEdBQUcsQ0FBQztZQUNsQyxJQUFJb0QsZUFBZTtnQkFDakIsaUVBQWlFO2dCQUNqRSxNQUFNYixtQkFBbUIsR0FBRyxvQ0FBb0M7Z0JBQ2hFaEUsaURBQU9BLENBQUNtRCxHQUFHLENBQUMsYUFBYXRCLEtBQUt1QixTQUFTLENBQUNrQixjQUFjO29CQUFFTCxTQUFTRDtnQkFBaUI7WUFDcEY7UUFDRjtJQUNBLDREQUE0RDtJQUU5RDtJQUVBLE1BQU1jLFFBQXlCO1FBQzdCbkU7UUFDQUk7UUFDQXNCO1FBQ0F4QjtRQUNBMEM7UUFDQVc7UUFDQUU7UUFDQUM7UUFDQXBELFNBQVNBLFdBQVcsQ0FBQ0U7UUFDckI0RCxpQkFBaUI1RCxXQUFXLENBQUMsQ0FBQ1IsUUFBUSxDQUFDLENBQUNJO0lBRTFDO0lBRUEscUJBQU8sOERBQUNYLFlBQVk0RSxRQUFRO1FBQUNGLE9BQU9BO2tCQUFRcEU7Ozs7OztBQUM5QyxFQUFFO0lBck5XRDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxBdXRoQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgYXV0aFNlcnZpY2UgfSBmcm9tICcuLi9zZXJ2aWNlcy9hdXRoLnNlcnZpY2UnO1xyXG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xyXG5pbXBvcnQgeyBzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyLCBpc1Rva2VuRXhwaXJlZCB9IGZyb20gJy4uL2xpYi9hdXRoVXRpbHMnO1xyXG5pbXBvcnQgeyBBdXRoUmVzcG9uc2UsIFVzZXIsIFJlZ2lzdGVyRGF0YSwgVXBkYXRlVXNlckRhdGEgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgdXNlclNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL3VzZXJTZXJ2aWNlJztcclxuXHJcbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xyXG4gIHVzZXI6IFVzZXIgfCBudWxsO1xyXG4gIHRva2VuOiBzdHJpbmcgfCBudWxsO1xyXG4gIHR3b0ZhOiBVc2VyIHwgbnVsbDtcclxuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHJlbWVtYmVyTWU/OiBib29sZWFuKSA9PiBQcm9taXNlPEF1dGhSZXNwb25zZT47XHJcbiAgY29tcGxldGVUd29GYWN0b3JMb2dpbjogKHRva2VuOiBzdHJpbmcsIHVzZXJEYXRhOiBVc2VyLCByZW1lbWJlck1lPzogYm9vbGVhbikgPT4gUHJvbWlzZTx2b2lkPjtcclxuICByZWdpc3RlcjogKHVzZXJEYXRhOiBSZWdpc3RlckRhdGEpID0+IFByb21pc2U8QXV0aFJlc3BvbnNlPjtcclxuICBsb2dvdXQ6ICgpID0+IHZvaWQ7XHJcbiAgdXBkYXRlVXNlcjogKHVzZXI6IFVwZGF0ZVVzZXJEYXRhKSA9PiB2b2lkO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xyXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xyXG4gIH1cclxuICByZXR1cm4gY29udGV4dDtcclxufTtcclxuXHJcbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdHdvRmEsIHNldFR3b0ZhXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdG9rZW4sIHNldFRva2VuXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAvLyBTZXQgbW91bnRlZCB0byB0cnVlIGFmdGVyIGh5ZHJhdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRNb3VudGVkKHRydWUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gU3RhcnQgdG9rZW4gdmFsaWRhdGlvbiB0aW1lciB3aGVuIG1vdW50ZWQgYW5kIGF1dGhlbnRpY2F0ZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFtb3VudGVkIHx8ICF1c2VyIHx8ICF0b2tlbikgcmV0dXJuO1xyXG4gICAgLy8gU3RhcnQgcGVyaW9kaWMgdG9rZW4gdmFsaWRhdGlvbiAoY2hlY2sgZXZlcnkgNSBtaW51dGVzIGluc3RlYWQgb2YgMSBtaW51dGUpXHJcbiAgICBjb25zdCB2YWxpZGF0aW9uVGltZXIgPSBzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyKDMwMDAwMCk7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBjbGVhckludGVydmFsKHZhbGlkYXRpb25UaW1lcik7XHJcbiAgICB9O1xyXG4gIH0sIFttb3VudGVkLCB1c2VyLCB0b2tlbl0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFtb3VudGVkKSByZXR1cm47XHJcbiAgICAvLyBFeGVjdXRlIGltbWVkaWF0ZWx5IGluc3RlYWQgb2Ygd2FpdGluZyBmb3IgbmV4dCByZW5kZXIgY3ljbGVcclxuICAgIGluaXRBdXRoKCk7XHJcbiAgfSwgW21vdW50ZWRdKTtcclxuXHJcbiAgLy8gQ2hlY2sgZm9yIGV4aXN0aW5nIHRva2VuIG9uIG1vdW50IHdpdGggdmFsaWRhdGlvblxyXG4gIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8gR2V0IHNhdmVkIGRhdGEgZnJvbSBjb29raWVzXHJcbiAgICBjb25zdCBzYXZlZFRva2VuID0gQ29va2llcy5nZXQoJ2F1dGhfdG9rZW4nKTtcclxuICAgIGNvbnN0IHNhdmVkVXNlciA9IENvb2tpZXMuZ2V0KCdhdXRoX3VzZXInKTtcclxuICAgIGNvbnNvbGUubG9nKDEgKyBcIj09PT09PT1cIilcclxuICAgIGNvbnNvbGUubG9nKDEgKyBcIj09PT09PT1cIiArIHNhdmVkVG9rZW4pXHJcbiAgICBjb25zb2xlLmxvZygxICsgXCI9PT09PT09XCIgKyBzYXZlZFVzZXIpXHJcbiAgICBpZiAoc2F2ZWRUb2tlbiAmJiBzYXZlZFVzZXIpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZShzYXZlZFVzZXIpO1xyXG4gICAgICAgIC8vIFZhbGlkYXRlIHRva2VuIGlzIG5vdCBleHBpcmVkXHJcbiAgICAgICAgaWYgKCFpc1Rva2VuRXhwaXJlZChzYXZlZFRva2VuKSkge1xyXG4gICAgICAgICAgc2V0VG9rZW4oc2F2ZWRUb2tlbik7XHJcbiAgICAgICAgICBzZXRVc2VyKHVzZXIpO1xyXG4gICAgICAgICAgYXV0aFNlcnZpY2Uuc2V0QXV0aFRva2VuKHNhdmVkVG9rZW4pO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBhdXRoU2VydmljZS5jbGVhckF1dGhUb2tlbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBhdXRoU2VydmljZS5jbGVhckF1dGhUb2tlbigpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBVc2VyIGRhdGEgcGFyc2UgZmFpbGVkJywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICAgIHRyeXtcclxuICAgICAgICAgIGNvbnN0IHNhdmVkMmZhVXNlciA9IENvb2tpZXMuZ2V0KCcyZmFfbG9naW5fdXNlcicpO1xyXG4gICAgICAgICAgY29uc3QgdHdvRmFVc2VyID0gSlNPTi5wYXJzZShzYXZlZDJmYVVzZXIgfHwgJycpO1xyXG4gICAgICAgICAgc2V0VHdvRmEodHdvRmFVc2VyKTtcclxuICAgICAgfSBjYXRjaChlKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IDJGQSB1c2VyIHBhcnNlIGZhaWxlZCcsIGUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgfVxyXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBsb2dpbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCByZW1lbWJlck1lOiBib29sZWFuID0gZmFsc2UpOiBQcm9taXNlPEF1dGhSZXNwb25zZT4gPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2U6IEF1dGhSZXNwb25zZSA9IGF3YWl0IGF1dGhTZXJ2aWNlLmxvZ2luKHsgZW1haWwsIHBhc3N3b3JkIH0pO1xyXG4gICAgICAvLyBWYWxpZGF0ZSByZXNwb25zZSBzdHJ1Y3R1cmUgZm9yIG5vcm1hbCBsb2dpblxyXG4gICAgICBpZiAoIXJlc3BvbnNlIHx8ICFyZXNwb25zZS51c2VyKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHJlc3BvbnNlIGZyb20gYXV0aGVudGljYXRpb24gc2VydmljZScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTdG9yZSByZW1lbWJlciBtZSBwcmVmZXJlbmNlIGZvciAyRkEgY29tcGxldGlvblxyXG4gICAgICBpZiAocmVtZW1iZXJNZSkge1xyXG4gICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3JlbWVtYmVyX21lJywgJ3RydWUnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdyZW1lbWJlcl9tZScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDaGVjayBpZiAyRkEgaXMgcmVxdWlyZWQgKGJhY2tlbmQgcmV0dXJucyBlbXB0eSBhY2Nlc3NfdG9rZW4gd2hlbiAyRkEgaXMgbmVlZGVkKVxyXG4gICAgICBjb25zdCByZXF1aXJlczJGQSA9IHJlc3BvbnNlLnVzZXIudHdvX2ZhY3Rvcl9lbmFibGVkICYmICghcmVzcG9uc2UuYWNjZXNzX3Rva2VuIHx8IHJlc3BvbnNlLmFjY2Vzc190b2tlbiA9PT0gJycpO1xyXG5cclxuICAgICAgLy8gQ2hlY2sgaWYgZW1haWwgdmVyaWZpY2F0aW9uIGlzIHJlcXVpcmVkIChuZXcgZmllbGQgZnJvbSBiYWNrZW5kKVxyXG4gICAgICBjb25zdCByZXF1aXJlc0VtYWlsVmVyaWZpY2F0aW9uID0gcmVzcG9uc2UucmVxdWlyZXNfMmZhICYmIHJlc3BvbnNlLmFjdGlvbiA9PT0gJ3JlZ2lzdGVyJztcclxuXHJcbiAgICAgIGlmICghcmVxdWlyZXMyRkEgJiYgIXJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24pIHtcclxuICAgICAgICAvLyBObyAyRkEgb3IgZW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkLCBzZXQgYXV0aCBjb250ZXh0IGltbWVkaWF0ZWx5XHJcbiAgICAgICAgQ29va2llcy5zZXQoJ2F1dGhfdXNlcicsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLnVzZXIpKTtcclxuICAgICAgICBDb29raWVzLnNldCgnYXV0aF90b2tlbicsIHJlc3BvbnNlLmFjY2Vzc190b2tlbiB8fCAnJyk7XHJcbiAgICAgICAgaWYocmVzcG9uc2UudXNlci50d29fZmFjdG9yX2VuYWJsZWQgPT0gdHJ1ZSB8fCByZXNwb25zZS51c2VyLnR3b19mYWN0b3JfZW5hYmxlZCA9PSAndHJ1ZScpe1xyXG4gICAgICAgICAgYXdhaXQgYXV0aFNlcnZpY2UuZ2VuZXJhdGVUd29GYWN0b3JDb2RlKHJlc3BvbnNlLnVzZXIudXNlcl9pZCwgJ2xvZ2luJyk7XHJcbiAgICAgICAgICBDb29raWVzLnNldCgnMmZhX2xvZ2luX3VzZXInLCAgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlcikpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBhd2FpdCBpbml0QXV0aCgpO1xyXG4gICAgICB9IGVsc2UgaWYgKHJlcXVpcmVzRW1haWxWZXJpZmljYXRpb24pIHtcclxuICAgICAgICAvLyBFbWFpbCB2ZXJpZmljYXRpb24gcmVxdWlyZWQgLSBzdG9yZSB1c2VyIGRhdGEgZm9yIHZlcmlmaWNhdGlvbiBwYWdlXHJcbiAgICAgICAgQ29va2llcy5zZXQoJzJmYV9sb2dpbl91c2VyJywgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlcikpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogRW1haWwgdmVyaWZpY2F0aW9uIHJlcXVpcmVkIGZvciB1c2VyOicsIHJlc3BvbnNlLnVzZXIuZW1haWwpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gcmVzcG9uc2U7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoQ29udGV4dDogTG9naW4gZmFpbGVkJywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBjb21wbGV0ZVR3b0ZhY3RvckxvZ2luID0gYXN5bmMgKHRva2VuOiBzdHJpbmcsIHVzZXJEYXRhOiBVc2VyLCByZW1lbWJlck1lOiBib29sZWFuID0gZmFsc2UpOiBQcm9taXNlPHZvaWQ+ID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEVuc3VyZSByb2xlcyBpcyBhbiBhcnJheVxyXG4gICAgICBjb25zdCByb2xlczogc3RyaW5nW10gPSBBcnJheS5pc0FycmF5KHVzZXJEYXRhLnJvbGVzKSA/IHVzZXJEYXRhLnJvbGVzIDogW107XHJcblxyXG4gICAgICAvLyBBZGQgY29tcHV0ZWQgaXNBZG1pbiBwcm9wZXJ0eSBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG4gICAgICBjb25zdCB1c2VyID0ge1xyXG4gICAgICAgIC4uLnVzZXJEYXRhLFxyXG4gICAgICAgIHJvbGVzLFxyXG4gICAgICAgIGlzQWRtaW46IHJvbGVzLmluY2x1ZGVzKCdhZG1pbmlzdHJhdG9yJyksXHJcbiAgICAgICAgaXNDdXN0b21lcjogcm9sZXMuaW5jbHVkZXMoJ2N1c3RvbWVyJyksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IDJGQSBsb2dpbiBzdWNjZXNzZnVsLCBzZXR0aW5nIHVzZXInLCB1c2VyKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgc2V0VG9rZW4odG9rZW4pO1xyXG4gICAgICBzZXRVc2VyKHVzZXIpO1xyXG5cclxuICAgICAgLy8gU2V0IGNvb2tpZXMgd2l0aCBhcHByb3ByaWF0ZSBleHBpcmF0aW9uIGJhc2VkIG9uIHJlbWVtYmVyTWVcclxuICAgICAgY29uc3QgY29va2llRXhwaXJhdGlvbiA9IHJlbWVtYmVyTWUgPyAzMCA6IDE7IC8vIDMwIGRheXMgaWYgcmVtZW1iZXIgbWUsIDEgZGF5IG90aGVyd2lzZVxyXG4gICAgICBDb29raWVzLnNldCgnYXV0aF90b2tlbicsIHRva2VuLCB7IGV4cGlyZXM6IGNvb2tpZUV4cGlyYXRpb24gfSk7XHJcbiAgICAgIENvb2tpZXMuc2V0KCdhdXRoX3VzZXInLCBKU09OLnN0cmluZ2lmeSh1c2VyKSwgeyBleHBpcmVzOiBjb29raWVFeHBpcmF0aW9uIH0pO1xyXG4gICAgICBhdXRoU2VydmljZS5zZXRBdXRoVG9rZW4odG9rZW4pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IDJGQSBsb2dpbiBjb21wbGV0aW9uIGZhaWxlZCcsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiA9PiB7XHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBhdXRoU2VydmljZS5yZWdpc3Rlcih1c2VyRGF0YSk7XHJcbiAgICBpZihyZXN1bHQudXNlcikge1xyXG4gICAgICBsb2dpbih1c2VyRGF0YS5lbWFpbCwgdXNlckRhdGEucGFzc3dvcmQpO1xyXG4gICAgfVxyXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogUmVnaXN0cmF0aW9uIHN1Y2Nlc3NmdWwgLSB1c2VyIHNob3VsZCBsb2dpbiBtYW51YWxseScpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIERvbid0IGF1dG9tYXRpY2FsbHkgbG9nIGluIHRoZSB1c2VyIGFmdGVyIHJlZ2lzdHJhdGlvblxyXG4gICAgLy8gVXNlciBzaG91bGQgYmUgcmVkaXJlY3RlZCB0byBsb2dpbiBwYWdlIHRvIG1hbnVhbGx5IGxvZyBpblxyXG4gICAgLy8gVGhpcyBmb2xsb3dzIHRoZSByZXF1aXJlbWVudCB0aGF0IGFmdGVyIGFjY291bnQgY3JlYXRpb24sXHJcbiAgICAvLyB1c2VycyBzaG91bGQgYmUgcmVkaXJlY3RlZCB0byBsb2dpbiBwYWdlLCBub3QgZGFzaGJvYXJkXHJcblxyXG4gICAgcmV0dXJuIHJlc3VsdDtcclxuICB9O1xyXG5cclxuICBjb25zdCBsb2dvdXQgPSAoKTogdm9pZCA9PiB7XHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBMb2dnaW5nIG91dCB1c2VyJyk7XHJcbiAgICB9XHJcbiAgICAvLyBDbGVhciBzdGF0ZVxyXG4gICAgc2V0VXNlcihudWxsKTtcclxuICAgIHNldFRva2VuKG51bGwpO1xyXG4gICAgc2V0VHdvRmEobnVsbCk7XHJcbiAgICAvLyBDbGVhciBhdXRoIHNlcnZpY2UgdG9rZW5cclxuICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aFRva2VuKCk7XHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBMb2dvdXQgY29tcGxldGUnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVVc2VyID0gKHVwZGF0ZWRVc2VyOiBVcGRhdGVVc2VyRGF0YSk6IHZvaWQgPT4ge1xyXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogVXBkYXRpbmcgdXNlcicsIHVwZGF0ZWRVc2VyKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDb252ZXJ0IHJvbGVzIHRvIHN0cmluZyBhcnJheSBpZiB0aGV5J3JlIG9iamVjdHNcclxuICAgIGxldCByb2xlczogc3RyaW5nW10gPSBbXTtcclxuICAgIGlmICh1cGRhdGVkVXNlci5yb2xlcykge1xyXG4gICAgICByb2xlcyA9IHVwZGF0ZWRVc2VyLnJvbGVzLm1hcCgocm9sZTogc3RyaW5nIHwgeyBuYW1lPzogc3RyaW5nOyByb2xlX25hbWU/OiBzdHJpbmcgfSkgPT5cclxuICAgICAgICB0eXBlb2Ygcm9sZSA9PT0gJ3N0cmluZycgPyByb2xlIDogcm9sZS5uYW1lIHx8IHJvbGUucm9sZV9uYW1lIHx8ICd1bmtub3duJ1xyXG4gICAgICApO1xyXG4gICAgfVxyXG5cclxuICAgIHVzZXJTZXJ2aWNlLmdldFVzZXJCeUlkKHVwZGF0ZWRVc2VyLnVzZXJfaWQpLnRoZW4odXNlciA9PiB7XHJcbiAgICAgIHVzZXIuaXNDdXN0b21lciA9ICByb2xlcy5pbmNsdWRlcygnY3VzdG9tZXInKVxyXG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgfVxyXG4gICAgICBzZXRVc2VyKHVzZXIpO1xyXG4gICAgICAvLyBVcGRhdGUgY29va2llcyB3aXRoIG5ldyB1c2VyIGRhdGEgLSB1c2UgZXhpc3RpbmcgdG9rZW4gZXhwaXJhdGlvblxyXG4gICAgICBjb25zdCBleGlzdGluZ1Rva2VuID0gQ29va2llcy5nZXQoJ2F1dGhfdG9rZW4nKTtcclxuICAgICAgaWYgKGV4aXN0aW5nVG9rZW4pIHtcclxuICAgICAgICAvLyBUcnkgdG8gZGV0ZXJtaW5lIG9yaWdpbmFsIGV4cGlyYXRpb24gZnJvbSB0b2tlbiBvciB1c2UgZGVmYXVsdFxyXG4gICAgICAgIGNvbnN0IGNvb2tpZUV4cGlyYXRpb24gPSAxOyAvLyBEZWZhdWx0IHRvIDEgZGF5IGZvciB1c2VyIHVwZGF0ZXNcclxuICAgICAgICBDb29raWVzLnNldCgnYXV0aF91c2VyJywgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFVzZXIpLCB7IGV4cGlyZXM6IGNvb2tpZUV4cGlyYXRpb24gfSk7XHJcbiAgICAgIH1cclxuICAgIH0pXHJcbiAgICAvLyBFbnN1cmUgaXNBZG1pbiBwcm9wZXJ0eSBpcyBzZXQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdmFsdWU6IEF1dGhDb250ZXh0VHlwZSA9IHtcclxuICAgIHVzZXIsXHJcbiAgICB0b2tlbixcclxuICAgIGxvZ2luLFxyXG4gICAgdHdvRmEsXHJcbiAgICBjb21wbGV0ZVR3b0ZhY3RvckxvZ2luLFxyXG4gICAgcmVnaXN0ZXIsXHJcbiAgICBsb2dvdXQsXHJcbiAgICB1cGRhdGVVc2VyLFxyXG4gICAgbG9hZGluZzogbG9hZGluZyB8fCAhbW91bnRlZCxcclxuICAgIGlzQXV0aGVudGljYXRlZDogbW91bnRlZCAmJiAhIXVzZXIgJiYgISF0b2tlbixcclxuICAgIFxyXG4gIH07XHJcblxyXG4gIHJldHVybiA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT57Y2hpbGRyZW59PC9BdXRoQ29udGV4dC5Qcm92aWRlcj47XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJhdXRoU2VydmljZSIsIkNvb2tpZXMiLCJzdGFydFRva2VuVmFsaWRhdGlvblRpbWVyIiwiaXNUb2tlbkV4cGlyZWQiLCJ1c2VyU2VydmljZSIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJ0d29GYSIsInNldFR3b0ZhIiwidG9rZW4iLCJzZXRUb2tlbiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJ2YWxpZGF0aW9uVGltZXIiLCJjbGVhckludGVydmFsIiwiaW5pdEF1dGgiLCJzYXZlZFRva2VuIiwiZ2V0Iiwic2F2ZWRVc2VyIiwiY29uc29sZSIsImxvZyIsIkpTT04iLCJwYXJzZSIsInNldEF1dGhUb2tlbiIsImNsZWFyQXV0aFRva2VuIiwiZXJyb3IiLCJzYXZlZDJmYVVzZXIiLCJ0d29GYVVzZXIiLCJlIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicmVtZW1iZXJNZSIsInJlc3BvbnNlIiwic2Vzc2lvblN0b3JhZ2UiLCJzZXRJdGVtIiwicmVtb3ZlSXRlbSIsInJlcXVpcmVzMkZBIiwidHdvX2ZhY3Rvcl9lbmFibGVkIiwiYWNjZXNzX3Rva2VuIiwicmVxdWlyZXNFbWFpbFZlcmlmaWNhdGlvbiIsInJlcXVpcmVzXzJmYSIsImFjdGlvbiIsInNldCIsInN0cmluZ2lmeSIsImdlbmVyYXRlVHdvRmFjdG9yQ29kZSIsInVzZXJfaWQiLCJjb21wbGV0ZVR3b0ZhY3RvckxvZ2luIiwidXNlckRhdGEiLCJyb2xlcyIsIkFycmF5IiwiaXNBcnJheSIsImlzQWRtaW4iLCJpbmNsdWRlcyIsImlzQ3VzdG9tZXIiLCJwcm9jZXNzIiwiY29va2llRXhwaXJhdGlvbiIsImV4cGlyZXMiLCJyZWdpc3RlciIsInJlc3VsdCIsImxvZ291dCIsInVwZGF0ZVVzZXIiLCJ1cGRhdGVkVXNlciIsIm1hcCIsInJvbGUiLCJuYW1lIiwicm9sZV9uYW1lIiwiZ2V0VXNlckJ5SWQiLCJ0aGVuIiwiZXhpc3RpbmdUb2tlbiIsInZhbHVlIiwiaXNBdXRoZW50aWNhdGVkIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts":
/*!*****************************************************************!*\
  !*** ./src/services/consumer-affairs/consumerAffairsService.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   consumerAffairsService: () => (/* binding */ consumerAffairsService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nconst consumerAffairsService = {\n    // Create new complaint\n    async createComplaint (data) {\n        try {\n            console.log('🔄 Creating consumer affairs complaint:', {\n                title: data.title,\n                category: data.category,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            const formData = new FormData();\n            formData.append('title', data.title);\n            formData.append('description', data.description);\n            formData.append('category', data.category);\n            if (data.priority) {\n                formData.append('priority', data.priority);\n            }\n            // Add attachments if provided\n            if (data.attachments && data.attachments.length > 0) {\n                data.attachments.forEach((file)=>{\n                    formData.append('attachments', file);\n                });\n            }\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/consumer-affairs', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            throw error;\n        }\n    },\n    // Get all complaints with pagination\n    async getComplaints () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID\n    async getComplaint (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get complaint by ID (alias for consistency)\n    async getComplaintById (id) {\n        return this.getComplaint(id);\n    },\n    // Update complaint\n    async updateComplaint (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id), data);\n        // Backend returns { success: true, message: string, data: complaint }\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Delete complaint\n    async deleteComplaint (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/consumer-affairs/\".concat(id));\n    },\n    // Update complaint status (for staff)\n    async updateComplaintStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Update complainee (for staff)\n    async updateComplainee (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id, \"/complainee\"), data);\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Assign complaint to staff member (for staff) - Updated to use the general update endpoint\n    async assignComplaint (id, assignedTo) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/consumer-affairs/\".concat(id), {\n            assigned_to: assignedTo\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Add single attachment to complaint\n    async addAttachment (id, file) {\n        const formData = new FormData();\n        formData.append('files', file);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Add multiple attachments to complaint\n    async addAttachments (id, files) {\n        const formData = new FormData();\n        files.forEach((file)=>{\n            formData.append('files', file);\n        });\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/consumer-affairs/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Get all attachments for a complaint\n    async getAttachments (complaintId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments\"));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Remove attachment from complaint\n    async removeAttachment (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result;\n    },\n    // Download attachment from complaint\n    async downloadAttachment (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId, \"/download\"), {\n            responseType: 'blob'\n        });\n        return response.data;\n    },\n    // Get download URL for attachment (returns URL instead of blob for better performance)\n    async getAttachmentDownloadUrl (complaintId, attachmentId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/\".concat(complaintId, \"/attachments/\").concat(attachmentId, \"/download-url\"));\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Get statistics summary (for staff)\n    async getStatsSummary () {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/consumer-affairs/stats/summary');\n        const result = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        return result.data || result;\n    },\n    // Export complaints to CSV (for staff)\n    async exportToCsv (filters) {\n        const params = new URLSearchParams();\n        if (filters) {\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    params.set(key, value.toString());\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/consumer-affairs/export/csv?\".concat(params.toString()), {\n            responseType: 'blob'\n        });\n        return response.data;\n    },\n    // File validation utilities\n    validateFile (file) {\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        const allowedTypes = [\n            'application/pdf',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n            'application/vnd.ms-excel',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n            'image/jpeg',\n            'image/png',\n            'image/gif',\n            'text/plain'\n        ];\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                error: \"File size must be less than 10MB. Current size: \".concat((file.size / 1024 / 1024).toFixed(2), \"MB\")\n            };\n        }\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                isValid: false,\n                error: 'Invalid file type. Allowed types: PDF, Word, Excel, Images (JPEG, PNG, GIF), Text files'\n            };\n        }\n        return {\n            isValid: true\n        };\n    },\n    // Validate multiple files\n    validateFiles (files) {\n        const errors = [];\n        if (files.length > 5) {\n            errors.push('Maximum 5 files allowed per upload');\n        }\n        files.forEach((file, index)=>{\n            const validation = this.validateFile(file);\n            if (!validation.isValid) {\n                errors.push(\"File \".concat(index + 1, \" (\").concat(file.name, \"): \").concat(validation.error));\n            }\n        });\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    },\n    // Get file type icon for display\n    getFileTypeIcon (fileType) {\n        const iconMap = {\n            'application/pdf': 'ri-file-pdf-line',\n            'application/msword': 'ri-file-word-line',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'ri-file-word-line',\n            'application/vnd.ms-excel': 'ri-file-excel-line',\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'ri-file-excel-line',\n            'image/jpeg': 'ri-image-line',\n            'image/png': 'ri-image-line',\n            'image/gif': 'ri-image-line',\n            'text/plain': 'ri-file-text-line'\n        };\n        return iconMap[fileType] || 'ri-file-line';\n    },\n    // Format file size for display\n    formatFileSize (bytes) {\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // Note: Investigative document methods are not yet implemented in the backend\n    // These will be added when the backend supports investigative document management\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getPriorityColor (priority) {\n        switch(priority === null || priority === void 0 ? void 0 : priority.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'urgent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Billing & Charges',\n                label: 'Billing & Charges'\n            },\n            {\n                value: 'Service Quality',\n                label: 'Service Quality'\n            },\n            {\n                value: 'Network Issues',\n                label: 'Network Issues'\n            },\n            {\n                value: 'Customer Service',\n                label: 'Customer Service'\n            },\n            {\n                value: 'Contract Disputes',\n                label: 'Contract Disputes'\n            },\n            {\n                value: 'Accessibility',\n                label: 'Accessibility'\n            },\n            {\n                value: 'Fraud & Scams',\n                label: 'Fraud & Scams'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/consumer-affairs/index.ts":
/*!************************************************!*\
  !*** ./src/services/consumer-affairs/index.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   consumerAffairsService: () => (/* reexport safe */ _consumerAffairsService__WEBPACK_IMPORTED_MODULE_0__.consumerAffairsService)\n/* harmony export */ });\n/* harmony import */ var _consumerAffairsService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./consumerAffairsService */ \"(app-pages-browser)/./src/services/consumer-affairs/consumerAffairsService.ts\");\n// Consumer Affairs Services\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9jb25zdW1lci1hZmZhaXJzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNEJBQTRCO0FBQ2E7QUFDeUIiLCJzb3VyY2VzIjpbIkQ6XFxNZW1vcnkgQnVzaW5lc3MgU29sdXRpb2luc1xcUHJvamVjdHNcXE1BQ1JBXFxwcm9qZWN0XFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxzZXJ2aWNlc1xcY29uc3VtZXItYWZmYWlyc1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29uc3VtZXIgQWZmYWlycyBTZXJ2aWNlc1xyXG5leHBvcnQgKiBmcm9tICcuL2NvbnN1bWVyQWZmYWlyc1NlcnZpY2UnO1xyXG5leHBvcnQgeyBjb25zdW1lckFmZmFpcnNTZXJ2aWNlIH0gZnJvbSAnLi9jb25zdW1lckFmZmFpcnNTZXJ2aWNlJztcclxuIl0sIm5hbWVzIjpbImNvbnN1bWVyQWZmYWlyc1NlcnZpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/consumer-affairs/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/dataBreachService.ts":
/*!*******************************************************!*\
  !*** ./src/services/data-breach/dataBreachService.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataBreachCategory: () => (/* binding */ DataBreachCategory),\n/* harmony export */   DataBreachPriority: () => (/* binding */ DataBreachPriority),\n/* harmony export */   DataBreachSeverity: () => (/* binding */ DataBreachSeverity),\n/* harmony export */   DataBreachStatus: () => (/* binding */ DataBreachStatus),\n/* harmony export */   dataBreachService: () => (/* binding */ dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\n// Enums matching backend\nvar DataBreachCategory = /*#__PURE__*/ function(DataBreachCategory) {\n    DataBreachCategory[\"PERSONAL_DATA\"] = \"Personal Data\";\n    DataBreachCategory[\"FINANCIAL_DATA\"] = \"Financial Data\";\n    DataBreachCategory[\"HEALTH_DATA\"] = \"Health Data\";\n    DataBreachCategory[\"TECHNICAL_DATA\"] = \"Technical Data\";\n    DataBreachCategory[\"COMMUNICATION_DATA\"] = \"Communication Data\";\n    DataBreachCategory[\"OTHER\"] = \"Other\";\n    return DataBreachCategory;\n}({});\nvar DataBreachSeverity = /*#__PURE__*/ function(DataBreachSeverity) {\n    DataBreachSeverity[\"LOW\"] = \"low\";\n    DataBreachSeverity[\"MEDIUM\"] = \"medium\";\n    DataBreachSeverity[\"HIGH\"] = \"high\";\n    DataBreachSeverity[\"CRITICAL\"] = \"critical\";\n    return DataBreachSeverity;\n}({});\nvar DataBreachStatus = /*#__PURE__*/ function(DataBreachStatus) {\n    DataBreachStatus[\"SUBMITTED\"] = \"submitted\";\n    DataBreachStatus[\"UNDER_REVIEW\"] = \"under_review\";\n    DataBreachStatus[\"INVESTIGATING\"] = \"investigating\";\n    DataBreachStatus[\"RESOLVED\"] = \"resolved\";\n    DataBreachStatus[\"CLOSED\"] = \"closed\";\n    return DataBreachStatus;\n}({});\nvar DataBreachPriority = /*#__PURE__*/ function(DataBreachPriority) {\n    DataBreachPriority[\"LOW\"] = \"low\";\n    DataBreachPriority[\"MEDIUM\"] = \"medium\";\n    DataBreachPriority[\"HIGH\"] = \"high\";\n    DataBreachPriority[\"URGENT\"] = \"urgent\";\n    return DataBreachPriority;\n}({});\nconst dataBreachService = {\n    // Create new report\n    async createReport (data) {\n        try {\n            console.log('🔄 Creating data breach report:', {\n                title: data.title,\n                category: data.category,\n                severity: data.severity,\n                hasAttachments: data.attachments && data.attachments.length > 0\n            });\n            const formData = new FormData();\n            formData.append('title', data.title);\n            formData.append('description', data.description);\n            formData.append('category', data.category);\n            formData.append('severity', data.severity);\n            formData.append('incident_date', data.incident_date);\n            formData.append('organization_involved', data.organization_involved);\n            if (data.priority) {\n                formData.append('priority', data.priority);\n            }\n            if (data.affected_data_types) {\n                formData.append('affected_data_types', data.affected_data_types);\n            }\n            if (data.contact_attempts) {\n                formData.append('contact_attempts', data.contact_attempts);\n            }\n            // Add attachments if provided\n            if (data.attachments && data.attachments.length > 0) {\n                data.attachments.forEach((file)=>{\n                    formData.append('attachments', file);\n                });\n            }\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/data-breach-reports', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Data breach report created successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error creating data breach report:', error);\n            throw error;\n        }\n    },\n    // Get report by ID\n    async getReportById (reportId) {\n        try {\n            console.log('🔄 Fetching data breach report by ID:', reportId);\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(reportId));\n            console.log('✅ Data breach report fetched successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error fetching data breach report:', error);\n            throw error;\n        }\n    },\n    // Update report status\n    async updateStatus (reportId, status, comment) {\n        try {\n            console.log('🔄 Updating data breach report status:', {\n                reportId,\n                status,\n                comment\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/status\"), {\n                status,\n                comment\n            });\n            console.log('✅ Data breach report status updated successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error updating data breach report status:', error);\n            throw error;\n        }\n    },\n    // Assign report to officer\n    async assignReport (reportId, assignedTo) {\n        try {\n            console.log('🔄 Assigning data breach report:', {\n                reportId,\n                assignedTo\n            });\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(reportId, \"/assign\"), {\n                assigned_to: assignedTo\n            });\n            console.log('✅ Data breach report assigned successfully:', response.data);\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        } catch (error) {\n            console.error('❌ Error assigning data breach report:', error);\n            throw error;\n        }\n    },\n    // Get all reports with pagination\n    async getReports () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Get report by ID\n    async getReport (id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/data-breach-reports/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Update report\n    async updateReport (id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Delete report\n    async deleteReport (id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/data-breach-reports/\".concat(id));\n    },\n    // Update report status (for staff)\n    async updateReportStatus (id, status, comment) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/data-breach-reports/\".concat(id, \"/status\"), {\n            status,\n            comment\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Add attachment to report\n    async addAttachment (id, file) {\n        const formData = new FormData();\n        formData.append('files', file);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/data-breach-reports/\".concat(id, \"/attachments\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    },\n    // Remove attachment from report\n    async removeAttachment (reportId, attachmentId) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/data-breach-reports/\".concat(reportId, \"/attachments/\").concat(attachmentId));\n    },\n    // Helper methods\n    getStatusColor (status) {\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'resolved':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getSeverityColor (severity) {\n        switch(severity === null || severity === void 0 ? void 0 : severity.toLowerCase()){\n            case 'low':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n            case 'high':\n                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n            case 'critical':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    },\n    getStatusOptions () {\n        return [\n            {\n                value: 'submitted',\n                label: 'Submitted'\n            },\n            {\n                value: 'under_review',\n                label: 'Under Review'\n            },\n            {\n                value: 'investigating',\n                label: 'Investigating'\n            },\n            {\n                value: 'resolved',\n                label: 'Resolved'\n            },\n            {\n                value: 'closed',\n                label: 'Closed'\n            }\n        ];\n    },\n    getCategoryOptions () {\n        return [\n            {\n                value: 'Personal Data',\n                label: 'Personal Data'\n            },\n            {\n                value: 'Financial Data',\n                label: 'Financial Data'\n            },\n            {\n                value: 'Health Data',\n                label: 'Health Data'\n            },\n            {\n                value: 'Technical Data',\n                label: 'Technical Data'\n            },\n            {\n                value: 'Communication Data',\n                label: 'Communication Data'\n            },\n            {\n                value: 'Other',\n                label: 'Other'\n            }\n        ];\n    },\n    getSeverityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'critical',\n                label: 'Critical'\n            }\n        ];\n    },\n    getPriorityOptions () {\n        return [\n            {\n                value: 'low',\n                label: 'Low'\n            },\n            {\n                value: 'medium',\n                label: 'Medium'\n            },\n            {\n                value: 'high',\n                label: 'High'\n            },\n            {\n                value: 'urgent',\n                label: 'Urgent'\n            }\n        ];\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/data-breach/index.ts":
/*!*******************************************!*\
  !*** ./src/services/data-breach/index.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataBreachCategory: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachCategory),\n/* harmony export */   DataBreachPriority: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachPriority),\n/* harmony export */   DataBreachSeverity: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachSeverity),\n/* harmony export */   DataBreachStatus: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.DataBreachStatus),\n/* harmony export */   dataBreachService: () => (/* reexport safe */ _dataBreachService__WEBPACK_IMPORTED_MODULE_0__.dataBreachService)\n/* harmony export */ });\n/* harmony import */ var _dataBreachService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataBreachService */ \"(app-pages-browser)/./src/services/data-breach/dataBreachService.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhLWJyZWFjaC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0MiLCJzb3VyY2VzIjpbIkQ6XFxNZW1vcnkgQnVzaW5lc3MgU29sdXRpb2luc1xcUHJvamVjdHNcXE1BQ1JBXFxwcm9qZWN0XFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxzZXJ2aWNlc1xcZGF0YS1icmVhY2hcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZGF0YUJyZWFjaFNlcnZpY2UnO1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/data-breach/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy91c2VyU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDcUQ7QUFHSDtBQUVsRCx1Q0FBdUM7QUFFaEMsTUFBTUcsY0FBYztJQUN6QixnQ0FBZ0M7SUFDaEMsTUFBTUM7WUFBU0MsUUFBQUEsaUVBQXVCLENBQUM7UUFDckMsTUFBTUMsU0FBUyxJQUFJQztRQUVuQixJQUFJRixNQUFNRyxJQUFJLEVBQUVGLE9BQU9HLEdBQUcsQ0FBQyxRQUFRSixNQUFNRyxJQUFJLENBQUNFLFFBQVE7UUFDdEQsSUFBSUwsTUFBTU0sS0FBSyxFQUFFTCxPQUFPRyxHQUFHLENBQUMsU0FBU0osTUFBTU0sS0FBSyxDQUFDRCxRQUFRO1FBQ3pELElBQUlMLE1BQU1PLE1BQU0sRUFBRU4sT0FBT0csR0FBRyxDQUFDLFVBQVVKLE1BQU1PLE1BQU07UUFDbkQsSUFBSVAsTUFBTVEsTUFBTSxFQUFFO1lBQ2hCUixNQUFNUSxNQUFNLENBQUNDLE9BQU8sQ0FBQ0MsQ0FBQUEsT0FBUVQsT0FBT1UsTUFBTSxDQUFDLFVBQVVEO1FBQ3ZEO1FBQ0EsSUFBSVYsTUFBTVksUUFBUSxFQUFFO1lBQ2xCWixNQUFNWSxRQUFRLENBQUNILE9BQU8sQ0FBQ0YsQ0FBQUEsU0FBVU4sT0FBT1UsTUFBTSxDQUFDLFlBQVlKO1FBQzdEO1FBQ0EsSUFBSVAsTUFBTWEsTUFBTSxFQUFFO1lBQ2hCQyxPQUFPQyxPQUFPLENBQUNmLE1BQU1hLE1BQU0sRUFBRUosT0FBTyxDQUFDO29CQUFDLENBQUNPLEtBQUtDLE1BQU07Z0JBQ2hELElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUTtvQkFDeEJBLE1BQU1SLE9BQU8sQ0FBQ1csQ0FBQUEsSUFBS25CLE9BQU9VLE1BQU0sQ0FBQyxVQUFjLE9BQUpLLE1BQU9JO2dCQUNwRCxPQUFPO29CQUNMbkIsT0FBT0csR0FBRyxDQUFDLFVBQWMsT0FBSlksTUFBT0M7Z0JBQzlCO1lBQ0Y7UUFDRjtRQUVBLE1BQU1JLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDLElBQXNCLE9BQWxCckIsT0FBT0ksUUFBUTtRQUM3RCxPQUFPVixrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNRSxTQUFRQyxFQUFVO1FBQ3RCLE1BQU1ILFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDLElBQU8sT0FBSEU7UUFDOUMsT0FBTzdCLGtFQUFrQkEsQ0FBQzBCO0lBQzVCO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU1JLGVBQWNDLFVBQW1CO1FBQ3JDLElBQUk7Z0JBU2FDO1lBUmYsTUFBTTFCLFNBQVMsSUFBSUM7WUFDbkIsSUFBSXdCLFlBQVl6QixPQUFPRyxHQUFHLENBQUMsVUFBVXNCO1lBQ3JDekIsT0FBT0csR0FBRyxDQUFDLFNBQVMsT0FBTywwQkFBMEI7WUFFckQsTUFBTWlCLFdBQVcsTUFBTXpCLDJDQUFTQSxDQUFDMEIsR0FBRyxDQUFDLFVBQTRCLE9BQWxCckIsT0FBT0ksUUFBUTtZQUM5RCxNQUFNc0IsWUFBWWhDLGtFQUFrQkEsQ0FBQzBCO1lBRXJDLDRCQUE0QjtZQUM1QixNQUFNTyxTQUFTRCxFQUFBQSxrQkFBQUEsVUFBVUUsSUFBSSxjQUFkRixzQ0FBQUEsZ0JBQWdCRyxHQUFHLENBQUMsQ0FBQ0MsT0FBZUEsS0FBS0MsS0FBSyxFQUFFbkIsTUFBTSxDQUFDb0IsYUFBWSxFQUFFO1lBQ3BGLE9BQU9MO1FBQ1QsRUFBRSxPQUFPTSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyxnQ0FBZ0NGO1lBQzdDLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTUcsYUFBWWIsRUFBVTtRQUMxQixPQUFPLElBQUksQ0FBQ0QsT0FBTyxDQUFDQztJQUN0QjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNYztRQUNKLE1BQU1qQixXQUFXLE1BQU14QixnREFBY0EsQ0FBQ3lCLEdBQUcsQ0FBQztRQUMxQyxPQUFPM0Isa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTWtCLFlBQVdDLFFBQXVCO1FBQ3RDLE1BQU1uQixXQUFXLE1BQU14QixnREFBY0EsQ0FBQzRDLElBQUksQ0FBQyxJQUFJRDtRQUMvQyxPQUFPN0Msa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSxjQUFjO0lBQ2QsTUFBTXFCLFlBQVdsQixFQUFVLEVBQUVnQixRQUF1QjtRQUNsRCxNQUFNbkIsV0FBVyxNQUFNeEIsZ0RBQWNBLENBQUM4QyxHQUFHLENBQUMsSUFBTyxPQUFIbkIsS0FBTWdCO1FBQ3BELE9BQU83QyxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNdUIsZUFBY0osUUFBMEI7UUFDNUMsTUFBTW5CLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDOEMsR0FBRyxDQUFDLFlBQVlIO1FBQ3RELE9BQU83QyxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNd0IsZ0JBQWVDLFlBQStCO1FBQ2xELE1BQU16QixXQUFXLE1BQU14QixnREFBY0EsQ0FBQzhDLEdBQUcsQ0FBQyxxQkFBcUJHO1FBQy9ELE9BQU9uRCxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNMEIsY0FBYUMsSUFBVTtRQUMzQmIsUUFBUWMsR0FBRyxDQUFDLG9DQUFvQztZQUM5Q0MsVUFBVUYsS0FBS0csSUFBSTtZQUNuQkMsVUFBVUosS0FBS0ssSUFBSTtZQUNuQkMsVUFBVU4sS0FBS08sSUFBSTtRQUNyQjtRQUVBLE1BQU1DLFdBQVcsSUFBSUM7UUFDckJELFNBQVM3QyxNQUFNLENBQUMsVUFBVXFDO1FBRzFCLElBQUk7WUFDRixNQUFNM0IsV0FBVyxNQUFNeEIsZ0RBQWNBLENBQUM0QyxJQUFJLENBQUMsbUJBQW1CZSxVQUFVO2dCQUN0RUUsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxPQUFPL0Qsa0VBQWtCQSxDQUFDMEI7UUFDNUIsRUFBRSxPQUFPYSxPQUFnQjtnQkFHYnlCLHNCQUNJQSx1QkFDTkE7WUFKUixNQUFNQSxhQUFhekI7WUFDbkJDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEI7Z0JBQzFDMEIsTUFBTSxHQUFFRCx1QkFBQUEsV0FBV3RDLFFBQVEsY0FBbkJzQywyQ0FBQUEscUJBQXFCQyxNQUFNO2dCQUNuQ0MsVUFBVSxHQUFFRix3QkFBQUEsV0FBV3RDLFFBQVEsY0FBbkJzQyw0Q0FBQUEsc0JBQXFCRSxVQUFVO2dCQUMzQ2hDLElBQUksR0FBRThCLHdCQUFBQSxXQUFXdEMsUUFBUSxjQUFuQnNDLDRDQUFBQSxzQkFBcUI5QixJQUFJO2dCQUMvQmlDLFNBQVNILFdBQVdHLE9BQU87WUFDN0I7WUFDQSxNQUFNNUI7UUFDUjtJQUNGO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU02QjtRQUNKLE1BQU0xQyxXQUFXLE1BQU14QixnREFBY0EsQ0FBQ21FLE1BQU0sQ0FBQztRQUM3QyxPQUFPckUsa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTTRDO1FBQ0osTUFBTTVDLFdBQVcsTUFBTXhCLGdEQUFjQSxDQUFDeUIsR0FBRyxDQUFDO1FBQzFDLE9BQU8zQixrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLGNBQWM7SUFDZCxNQUFNNkMsWUFBVzFDLEVBQVU7UUFDekIsTUFBTTNCLGdEQUFjQSxDQUFDbUUsTUFBTSxDQUFDLElBQU8sT0FBSHhDO0lBQ2xDO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU0yQztRQUNKLE1BQU05QyxXQUFXLE1BQU16QiwyQ0FBU0EsQ0FBQzBCLEdBQUcsQ0FBQztRQUNyQyxPQUFPM0Isa0VBQWtCQSxDQUFDMEI7SUFDNUI7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTStDLGlCQUFnQnBCLElBQVU7UUFDOUIsTUFBTVEsV0FBVyxJQUFJQztRQUNyQkQsU0FBUzdDLE1BQU0sQ0FBQyxhQUFhcUM7UUFFN0IsTUFBTTNCLFdBQVcsTUFBTXpCLDJDQUFTQSxDQUFDNkMsSUFBSSxDQUFDLDZCQUE2QmUsVUFBVTtZQUMzRUUsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUNBLE9BQU8vRCxrRUFBa0JBLENBQUMwQjtJQUM1QjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNZ0Q7UUFDSixNQUFNekUsMkNBQVNBLENBQUNvRSxNQUFNLENBQUM7SUFDekI7QUFDRixFQUFFIiwic291cmNlcyI6WyJEOlxcTWVtb3J5IEJ1c2luZXNzIFNvbHV0aW9pbnNcXFByb2plY3RzXFxNQUNSQVxccHJvamVjdFxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcc2VydmljZXNcXHVzZXJTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF4aW9zRXJyb3IgfSBmcm9tICdheGlvcyc7XHJcbmltcG9ydCB7IHByb2Nlc3NBcGlSZXNwb25zZSB9IGZyb20gJ0AvbGliL2F1dGhVdGlscyc7XHJcbmltcG9ydCB7IENoYW5nZVBhc3N3b3JkRHRvLCBDcmVhdGVVc2VyRHRvLCBVcGRhdGVQcm9maWxlRHRvLCBVcGRhdGVVc2VyRHRvLCBVc2VyIH0gZnJvbSAnQC90eXBlcy91c2VyJztcclxuaW1wb3J0IHsgUGFnaW5hdGVkUmVzcG9uc2UsIFBhZ2luYXRlUXVlcnkgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgYXBpQ2xpZW50LCB1c2Vyc0FwaUNsaWVudCB9IGZyb20gJ0AvbGliJztcclxuXHJcbi8vIFJlLWV4cG9ydCBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZXJTZXJ2aWNlID0ge1xyXG4gIC8vIEdldCBhbGwgdXNlcnMgd2l0aCBwYWdpbmF0aW9uXHJcbiAgYXN5bmMgZ2V0VXNlcnMocXVlcnk6IFBhZ2luYXRlUXVlcnkgPSB7fSk6IFByb21pc2U8UGFnaW5hdGVkUmVzcG9uc2U8VXNlcj4+IHtcclxuICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcclxuXHJcbiAgICBpZiAocXVlcnkucGFnZSkgcGFyYW1zLnNldCgncGFnZScsIHF1ZXJ5LnBhZ2UudG9TdHJpbmcoKSk7XHJcbiAgICBpZiAocXVlcnkubGltaXQpIHBhcmFtcy5zZXQoJ2xpbWl0JywgcXVlcnkubGltaXQudG9TdHJpbmcoKSk7XHJcbiAgICBpZiAocXVlcnkuc2VhcmNoKSBwYXJhbXMuc2V0KCdzZWFyY2gnLCBxdWVyeS5zZWFyY2gpO1xyXG4gICAgaWYgKHF1ZXJ5LnNvcnRCeSkge1xyXG4gICAgICBxdWVyeS5zb3J0QnkuZm9yRWFjaChzb3J0ID0+IHBhcmFtcy5hcHBlbmQoJ3NvcnRCeScsIHNvcnQpKTtcclxuICAgIH1cclxuICAgIGlmIChxdWVyeS5zZWFyY2hCeSkge1xyXG4gICAgICBxdWVyeS5zZWFyY2hCeS5mb3JFYWNoKHNlYXJjaCA9PiBwYXJhbXMuYXBwZW5kKCdzZWFyY2hCeScsIHNlYXJjaCkpO1xyXG4gICAgfVxyXG4gICAgaWYgKHF1ZXJ5LmZpbHRlcikge1xyXG4gICAgICBPYmplY3QuZW50cmllcyhxdWVyeS5maWx0ZXIpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xyXG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xyXG4gICAgICAgICAgdmFsdWUuZm9yRWFjaCh2ID0+IHBhcmFtcy5hcHBlbmQoYGZpbHRlci4ke2tleX1gLCB2KSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHBhcmFtcy5zZXQoYGZpbHRlci4ke2tleX1gLCB2YWx1ZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LmdldChgPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBHZXQgdXNlciBieSBJRFxyXG4gIGFzeW5jIGdldFVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5nZXQoYC8ke2lkfWApO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHVzZXIgZW1haWxzIGZvciBwcmVkaWN0aXZlIGlucHV0XHJcbiAgYXN5bmMgZ2V0VXNlckVtYWlscyhzZWFyY2hUZXJtPzogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG4gICAgICBpZiAoc2VhcmNoVGVybSkgcGFyYW1zLnNldCgnc2VhcmNoJywgc2VhcmNoVGVybSk7XHJcbiAgICAgIHBhcmFtcy5zZXQoJ2xpbWl0JywgJzIwJyk7IC8vIExpbWl0IHRvIDIwIHN1Z2dlc3Rpb25zXHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYC91c2Vycz8ke3BhcmFtcy50b1N0cmluZygpfWApO1xyXG4gICAgICBjb25zdCB1c2Vyc0RhdGEgPSBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG5cclxuICAgICAgLy8gRXh0cmFjdCBlbWFpbHMgZnJvbSB1c2Vyc1xyXG4gICAgICBjb25zdCBlbWFpbHMgPSB1c2Vyc0RhdGEuZGF0YT8ubWFwKCh1c2VyOiBVc2VyKSA9PiB1c2VyLmVtYWlsKS5maWx0ZXIoQm9vbGVhbikgfHwgW107XHJcbiAgICAgIHJldHVybiBlbWFpbHM7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBmZXRjaCB1c2VyIGVtYWlsczonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBbXTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBHZXQgdXNlciBieSBJRCAoYWxpYXMgZm9yIGNvbnNpc3RlbmN5KVxyXG4gIGFzeW5jIGdldFVzZXJCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPFVzZXI+IHtcclxuICAgIHJldHVybiB0aGlzLmdldFVzZXIoaWQpO1xyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBjdXJyZW50IHVzZXIgcHJvZmlsZVxyXG4gIGFzeW5jIGdldFByb2ZpbGUoKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LmdldCgnL3Byb2ZpbGUnKTtcclxuICAgIHJldHVybiBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG4gIH0sXHJcblxyXG4gIC8vIENyZWF0ZSBuZXcgdXNlclxyXG4gIGFzeW5jIGNyZWF0ZVVzZXIodXNlckRhdGE6IENyZWF0ZVVzZXJEdG8pOiBQcm9taXNlPFVzZXI+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXNlcnNBcGlDbGllbnQucG9zdCgnJywgdXNlckRhdGEpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBkYXRlIHVzZXJcclxuICBhc3luYyB1cGRhdGVVc2VyKGlkOiBzdHJpbmcsIHVzZXJEYXRhOiBVcGRhdGVVc2VyRHRvKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJzQXBpQ2xpZW50LnB1dChgLyR7aWR9YCwgdXNlckRhdGEpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBkYXRlIGN1cnJlbnQgdXNlciBwcm9maWxlXHJcbiAgYXN5bmMgdXBkYXRlUHJvZmlsZSh1c2VyRGF0YTogVXBkYXRlUHJvZmlsZUR0byk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5wdXQoJy9wcm9maWxlJywgdXNlckRhdGEpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gQ2hhbmdlIHBhc3N3b3JkXHJcbiAgYXN5bmMgY2hhbmdlUGFzc3dvcmQocGFzc3dvcmREYXRhOiBDaGFuZ2VQYXNzd29yZER0byk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5wdXQoJy9wcm9maWxlL3Bhc3N3b3JkJywgcGFzc3dvcmREYXRhKTtcclxuICAgIHJldHVybiBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG4gIH0sXHJcblxyXG4gIC8vIFVwbG9hZCBhdmF0YXJcclxuICBhc3luYyB1cGxvYWRBdmF0YXIoZmlsZTogRmlsZSk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc29sZS5sb2coJ3VzZXJTZXJ2aWNlOiB1cGxvYWRBdmF0YXIgY2FsbGVkJywge1xyXG4gICAgICBmaWxlTmFtZTogZmlsZS5uYW1lLFxyXG4gICAgICBmaWxlU2l6ZTogZmlsZS5zaXplLFxyXG4gICAgICBmaWxlVHlwZTogZmlsZS50eXBlXHJcbiAgICB9KTtcclxuXHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdhdmF0YXInLCBmaWxlKTtcclxuXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5wb3N0KCcvcHJvZmlsZS9hdmF0YXInLCBmb3JtRGF0YSwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XHJcbiAgICAgIGNvbnN0IGF4aW9zRXJyb3IgPSBlcnJvciBhcyBBeGlvc0Vycm9yO1xyXG4gICAgICBjb25zb2xlLmVycm9yKCd1c2VyU2VydmljZTogVXBsb2FkIGZhaWxlZCcsIHtcclxuICAgICAgICBzdGF0dXM6IGF4aW9zRXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICBzdGF0dXNUZXh0OiBheGlvc0Vycm9yLnJlc3BvbnNlPy5zdGF0dXNUZXh0LFxyXG4gICAgICAgIGRhdGE6IGF4aW9zRXJyb3IucmVzcG9uc2U/LmRhdGEsXHJcbiAgICAgICAgbWVzc2FnZTogYXhpb3NFcnJvci5tZXNzYWdlXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBSZW1vdmUgYXZhdGFyXHJcbiAgYXN5bmMgcmVtb3ZlQXZhdGFyKCk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5kZWxldGUoJy9wcm9maWxlL2F2YXRhcicpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHVzZXIgYXZhdGFyIGRvY3VtZW50XHJcbiAgYXN5bmMgZ2V0VXNlckF2YXRhcigpOiBQcm9taXNlPGFueT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2Vyc0FwaUNsaWVudC5nZXQoJy9wcm9maWxlL2F2YXRhcicpO1xyXG4gICAgcmV0dXJuIHByb2Nlc3NBcGlSZXNwb25zZShyZXNwb25zZSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gRGVsZXRlIHVzZXJcclxuICBhc3luYyBkZWxldGVVc2VyKGlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIGF3YWl0IHVzZXJzQXBpQ2xpZW50LmRlbGV0ZShgLyR7aWR9YCk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHVzZXIgc2lnbmF0dXJlXHJcbiAgYXN5bmMgZ2V0VXNlclNpZ25hdHVyZSgpOiBQcm9taXNlPGFueT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvZG9jdW1lbnRzL3VzZXIvc2lnbmF0dXJlJyk7XHJcbiAgICByZXR1cm4gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGxvYWQgdXNlciBzaWduYXR1cmVcclxuICBhc3luYyB1cGxvYWRTaWduYXR1cmUoZmlsZTogRmlsZSk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdzaWduYXR1cmUnLCBmaWxlKTtcclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvZG9jdW1lbnRzL3VzZXIvc2lnbmF0dXJlJywgZm9ybURhdGEsIHtcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICAgIHJldHVybiBwcm9jZXNzQXBpUmVzcG9uc2UocmVzcG9uc2UpO1xyXG4gIH0sXHJcblxyXG4gIC8vIERlbGV0ZSB1c2VyIHNpZ25hdHVyZVxyXG4gIGFzeW5jIGRlbGV0ZVNpZ25hdHVyZSgpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIGF3YWl0IGFwaUNsaWVudC5kZWxldGUoJy9kb2N1bWVudHMvdXNlci9zaWduYXR1cmUnKTtcclxuICB9LFxyXG59O1xyXG4iXSwibmFtZXMiOlsicHJvY2Vzc0FwaVJlc3BvbnNlIiwiYXBpQ2xpZW50IiwidXNlcnNBcGlDbGllbnQiLCJ1c2VyU2VydmljZSIsImdldFVzZXJzIiwicXVlcnkiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJwYWdlIiwic2V0IiwidG9TdHJpbmciLCJsaW1pdCIsInNlYXJjaCIsInNvcnRCeSIsImZvckVhY2giLCJzb3J0IiwiYXBwZW5kIiwic2VhcmNoQnkiLCJmaWx0ZXIiLCJPYmplY3QiLCJlbnRyaWVzIiwia2V5IiwidmFsdWUiLCJBcnJheSIsImlzQXJyYXkiLCJ2IiwicmVzcG9uc2UiLCJnZXQiLCJnZXRVc2VyIiwiaWQiLCJnZXRVc2VyRW1haWxzIiwic2VhcmNoVGVybSIsInVzZXJzRGF0YSIsImVtYWlscyIsImRhdGEiLCJtYXAiLCJ1c2VyIiwiZW1haWwiLCJCb29sZWFuIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsImdldFVzZXJCeUlkIiwiZ2V0UHJvZmlsZSIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsInBvc3QiLCJ1cGRhdGVVc2VyIiwicHV0IiwidXBkYXRlUHJvZmlsZSIsImNoYW5nZVBhc3N3b3JkIiwicGFzc3dvcmREYXRhIiwidXBsb2FkQXZhdGFyIiwiZmlsZSIsImxvZyIsImZpbGVOYW1lIiwibmFtZSIsImZpbGVTaXplIiwic2l6ZSIsImZpbGVUeXBlIiwidHlwZSIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJoZWFkZXJzIiwiYXhpb3NFcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJtZXNzYWdlIiwicmVtb3ZlQXZhdGFyIiwiZGVsZXRlIiwiZ2V0VXNlckF2YXRhciIsImRlbGV0ZVVzZXIiLCJnZXRVc2VyU2lnbmF0dXJlIiwidXBsb2FkU2lnbmF0dXJlIiwiZGVsZXRlU2lnbmF0dXJlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ })

});