import { 
  Address 
} from '../entities/address.entity';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { AddressService } from './address.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateAddressDto } from '../dto/address/create.dto';
import { UpdateAddressDto } from '../dto/address/update.dto';
import { Audit } from '../common/interceptors/audit.interceptor';

@Controller('address')
@ApiTags('Addresses')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AddressController {
  constructor(private readonly addressService: AddressService) {}

  @ApiOperation({ summary: 'Add address based on type' })
  @ApiResponse({ status: 201, description: 'Address added successfully' })
  @ApiBody({ type: CreateAddressDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.ADDRESS_MANAGEMENT,
    resourceType: 'Address',
    description: 'Created address',
  })
  @Post('create')
  async createAddress(@Body() createAddressDto: CreateAddressDto, @Request() req: any): Promise<Address> {
    return this.addressService.createAddress(createAddressDto, req.user.userId);
  }

  @ApiOperation({ summary: 'Edit existing address' })
  @ApiResponse({ status: 200, description: 'Address edit successful' })
  @ApiResponse({ status: 404, description: 'Address not found' })
  @ApiBody({ type: UpdateAddressDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.ADDRESS_MANAGEMENT,
    resourceType: 'Address',
    description: 'Edited address',
  })
  @Put(':id')
  async editAddress(@Param('id', ParseUUIDPipe) id: string, @Body() updateDto: UpdateAddressDto, @Request() req: any): Promise<Address> {
    return this.addressService.editAddress(id, updateDto, req.user.userId);
  }

  @ApiOperation({ summary: 'List all addresses (optional filters)' })
  @ApiResponse({ status: 200, description: 'List of addresses' })
  @Get('all')
  async getAllAddresses(
    @Query('entity_type') entity_type?: string,
    @Query('entity_id') entity_id?: string,
    @Query('address_type') address_type?: string
  ): Promise<Address[]> {
    return this.addressService.findAll({ entity_type, entity_id, address_type });
  }

  @ApiOperation({ summary: 'Get a single address by ID' })
  @ApiResponse({ status: 200, description: 'Address found' })
  @Get(':id')
  async getAddressById(@Param('id') id: string): Promise<Address> {
    return this.addressService.findOneById(id);
  }

  @ApiOperation({ summary: 'Soft delete an address' })
  @ApiResponse({ status: 200, description: 'Address soft-deleted' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.ADDRESS_MANAGEMENT,
    resourceType: 'Address',
    description: 'Soft-deleted address',
  })
  @Delete('soft/:id')
  async softDeleteAddress(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.addressService.softDelete(id, req.user.userId);
  }

  @ApiOperation({ summary: 'Restore a soft-deleted address' })
  @ApiResponse({ status: 200, description: 'Address restored' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.ADDRESS_MANAGEMENT,
    resourceType: 'Address',
    description: 'Restored address',
  })
  @Put('restore/:id')
  async restoreAddress(@Param('id') id: string): Promise<void> {
    return this.addressService.restore(id);
  }

  @ApiOperation({ summary: 'Permanently delete an address (dangerous)' })
  @ApiResponse({ status: 200, description: 'Address permanently deleted' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.ADDRESS_MANAGEMENT,
    resourceType: 'Address',
    description: 'Hard-deleted address',
  })
  @Delete('hard/:id')
  async hardDeleteAddress(@Param('id') id: string): Promise<void> {
    return this.addressService.hardDelete(id);
  }
}
