import { <PERSON><PERSON>ty, <PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn, BeforeInsert } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { User } from "./user.entity";
import { Applications } from "./applications.entity";
import { IsUUID, Length, Matches, MaxLength, IsOptional } from "class-validator";

@Entity('devices')
export class Device {
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID('4', { message: 'Device ID must be a valid UUID.' })
  device_id: string;

  // Can be filled if the device is being added as part of an application
  @Column({ type: 'uuid', nullable: true })
  @IsUUID('4', { message: 'Application ID must be a valid UUID if provided.' })
  application_id?: string;

  // Manufacturer Information (embedded)
  @Column({ type: 'varchar', length: 100, nullable: false })
  @Length(1, 100, { message: 'Manufacturer name must be between 1 and 100 characters' })
  manufacturer_name: string;

  @Column({ type: 'text', nullable: true })
  manufacturer_address?: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  @Length(1, 100, { message: 'Manufacturer country must be between 1 and 100 characters' })
  manufacturer_country: string;

  // Equipment Information (embedded)
  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(1, 100, { message: 'Brand/Trade name must be between 1 and 100 characters' })
  brand_trade_name?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(1, 100, { message: 'Product type must be between 1 and 100 characters' })
  product_type_name?: string;

  @Column({ type: 'varchar', nullable: true })
  equipment_category?: string;

  // IMEI: 15 digits, globally unique identifier for mobile devices (optional)
  // Format: AA-BBBBBB-CCCCCC-D (TAC-Serial-Check)
  @Column({ type: 'varchar', length: 15, unique: true, nullable: true })
  @IsOptional()
  @Matches(/^\d{15}$/, { message: 'IMEI must be exactly 15 digits' })
  imei?: string;

  // Approval status - can be pending, approved, rejected, etc.
  @Column({ type: 'varchar', length: 50, default: 'pending' })
  approval_status: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(1, 100, { message: 'Device approval number must be between 1 and 100 characters' })
  device_approval_number?: string;

  @Column({ type: 'date', nullable: true })
  device_approval_date?: Date;

  @Column({ type: 'varchar', nullable: true })
  equipment_model?: string;

  @Column({ type: 'text', nullable: true })
  approval_notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided.' })
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsUUID('4', { message: 'Updated by must be a valid UUID if provided.' })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.device_id) {
      this.device_id = uuidv4();
    }
  }
}
