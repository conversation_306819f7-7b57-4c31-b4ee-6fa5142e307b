import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivityNote } from '../entities/activity-notes.entity';
import { ActivityNotesService } from '../services/activity-notes.service';
import { ActivityNotesController } from '../controllers/activity-notes.controller';
import { NotificationsModule } from '../notifications/notifications.module';
import { ApplicationsModule } from '../applications/applications.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ActivityNote]),
    forwardRef(() => NotificationsModule),
    forwardRef(() => ApplicationsModule)
  ],
  controllers: [ActivityNotesController],
  providers: [ActivityNotesService],
  exports: [ActivityNotesService],
})
export class ActivityNotesModule {}
