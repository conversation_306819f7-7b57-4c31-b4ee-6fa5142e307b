'use client';

import React, { useState, useRef } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { formatAmount, formatDate } from '@/utils/formatters';

interface Invoice {
  invoice_id: string;
  invoice_number: string;
  amount: number;
  due_date: string;
  status: string;
  description: string;
  created_at: string;
  balance?: number;
}

interface UploadProofOfPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: Invoice | null;
  onSuccess?: () => void;
}

const UploadProofOfPaymentModal: React.FC<UploadProofOfPaymentModalProps> = ({
  isOpen,
  onClose,
  invoice,
  onSuccess
}) => {
  const { showSuccess, showError } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [formData, setFormData] = useState({
    paymentMethod: '',
    transactionReference: '',
    paymentDate: '',
    amount: '',
    notes: '',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const paymentMethods = [
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'debit_card', label: 'Debit Card' },
    { value: 'cash', label: 'Cash' },
    { value: 'cheque', label: 'Cheque' },
    { value: 'other', label: 'Other' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateAndSetFile = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      showError('Please select a valid file type (JPEG, PNG, PDF)');
      return false;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return false;
    }

    setSelectedFile(file);
    return true;
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndSetFile(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async () => {
    // Validation
    if (!formData.paymentMethod) {
      showError('Please select a payment method');
      return;
    }
    if (!formData.transactionReference) {
      showError('Please enter a transaction reference');
      return;
    }
    if (!formData.paymentDate) {
      showError('Please select a payment date');
      return;
    }
    if (!formData.amount) {
      showError('Please enter the payment amount');
      return;
    }
    if (!selectedFile) {
      showError('Please upload proof of payment');
      return;
    }

    setUploading(true);
    try {
      // Create FormData for file upload
      const uploadData = new FormData();
      uploadData.append('invoice_id', invoice?.invoice_id || '');
      uploadData.append('payment_method', formData.paymentMethod);
      uploadData.append('transaction_reference', formData.transactionReference);
      uploadData.append('payment_date', formData.paymentDate);
      uploadData.append('amount', formData.amount);
      uploadData.append('notes', formData.notes);
      uploadData.append('proof_file', selectedFile);

      // TODO: Replace with actual API call
      // await paymentService.uploadProofOfPayment(uploadData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showSuccess('Proof of payment uploaded successfully');
      onSuccess?.();
      onClose();
      
      // Reset form
      setFormData({
        paymentMethod: '',
        transactionReference: '',
        paymentDate: '',
        amount: '',
        notes: '',
      });
      setSelectedFile(null);
    } catch (error) {
      console.error('Error uploading proof of payment:', error);
      showError('Failed to upload proof of payment. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen || !invoice) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl">
        {/* Header - Fixed */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                <i className="ri-upload-line text-white text-lg"></i>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Upload Proof of Payment
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Submit payment evidence for invoice verification
              </p>
            </div>
          </div>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={uploading}
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900">
          {/* Invoice Details */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
              <i className="ri-file-text-line mr-2"></i>
              Invoice Details
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700 dark:text-blue-300 font-medium">Invoice #:</span>
                <p className="text-blue-600 dark:text-blue-400">{invoice.invoice_number}</p>
              </div>
              <div>
                <span className="text-blue-700 dark:text-blue-300 font-medium">Amount:</span>
                <p className="text-blue-600 dark:text-blue-400 font-semibold">{formatAmount(invoice.amount)}</p>
              </div>
              <div>
                <span className="text-blue-700 dark:text-blue-300 font-medium">Due Date:</span>
                <p className="text-blue-600 dark:text-blue-400">{formatDate(invoice.due_date)}</p>
              </div>
              <div>
                <span className="text-blue-700 dark:text-blue-300 font-medium">Status:</span>
                <p className="text-blue-600 dark:text-blue-400 capitalize">{invoice.status}</p>
              </div>
            </div>
            <div className="mt-3">
              <span className="text-blue-700 dark:text-blue-300 font-medium">Description:</span>
              <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">{invoice.description}</p>
            </div>
            {invoice.balance !== undefined && (
              <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
                <span className="text-yellow-700 dark:text-yellow-300 font-medium text-sm">Outstanding Balance:</span>
                <p className="text-yellow-600 dark:text-yellow-400 font-semibold">{formatAmount(invoice.balance)}</p>
              </div>
            )}
          </div>

          {/* Payment Information Form */}
          <div className="space-y-4">
            {/* Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Method *
              </label>
              <select
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                disabled={uploading}
              >
                <option value="">Select payment method</option>
                {paymentMethods.map(method => (
                  <option key={method.value} value={method.value}>
                    {method.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Transaction Reference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Transaction Reference *
              </label>
              <input
                type="text"
                name="transactionReference"
                value={formData.transactionReference}
                onChange={handleInputChange}
                placeholder="Enter transaction reference number"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                disabled={uploading}
              />
            </div>

            {/* Payment Date and Amount */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Payment Date *
                </label>
                <input
                  type="date"
                  name="paymentDate"
                  value={formData.paymentDate}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  disabled={uploading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Amount Paid *
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  disabled={uploading}
                />
              </div>
            </div>

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Proof of Payment *
              </label>
              <div
                className={`border-2 border-dashed rounded-lg p-4 transition-colors duration-200 ${
                  dragActive
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {selectedFile ? (
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="flex items-center space-x-3">
                      <i className="ri-file-line text-green-600 dark:text-green-400 text-xl"></i>
                      <div>
                        <p className="text-sm font-medium text-green-900 dark:text-green-100">{selectedFile.name}</p>
                        <p className="text-xs text-green-600 dark:text-green-400">{formatFileSize(selectedFile.size)}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={handleRemoveFile}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      disabled={uploading}
                    >
                      <i className="ri-close-line text-lg"></i>
                    </button>
                  </div>
                ) : (
                  <div className="text-center">
                    <i className={`ri-upload-cloud-line text-4xl mb-2 transition-colors duration-200 ${
                      dragActive
                        ? 'text-blue-500 dark:text-blue-400'
                        : 'text-gray-400 dark:text-gray-500'
                    }`}></i>
                    <p className={`text-sm mb-2 transition-colors duration-200 ${
                      dragActive
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {dragActive ? 'Drop your file here' : 'Click to upload or drag and drop'}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      PNG, JPG, PDF up to 5MB
                    </p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".jpg,.jpeg,.png,.pdf"
                      onChange={handleFileSelect}
                      className="hidden"
                      disabled={uploading}
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="mt-2 px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      disabled={uploading}
                    >
                      Choose File
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Additional Notes
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                placeholder="Any additional information about this payment..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                disabled={uploading}
              />
            </div>
          </div>
        </div>

        {/* Footer - Fixed */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={uploading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={uploading || !formData.paymentMethod || !formData.transactionReference || !formData.paymentDate || !formData.amount || !selectedFile}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Uploading...
              </>
            ) : (
              <>
                <i className="ri-upload-line mr-2"></i>
                Upload Proof
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadProofOfPaymentModal;
