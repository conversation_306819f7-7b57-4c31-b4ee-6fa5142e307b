"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintStatusHistory = exports.ConsumerAffairsComplaint = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const class_validator_1 = require("class-validator");
const user_entity_1 = require("./user.entity");
const consumer_affairs_constants_1 = require("../consumer-affairs/consumer-affairs-constants");
const documents_entity_1 = require("./documents.entity");
let ConsumerAffairsComplaint = class ConsumerAffairsComplaint {
    generateId() {
        if (!this.complaint_id) {
            this.complaint_id = (0, uuid_1.v4)();
        }
        if (!this.complaint_number) {
            const year = new Date().getFullYear();
            const randomNum = Math.floor(Math.random() * 999) + 1;
            this.complaint_number = `COMP-${year}-${randomNum.toString().padStart(3, '0')}`;
        }
    }
};
exports.ConsumerAffairsComplaint = ConsumerAffairsComplaint;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complaint_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complaint_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36 }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complainant_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complainee_reg_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
    }),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, default: consumer_affairs_constants_1.ComplaintStatus.SUBMITTED }),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "assigned_to", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "resolution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "internal_notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "resolved_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'complainant_id' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "complainant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assigned_to' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "assignee", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => documents_entity_1.Documents, (attachment) => attachment.entity_id),
    __metadata("design:type", Array)
], ConsumerAffairsComplaint.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ConsumerAffairsComplaintStatusHistory, (history) => history.complaint),
    __metadata("design:type", Array)
], ConsumerAffairsComplaint.prototype, "status_history", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConsumerAffairsComplaint.prototype, "generateId", null);
exports.ConsumerAffairsComplaint = ConsumerAffairsComplaint = __decorate([
    (0, typeorm_1.Entity)('consumer_affairs_complaints')
], ConsumerAffairsComplaint);
let ConsumerAffairsComplaintStatusHistory = class ConsumerAffairsComplaintStatusHistory {
    generateId() {
        if (!this.history_id) {
            this.history_id = (0, uuid_1.v4)();
        }
    }
};
exports.ConsumerAffairsComplaintStatusHistory = ConsumerAffairsComplaintStatusHistory;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "history_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36 }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "complaint_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, default: consumer_affairs_constants_1.ComplaintStatus.SUBMITTED }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaintStatusHistory.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36 }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ConsumerAffairsComplaint, (complaint) => complaint.status_history),
    (0, typeorm_1.JoinColumn)({ name: 'complaint_id' }),
    __metadata("design:type", ConsumerAffairsComplaint)
], ConsumerAffairsComplaintStatusHistory.prototype, "complaint", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaintStatusHistory.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConsumerAffairsComplaintStatusHistory.prototype, "generateId", null);
exports.ConsumerAffairsComplaintStatusHistory = ConsumerAffairsComplaintStatusHistory = __decorate([
    (0, typeorm_1.Entity)('consumer_affairs_complaint_status_history')
], ConsumerAffairsComplaintStatusHistory);
//# sourceMappingURL=consumer-affairs-complaint.entity.js.map