import {
  IsBoolean,
  <PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>tring,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateLegalHistoryDto {
  @IsUUID('4', { message: 'Application ID not valid!' })
  @IsNotEmpty({ message: 'Application ID is required' })
  application_id: string;

  @IsBoolean({ message: 'Criminal history must be a boolean value' })
  criminal_history: boolean;

  @IsString({ message: 'Criminal details contains invalid characters!' })
  @MaxLength(2000, { message: 'Criminal details must not exceed 2000 characters' })
  @IsOptional()
  criminal_details?: string;

  @IsBoolean({ message: 'Bankruptcy history must be a boolean value' })
  bankruptcy_history: boolean;

  @IsString({ message: 'Bankruptcy details contains invalid characters!' })
  @MaxLength(2000, { message: 'Bankruptcy details must not exceed 2000 characters' })
  @IsOptional()
  bankruptcy_details?: string;

  @IsBoolean({ message: 'Regulatory actions must be a boolean value' })
  regulatory_actions: boolean;

  @IsString({ message: 'Regulatory details contains invalid characters!' })
  @MaxLength(2000, { message: 'Regulatory details must not exceed 2000 characters' })
  @IsOptional()
  regulatory_details?: string;

  @IsBoolean({ message: 'Litigation history must be a boolean value' })
  litigation_history: boolean;

  @IsString({ message: 'Litigation details contains invalid characters!' })
  @MaxLength(2000, { message: 'Litigation details must not exceed 2000 characters' })
  @IsOptional()
  litigation_details?: string;

  @IsString({ message: 'Compliance record contains invalid characters!' })
  @MaxLength(2000, { message: 'Compliance record must not exceed 2000 characters' })
  @IsOptional()
  compliance_record?: string;

  @IsString({ message: 'Previous licenses contains invalid characters!' })
  @MaxLength(2000, { message: 'Previous licenses must not exceed 2000 characters' })
  @IsOptional()
  previous_licenses?: string;

  @IsBoolean({ message: 'Declaration accepted must be a boolean value' })
  @IsNotEmpty({ message: 'Declaration must be accepted' })
  declaration_accepted: boolean;
}
