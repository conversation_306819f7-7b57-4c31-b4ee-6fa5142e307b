"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const data_breachs_service_1 = require("./data-breachs.service");
const data_breach_report_dto_1 = require("../dto/data-breach/data-breach-report.dto");
const swagger_1 = require("@nestjs/swagger");
const nestjs_paginate_1 = require("nestjs-paginate");
let DataBreachReportController = class DataBreachReportController {
    constructor(reportService) {
        this.reportService = reportService;
    }
    async create(createDto, req) {
        const report = await this.reportService.create(createDto, req.user.user_id);
        return report;
    }
    async findAll(query, req) {
        return this.reportService.findAll(query, req.user.user_id, req.user.isStaff || false);
    }
    async findOne(id, req) {
        return this.reportService.findOne(id, req.user.user_id, req.user.isStaff || false);
    }
    async update(id, updateDto, req) {
        const report = await this.reportService.update(id, updateDto, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Data breach report updated successfully',
            data: report,
        };
    }
    async delete(id, req) {
        await this.reportService.delete(id, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Data breach report deleted successfully',
        };
    }
    async updateStatus(id, statusDto, req) {
        const report = await this.reportService.updateStatus(id, statusDto, req.user.user_id);
        return {
            success: true,
            message: 'Report status updated successfully',
            data: report,
        };
    }
    async assignReport(id, assignedTo, req) {
        const report = await this.reportService.update(id, { assigned_to: assignedTo }, req.user.user_id, true);
        return {
            success: true,
            message: 'Report assigned successfully',
            data: report,
        };
    }
    async getStatsSummary(req) {
        return {
            success: true,
            message: 'Statistics retrieved successfully',
            data: {
                total: 0,
                by_status: {},
                by_category: {},
                by_severity: {},
                by_priority: {},
            },
        };
    }
    async exportToCsv(filterDto, req) {
        return {
            success: true,
            message: 'Export functionality not yet implemented',
        };
    }
    async getUrgentAlerts(req) {
        const urgentReports = await this.reportService.findAll({
            path: '/data-breach-reports/urgent/alerts',
            filter: {
                severity: 'critical',
                status: 'submitted'
            },
            limit: 10
        }, req.user.user_id, true);
        return {
            success: true,
            message: 'Urgent data breach alerts retrieved successfully',
            data: urgentReports.data,
        };
    }
};
exports.DataBreachReportController = DataBreachReportController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new data breach report' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Report created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_breach_report_dto_1.CreateDataBreachReportDto, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all data breach reports with pagination' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Reports retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific data breach report by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Report retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Report not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, data_breach_report_dto_1.UpdateDataBreachReportDto, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "delete", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, data_breach_report_dto_1.UpdateDataBreachReportStatusDto, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)(':id/assign'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('assigned_to', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "assignReport", null);
__decorate([
    (0, common_1.Get)('stats/summary'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "getStatsSummary", null);
__decorate([
    (0, common_1.Get)('export/csv'),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_breach_report_dto_1.DataBreachReportFilterDto, Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "exportToCsv", null);
__decorate([
    (0, common_1.Get)('urgent/alerts'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DataBreachReportController.prototype, "getUrgentAlerts", null);
exports.DataBreachReportController = DataBreachReportController = __decorate([
    (0, common_1.Controller)('data-breach-reports'),
    (0, swagger_1.ApiTags)('Data Breach Reports'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [data_breachs_service_1.DataBreachReportService])
], DataBreachReportController);
//# sourceMappingURL=data-breachs.controller.js.map