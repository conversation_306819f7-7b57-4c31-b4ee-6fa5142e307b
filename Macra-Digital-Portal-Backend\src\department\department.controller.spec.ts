import { Test, TestingModule } from '@nestjs/testing';
import { DepartmentController } from './department.controller';
import { DepartmentService } from './department.service';
import { Department } from '../entities/department.entity';
import { NotFoundException } from '@nestjs/common';

const mockDepartment = {
  department_id: 'uuid-1234',
  code: 'HR',
  name: 'Human Resources',
  description: 'Handles HR',
  email: '<EMAIL>',
};

describe('DepartmentController', () => {
  let controller: DepartmentController;
  let service: DepartmentService;

  const mockService = {
    create: jest.fn().mockResolvedValue(mockDepartment),
    findAll: jest.fn().mockResolvedValue([mockDepartment]),
    findOne: jest.fn().mockResolvedValue(mockDepartment),
    update: jest.fn().mockResolvedValue({ ...mockDepartment, name: 'HR Updated' }),
    remove: jest.fn().mockResolvedValue(undefined),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DepartmentController],
      providers: [
        {
          provide: DepartmentService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<DepartmentController>(DepartmentController);
    service = module.get<DepartmentService>(DepartmentService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a department', async () => {
      const dto = {
        code: 'HR',
        name: 'Human Resources',
        description: 'Handles HR',
        email: '<EMAIL>',
      };
      expect(await controller.create(dto)).toEqual(mockDepartment);
      expect(mockService.create).toHaveBeenCalledWith(dto);
    });
  });

  describe('findAll', () => {
    it('should return array of departments', async () => {
      expect(await controller.findAll()).toEqual([mockDepartment]);
      expect(mockService.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a department', async () => {
      expect(await controller.findOne('uuid-1234')).toEqual(mockDepartment);
      expect(mockService.findOne).toHaveBeenCalledWith('uuid-1234');
    });

    it('should throw NotFoundException if not found', async () => {
      mockService.findOne.mockRejectedValueOnce(new NotFoundException());
      await expect(controller.findOne('nonexistent-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update and return the department', async () => {
      const updateDto = { name: 'HR Updated' };
      expect(await controller.update('uuid-1234', updateDto)).toEqual({
        ...mockDepartment,
        name: 'HR Updated',
      });
      expect(mockService.update).toHaveBeenCalledWith('uuid-1234', updateDto);
    });
  });

  describe('remove', () => {
    it('should soft delete the department', async () => {
      await expect(controller.remove('uuid-1234')).resolves.toBeUndefined();
      expect(mockService.remove).toHaveBeenCalledWith('uuid-1234');
    });
  });
});
