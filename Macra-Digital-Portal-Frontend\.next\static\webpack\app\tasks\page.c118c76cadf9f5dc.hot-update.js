"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/page",{

/***/ "(app-pages-browser)/./src/app/tasks/page.tsx":
/*!********************************!*\
  !*** ./src/app/tasks/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/common/DataTable */ \"(app-pages-browser)/./src/components/common/DataTable.tsx\");\n/* harmony import */ var _components_common_Select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/common/Select */ \"(app-pages-browser)/./src/components/common/Select.tsx\");\n/* harmony import */ var _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/useTaskNavigation */ \"(app-pages-browser)/./src/hooks/useTaskNavigation.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/tasks/TaskModal */ \"(app-pages-browser)/./src/components/tasks/TaskModal.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/ConfirmationModal */ \"(app-pages-browser)/./src/components/common/ConfirmationModal.tsx\");\n/* harmony import */ var _components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/tasks/ReassignTaskModal */ \"(app-pages-browser)/./src/components/tasks/ReassignTaskModal.tsx\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/formatters */ \"(app-pages-browser)/./src/utils/formatters.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TasksPage() {\n    _s();\n    const { showSuccess } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTask, setEditingTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tasksData, setTasksData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToDelete, setTaskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReassignModal, setShowReassignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToReassign, setTaskToReassign] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING // Default to showing pending tasks\n    });\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    // Add task navigation hook\n    const { openTaskView, isLoading: isNavigating } = (0,_hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation)();\n    const handleCreateTask = ()=>{\n        setEditingTask(null);\n        setIsModalOpen(true);\n    };\n    const canViewTask = (task)=>{\n        console.log('canViewTask:', task, user);\n        if (task.assigned_at && task.assigned_to == (user === null || user === void 0 ? void 0 : user.user_id)) return true;\n        return false;\n    };\n    const handleModalClose = ()=>{\n        setIsModalOpen(false);\n        setEditingTask(null);\n    };\n    const handleTaskSaved = ()=>{\n        if (editingTask) {\n            showSuccess('Task updated successfully!');\n        } else {\n            showSuccess('Task created successfully!');\n        }\n        // Reload tasks to show updated data\n        loadTasks(currentQuery);\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === '' ? undefined : value\n            }));\n    };\n    const handleDeleteTask = (task)=>{\n        setTaskToDelete(task);\n        setShowDeleteModal(true);\n    };\n    const handleCancelDelete = ()=>{\n        setShowDeleteModal(false);\n        setTaskToDelete(null);\n    };\n    const handleReassignTask = (task)=>{\n        setTaskToReassign(task);\n        setShowReassignModal(true);\n    };\n    const handleCancelReassign = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n    };\n    const handleReassignSuccess = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n        // Reload tasks to show updated assignment\n        loadTasks(currentQuery);\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!taskToDelete) return;\n        setIsDeleting(true);\n        try {\n            await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.deleteTask(taskToDelete.task_id);\n            setShowDeleteModal(false);\n            setTaskToDelete(null);\n            // Reload tasks\n            loadTasks(currentQuery);\n        } catch (err) {\n            console.error('Error deleting task:', err);\n            setError('Failed to delete task');\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const loadTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[loadTasks]\": async (query)=>{\n            try {\n                setLoading(true);\n                setError(null);\n                setCurrentQuery(query);\n                // Combine query with current filters\n                const params = {\n                    ...query,\n                    ...filters\n                };\n                const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getTasks(params);\n                setTasksData(response);\n            } catch (err) {\n                console.error('Error loading tasks:', err);\n                let errorMessage = 'Failed to load tasks. Please try again.';\n                if (err && typeof err === 'object') {\n                    if ('response' in err && err.response && typeof err.response === 'object') {\n                        if ('status' in err.response) {\n                            const status = err.response.status;\n                            if (status === 401) {\n                                errorMessage = 'Authentication required. Please log in again.';\n                            } else if (status === 403) {\n                                errorMessage = 'You do not have permission to view tasks.';\n                            } else if (status === 500) {\n                                errorMessage = 'Server error. Please try again later.';\n                            } else if ('data' in err.response && err.response.data && typeof err.response.data === 'object' && 'message' in err.response.data && typeof err.response.data.message === 'string') {\n                                errorMessage = err.response.data.message;\n                            }\n                        }\n                    } else if ('message' in err && typeof err.message === 'string') {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setTasksData({\n                    data: [],\n                    meta: {\n                        itemsPerPage: query.limit || 10,\n                        totalItems: 0,\n                        currentPage: query.page || 1,\n                        totalPages: 0,\n                        sortBy: [],\n                        searchBy: [],\n                        search: '',\n                        select: []\n                    },\n                    links: {\n                        current: ''\n                    }\n                });\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"TasksPage.useCallback[loadTasks]\"], [\n        filters\n    ]);\n    // Load tasks when component mounts or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            console.log('🔄 Filters changed, loading tasks:', filters);\n            loadTasks({\n                page: 1,\n                limit: 10\n            });\n        }\n    }[\"TasksPage.useEffect\"], [\n        filters,\n        loadTasks\n    ]);\n    // Load users on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"TasksPage.useEffect\"], []);\n    // Handler for DataTable query changes (pagination, search, sorting)\n    const handleQueryChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[handleQueryChange]\": (query)=>{\n            loadTasks(query);\n        }\n    }[\"TasksPage.useCallback[handleQueryChange]\"], [\n        loadTasks\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            const usersResponse = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getOfficers();\n            setUsers(usersResponse.data);\n        } catch (err) {\n            console.error('Error loading users:', err);\n            setUsers([]);\n        }\n    };\n    const taskColumns = [\n        {\n            key: 'task_number',\n            label: 'Task Number',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: canViewTask(task) ? \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\" : \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                    onClick: ()=>canViewTask(task) ? openTaskView(task.task_id) : null,\n                    title: \"Click to view task\",\n                    children: String(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: canViewTask(task) ? \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\" : \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            onClick: ()=>canViewTask(task) ? openTaskView(task.task_id) : null,\n                            title: \"Click to view task\",\n                            children: String(value)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'task_type',\n            label: 'Type',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100 capitalize\",\n                    children: String(value).replace('_', ' ')\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'status',\n            label: 'Status',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat((0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.getStatusColor)(value)),\n                    children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.formatStatus)(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'assignee',\n            label: 'Assigned To',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100\",\n                    children: task.assignee ? \"\".concat(task.assignee.first_name, \" \").concat(task.assignee.last_name) : 'Unassigned'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'due_date',\n            label: 'Due Date',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                    children: value ? new Date(String(value)).toLocaleDateString() : 'No due date'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'actions',\n            label: 'Actions',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        canViewTask(task) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>openTaskView(task.task_id),\n                            disabled: isNavigating,\n                            className: \"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50\",\n                            title: \"Open in new tab\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-external-link-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                \" View\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this) : null,\n                        task.status !== _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleReassignTask(task),\n                            className: \"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900\",\n                            title: \"Reassign task\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-user-shared-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this),\n                                task.assignee ? 'Reassign' : 'Assign'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 italic flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-check-circle-line mr-1 text-green-600 dark:text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                \"Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: \"Task Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Manage and track tasks across your organization\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: handleCreateTask,\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-add-line w-5 h-5 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this),\n                        \"Add Task\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Status\",\n                                value: filters.status || '',\n                                onChange: (value)=>handleFilterChange('status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Statuses'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                                        label: 'Pending'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED,\n                                        label: 'Completed'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.CANCELLED,\n                                        label: 'Cancelled'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.ON_HOLD,\n                                        label: 'On Hold'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Priority\",\n                                value: filters.priority || '',\n                                onChange: (value)=>handleFilterChange('priority', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Priorities'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.LOW,\n                                        label: 'Low'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.MEDIUM,\n                                        label: 'Medium'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                                        label: 'High'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.URGENT,\n                                        label: 'Urgent'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Assignment Status\",\n                                value: filters.assigned_to || '',\n                                onChange: (value)=>handleFilterChange('assignment_status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Tasks'\n                                    },\n                                    {\n                                        value: 'assigned',\n                                        label: 'Assigned'\n                                    },\n                                    {\n                                        value: 'unassigned',\n                                        label: 'Unassigned'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                columns: taskColumns,\n                data: tasksData,\n                loading: loading,\n                onQueryChange: handleQueryChange,\n                searchPlaceholder: \"Search tasks by title, description, or task number...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDeleteModal,\n                onClose: handleCancelDelete,\n                onConfirm: handleConfirmDelete,\n                title: \"Delete Task\",\n                message: taskToDelete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: [\n                                \"Are you sure you want to delete task \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: taskToDelete.task_number\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 54\n                                }, void 0),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"This action cannot be undone. All data associated with this task will be permanently removed.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 13\n                }, void 0) : 'Are you sure you want to delete this task?',\n                confirmText: \"Yes, Delete Task\",\n                cancelText: \"Cancel\",\n                confirmVariant: \"danger\",\n                loading: isDeleting\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showReassignModal,\n                onClose: handleCancelReassign,\n                task: taskToReassign,\n                onReassignSuccess: handleReassignSuccess\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: handleModalClose,\n                onSave: handleTaskSaved,\n                task: editingTask\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, this);\n}\n_s(TasksPage, \"BnF9w3bz05KinPghCnyHZ4vWRu8=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth,\n        _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation\n    ];\n});\n_c = TasksPage;\nvar _c;\n$RefreshReg$(_c, \"TasksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/page.tsx\n"));

/***/ })

});