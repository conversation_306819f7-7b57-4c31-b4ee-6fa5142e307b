'use client';

import React from 'react';
import DataDisplayCard from '@/components/evaluation/DataDisplayCard';
import { DataBreachReport } from '@/types/data-breach';

interface DataBreachCardProps {
  report: DataBreachReport;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const DataBreachCard: React.FC<DataBreachCardProps> = ({
  report,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'text-blue-600';
      case 'under_review': return 'text-yellow-600';
      case 'investigating': return 'text-orange-600';
      case 'resolved': return 'text-green-600';
      case 'closed': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatStatus = (status: string) => {
    return status?.replace('_', ' ').toUpperCase() || 'UNKNOWN';
  };

  const formatSeverity = (severity: string) => {
    return severity?.toUpperCase() || 'UNKNOWN';
  };

  const formatPriority = (priority: string) => {
    return priority?.toUpperCase() || 'MEDIUM';
  };

  const reportFields = [
    {
      label: 'Report Number',
      value: report.report_number,
      icon: 'ri-file-text-line',
      type: 'text' as const
    },
    {
      label: 'Title',
      value: report.title,
      icon: 'ri-article-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Status',
      value: formatStatus(report.status),
      icon: 'ri-flag-line',
      type: 'text' as const
    },
    {
      label: 'Severity',
      value: formatSeverity(report.severity),
      icon: 'ri-alert-line',
      type: 'text' as const
    },
    {
      label: 'Priority',
      value: formatPriority(report.priority),
      icon: 'ri-star-line',
      type: 'text' as const
    },
    {
      label: 'Category',
      value: report.category,
      icon: 'ri-folder-line',
      type: 'text' as const
    },
    {
      label: 'Incident Date',
      value: report.incident_date,
      icon: 'ri-calendar-line',
      type: 'date' as const
    },
    {
      label: 'Organization Involved',
      value: report.organization_involved,
      icon: 'ri-building-line',
      type: 'text' as const
    },
    {
      label: 'Reporter Name',
      value: report.reporter ? `${report.reporter.first_name} ${report.reporter.last_name}` : 'Unknown',
      icon: 'ri-user-line',
      type: 'text' as const
    },
    {
      label: 'Reporter Email',
      value: report.reporter?.email,
      icon: 'ri-mail-line',
      type: 'email' as const
    },
    {
      label: 'Assigned To',
      value: report.assigned_to || 'Unassigned',
      icon: 'ri-user-star-line',
      type: 'text' as const
    },
    {
      label: 'Affected Data Types',
      value: report.affected_data_types,
      icon: 'ri-database-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Contact Attempts',
      value: report.contact_attempts,
      icon: 'ri-phone-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Description',
      value: report.description,
      icon: 'ri-file-text-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Resolution',
      value: report.resolution,
      icon: 'ri-check-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Internal Notes',
      value: report.internal_notes,
      icon: 'ri-sticky-note-line',
      type: 'text' as const,
      fullWidth: true
    },
    {
      label: 'Created Date',
      value: report.created_at,
      icon: 'ri-calendar-line',
      type: 'date' as const
    },
    {
      label: 'Last Updated',
      value: report.updated_at,
      icon: 'ri-calendar-line',
      type: 'date' as const
    },
    {
      label: 'Resolved Date',
      value: report.resolved_at,
      icon: 'ri-calendar-check-line',
      type: 'date' as const
    }
  ];

  return (
    <DataDisplayCard
      title="Data Breach Report Details"
      icon="ri-shield-cross-line"
      fields={reportFields}
      className={className}
      showEmptyFields={showEmptyFields}
      defaultCollapsed={defaultCollapsed}
      creatorEmail={report.reporter?.email}
      creatorName={report.reporter ? `${report.reporter.first_name} ${report.reporter.last_name}` : undefined}
      entityId={report.report_id}
      entityType="data-breach"
      showEmailButton={true}
    />
  );
};

export default DataBreachCard;
