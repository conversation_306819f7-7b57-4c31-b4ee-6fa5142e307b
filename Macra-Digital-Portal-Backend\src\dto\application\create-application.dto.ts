import { IsString, IsEnum, IsOptional, IsUUID, IsInt, IsDateString, Min, Max, Matches, IsNotEmpty, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ApplicationStatus } from '../../entities/applications.entity';

export class CreateApplicationDto {
  @ApiPropertyOptional({
    description: 'Unique application number (auto-generated if not provided)',
    example: 'APP-2024-000001',
  })
  @IsOptional()
  @IsString()
  application_number?: string;

  @ApiProperty({
    description: 'UUID of the applicant',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  applicant_id: string;

  @ApiProperty({
    description: 'UUID of the license category',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  license_category_id: string;

  @ApiPropertyOptional({
    description: 'Application status',
    enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'],
    default: 'draft',
    example: 'draft',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    // If no status provided, default to draft
    if (!value || value === '') {
      return 'draft';
    }
    // Ensure the value is a valid status value
    const validStatuses = ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'];
    return validStatuses.includes(value) ? value : 'draft';
  })
  status?: string;

  @ApiPropertyOptional({
    description: 'Current step in the application process (1)',
    minimum: 1,
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  current_step?: number;

  @ApiPropertyOptional({
    description: 'Application progress percentage (0-100)',
    minimum: 0,
    maximum: 100,
    example: 25,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  progress_percentage?: number;

  @ApiPropertyOptional({
    description: 'Date when the application was submitted',
    example: '2023-01-01T12:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  submitted_at?: Date;
}
