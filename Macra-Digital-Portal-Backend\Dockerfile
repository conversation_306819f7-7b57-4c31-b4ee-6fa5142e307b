#  ----- Stage 1: Dependencies stage
FROM node:22-alpine3.20 AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci

#  ----- Stage 2: Build stage
FROM node:22-alpine3.20 AS builder
WORKDIR /app
# ENV NODE_ENV=production
ENV NEXT_PUBLIC_STACK_PROJECT_ID = "3d0c004b-1aea-4342-ac42-529aecf65c74"
ENV NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY = "pck_5rnye6cbs5jyt35sbp4hvqp815k4vz10grzq066mec1dr"

# Install rsync
RUN apk add --no-cache rsync

# Copy deps from the previous stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source and build
COPY . .
RUN npm run build:linux
RUN npm prune --production

#  ----- Stage 3: Runtime stage
FROM node:22-alpine3.20 AS runner
WORKDIR /app

# Install runtime tools
RUN apk add --no-cache \
    dumb-init \
    tzdata \
    postgresql-client \
    curl

# Copy artifacts
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist         ./dist
COPY --from=builder /app/package.json ./
COPY --from=builder /app/nest-cli.json ./
COPY --from=builder /app/tsconfig.json ./
COPY --from=builder /app/tsconfig.build.json ./

# ENV NODE_ENV=production
# EXPOSE 3001

# Entrypoint delegates to shell
ENTRYPOINT ["dumb-init","sh","-c"]

# Default: start the NestJS API
CMD ["node dist/main.js"]
