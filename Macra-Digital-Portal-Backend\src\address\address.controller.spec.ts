import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from '../users/users.controller';
import { UsersService } from '../users/users.service';
import { User, UserStatus } from '../entities/user.entity';

describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  // A full realistic mock User entity matching your User entity structure
  const mockUser: User = {
    user_id: 'user-1',
    email: '<EMAIL>',
    password: 'hashedpassword',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    middle_name: undefined,
    phone: '1234567890',
    status: UserStatus.ACTIVE,
    profile_image: undefined,
    two_factor_next_verification: undefined,
    two_factor_code: undefined,
    two_factor_enabled: false,
    two_factor_temp: undefined,
    email_verified_at: undefined,
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: undefined,
    created_by: undefined,
    updated_by: undefined,
    last_login: undefined,
    roles: [],
    creator: undefined,
    updater: undefined,
    identifications: [],
    employee_records: [],
    generateId: () => 'user-1', // this won't actually run in tests but here for completeness
  };

  // Mock UsersService with jest.fn() implementations
  const mockUsersService = {
    createUser: jest.fn(),
    updateUser: jest.fn(),
    findUserById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create and return a user', async () => {
      mockUsersService.createUser.mockResolvedValue(mockUser);

      const createUserDto = {
        email: mockUser.email,
        password: 'password123',
        first_name: mockUser.first_name,
        last_name: mockUser.last_name,
        phone: mockUser.phone,
      };

      const result = await controller.create(createUserDto);

      expect(service.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(mockUser);
    });
  });

  describe('updateUser', () => {
    it('should update and return the user', async () => {
      const updateUserDto = {
        first_name: 'Jane',
        last_name: 'Smith',
      };

      const updatedUser = { ...mockUser, ...updateUserDto };

      mockUsersService.updateUser.mockResolvedValue(updatedUser);

      const userId = mockUser.user_id;

      const result = await controller.update(userId, updateUserDto);

      expect(service.update).toHaveBeenCalledWith(userId, updateUserDto);
      expect(result).toEqual(updatedUser);
    });
  });

  describe('findUserById', () => {
    it('should return a user by ID', async () => {
      mockUsersService.findUserById.mockResolvedValue(mockUser);

      const userId = mockUser.user_id;

      const result = await controller.findOne(userId);

      expect(service.findById).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockUser);
    });
  });
});
