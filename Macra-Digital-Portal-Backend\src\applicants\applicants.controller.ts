import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApplicantsService } from './applicants.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateApplicantDto } from '../dto/applicant/create-applicant.dto';
import { UpdateApplicantDto } from '../dto/applicant/update-applicant.dto';
import { Applicants } from '../entities/applicant.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Applicants')
@Controller('applicants')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ApplicantsController {
  constructor(private readonly applicantsService: ApplicantsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new applicant' })
  @ApiResponse({
    status: 201,
    description: 'Applicant created successfully',
    type: Applicants,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Created new applicant',
  })
  async create(
    @Body() createApplicantDto: CreateApplicantDto,
    @Request() req: any,
  ): Promise<Applicants> {
    return this.applicantsService.create(createApplicantDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all applicants with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Applicants retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Viewed applicants list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<Applicants>> {
    const result = await this.applicantsService.findAll(query);
    return PaginationTransformer.transform<Applicants>(result);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search applicants' })
  @ApiQuery({ name: 'q', description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
    type: [Applicants],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Searched applicants',
  })
  async search(@Query('q') searchTerm: string): Promise<Applicants[]> {
    return this.applicantsService.search(searchTerm);
  }

  @Get('by-business-registration/:businessRegistrationNumber')
  @ApiOperation({ summary: 'Get applicant by business registration number' })
  @ApiParam({ name: 'businessRegistrationNumber', description: 'Business registration number' })
  @ApiResponse({
    status: 200,
    description: 'Applicant retrieved successfully',
    type: Applicants,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Viewed applicant by business registration number',
  })
  async findByBusinessRegistrationNumber(
    @Param('businessRegistrationNumber') businessRegistrationNumber: string,
  ): Promise<Applicants | null> {
    return this.applicantsService.findByBusinessRegistrationNumber(businessRegistrationNumber);
  }

  @Get('by-tpin/:tpin')
  @ApiOperation({ summary: 'Get applicant by TPIN' })
  @ApiParam({ name: 'tpin', description: 'Tax Payer Identification Number' })
  @ApiResponse({
    status: 200,
    description: 'Applicant retrieved successfully',
    type: Applicants,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Viewed applicant by TPIN',
  })
  async findByTpin(@Param('tpin') tpin: string): Promise<Applicants | null> {
    return this.applicantsService.findByTpin(tpin);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get applicant by ID' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applicant retrieved successfully',
    type: Applicants,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Viewed applicant details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Applicants> {
    return this.applicantsService.findOne(id);
  }

  @Get(':id/with-related-data')
  @ApiOperation({ summary: 'Get applicant with all related data (addresses and contacts)' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applicant with related data retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Viewed applicant with related data',
  })
  async findOneWithRelatedData(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    return this.applicantsService.findOneWithRelatedData(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update applicant' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applicant updated successfully',
    type: Applicants,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Updated applicant',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateApplicantDto: UpdateApplicantDto,
    @Request() req: any,
  ): Promise<Applicants> {
    return this.applicantsService.update(id, updateApplicantDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete applicant' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applicant deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Applicant',
    description: 'Deleted applicant',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.applicantsService.remove(id);
    return { message: 'Applicant deleted successfully' };
  }

  @Post(':id/address')
  @ApiOperation({ summary: 'Create address for applicant' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 201,
    description: 'Address created successfully for applicant',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Address',
    description: 'Created address for applicant',
  })
  async createAddress(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addressData: any,
    @Request() req: any,
  ): Promise<any> {
    return this.applicantsService.createAddressForApplicant(id, addressData, req.user.userId);
  }

  @Post(':id/contact')
  @ApiOperation({ summary: 'Create contact person for applicant' })
  @ApiParam({ name: 'id', description: 'Applicant UUID' })
  @ApiResponse({
    status: 201,
    description: 'Contact person created successfully for applicant',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'ContactPerson',
    description: 'Created contact person for applicant',
  })
  async createContact(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() contactData: any,
    @Request() req: any,
  ): Promise<any> {
    return this.applicantsService.createContactPersonForApplicant(id, contactData, req.user.userId);
  }
}
