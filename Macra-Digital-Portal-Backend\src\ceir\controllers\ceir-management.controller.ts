import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Audit } from '../../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../../entities/audit-trail.entity';

// Import services
import { CeirCertificationBodiesService } from '../services/ceir-certification-bodies.service';
import { CeirEquipmentCategoriesService } from '../services/ceir-equipment-categories.service';
import { CeirTechnicalStandardsService } from '../services/ceir-technical-standards.service';

// Import DTOs
import { CreateCeirCertificationBodyDto, UpdateCeirCertificationBodyDto } from '../dto/ceir-certification-bodies';
import { CreateCeirEquipmentCategoryDto, UpdateCeirEquipmentCategoryDto } from '../dto/ceir-equipment-categories';
import { CreateCeirTechnicalStandardDto, UpdateCeirTechnicalStandardDto } from '../dto/ceir-technical-standards';

// Import entities
import { CeirCertificationBodies } from '../entities/ceir-certification-bodies.entity';
import { CeirEquipmentTypeCategories } from '../entities/ceir-equipment-type-categories.entity';
import { CeirTechnicalStandards } from '../entities/ceir-technical-standards.entity';

@ApiTags('CEIR Management')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller('ceir/management')
export class CeirManagementController {
  constructor(
    private readonly ceirCertificationBodiesService: CeirCertificationBodiesService,
    private readonly ceirEquipmentCategoriesService: CeirEquipmentCategoriesService,
    private readonly ceirTechnicalStandardsService: CeirTechnicalStandardsService,
  ) {}

  // ===== CERTIFICATION BODIES ENDPOINTS =====

  @Post('certification-bodies')
  @ApiOperation({ summary: 'Create a new CEIR certification body' })
  @ApiResponse({
    status: 201,
    description: 'Certification body created successfully',
    type: CeirCertificationBodies,
  })
  @ApiBody({ type: CreateCeirCertificationBodyDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Created CEIR certification body',
  })
  async createCertificationBody(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createDto: CreateCeirCertificationBodyDto,
    @Request() req: any,
  ): Promise<CeirCertificationBodies> {
    return this.ceirCertificationBodiesService.create(createDto, req.user?.userId);
  }

  @Get('certification-bodies')
  @ApiOperation({ summary: 'Get all CEIR certification bodies' })
  @ApiResponse({
    status: 200,
    description: 'List of certification bodies',
    type: [CeirCertificationBodies],
  })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Viewed CEIR certification bodies',
  })
  async findAllCertificationBodies(@Query('active') active?: boolean): Promise<CeirCertificationBodies[]> {
    if (active === true) {
      return this.ceirCertificationBodiesService.findAllActive();
    }
    return this.ceirCertificationBodiesService.findAll();
  }

  @Get('certification-bodies/statistics')
  @ApiOperation({ summary: 'Get CEIR certification bodies statistics' })
  @ApiResponse({
    status: 200,
    description: 'Certification bodies statistics',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Viewed certification bodies statistics',
  })
  async getCertificationBodiesStatistics() {
    return this.ceirCertificationBodiesService.getStatistics();
  }

  @Get('certification-bodies/macra-recognized')
  @ApiOperation({ summary: 'Get MACRA recognized certification bodies' })
  @ApiResponse({
    status: 200,
    description: 'List of MACRA recognized certification bodies',
    type: [CeirCertificationBodies],
  })
  async findMacraRecognizedBodies(): Promise<CeirCertificationBodies[]> {
    return this.ceirCertificationBodiesService.findMacraRecognized();
  }

  @Get('certification-bodies/ceir-certifiers')
  @ApiOperation({ summary: 'Get certification bodies that can issue CEIR certificates' })
  @ApiResponse({
    status: 200,
    description: 'List of CEIR certificate issuers',
    type: [CeirCertificationBodies],
  })
  async findCeirCertifiers(): Promise<CeirCertificationBodies[]> {
    return this.ceirCertificationBodiesService.findCeirCertifiers();
  }

  @Get('certification-bodies/expiring-accreditations')
  @ApiOperation({ summary: 'Get certification bodies with expiring accreditations' })
  @ApiQuery({ name: 'days', required: false, type: Number, description: 'Number of days to look ahead (default: 30)' })
  @ApiResponse({
    status: 200,
    description: 'List of certification bodies with expiring accreditations',
    type: [CeirCertificationBodies],
  })
  async findExpiringAccreditations(@Query('days') days?: number): Promise<CeirCertificationBodies[]> {
    return this.ceirCertificationBodiesService.findExpiringAccreditations(days || 30);
  }

  @Get('certification-bodies/:id')
  @ApiOperation({ summary: 'Get CEIR certification body by ID' })
  @ApiParam({ name: 'id', description: 'Certification body UUID' })
  @ApiResponse({
    status: 200,
    description: 'Certification body found',
    type: CeirCertificationBodies,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Viewed CEIR certification body details',
  })
  async findOneCertificationBody(@Param('id', ParseUUIDPipe) id: string): Promise<CeirCertificationBodies> {
    return this.ceirCertificationBodiesService.findOne(id);
  }

  @Patch('certification-bodies/:id')
  @ApiOperation({ summary: 'Update CEIR certification body' })
  @ApiParam({ name: 'id', description: 'Certification body UUID' })
  @ApiResponse({
    status: 200,
    description: 'Certification body updated successfully',
    type: CeirCertificationBodies,
  })
  @ApiBody({ type: UpdateCeirCertificationBodyDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Updated CEIR certification body',
  })
  async updateCertificationBody(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateDto: UpdateCeirCertificationBodyDto,
    @Request() req: any,
  ): Promise<CeirCertificationBodies> {
    return this.ceirCertificationBodiesService.update(id, updateDto, req.user?.userId);
  }

  @Delete('certification-bodies/:id')
  @ApiOperation({ summary: 'Delete CEIR certification body' })
  @ApiParam({ name: 'id', description: 'Certification body UUID' })
  @ApiResponse({
    status: 200,
    description: 'Certification body deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirCertificationBody',
    description: 'Deleted CEIR certification body',
  })
  async removeCertificationBody(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.ceirCertificationBodiesService.remove(id);
  }

  // ===== EQUIPMENT CATEGORIES ENDPOINTS =====

  @Post('equipment-categories')
  @ApiOperation({ summary: 'Create a new CEIR equipment category' })
  @ApiResponse({
    status: 201,
    description: 'Equipment category created successfully',
    type: CeirEquipmentTypeCategories,
  })
  @ApiBody({ type: CreateCeirEquipmentCategoryDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Created CEIR equipment category',
  })
  async createEquipmentCategory(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createDto: CreateCeirEquipmentCategoryDto,
    @Request() req: any,
  ): Promise<CeirEquipmentTypeCategories> {
    return this.ceirEquipmentCategoriesService.create(createDto, req.user?.userId);
  }

  @Get('equipment-categories')
  @ApiOperation({ summary: 'Get all CEIR equipment categories' })
  @ApiResponse({
    status: 200,
    description: 'List of equipment categories',
    type: [CeirEquipmentTypeCategories],
  })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Viewed CEIR equipment categories',
  })
  async findAllEquipmentCategories(@Query('active') active?: boolean): Promise<CeirEquipmentTypeCategories[]> {
    if (active === true) {
      return this.ceirEquipmentCategoriesService.findAllActive();
    }
    return this.ceirEquipmentCategoriesService.findAll();
  }

  @Get('equipment-categories/statistics')
  @ApiOperation({ summary: 'Get CEIR equipment categories statistics' })
  @ApiResponse({
    status: 200,
    description: 'Equipment categories statistics',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Viewed equipment categories statistics',
  })
  async getEquipmentCategoriesStatistics() {
    return this.ceirEquipmentCategoriesService.getStatistics();
  }

  @Get('equipment-categories/by-type/:categoryType')
  @ApiOperation({ summary: 'Get equipment category by type' })
  @ApiParam({ name: 'categoryType', description: 'Category type to find' })
  @ApiResponse({
    status: 200,
    description: 'Equipment category found',
    type: CeirEquipmentTypeCategories,
  })
  async findEquipmentCategoryByType(
    @Param('categoryType') categoryType: string,
  ): Promise<CeirEquipmentTypeCategories> {
    return this.ceirEquipmentCategoriesService.findByType(categoryType);
  }

  @Get('equipment-categories/by-ceir-code/:ceirCode')
  @ApiOperation({ summary: 'Get equipment category by CEIR code' })
  @ApiParam({ name: 'ceirCode', description: 'CEIR standard code to find' })
  @ApiResponse({
    status: 200,
    description: 'Equipment category found',
    type: CeirEquipmentTypeCategories,
  })
  async findEquipmentCategoryByCeirCode(
    @Param('ceirCode') ceirCode: string,
  ): Promise<CeirEquipmentTypeCategories> {
    return this.ceirEquipmentCategoriesService.findByCeirCode(ceirCode);
  }

  @Get('equipment-categories/:id')
  @ApiOperation({ summary: 'Get CEIR equipment category by ID' })
  @ApiParam({ name: 'id', description: 'Equipment category UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment category found',
    type: CeirEquipmentTypeCategories,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Viewed CEIR equipment category details',
  })
  async findOneEquipmentCategory(@Param('id', ParseUUIDPipe) id: string): Promise<CeirEquipmentTypeCategories> {
    return this.ceirEquipmentCategoriesService.findOne(id);
  }

  @Patch('equipment-categories/:id')
  @ApiOperation({ summary: 'Update CEIR equipment category' })
  @ApiParam({ name: 'id', description: 'Equipment category UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment category updated successfully',
    type: CeirEquipmentTypeCategories,
  })
  @ApiBody({ type: UpdateCeirEquipmentCategoryDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Updated CEIR equipment category',
  })
  async updateEquipmentCategory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateDto: UpdateCeirEquipmentCategoryDto,
    @Request() req: any,
  ): Promise<CeirEquipmentTypeCategories> {
    return this.ceirEquipmentCategoriesService.update(id, updateDto, req.user?.userId);
  }

  @Delete('equipment-categories/:id')
  @ApiOperation({ summary: 'Delete CEIR equipment category' })
  @ApiParam({ name: 'id', description: 'Equipment category UUID' })
  @ApiResponse({
    status: 200,
    description: 'Equipment category deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirEquipmentCategory',
    description: 'Deleted CEIR equipment category',
  })
  async removeEquipmentCategory(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.ceirEquipmentCategoriesService.remove(id);
  }

  // ===== TECHNICAL STANDARDS ENDPOINTS =====

  @Post('technical-standards')
  @ApiOperation({ summary: 'Create a new CEIR technical standard' })
  @ApiResponse({
    status: 201,
    description: 'Technical standard created successfully',
    type: CeirTechnicalStandards,
  })
  @ApiBody({ type: CreateCeirTechnicalStandardDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Created CEIR technical standard',
  })
  async createTechnicalStandard(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createDto: CreateCeirTechnicalStandardDto,
    @Request() req: any,
  ): Promise<CeirTechnicalStandards> {
    return this.ceirTechnicalStandardsService.create(createDto, req.user?.userId);
  }

  @Get('technical-standards')
  @ApiOperation({ summary: 'Get all CEIR technical standards' })
  @ApiResponse({
    status: 200,
    description: 'List of technical standards',
    type: [CeirTechnicalStandards],
  })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Viewed CEIR technical standards',
  })
  async findAllTechnicalStandards(@Query('active') active?: boolean): Promise<CeirTechnicalStandards[]> {
    if (active === true) {
      return this.ceirTechnicalStandardsService.findAllActive();
    }
    return this.ceirTechnicalStandardsService.findAll();
  }

  @Get('technical-standards/statistics')
  @ApiOperation({ summary: 'Get CEIR technical standards statistics' })
  @ApiResponse({
    status: 200,
    description: 'Technical standards statistics',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Viewed technical standards statistics',
  })
  async getTechnicalStandardsStatistics() {
    return this.ceirTechnicalStandardsService.getStatistics();
  }

  @Get('technical-standards/by-reference/:reference')
  @ApiOperation({ summary: 'Get technical standard by reference' })
  @ApiParam({ name: 'reference', description: 'Standard reference to find' })
  @ApiResponse({
    status: 200,
    description: 'Technical standard found',
    type: CeirTechnicalStandards,
  })
  async findTechnicalStandardByReference(
    @Param('reference') reference: string,
  ): Promise<CeirTechnicalStandards> {
    return this.ceirTechnicalStandardsService.findByReference(reference);
  }

  @Get('technical-standards/:id')
  @ApiOperation({ summary: 'Get CEIR technical standard by ID' })
  @ApiParam({ name: 'id', description: 'Technical standard UUID' })
  @ApiResponse({
    status: 200,
    description: 'Technical standard found',
    type: CeirTechnicalStandards,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Viewed CEIR technical standard details',
  })
  async findOneTechnicalStandard(@Param('id', ParseUUIDPipe) id: string): Promise<CeirTechnicalStandards> {
    return this.ceirTechnicalStandardsService.findOne(id);
  }

  @Patch('technical-standards/:id')
  @ApiOperation({ summary: 'Update CEIR technical standard' })
  @ApiParam({ name: 'id', description: 'Technical standard UUID' })
  @ApiResponse({
    status: 200,
    description: 'Technical standard updated successfully',
    type: CeirTechnicalStandards,
  })
  @ApiBody({ type: UpdateCeirTechnicalStandardDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Updated CEIR technical standard',
  })
  async updateTechnicalStandard(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateDto: UpdateCeirTechnicalStandardDto,
    @Request() req: any,
  ): Promise<CeirTechnicalStandards> {
    return this.ceirTechnicalStandardsService.update(id, updateDto, req.user?.userId);
  }

  @Delete('technical-standards/:id')
  @ApiOperation({ summary: 'Delete CEIR technical standard' })
  @ApiParam({ name: 'id', description: 'Technical standard UUID' })
  @ApiResponse({
    status: 200,
    description: 'Technical standard deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'CeirTechnicalStandard',
    description: 'Deleted CEIR technical standard',
  })
  async removeTechnicalStandard(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.ceirTechnicalStandardsService.remove(id);
  }
}
