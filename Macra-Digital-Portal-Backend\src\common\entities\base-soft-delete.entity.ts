import { 
  CreateDateColumn, 
  UpdateDateColumn, 
  DeleteDateColumn, 
  Column,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { User } from '../../entities/user.entity';

/**
 * Base entity class that provides standard audit fields including soft delete
 * All entities should extend this class to ensure consistent audit trail
 */
export abstract class BaseSoftDeleteEntity {
  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations for audit trail
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  /**
   * Check if the entity is soft deleted
   */
  get isDeleted(): boolean {
    return this.deleted_at !== null && this.deleted_at !== undefined;
  }

  /**
   * Get the deletion date as a formatted string
   */
  get deletedAtFormatted(): string | null {
    return this.deleted_at ? this.deleted_at.toISOString() : null;
  }
}
