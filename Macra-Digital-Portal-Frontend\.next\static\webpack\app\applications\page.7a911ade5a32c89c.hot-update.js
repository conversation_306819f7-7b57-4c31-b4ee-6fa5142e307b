"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/applications/page",{

/***/ "(app-pages-browser)/./src/components/common/AssignModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/common/AssignModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AssignModal = (param)=>{\n    let { isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess, task, onReassignSuccess } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [officers, setOfficers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assigning, setAssigning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOfficer, setSelectedOfficer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loadTask, setTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReassignMode, setIsReassignMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssignModal.useEffect\": ()=>{\n            if (isOpen) {\n                fetchOfficers();\n                fetchExistingTasks();\n                setComment('');\n            }\n        }\n    }[\"AssignModal.useEffect\"], [\n        isOpen\n    ]);\n    // Separate effect to handle task data initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssignModal.useEffect\": ()=>{\n            if (isOpen && loadTask) {\n                // For reassignment, pre-select the current assignee\n                setSelectedOfficer(loadTask.assigned_to || '');\n                // For reassignment, pre-fill the current due date if available\n                setDueDate(loadTask.due_date ? loadTask.due_date.split('T')[0] : '');\n            } else if (isOpen && !isReassignMode) {\n                // For new assignments, clear the selection\n                setSelectedOfficer('');\n                setDueDate('');\n            }\n        }\n    }[\"AssignModal.useEffect\"], [\n        isOpen,\n        loadTask,\n        isReassignMode\n    ]);\n    const fetchExistingTasks = async ()=>{\n        if (task) {\n            // Task provided directly (reassignment mode)\n            // Always fetch fresh task data to ensure we have the latest assignee info\n            try {\n                const freshTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getTask(task.task_id);\n                if (freshTask) {\n                    setTask(freshTask);\n                } else {\n                    setTask(task);\n                }\n            } catch (error) {\n                console.error('Error fetching fresh task data, using provided task:', error);\n                setTask(task);\n            }\n            setIsReassignMode(true);\n            return;\n        }\n        if (itemId && itemType === 'application') {\n            // Fetch task for application\n            try {\n                const existingTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getTaskForApplication(itemId);\n                if (existingTask && existingTask.task_id) {\n                    setTask(existingTask);\n                    setIsReassignMode(true);\n                } else {\n                    setIsReassignMode(false);\n                    setTask(null);\n                }\n            } catch (error) {\n                console.error('Error fetching existing task:', error);\n                setIsReassignMode(false);\n                setTask(null);\n            }\n        } else {\n            // No existing task, new assignment mode\n            setIsReassignMode(false);\n            setTask(null);\n        }\n    };\n    const fetchOfficers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getOfficers();\n            const officersData = response.data || [];\n            setOfficers(officersData);\n            if (officersData.length === 0) {\n                console.warn('No officers found for task assignment');\n            }\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            setOfficers([]);\n            showError('Failed to load officers. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getTaskTypeFromItemType = (itemType)=>{\n        switch(itemType){\n            case 'application':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.EVALUATION;\n            case 'data_breach':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DATA_BREACH;\n            case 'complaint':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.COMPLAINT;\n            case 'inspection':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.INSPECTION;\n            case 'document_review':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DOCUMENT_REVIEW;\n            case 'task':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION; // Default for existing tasks\n            default:\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION;\n        }\n    };\n    const getTaskTitle = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'Untitled';\n        switch(itemType){\n            case 'application':\n                return \"Application Evaluation: \".concat(baseTitle);\n            case 'data_breach':\n                return \"Data Breach Investigation: \".concat(baseTitle);\n            case 'complaint':\n                return \"Complaint Review: \".concat(baseTitle);\n            case 'inspection':\n                return \"Inspection Task: \".concat(baseTitle);\n            case 'document_review':\n                return \"Document Review: \".concat(baseTitle);\n            default:\n                return \"Task: \".concat(baseTitle);\n        }\n    };\n    const getTaskDescription = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'item';\n        switch(itemType){\n            case 'application':\n                return \"Evaluate and review application \".concat(baseTitle, \" for compliance and approval.\");\n            case 'data_breach':\n                return \"Investigate and assess data breach report \".concat(baseTitle, \" for regulatory compliance.\");\n            case 'complaint':\n                return \"Review and resolve complaint \".concat(baseTitle, \" according to regulatory procedures.\");\n            case 'inspection':\n                return \"Conduct inspection for \".concat(baseTitle, \" to ensure regulatory compliance.\");\n            case 'document_review':\n                return \"Review and validate document \".concat(baseTitle, \" for accuracy and compliance.\");\n            default:\n                return \"Process and review \".concat(baseTitle, \".\");\n        }\n    };\n    const handleAssign = async ()=>{\n        // For new assignments, officer selection is required\n        // For reassignments, allow unassigning (empty selection)\n        if (!isReassignMode && !selectedOfficer) {\n            showError('Please select an officer');\n            return;\n        }\n        if (!dueDate) {\n            showError('Please select a due date');\n            return;\n        }\n        if (!isReassignMode && !itemId) {\n            showError('Item ID is missing');\n            return;\n        }\n        setAssigning(true);\n        try {\n            if (isReassignMode && loadTask) {\n                // Reassign existing task (or unassign if selectedOfficer is empty)\n                await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.reassignTask(loadTask.task_id, {\n                    assignedTo: selectedOfficer || undefined,\n                    comment: comment.trim() || undefined,\n                    due_date: dueDate,\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM\n                });\n                showSuccess(selectedOfficer ? 'Task reassigned successfully' : 'Task unassigned successfully');\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            } else {\n                const taskData = {\n                    task_type: getTaskTypeFromItemType(itemType),\n                    title: getTaskTitle(itemType, itemTitle),\n                    description: getTaskDescription(itemType, itemTitle),\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM,\n                    status: _types__WEBPACK_IMPORTED_MODULE_4__.TaskStatus.PENDING,\n                    entity_type: itemType,\n                    entity_id: itemId,\n                    assigned_to: selectedOfficer,\n                    due_date: dueDate,\n                    metadata: {\n                        comment: comment.trim() || undefined,\n                        original_item_title: itemTitle,\n                        assignment_context: 'manual_assignment'\n                    }\n                };\n                // For other entity types, create a new task\n                await createNewTask(taskData);\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error \".concat(isReassignMode ? 'reassigning' : 'creating assignment', \" task:\"), error);\n            showError(\"Failed to \".concat(isReassignMode ? 'reassign' : 'assign', \" task\"));\n        } finally{\n            setAssigning(false);\n        }\n    };\n    // Helper function to create a new task\n    async function createNewTask(taskData) {\n        const createdTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.createTask(taskData);\n        showSuccess(\"Successfully assigned \".concat(itemType.replace('_', ' '), \" to officer\"));\n        onAssignSuccess === null || onAssignSuccess === void 0 ? void 0 : onAssignSuccess();\n    }\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: isReassignMode ? 'Reassign Task' : \"Create Task for \".concat(itemType.replace('_', ' ').toUpperCase())\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-task-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isReassignMode ? 'Task Details:' : 'Task to be Created:'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300 font-medium\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.title : getTaskTitle(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.description : getTaskDescription(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Task #\",\n                                        loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assignee) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Currently assigned to: \",\n                                        loadTask.assignee.first_name,\n                                        \" \",\n                                        loadTask.assignee.last_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                                    children: \"Loading officers...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedOfficer,\n                                    onChange: (e)=>setSelectedOfficer(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: isReassignMode ? 'Select an officer or unassign...' : 'Select an officer...'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isReassignMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            className: \"text-red-600 font-medium\",\n                                            children: \"\\uD83D\\uDEAB Unassign Task (Remove assignee)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        officers.map((officer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: officer.user_id,\n                                                children: [\n                                                    officer.first_name,\n                                                    \" \",\n                                                    officer.last_name,\n                                                    \" - \",\n                                                    officer.email,\n                                                    officer.department ? \" (\".concat(typeof officer.department === 'string' ? officer.department : officer.department.name, \")\") : ''\n                                                ]\n                                            }, officer.user_id, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Due Date *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    value: dueDate,\n                                    onChange: (e)=>setDueDate(e.target.value),\n                                    min: new Date().toISOString().split('T')[0],\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: comment,\n                                    onChange: (e)=>setComment(e.target.value),\n                                    placeholder: isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...',\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedOfficer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-green-900 dark:text-green-100 mb-2\",\n                                    children: \"Selected Officer:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200\",\n                                    children: (()=>{\n                                        const officer = officers.find((o)=>o.user_id === selectedOfficer);\n                                        return officer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                officer.first_name,\n                                                \" \",\n                                                officer.last_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                officer.email,\n                                                officer.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \"Department: \",\n                                                        typeof officer.department === 'string' ? officer.department : officer.department.name\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true) : 'Officer not found';\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined),\n                                dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Due Date:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" \",\n                                        new Date(dueDate).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleAssign,\n                            disabled: !isReassignMode && !selectedOfficer || !dueDate || assigning,\n                            className: \"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                            children: assigning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassigning...' : 'Creating Task...'\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassign Task' : 'Create Task'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AssignModal, \"ZGsPfKHvEAdKKrOIH+QIFnnqxG0=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AssignModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AssignModal);\nvar _c;\n$RefreshReg$(_c, \"AssignModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9Bc3NpZ25Nb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0E7QUFDTTtBQUN5QjtBQTBCbEYsTUFBTVEsY0FBMEM7UUFBQyxFQUMvQ0MsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLGVBQWUsRUFDZkMsSUFBSSxFQUNKQyxpQkFBaUIsRUFDbEI7O0lBQ0MsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHZixnRUFBUUE7SUFDM0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHbkIsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNvQixTQUFTQyxXQUFXLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQixXQUFXQyxhQUFhLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN3QixpQkFBaUJDLG1CQUFtQixHQUFHekIsK0NBQVFBLENBQVM7SUFDL0QsTUFBTSxDQUFDMEIsU0FBU0MsV0FBVyxHQUFHM0IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDNEIsU0FBU0MsV0FBVyxHQUFHN0IsK0NBQVFBLENBQVM7SUFDL0MsTUFBTSxDQUFDOEIsVUFBVUMsUUFBUSxHQUFHL0IsK0NBQVFBLENBQWM7SUFDbEQsTUFBTSxDQUFDZ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2pDLCtDQUFRQSxDQUFVO0lBQzlEQyxnREFBU0E7aUNBQUM7WUFDUixJQUFJTyxRQUFRO2dCQUNWMEI7Z0JBQ0FDO2dCQUNBUixXQUFXO1lBQ2I7UUFDRjtnQ0FBRztRQUFDbkI7S0FBTztJQUVYLHFEQUFxRDtJQUNyRFAsZ0RBQVNBO2lDQUFDO1lBQ1IsSUFBSU8sVUFBVXNCLFVBQVU7Z0JBQ3RCLG9EQUFvRDtnQkFDcERMLG1CQUFtQkssU0FBU00sV0FBVyxJQUFJO2dCQUMzQywrREFBK0Q7Z0JBQy9EUCxXQUFXQyxTQUFTTyxRQUFRLEdBQUdQLFNBQVNPLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7WUFDbkUsT0FBTyxJQUFJOUIsVUFBVSxDQUFDd0IsZ0JBQWdCO2dCQUNwQywyQ0FBMkM7Z0JBQzNDUCxtQkFBbUI7Z0JBQ25CSSxXQUFXO1lBQ2I7UUFDRjtnQ0FBRztRQUFDckI7UUFBUXNCO1FBQVVFO0tBQWU7SUFHckMsTUFBTUcscUJBQXFCO1FBQ3pCLElBQUdyQixNQUFNO1lBQ1AsNkNBQTZDO1lBQzdDLDBFQUEwRTtZQUMxRSxJQUFJO2dCQUNGLE1BQU15QixZQUFZLE1BQU1wQyxrRUFBV0EsQ0FBQ3FDLE9BQU8sQ0FBQzFCLEtBQUsyQixPQUFPO2dCQUN4RCxJQUFJRixXQUFXO29CQUNiUixRQUFRUTtnQkFDVixPQUFPO29CQUNMUixRQUFRakI7Z0JBQ1Y7WUFDRixFQUFFLE9BQU80QixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsd0RBQXdEQTtnQkFDdEVYLFFBQVFqQjtZQUNWO1lBQ0FtQixrQkFBa0I7WUFDbEI7UUFDRjtRQUVBLElBQUl2QixVQUFVQyxhQUFhLGVBQWU7WUFDeEMsNkJBQTZCO1lBQzdCLElBQUk7Z0JBQ0YsTUFBTWlDLGVBQWUsTUFBTXpDLGtFQUFXQSxDQUFDMEMscUJBQXFCLENBQUNuQztnQkFDN0QsSUFBR2tDLGdCQUFnQkEsYUFBYUgsT0FBTyxFQUFFO29CQUN2Q1YsUUFBUWE7b0JBQ1JYLGtCQUFrQjtnQkFDcEIsT0FBTztvQkFDTEEsa0JBQWtCO29CQUNsQkYsUUFBUTtnQkFDVjtZQUNGLEVBQUUsT0FBT1csT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9DVCxrQkFBa0I7Z0JBQ2xCRixRQUFRO1lBQ1Y7UUFDRixPQUFPO1lBQ0wsd0NBQXdDO1lBQ3hDRSxrQkFBa0I7WUFDbEJGLFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTUcsZ0JBQWdCO1FBQ3BCYixXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU15QixXQUFXLE1BQU0zQyxrRUFBV0EsQ0FBQzRDLFdBQVc7WUFDOUMsTUFBTUMsZUFBZUYsU0FBU0csSUFBSSxJQUFJLEVBQUU7WUFDeEM5QixZQUFZNkI7WUFFWixJQUFJQSxhQUFhRSxNQUFNLEtBQUssR0FBRztnQkFDN0JQLFFBQVFRLElBQUksQ0FBQztZQUNmO1FBQ0YsRUFBRSxPQUFPVCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDdkIsWUFBWSxFQUFFO1lBQ2RGLFVBQVU7UUFDWixTQUFVO1lBQ1JJLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTStCLDBCQUEwQixDQUFDekM7UUFDL0IsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU9QLDRDQUFRQSxDQUFDaUQsVUFBVTtZQUM1QixLQUFLO2dCQUNILE9BQU9qRCw0Q0FBUUEsQ0FBQ2tELFdBQVc7WUFDN0IsS0FBSztnQkFDSCxPQUFPbEQsNENBQVFBLENBQUNtRCxTQUFTO1lBQzNCLEtBQUs7Z0JBQ0gsT0FBT25ELDRDQUFRQSxDQUFDb0QsVUFBVTtZQUM1QixLQUFLO2dCQUNILE9BQU9wRCw0Q0FBUUEsQ0FBQ3FELGVBQWU7WUFDakMsS0FBSztnQkFDSCxPQUFPckQsNENBQVFBLENBQUNzRCxXQUFXLEVBQUUsNkJBQTZCO1lBQzVEO2dCQUNFLE9BQU90RCw0Q0FBUUEsQ0FBQ3NELFdBQVc7UUFDL0I7SUFDRjtJQUVBLE1BQU1DLGVBQWUsQ0FBQ2hELFVBQWtCQztRQUN0QyxNQUFNZ0QsWUFBWWhELGFBQWE7UUFDL0IsT0FBUUQ7WUFDTixLQUFLO2dCQUNILE9BQU8sMkJBQXFDLE9BQVZpRDtZQUNwQyxLQUFLO2dCQUNILE9BQU8sOEJBQXdDLE9BQVZBO1lBQ3ZDLEtBQUs7Z0JBQ0gsT0FBTyxxQkFBK0IsT0FBVkE7WUFDOUIsS0FBSztnQkFDSCxPQUFPLG9CQUE4QixPQUFWQTtZQUM3QixLQUFLO2dCQUNILE9BQU8sb0JBQThCLE9BQVZBO1lBQzdCO2dCQUNFLE9BQU8sU0FBbUIsT0FBVkE7UUFDcEI7SUFDRjtJQUVBLE1BQU1DLHFCQUFxQixDQUFDbEQsVUFBa0JDO1FBQzVDLE1BQU1nRCxZQUFZaEQsYUFBYTtRQUMvQixPQUFRRDtZQUNOLEtBQUs7Z0JBQ0gsT0FBTyxtQ0FBNkMsT0FBVmlELFdBQVU7WUFDdEQsS0FBSztnQkFDSCxPQUFPLDZDQUF1RCxPQUFWQSxXQUFVO1lBQ2hFLEtBQUs7Z0JBQ0gsT0FBTyxnQ0FBMEMsT0FBVkEsV0FBVTtZQUNuRCxLQUFLO2dCQUNILE9BQU8sMEJBQW9DLE9BQVZBLFdBQVU7WUFDN0MsS0FBSztnQkFDSCxPQUFPLGdDQUEwQyxPQUFWQSxXQUFVO1lBQ25EO2dCQUNFLE9BQU8sc0JBQWdDLE9BQVZBLFdBQVU7UUFDM0M7SUFDRjtJQUVBLE1BQU1FLGVBQWU7UUFDbkIscURBQXFEO1FBQ3JELHlEQUF5RDtRQUN6RCxJQUFJLENBQUM5QixrQkFBa0IsQ0FBQ1IsaUJBQWlCO1lBQ3ZDUCxVQUFVO1lBQ1Y7UUFDRjtRQUVBLElBQUksQ0FBQ1csU0FBUztZQUNaWCxVQUFVO1lBQ1Y7UUFDRjtRQUVBLElBQUksQ0FBQ2Usa0JBQWtCLENBQUN0QixRQUFRO1lBQzlCTyxVQUFVO1lBQ1Y7UUFDRjtRQUVBTSxhQUFhO1FBR2IsSUFBSTtZQUNGLElBQUlTLGtCQUFrQkYsVUFBVTtnQkFDOUIsbUVBQW1FO2dCQUNuRSxNQUFNM0Isa0VBQVdBLENBQUM0RCxZQUFZLENBQUNqQyxTQUFTVyxPQUFPLEVBQUU7b0JBQy9DdUIsWUFBWXhDLG1CQUFtQnlDO29CQUMvQnZDLFNBQVNBLFFBQVF3QyxJQUFJLE1BQU1EO29CQUMzQjVCLFVBQVVUO29CQUNWdUMsVUFBVTlELGdEQUFZQSxDQUFDK0QsTUFBTTtnQkFDL0I7Z0JBQ0FwRCxZQUFZUSxrQkFBa0IsaUNBQWlDO2dCQUMvRFQsOEJBQUFBLHdDQUFBQTtZQUVGLE9BQU87Z0JBQ0wsTUFBTXNELFdBQTJCO29CQUMvQkMsV0FBV2xCLHdCQUF3QnpDO29CQUNuQzRELE9BQU9aLGFBQWFoRCxVQUFVQztvQkFDOUI0RCxhQUFhWCxtQkFBbUJsRCxVQUFVQztvQkFDMUN1RCxVQUFVOUQsZ0RBQVlBLENBQUMrRCxNQUFNO29CQUM3QkssUUFBUW5FLDhDQUFVQSxDQUFDb0UsT0FBTztvQkFDMUJDLGFBQWFoRTtvQkFDYmlFLFdBQVdsRTtvQkFDWDBCLGFBQWFaO29CQUNiYSxVQUFVVDtvQkFDVmlELFVBQVU7d0JBQ1JuRCxTQUFTQSxRQUFRd0MsSUFBSSxNQUFNRDt3QkFDM0JhLHFCQUFxQmxFO3dCQUNyQm1FLG9CQUFvQjtvQkFDdEI7Z0JBQ0Y7Z0JBQ0EsNENBQTRDO2dCQUM1QyxNQUFNQyxjQUFjWDtnQkFDcEJ0RCw4QkFBQUEsd0NBQUFBO1lBQ0Y7WUFFQU47UUFDRixFQUFFLE9BQU9pQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxTQUFnRSxPQUF2RFYsaUJBQWlCLGdCQUFnQix1QkFBc0IsV0FBU1U7WUFDdkZ6QixVQUFVLGFBQW9ELE9BQXZDZSxpQkFBaUIsYUFBYSxVQUFTO1FBQ2hFLFNBQVU7WUFDUlQsYUFBYTtRQUNmO0lBQ0Y7SUFFRSx1Q0FBdUM7SUFDekMsZUFBZXlELGNBQWNYLFFBQXVCO1FBQ2xELE1BQU1ZLGNBQWMsTUFBTTlFLGtFQUFXQSxDQUFDK0UsVUFBVSxDQUFDYjtRQUNqRHJELFlBQVkseUJBQW9ELE9BQTNCTCxTQUFTd0UsT0FBTyxDQUFDLEtBQUssTUFBSztRQUNoRXRFLDRCQUFBQSxzQ0FBQUE7SUFDRjtJQUVBLElBQUksQ0FBQ0wsUUFBUSxPQUFPO0lBRXBCLHFCQUNFLDhEQUFDNEU7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFFRCxXQUFXckQsaUJBQWlCLDJDQUEyQzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHOUUsOERBQUNvRDs7c0RBQ0MsOERBQUNHOzRDQUFHRixXQUFVO3NEQUNYckQsaUJBQWlCLGtCQUFrQixtQkFBNEQsT0FBekNyQixTQUFTd0UsT0FBTyxDQUFDLEtBQUssS0FBS0ssV0FBVzs7Ozs7O3NEQUUvRiw4REFBQ0M7NENBQUVKLFdBQVU7c0RBQ1ZyRCxpQkFBaUIscUNBQXFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSTdELDhEQUFDMEQ7NEJBQ0NDLE1BQUs7NEJBQ0xDLFNBQVNuRjs0QkFDVDRFLFdBQVU7c0NBRVYsNEVBQUNDO2dDQUFFRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLakIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUTtvQ0FBR1IsV0FBVTs7c0RBQ1osOERBQUNDOzRDQUFFRCxXQUFVOzs7Ozs7d0NBQ1pyRCxpQkFBaUIsa0JBQWtCOzs7Ozs7OzhDQUV0Qyw4REFBQ3lEO29DQUFFSixXQUFVOzhDQUNWckQsaUJBQWlCRixxQkFBQUEsK0JBQUFBLFNBQVV5QyxLQUFLLEdBQUdaLGFBQWFoRCxVQUFVQzs7Ozs7OzhDQUU3RCw4REFBQzZFO29DQUFFSixXQUFVOzhDQUNWckQsaUJBQWlCRixxQkFBQUEsK0JBQUFBLFNBQVUwQyxXQUFXLEdBQUdYLG1CQUFtQmxELFVBQVVDOzs7Ozs7Z0NBRXhFb0IsbUJBQWtCRixxQkFBQUEsK0JBQUFBLFNBQVVnRSxXQUFXLG1CQUN0Qyw4REFBQ0w7b0NBQUVKLFdBQVU7O3dDQUFnRDt3Q0FDcER2RCxxQkFBQUEsK0JBQUFBLFNBQVVnRSxXQUFXOzs7Ozs7O2dDQUcvQjlELG1CQUFrQkYscUJBQUFBLCtCQUFBQSxTQUFVaUUsUUFBUSxtQkFDbkMsOERBQUNOO29DQUFFSixXQUFVOzt3Q0FBZ0Q7d0NBQ25DdkQsU0FBU2lFLFFBQVEsQ0FBQ0MsVUFBVTt3Q0FBQzt3Q0FBRWxFLFNBQVNpRSxRQUFRLENBQUNFLFNBQVM7Ozs7Ozs7Ozs7Ozs7c0NBTXhGLDhEQUFDYjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNhO29DQUFNYixXQUFVOzhDQUNkckQsaUJBQWlCLDBCQUEwQjs7Ozs7O2dDQUU3Q1osd0JBQ0MsOERBQUNnRTtvQ0FBSUMsV0FBVTs4Q0FBb0Q7Ozs7OzhEQUluRSw4REFBQ2M7b0NBQ0NDLE9BQU81RTtvQ0FDUDZFLFVBQVUsQ0FBQ0MsSUFBTTdFLG1CQUFtQjZFLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvQ0FDbERmLFdBQVU7O3NEQUVWLDhEQUFDbUI7NENBQU9KLE9BQU07c0RBQUlwRSxpQkFBaUIscUNBQXFDOzs7Ozs7d0NBQ3ZFQSxnQ0FDQyw4REFBQ3dFOzRDQUFPSixPQUFNOzRDQUFHZixXQUFVO3NEQUEyQjs7Ozs7O3dDQUl2RG5FLFNBQVN1RixHQUFHLENBQUMsQ0FBQ0Msd0JBQ2IsOERBQUNGO2dEQUE2QkosT0FBT00sUUFBUUMsT0FBTzs7b0RBQ2pERCxRQUFRVixVQUFVO29EQUFDO29EQUFFVSxRQUFRVCxTQUFTO29EQUFDO29EQUFJUyxRQUFRRSxLQUFLO29EQUN4REYsUUFBUUcsVUFBVSxHQUFHLEtBQTJGLE9BQXRGLE9BQU9ILFFBQVFHLFVBQVUsS0FBSyxXQUFXSCxRQUFRRyxVQUFVLEdBQUdILFFBQVFHLFVBQVUsQ0FBQ0MsSUFBSSxFQUFDLE9BQUs7OytDQUYzR0osUUFBUUMsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXBDLDhEQUFDdkI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FBTWIsV0FBVTs4Q0FBa0U7Ozs7Ozs4Q0FHbkYsOERBQUMwQjtvQ0FDQ3BCLE1BQUs7b0NBQ0xTLE9BQU94RTtvQ0FDUHlFLFVBQVUsQ0FBQ0MsSUFBTXpFLFdBQVd5RSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzFDWSxLQUFLLElBQUlDLE9BQU9DLFdBQVcsR0FBRzVFLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQ0FDM0MrQyxXQUFVOzs7Ozs7Ozs7Ozs7c0NBS2QsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2E7b0NBQU1iLFdBQVU7OENBQ2RyRCxpQkFBaUIsa0NBQWtDOzs7Ozs7OENBRXRELDhEQUFDbUY7b0NBQ0NmLE9BQU8xRTtvQ0FDUDJFLFVBQVUsQ0FBQ0MsSUFBTTNFLFdBQVcyRSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzFDZ0IsYUFBYXBGLGlCQUFpQiw2Q0FBNkM7b0NBQzNFcUYsTUFBTTtvQ0FDTmhDLFdBQVU7Ozs7Ozs7Ozs7Ozt3QkFLYjdELGlDQUNDLDhEQUFDNEQ7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUTtvQ0FBR1IsV0FBVTs4Q0FBOEQ7Ozs7Ozs4Q0FHNUUsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaLENBQUM7d0NBQ0EsTUFBTXFCLFVBQVV4RixTQUFTb0csSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFWixPQUFPLEtBQUtuRjt3Q0FDakQsT0FBT2tGLHdCQUNMOztnREFDR0EsUUFBUVYsVUFBVTtnREFBQztnREFBRVUsUUFBUVQsU0FBUzs4REFDdkMsOERBQUN1Qjs7Ozs7Z0RBQ0FkLFFBQVFFLEtBQUs7Z0RBQ2JGLFFBQVFHLFVBQVUsa0JBQ2pCOztzRUFDRSw4REFBQ1c7Ozs7O3dEQUFLO3dEQUNPLE9BQU9kLFFBQVFHLFVBQVUsS0FBSyxXQUFXSCxRQUFRRyxVQUFVLEdBQUdILFFBQVFHLFVBQVUsQ0FBQ0MsSUFBSTs7OzsyREFJdEc7b0NBQ047Ozs7OztnQ0FFRGxGLHlCQUNDLDhEQUFDd0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDb0M7c0RBQU87Ozs7Ozt3Q0FBa0I7d0NBQUUsSUFBSVIsS0FBS3JGLFNBQVM4RixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUTFFLDhEQUFDdEM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBU25GOzRCQUNUNEUsV0FBVTtzQ0FDWDs7Ozs7O3NDQUdELDhEQUFDSzs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBUzlCOzRCQUNUNkQsVUFBVSxDQUFFM0Ysa0JBQWtCLENBQUNSLG1CQUFvQixDQUFDSSxXQUFXTjs0QkFDL0QrRCxXQUFVO3NDQUVUL0QsMEJBQ0M7O2tEQUNFLDhEQUFDOEQ7d0NBQUlDLFdBQVU7Ozs7OztvQ0FDZHJELGlCQUFpQixtQkFBbUI7OzZEQUd2Qzs7a0RBQ0UsOERBQUNzRDt3Q0FBRUQsV0FBV3JELGlCQUFpQiw2QkFBNkI7Ozs7OztvQ0FDM0RBLGlCQUFpQixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRcEQ7R0F6Wk16Qjs7UUFVK0JMLDREQUFRQTs7O0tBVnZDSztBQTJaTixpRUFBZUEsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsiRDpcXE1lbW9yeSBCdXNpbmVzcyBTb2x1dGlvaW5zXFxQcm9qZWN0c1xcTUFDUkFcXHByb2plY3RcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNvbW1vblxcQXNzaWduTW9kYWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29udGV4dHMvVG9hc3RDb250ZXh0JztcclxuaW1wb3J0IHsgdGFza1NlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL3Rhc2stYXNzaWdubWVudCc7XHJcbmltcG9ydCB7IFRhc2ssIFRhc2tUeXBlLCBUYXNrUHJpb3JpdHksIFRhc2tTdGF0dXMsIENyZWF0ZVRhc2tEdG8gfSBmcm9tICdAL3R5cGVzJztcclxuXHJcbmludGVyZmFjZSBPZmZpY2VyIHtcclxuICB1c2VyX2lkOiBzdHJpbmc7XHJcbiAgZmlyc3RfbmFtZTogc3RyaW5nO1xyXG4gIGxhc3RfbmFtZTogc3RyaW5nO1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgZGVwYXJ0bWVudD86IHtcclxuICAgIGRlcGFydG1lbnRfaWQ6IHN0cmluZztcclxuICAgIG5hbWU6IHN0cmluZztcclxuICAgIGNvZGU6IHN0cmluZztcclxuICB9IHwgc3RyaW5nOyAvLyBDYW4gYmUgZWl0aGVyIGEgZGVwYXJ0bWVudCBvYmplY3Qgb3Igc3RyaW5nIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XHJcbn1cclxuXHJcbmludGVyZmFjZSBBc3NpZ25Nb2RhbFByb3BzIHtcclxuICBpc09wZW46IGJvb2xlYW47XHJcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcclxuICBpdGVtSWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgaXRlbVR5cGU6IHN0cmluZztcclxuICBpdGVtVGl0bGU/OiBzdHJpbmc7XHJcbiAgb25Bc3NpZ25TdWNjZXNzPzogKCkgPT4gdm9pZDtcclxuICAvLyBSZWFzc2lnbm1lbnQgbW9kZSBwcm9wc1xyXG4gIHRhc2s/OiBUYXNrIHwgbnVsbDsgLy8gRm9yIHJlYXNzaWdubWVudCBtb2RlXHJcbiAgb25SZWFzc2lnblN1Y2Nlc3M/OiAoKSA9PiB2b2lkO1xyXG59XHJcblxyXG5jb25zdCBBc3NpZ25Nb2RhbDogUmVhY3QuRkM8QXNzaWduTW9kYWxQcm9wcz4gPSAoe1xyXG4gIGlzT3BlbixcclxuICBvbkNsb3NlLFxyXG4gIGl0ZW1JZCxcclxuICBpdGVtVHlwZSxcclxuICBpdGVtVGl0bGUsXHJcbiAgb25Bc3NpZ25TdWNjZXNzLFxyXG4gIHRhc2ssXHJcbiAgb25SZWFzc2lnblN1Y2Nlc3NcclxufSkgPT4ge1xyXG4gIGNvbnN0IHsgc2hvd1N1Y2Nlc3MsIHNob3dFcnJvciB9ID0gdXNlVG9hc3QoKTtcclxuICBjb25zdCBbb2ZmaWNlcnMsIHNldE9mZmljZXJzXSA9IHVzZVN0YXRlPE9mZmljZXJbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbYXNzaWduaW5nLCBzZXRBc3NpZ25pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZE9mZmljZXIsIHNldFNlbGVjdGVkT2ZmaWNlcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcclxuICBjb25zdCBbY29tbWVudCwgc2V0Q29tbWVudF0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2R1ZURhdGUsIHNldER1ZURhdGVdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW2xvYWRUYXNrLCBzZXRUYXNrXSA9IHVzZVN0YXRlPFRhc2sgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNSZWFzc2lnbk1vZGUsIHNldElzUmVhc3NpZ25Nb2RlXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzT3Blbikge1xyXG4gICAgICBmZXRjaE9mZmljZXJzKCk7XHJcbiAgICAgIGZldGNoRXhpc3RpbmdUYXNrcygpO1xyXG4gICAgICBzZXRDb21tZW50KCcnKTtcclxuICAgIH1cclxuICB9LCBbaXNPcGVuXSk7XHJcblxyXG4gIC8vIFNlcGFyYXRlIGVmZmVjdCB0byBoYW5kbGUgdGFzayBkYXRhIGluaXRpYWxpemF0aW9uXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc09wZW4gJiYgbG9hZFRhc2spIHtcclxuICAgICAgLy8gRm9yIHJlYXNzaWdubWVudCwgcHJlLXNlbGVjdCB0aGUgY3VycmVudCBhc3NpZ25lZVxyXG4gICAgICBzZXRTZWxlY3RlZE9mZmljZXIobG9hZFRhc2suYXNzaWduZWRfdG8gfHwgJycpO1xyXG4gICAgICAvLyBGb3IgcmVhc3NpZ25tZW50LCBwcmUtZmlsbCB0aGUgY3VycmVudCBkdWUgZGF0ZSBpZiBhdmFpbGFibGVcclxuICAgICAgc2V0RHVlRGF0ZShsb2FkVGFzay5kdWVfZGF0ZSA/IGxvYWRUYXNrLmR1ZV9kYXRlLnNwbGl0KCdUJylbMF0gOiAnJyk7XHJcbiAgICB9IGVsc2UgaWYgKGlzT3BlbiAmJiAhaXNSZWFzc2lnbk1vZGUpIHtcclxuICAgICAgLy8gRm9yIG5ldyBhc3NpZ25tZW50cywgY2xlYXIgdGhlIHNlbGVjdGlvblxyXG4gICAgICBzZXRTZWxlY3RlZE9mZmljZXIoJycpO1xyXG4gICAgICBzZXREdWVEYXRlKCcnKTtcclxuICAgIH1cclxuICB9LCBbaXNPcGVuLCBsb2FkVGFzaywgaXNSZWFzc2lnbk1vZGVdKTtcclxuXHJcblxyXG4gIGNvbnN0IGZldGNoRXhpc3RpbmdUYXNrcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmKHRhc2spIHtcclxuICAgICAgLy8gVGFzayBwcm92aWRlZCBkaXJlY3RseSAocmVhc3NpZ25tZW50IG1vZGUpXHJcbiAgICAgIC8vIEFsd2F5cyBmZXRjaCBmcmVzaCB0YXNrIGRhdGEgdG8gZW5zdXJlIHdlIGhhdmUgdGhlIGxhdGVzdCBhc3NpZ25lZSBpbmZvXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgZnJlc2hUYXNrID0gYXdhaXQgdGFza1NlcnZpY2UuZ2V0VGFzayh0YXNrLnRhc2tfaWQpO1xyXG4gICAgICAgIGlmIChmcmVzaFRhc2spIHtcclxuICAgICAgICAgIHNldFRhc2soZnJlc2hUYXNrKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgc2V0VGFzayh0YXNrKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZnJlc2ggdGFzayBkYXRhLCB1c2luZyBwcm92aWRlZCB0YXNrOicsIGVycm9yKTtcclxuICAgICAgICBzZXRUYXNrKHRhc2spO1xyXG4gICAgICB9XHJcbiAgICAgIHNldElzUmVhc3NpZ25Nb2RlKHRydWUpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGl0ZW1JZCAmJiBpdGVtVHlwZSA9PT0gJ2FwcGxpY2F0aW9uJykge1xyXG4gICAgICAvLyBGZXRjaCB0YXNrIGZvciBhcHBsaWNhdGlvblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nVGFzayA9IGF3YWl0IHRhc2tTZXJ2aWNlLmdldFRhc2tGb3JBcHBsaWNhdGlvbihpdGVtSWQpO1xyXG4gICAgICAgIGlmKGV4aXN0aW5nVGFzayAmJiBleGlzdGluZ1Rhc2sudGFza19pZCkge1xyXG4gICAgICAgICAgc2V0VGFzayhleGlzdGluZ1Rhc2spO1xyXG4gICAgICAgICAgc2V0SXNSZWFzc2lnbk1vZGUodHJ1ZSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHNldElzUmVhc3NpZ25Nb2RlKGZhbHNlKTtcclxuICAgICAgICAgIHNldFRhc2sobnVsbCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGV4aXN0aW5nIHRhc2s6JywgZXJyb3IpO1xyXG4gICAgICAgIHNldElzUmVhc3NpZ25Nb2RlKGZhbHNlKTtcclxuICAgICAgICBzZXRUYXNrKG51bGwpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBObyBleGlzdGluZyB0YXNrLCBuZXcgYXNzaWdubWVudCBtb2RlXHJcbiAgICAgIHNldElzUmVhc3NpZ25Nb2RlKGZhbHNlKTtcclxuICAgICAgc2V0VGFzayhudWxsKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGZldGNoT2ZmaWNlcnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0YXNrU2VydmljZS5nZXRPZmZpY2VycygpO1xyXG4gICAgICBjb25zdCBvZmZpY2Vyc0RhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdO1xyXG4gICAgICBzZXRPZmZpY2VycyhvZmZpY2Vyc0RhdGEpO1xyXG5cclxuICAgICAgaWYgKG9mZmljZXJzRGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ05vIG9mZmljZXJzIGZvdW5kIGZvciB0YXNrIGFzc2lnbm1lbnQnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgb2ZmaWNlcnM6JywgZXJyb3IpO1xyXG4gICAgICBzZXRPZmZpY2VycyhbXSk7XHJcbiAgICAgIHNob3dFcnJvcignRmFpbGVkIHRvIGxvYWQgb2ZmaWNlcnMuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRUYXNrVHlwZUZyb21JdGVtVHlwZSA9IChpdGVtVHlwZTogc3RyaW5nKTogVGFza1R5cGUgPT4ge1xyXG4gICAgc3dpdGNoIChpdGVtVHlwZSkge1xyXG4gICAgICBjYXNlICdhcHBsaWNhdGlvbic6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLkVWQUxVQVRJT047XHJcbiAgICAgIGNhc2UgJ2RhdGFfYnJlYWNoJzpcclxuICAgICAgICByZXR1cm4gVGFza1R5cGUuREFUQV9CUkVBQ0g7XHJcbiAgICAgIGNhc2UgJ2NvbXBsYWludCc6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLkNPTVBMQUlOVDtcclxuICAgICAgY2FzZSAnaW5zcGVjdGlvbic6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLklOU1BFQ1RJT047XHJcbiAgICAgIGNhc2UgJ2RvY3VtZW50X3Jldmlldyc6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLkRPQ1VNRU5UX1JFVklFVztcclxuICAgICAgY2FzZSAndGFzayc6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLkFQUExJQ0FUSU9OOyAvLyBEZWZhdWx0IGZvciBleGlzdGluZyB0YXNrc1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5BUFBMSUNBVElPTjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRUYXNrVGl0bGUgPSAoaXRlbVR5cGU6IHN0cmluZywgaXRlbVRpdGxlPzogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICAgIGNvbnN0IGJhc2VUaXRsZSA9IGl0ZW1UaXRsZSB8fCAnVW50aXRsZWQnO1xyXG4gICAgc3dpdGNoIChpdGVtVHlwZSkge1xyXG4gICAgICBjYXNlICdhcHBsaWNhdGlvbic6XHJcbiAgICAgICAgcmV0dXJuIGBBcHBsaWNhdGlvbiBFdmFsdWF0aW9uOiAke2Jhc2VUaXRsZX1gO1xyXG4gICAgICBjYXNlICdkYXRhX2JyZWFjaCc6XHJcbiAgICAgICAgcmV0dXJuIGBEYXRhIEJyZWFjaCBJbnZlc3RpZ2F0aW9uOiAke2Jhc2VUaXRsZX1gO1xyXG4gICAgICBjYXNlICdjb21wbGFpbnQnOlxyXG4gICAgICAgIHJldHVybiBgQ29tcGxhaW50IFJldmlldzogJHtiYXNlVGl0bGV9YDtcclxuICAgICAgY2FzZSAnaW5zcGVjdGlvbic6XHJcbiAgICAgICAgcmV0dXJuIGBJbnNwZWN0aW9uIFRhc2s6ICR7YmFzZVRpdGxlfWA7XHJcbiAgICAgIGNhc2UgJ2RvY3VtZW50X3Jldmlldyc6XHJcbiAgICAgICAgcmV0dXJuIGBEb2N1bWVudCBSZXZpZXc6ICR7YmFzZVRpdGxlfWA7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIGBUYXNrOiAke2Jhc2VUaXRsZX1gO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldFRhc2tEZXNjcmlwdGlvbiA9IChpdGVtVHlwZTogc3RyaW5nLCBpdGVtVGl0bGU/OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgY29uc3QgYmFzZVRpdGxlID0gaXRlbVRpdGxlIHx8ICdpdGVtJztcclxuICAgIHN3aXRjaCAoaXRlbVR5cGUpIHtcclxuICAgICAgY2FzZSAnYXBwbGljYXRpb24nOlxyXG4gICAgICAgIHJldHVybiBgRXZhbHVhdGUgYW5kIHJldmlldyBhcHBsaWNhdGlvbiAke2Jhc2VUaXRsZX0gZm9yIGNvbXBsaWFuY2UgYW5kIGFwcHJvdmFsLmA7XHJcbiAgICAgIGNhc2UgJ2RhdGFfYnJlYWNoJzpcclxuICAgICAgICByZXR1cm4gYEludmVzdGlnYXRlIGFuZCBhc3Nlc3MgZGF0YSBicmVhY2ggcmVwb3J0ICR7YmFzZVRpdGxlfSBmb3IgcmVndWxhdG9yeSBjb21wbGlhbmNlLmA7XHJcbiAgICAgIGNhc2UgJ2NvbXBsYWludCc6XHJcbiAgICAgICAgcmV0dXJuIGBSZXZpZXcgYW5kIHJlc29sdmUgY29tcGxhaW50ICR7YmFzZVRpdGxlfSBhY2NvcmRpbmcgdG8gcmVndWxhdG9yeSBwcm9jZWR1cmVzLmA7XHJcbiAgICAgIGNhc2UgJ2luc3BlY3Rpb24nOlxyXG4gICAgICAgIHJldHVybiBgQ29uZHVjdCBpbnNwZWN0aW9uIGZvciAke2Jhc2VUaXRsZX0gdG8gZW5zdXJlIHJlZ3VsYXRvcnkgY29tcGxpYW5jZS5gO1xyXG4gICAgICBjYXNlICdkb2N1bWVudF9yZXZpZXcnOlxyXG4gICAgICAgIHJldHVybiBgUmV2aWV3IGFuZCB2YWxpZGF0ZSBkb2N1bWVudCAke2Jhc2VUaXRsZX0gZm9yIGFjY3VyYWN5IGFuZCBjb21wbGlhbmNlLmA7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIGBQcm9jZXNzIGFuZCByZXZpZXcgJHtiYXNlVGl0bGV9LmA7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQXNzaWduID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8gRm9yIG5ldyBhc3NpZ25tZW50cywgb2ZmaWNlciBzZWxlY3Rpb24gaXMgcmVxdWlyZWRcclxuICAgIC8vIEZvciByZWFzc2lnbm1lbnRzLCBhbGxvdyB1bmFzc2lnbmluZyAoZW1wdHkgc2VsZWN0aW9uKVxyXG4gICAgaWYgKCFpc1JlYXNzaWduTW9kZSAmJiAhc2VsZWN0ZWRPZmZpY2VyKSB7XHJcbiAgICAgIHNob3dFcnJvcignUGxlYXNlIHNlbGVjdCBhbiBvZmZpY2VyJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWR1ZURhdGUpIHtcclxuICAgICAgc2hvd0Vycm9yKCdQbGVhc2Ugc2VsZWN0IGEgZHVlIGRhdGUnKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICghaXNSZWFzc2lnbk1vZGUgJiYgIWl0ZW1JZCkge1xyXG4gICAgICBzaG93RXJyb3IoJ0l0ZW0gSUQgaXMgbWlzc2luZycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0QXNzaWduaW5nKHRydWUpO1xyXG5cclxuICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmIChpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaykge1xyXG4gICAgICAgIC8vIFJlYXNzaWduIGV4aXN0aW5nIHRhc2sgKG9yIHVuYXNzaWduIGlmIHNlbGVjdGVkT2ZmaWNlciBpcyBlbXB0eSlcclxuICAgICAgICBhd2FpdCB0YXNrU2VydmljZS5yZWFzc2lnblRhc2sobG9hZFRhc2sudGFza19pZCwge1xyXG4gICAgICAgICAgYXNzaWduZWRUbzogc2VsZWN0ZWRPZmZpY2VyIHx8IHVuZGVmaW5lZCwgLy8gdW5kZWZpbmVkIGZvciB1bmFzc2lnblxyXG4gICAgICAgICAgY29tbWVudDogY29tbWVudC50cmltKCkgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgICAgZHVlX2RhdGU6IGR1ZURhdGUsXHJcbiAgICAgICAgICBwcmlvcml0eTogVGFza1ByaW9yaXR5Lk1FRElVTVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHNob3dTdWNjZXNzKHNlbGVjdGVkT2ZmaWNlciA/ICdUYXNrIHJlYXNzaWduZWQgc3VjY2Vzc2Z1bGx5JyA6ICdUYXNrIHVuYXNzaWduZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgICAgb25SZWFzc2lnblN1Y2Nlc3M/LigpO1xyXG5cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zdCB0YXNrRGF0YTogQ3JlYXRlVGFza0R0byAgPSB7XHJcbiAgICAgICAgICB0YXNrX3R5cGU6IGdldFRhc2tUeXBlRnJvbUl0ZW1UeXBlKGl0ZW1UeXBlKSxcclxuICAgICAgICAgIHRpdGxlOiBnZXRUYXNrVGl0bGUoaXRlbVR5cGUsIGl0ZW1UaXRsZSksXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZ2V0VGFza0Rlc2NyaXB0aW9uKGl0ZW1UeXBlLCBpdGVtVGl0bGUpLFxyXG4gICAgICAgICAgcHJpb3JpdHk6IFRhc2tQcmlvcml0eS5NRURJVU0sXHJcbiAgICAgICAgICBzdGF0dXM6IFRhc2tTdGF0dXMuUEVORElORyxcclxuICAgICAgICAgIGVudGl0eV90eXBlOiBpdGVtVHlwZSxcclxuICAgICAgICAgIGVudGl0eV9pZDogaXRlbUlkISxcclxuICAgICAgICAgIGFzc2lnbmVkX3RvOiBzZWxlY3RlZE9mZmljZXIsXHJcbiAgICAgICAgICBkdWVfZGF0ZTogZHVlRGF0ZSxcclxuICAgICAgICAgIG1ldGFkYXRhOiB7XHJcbiAgICAgICAgICAgIGNvbW1lbnQ6IGNvbW1lbnQudHJpbSgpIHx8IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgb3JpZ2luYWxfaXRlbV90aXRsZTogaXRlbVRpdGxlLFxyXG4gICAgICAgICAgICBhc3NpZ25tZW50X2NvbnRleHQ6ICdtYW51YWxfYXNzaWdubWVudCdcclxuICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIC8vIEZvciBvdGhlciBlbnRpdHkgdHlwZXMsIGNyZWF0ZSBhIG5ldyB0YXNrXHJcbiAgICAgICAgYXdhaXQgY3JlYXRlTmV3VGFzayh0YXNrRGF0YSk7XHJcbiAgICAgICAgb25SZWFzc2lnblN1Y2Nlc3M/LigpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBvbkNsb3NlKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciAke2lzUmVhc3NpZ25Nb2RlID8gJ3JlYXNzaWduaW5nJyA6ICdjcmVhdGluZyBhc3NpZ25tZW50J30gdGFzazpgLCBlcnJvcik7XHJcbiAgICAgIHNob3dFcnJvcihgRmFpbGVkIHRvICR7aXNSZWFzc2lnbk1vZGUgPyAncmVhc3NpZ24nIDogJ2Fzc2lnbid9IHRhc2tgKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldEFzc2lnbmluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY3JlYXRlIGEgbmV3IHRhc2tcclxuICBhc3luYyBmdW5jdGlvbiBjcmVhdGVOZXdUYXNrKHRhc2tEYXRhOiBDcmVhdGVUYXNrRHRvKSB7XHJcbiAgICBjb25zdCBjcmVhdGVkVGFzayA9IGF3YWl0IHRhc2tTZXJ2aWNlLmNyZWF0ZVRhc2sodGFza0RhdGEpO1xyXG4gICAgc2hvd1N1Y2Nlc3MoYFN1Y2Nlc3NmdWxseSBhc3NpZ25lZCAke2l0ZW1UeXBlLnJlcGxhY2UoJ18nLCAnICcpfSB0byBvZmZpY2VyYCk7XHJcbiAgICBvbkFzc2lnblN1Y2Nlc3M/LigpO1xyXG4gIH1cclxuICBcclxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIHAtNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyB3LWZ1bGwgbWF4LXctMnhsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbCBzaGFkb3ctMnhsXCI+XHJcbiAgICAgICAgey8qIEhlYWRlciAtIEZpeGVkICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktOTAwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9e2lzUmVhc3NpZ25Nb2RlID8gXCJyaS11c2VyLXNoYXJlZC1saW5lIHRleHQtd2hpdGUgdGV4dC1sZ1wiIDogXCJyaS10YXNrLWxpbmUgdGV4dC13aGl0ZSB0ZXh0LWxnXCJ9PjwvaT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxyXG4gICAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gJ1JlYXNzaWduIFRhc2snIDogYENyZWF0ZSBUYXNrIGZvciAke2l0ZW1UeXBlLnJlcGxhY2UoJ18nLCAnICcpLnRvVXBwZXJDYXNlKCl9YH1cclxuICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdUcmFuc2ZlciB0YXNrIHRvIGFub3RoZXIgb2ZmaWNlcicgOiAnQXNzaWduIHRoaXMgaXRlbSB0byBhbiBvZmZpY2VyIGZvciBwcm9jZXNzaW5nJ31cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMzAwXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktY2xvc2UtbGluZSB0ZXh0LXhsXCI+PC9pPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBDb250ZW50IC0gU2Nyb2xsYWJsZSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02IGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgey8qIFRhc2sgRGV0YWlscyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctYmx1ZS01MCBkYXJrOmJnLWJsdWUtOTAwLzIwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCBkYXJrOmJvcmRlci1ibHVlLTgwMFwiPlxyXG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIGRhcms6dGV4dC1ibHVlLTEwMCBtYi0yIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktdGFzay1saW5lIG1yLTJcIj48L2k+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gJ1Rhc2sgRGV0YWlsczonIDogJ1Rhc2sgdG8gYmUgQ3JlYXRlZDonfVxyXG4gICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDAgZGFyazp0ZXh0LWJsdWUtMzAwIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gbG9hZFRhc2s/LnRpdGxlIDogZ2V0VGFza1RpdGxlKGl0ZW1UeXBlLCBpdGVtVGl0bGUpfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMSBicmVhay13b3JkcyB3aGl0ZXNwYWNlLXByZS13cmFwIG92ZXJmbG93LXdyYXAtYW55d2hlcmUgaHlwaGVucy1hdXRvXCI+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gbG9hZFRhc2s/LmRlc2NyaXB0aW9uIDogZ2V0VGFza0Rlc2NyaXB0aW9uKGl0ZW1UeXBlLCBpdGVtVGl0bGUpfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaz8udGFza19udW1iZXIgJiYgKFxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgVGFzayAje2xvYWRUYXNrPy50YXNrX251bWJlcn1cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaz8uYXNzaWduZWUgJiYgKFxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgQ3VycmVudGx5IGFzc2lnbmVkIHRvOiB7bG9hZFRhc2suYXNzaWduZWUuZmlyc3RfbmFtZX0ge2xvYWRUYXNrLmFzc2lnbmVlLmxhc3RfbmFtZX1cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogT2ZmaWNlciBTZWxlY3Rpb24gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cclxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdSZWFzc2lnbiB0byBPZmZpY2VyIConIDogJ0Fzc2lnbiB0byBPZmZpY2VyIConfVxyXG4gICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTQgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgIExvYWRpbmcgb2ZmaWNlcnMuLi5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRPZmZpY2VyfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZE9mZmljZXIoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXByaW1hcnkgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj57aXNSZWFzc2lnbk1vZGUgPyAnU2VsZWN0IGFuIG9mZmljZXIgb3IgdW5hc3NpZ24uLi4nIDogJ1NlbGVjdCBhbiBvZmZpY2VyLi4uJ308L29wdGlvbj5cclxuICAgICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICDwn5qrIFVuYXNzaWduIFRhc2sgKFJlbW92ZSBhc3NpZ25lZSlcclxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAge29mZmljZXJzLm1hcCgob2ZmaWNlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17b2ZmaWNlci51c2VyX2lkfSB2YWx1ZT17b2ZmaWNlci51c2VyX2lkfT5cclxuICAgICAgICAgICAgICAgICAgICB7b2ZmaWNlci5maXJzdF9uYW1lfSB7b2ZmaWNlci5sYXN0X25hbWV9IC0ge29mZmljZXIuZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAge29mZmljZXIuZGVwYXJ0bWVudCA/IGAgKCR7dHlwZW9mIG9mZmljZXIuZGVwYXJ0bWVudCA9PT0gJ3N0cmluZycgPyBvZmZpY2VyLmRlcGFydG1lbnQgOiBvZmZpY2VyLmRlcGFydG1lbnQubmFtZX0pYCA6ICcnfVxyXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIER1ZSBEYXRlICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICBEdWUgRGF0ZSAqXHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17ZHVlRGF0ZX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldER1ZURhdGUoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIG1pbj17bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19IC8vIFByZXZlbnQgcGFzdCBkYXRlc1xyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci1wcmltYXJ5IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMTAwXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBDb21tZW50ICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICB7aXNSZWFzc2lnbk1vZGUgPyAnUmVhc3NpZ25tZW50IE5vdGVzIChPcHRpb25hbCknIDogJ1Rhc2sgTm90ZXMgKE9wdGlvbmFsKSd9XHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgICAgIHZhbHVlPXtjb21tZW50fVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29tbWVudChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2lzUmVhc3NpZ25Nb2RlID8gJ0FkZCBhbnkgbm90ZXMgYWJvdXQgdGhpcyByZWFzc2lnbm1lbnQuLi4nIDogJ0FkZCBhbnkgc3BlY2lmaWMgaW5zdHJ1Y3Rpb25zIG9yIGNvbnRleHQgZm9yIHRoaXMgbG9hZFRhc2suLi4nfVxyXG4gICAgICAgICAgICAgIHJvd3M9ezN9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXByaW1hcnkgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFNlbGVjdGVkIE9mZmljZXIgU3VtbWFyeSAqL31cclxuICAgICAgICAgIHtzZWxlY3RlZE9mZmljZXIgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWdyZWVuLTUwIGRhcms6YmctZ3JlZW4tOTAwLzIwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTgwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi05MDAgZGFyazp0ZXh0LWdyZWVuLTEwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBTZWxlY3RlZCBPZmZpY2VyOlxyXG4gICAgICAgICAgICAgIDwvaDQ+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cclxuICAgICAgICAgICAgICAgIHsoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBvZmZpY2VyID0gb2ZmaWNlcnMuZmluZChvID0+IG8udXNlcl9pZCA9PT0gc2VsZWN0ZWRPZmZpY2VyKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIG9mZmljZXIgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgIHtvZmZpY2VyLmZpcnN0X25hbWV9IHtvZmZpY2VyLmxhc3RfbmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDxiciAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAge29mZmljZXIuZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICB7b2ZmaWNlci5kZXBhcnRtZW50ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBEZXBhcnRtZW50OiB7dHlwZW9mIG9mZmljZXIuZGVwYXJ0bWVudCA9PT0gJ3N0cmluZycgPyBvZmZpY2VyLmRlcGFydG1lbnQgOiBvZmZpY2VyLmRlcGFydG1lbnQubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICApIDogJ09mZmljZXIgbm90IGZvdW5kJztcclxuICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAge2R1ZURhdGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPkR1ZSBEYXRlOjwvc3Ryb25nPiB7bmV3IERhdGUoZHVlRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBGb290ZXIgLSBGaXhlZCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zIHAtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktOTAwXCI+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS02MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBc3NpZ259XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXsoIWlzUmVhc3NpZ25Nb2RlICYmICFzZWxlY3RlZE9mZmljZXIpIHx8ICFkdWVEYXRlIHx8IGFzc2lnbmluZ31cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBiZy1wcmltYXJ5IGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgcm91bmRlZC1tZCBob3ZlcjpiZy1wcmltYXJ5LWRhcmsgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHthc3NpZ25pbmcgPyAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTJcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdSZWFzc2lnbmluZy4uLicgOiAnQ3JlYXRpbmcgVGFzay4uLid9XHJcbiAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT17aXNSZWFzc2lnbk1vZGUgPyBcInJpLXVzZXItc2hhcmVkLWxpbmUgbXItMlwiIDogXCJyaS10YXNrLWxpbmUgbXItMlwifT48L2k+XHJcbiAgICAgICAgICAgICAgICB7aXNSZWFzc2lnbk1vZGUgPyAnUmVhc3NpZ24gVGFzaycgOiAnQ3JlYXRlIFRhc2snfVxyXG4gICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFzc2lnbk1vZGFsO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVRvYXN0IiwidGFza1NlcnZpY2UiLCJUYXNrVHlwZSIsIlRhc2tQcmlvcml0eSIsIlRhc2tTdGF0dXMiLCJBc3NpZ25Nb2RhbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJpdGVtSWQiLCJpdGVtVHlwZSIsIml0ZW1UaXRsZSIsIm9uQXNzaWduU3VjY2VzcyIsInRhc2siLCJvblJlYXNzaWduU3VjY2VzcyIsInNob3dTdWNjZXNzIiwic2hvd0Vycm9yIiwib2ZmaWNlcnMiLCJzZXRPZmZpY2VycyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiYXNzaWduaW5nIiwic2V0QXNzaWduaW5nIiwic2VsZWN0ZWRPZmZpY2VyIiwic2V0U2VsZWN0ZWRPZmZpY2VyIiwiY29tbWVudCIsInNldENvbW1lbnQiLCJkdWVEYXRlIiwic2V0RHVlRGF0ZSIsImxvYWRUYXNrIiwic2V0VGFzayIsImlzUmVhc3NpZ25Nb2RlIiwic2V0SXNSZWFzc2lnbk1vZGUiLCJmZXRjaE9mZmljZXJzIiwiZmV0Y2hFeGlzdGluZ1Rhc2tzIiwiYXNzaWduZWRfdG8iLCJkdWVfZGF0ZSIsInNwbGl0IiwiZnJlc2hUYXNrIiwiZ2V0VGFzayIsInRhc2tfaWQiLCJlcnJvciIsImNvbnNvbGUiLCJleGlzdGluZ1Rhc2siLCJnZXRUYXNrRm9yQXBwbGljYXRpb24iLCJyZXNwb25zZSIsImdldE9mZmljZXJzIiwib2ZmaWNlcnNEYXRhIiwiZGF0YSIsImxlbmd0aCIsIndhcm4iLCJnZXRUYXNrVHlwZUZyb21JdGVtVHlwZSIsIkVWQUxVQVRJT04iLCJEQVRBX0JSRUFDSCIsIkNPTVBMQUlOVCIsIklOU1BFQ1RJT04iLCJET0NVTUVOVF9SRVZJRVciLCJBUFBMSUNBVElPTiIsImdldFRhc2tUaXRsZSIsImJhc2VUaXRsZSIsImdldFRhc2tEZXNjcmlwdGlvbiIsImhhbmRsZUFzc2lnbiIsInJlYXNzaWduVGFzayIsImFzc2lnbmVkVG8iLCJ1bmRlZmluZWQiLCJ0cmltIiwicHJpb3JpdHkiLCJNRURJVU0iLCJ0YXNrRGF0YSIsInRhc2tfdHlwZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGF0dXMiLCJQRU5ESU5HIiwiZW50aXR5X3R5cGUiLCJlbnRpdHlfaWQiLCJtZXRhZGF0YSIsIm9yaWdpbmFsX2l0ZW1fdGl0bGUiLCJhc3NpZ25tZW50X2NvbnRleHQiLCJjcmVhdGVOZXdUYXNrIiwiY3JlYXRlZFRhc2siLCJjcmVhdGVUYXNrIiwicmVwbGFjZSIsImRpdiIsImNsYXNzTmFtZSIsImkiLCJoMyIsInRvVXBwZXJDYXNlIiwicCIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiaDQiLCJ0YXNrX251bWJlciIsImFzc2lnbmVlIiwiZmlyc3RfbmFtZSIsImxhc3RfbmFtZSIsImxhYmVsIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJtYXAiLCJvZmZpY2VyIiwidXNlcl9pZCIsImVtYWlsIiwiZGVwYXJ0bWVudCIsIm5hbWUiLCJpbnB1dCIsIm1pbiIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInRleHRhcmVhIiwicGxhY2Vob2xkZXIiLCJyb3dzIiwiZmluZCIsIm8iLCJiciIsInN0cm9uZyIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/AssignModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = saved2faUser ? JSON.parse(saved2faUser) : null;\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/activityNotesService.ts":
/*!**********************************************!*\
  !*** ./src/services/activityNotesService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityNotesService: () => (/* binding */ activityNotesService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nclass ActivityNotesService {\n    async create(data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(this.baseUrl, data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findAll(query) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(this.baseUrl, {\n            params: query\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntity(entityType, entityId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntityAndStep(entityType, entityId, step) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId, \"/step/\").concat(step));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findOne(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/\").concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async update(id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async archive(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id, \"/archive\"));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async softDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/soft\"));\n    }\n    async hardDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/hard\"));\n    }\n    // Specialized methods for evaluation workflow\n    async createEvaluationComment(applicationId, step, comment, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/evaluation-comment\"), {\n            applicationId,\n            step,\n            comment,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async createStatusUpdate(applicationId, statusChange, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/status-update\"), {\n            applicationId,\n            statusChange,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    // Helper methods for common use cases\n    async getEvaluationComments(applicationId, step) {\n        const query = {\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'evaluation_comment',\n            status: 'active'\n        };\n        if (step) {\n            query.step = step;\n        }\n        return this.findAll(query);\n    }\n    async getApplicationNotes(applicationId) {\n        return this.findByEntity('application', applicationId);\n    }\n    async getApplicationStatusUpdates(applicationId) {\n        return this.findAll({\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'status_update',\n            status: 'active'\n        });\n    }\n    // Data breach specific methods\n    async createDataBreachAction(reportId, action, note, metadata) {\n        return this.create({\n            entity_type: 'data-breach',\n            entity_id: reportId,\n            note: note,\n            note_type: action === 'close' ? 'status_update' : 'data_breach_action',\n            category: 'data_breach_management',\n            metadata: {\n                action,\n                timestamp: new Date().toISOString(),\n                ...metadata\n            },\n            priority: action === 'close' ? 'high' : 'normal',\n            is_internal: false\n        });\n    }\n    async getDataBreachNotes(reportId) {\n        return this.findByEntity('data-breach', reportId);\n    }\n    constructor(){\n        this.baseUrl = '/activity-notes';\n    }\n}\nconst activityNotesService = new ActivityNotesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/activityNotesService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});