<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Add Role - User Management</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer components {
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }
    
        .enhanced-select {
          @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
        }
    
        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }
    
        .main-button {
          @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
        }
    
        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }
    
        .custom-input {
          @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
        }
    
        .form-section {
          @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
        }
    
        .inner-form-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
        }
    
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
        }
    
        .form-group {
          @apply mb-6;
        }
    
        .photo-upload {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0;
        }
    
        .profile-icon {
          @apply min-h-40 max-h-60 max-w-60 text-gray-300;
        }
    
        .upload-info {
          @apply flex-col space-y-2;
        }
    
        .file-input {
          @apply relative bg-white shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50;
        }
    
        .helper-text {
          @apply text-xs text-gray-500;
        }
    
      }
    
      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    
    </style>
    <style>
      body { font-family: 'Inter', sans-serif; background-color: #f9fafb; }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar (copied from user-management.html) -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a href="../index.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-dashboard-line"></i></div>Dashboard
            </a>
            <a href="../license/license-management.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-key-line"></i></div>License Management
            </a>
            <a href="../spectrum/spectrum-management.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-wifi-line"></i></div>Spectrum Management
            </a>
            <a href="../financial/transaction-management.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-bank-line"></i></div>Financial Transactions
            </a>
            <a href="../reports/reports.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-bar-chart-line"></i></div>Reports & Analytics
            </a>
          </div>
          <div class="mt-8">
            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Settings</h3>
            <div class="mt-2 space-y-1">
              <a href="user-management.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
                <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-user-settings-line"></i></div>User Management
              </a>
              <a href="../audit-trail.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
                <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-shield-line"></i></div>Audit Trail
              </a>
              <a href="../help-support.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
                <div class="w-5 h-5 flex items-center justify-center mr-3"><i class="ri-question-line"></i></div>Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img class="h-10 w-10 rounded-full" src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <h1 class="text-2xl font-semibold text-gray-900">Add New Role</h1>
            <a href="user-management.html" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
              <div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-arrow-left-line"></i></div>Back to Roles
            </a>
          </div>
        </header>
        <!-- Main content area -->
        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">

            <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 md:p-10">
              <form id="addRolePageForm" class="space-y-10">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label for="roleName" class="block text-sm font-semibold text-gray-700 mb-1">Role Name</label>
                    <input type="text" id="roleName" name="roleName" class="appearance-none block w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base transition" required />
                  </div>
                  <div>
                    <label for="roleDescription" class="block text-sm font-semibold text-gray-700 mb-1">Description</label>
                    <input type="text" id="roleDescription" name="roleDescription" class="appearance-none block w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base transition" />
                  </div>
                </div>
                <!-- Permissions Section -->
                <div>
                  <h2 class="text-xl font-bold text-gray-900 mb-6 border-b pb-2 flex items-center gap-2"><i class="ri-lock-2-line text-primary"></i> Assign Permissions</h2>
                  <div class="space-y-8">
                    <!-- Users Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-user-line text-primary"></i> Users</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="users_create" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Create User</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="users_read" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>View Users</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="users_update" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Edit User</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="users_delete" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Delete User</span>
                        </label>
                      </div>
                    </div>
                    <!-- Licenses Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-key-line text-primary"></i> Licenses</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="licenses_create" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Create License</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="licenses_read" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>View Licenses</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="licenses_update" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Edit License</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="licenses_delete" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Delete License</span>
                        </label>
                      </div>
                    </div>
                    <!-- Spectrum Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-wifi-line text-primary"></i> Spectrum</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="spectrum_create" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Create Spectrum</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="spectrum_read" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>View Spectrum</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="spectrum_update" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Edit Spectrum</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="spectrum_delete" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Delete Spectrum</span>
                        </label>
                      </div>
                    </div>
                    <!-- Financial Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-bank-line text-primary"></i> Financial</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="financial_create" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Create Transaction</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="financial_read" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>View Transactions</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="financial_update" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Edit Transaction</span>
                        </label>
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="financial_delete" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Delete Transaction</span>
                        </label>
                      </div>
                    </div>
                    <!-- Reports Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-bar-chart-line text-primary"></i> Reports</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="reports_read" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>View Reports</span>
                        </label>
                      </div>
                    </div>
                    <!-- Settings Section -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-100">
                      <h3 class="font-semibold text-gray-800 mb-3 flex items-center gap-2"><i class="ri-settings-3-line text-primary"></i> Settings</h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center gap-2 text-gray-700">
                          <input type="checkbox" name="permissions" value="settings_update" class="form-checkbox h-5 w-5 text-primary border-gray-300 rounded focus:ring-primary">
                          <span>Update Settings</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex justify-end gap-4 pt-6 border-t mt-10">
                  <a href="user-management.html" class="inline-flex items-center justify-center py-2 px-5 border border-gray-300 shadow-sm text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition">Cancel</a>
                  <button type="submit" class="inline-flex items-center justify-center py-2 px-6 border border-transparent shadow-sm text-base font-semibold rounded-lg text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition">Save Role</button>
                </div>
              </form>
            </div>
          </div>
        </main>
      </div>
    </div>
    <script>
      document.getElementById('addRolePageForm').onsubmit = function(e) {
        e.preventDefault();
        alert('Role created! (Demo only)');
        window.location.href = 'user-management.html';
      };
    </script>
  </body>
</html>
