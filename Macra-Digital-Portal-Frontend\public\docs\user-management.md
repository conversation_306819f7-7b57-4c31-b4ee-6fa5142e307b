# User Management Guide (Admin Only)

## Table of Contents

1. [Overview](#overview)
2. [User Roles and Permissions](#user-roles-and-permissions)
3. [Managing User Accounts](#managing-user-accounts)
4. [User Profile Management](#user-profile-management)
5. [Security and Account Policies](#security-and-account-policies)
6. [Troubleshooting](#troubleshooting)

## Overview

This section is only available to MACRA administrators and authorized staff members. Regular users (customers) cannot access these features.

### Who Can Manage Users?

- **System Administrators** - Full access to all user management features


## User Roles and Permissions

### Available User Roles

- **Administrator** - Full system access and user management
- **Director General** - Executive oversight and final approvals
- **Officer** - Standard MACRA staff with application processing rights
- **Finance** - Financial processing and invoice management
- **Customer** - External users applying for licenses

### Role Assignment Rules

- **Staff Accounts** - Must use @macra.mw email addresses
- **Customer Accounts** - Can use any valid email address
- **Automatic Role Assignment** - System automatically assigns roles based on email domain
- **Manual Override** - Administrators can manually assign specific roles

## Managing User Accounts

### Viewing All Users

**What You'll See:**

- **User List** - Table showing all registered users
- **Search Function** - Find users by name, email, or phone number
- **Filter Options** - Filter by user type, status, or registration date
- **User Details** - Click on any user to see their full profile

#### Screenshot: Users List

![Users List](docs/images/UMListAll.png)

#### Screenshot: Users List (with filters applied)

![Users List (with filters applied)](docs/images/UMListFiltered.png)

### Creating New User Accounts

**When to Create Accounts:**

- New MACRA staff members
- External consultants or partners
- Special access accounts for government officials
- Test accounts for system testing

**How to Create a New User:**

1. Click **"Add New User"** button
2. Fill in the required information:
   - **Personal Details**: Name, email, phone number
   - **Account Type**: Select appropriate role (Staff, Officer, Admin, etc.)
   - **Department**: Choose the user's department
   - **Access Level**: Set permissions based on their role
3. Click **"Create Account"** button
4. The new user will receive an email with login instructions

#### Screenshot: Add User Form

![Add User Form](docs/images/UMCreateNew.png)

### Managing Existing Users

**Updating User Information:**

1. Find the user in the list
2. Click **"Edit"** or **"View Details"**
3. Update necessary information
4. Save changes

#### Screenshot: Edit User

![Edit User](docs/images/UMEdit.png)

**Deactivating User Accounts:**

1. Locate the user account
2. Click **"Deactivate"** or change status to "Inactive"
3. Confirm the action
4. The user will no longer be able to login

**Reactivating User Accounts:**

1. Find the inactive user
2. Click **"Activate"** or change status to "Active"
3. The user can now login again

#### Screenshot: Deactivate/ Reactivate User

![Deactivate/ Reactivate User](docs/images/UMDeactivate.png)

**Deleting a User (Soft Delete):**

1. Locate the user account
2. Click **"Delete"**
3. Confirm the action
4. The user will be removed from the system

#### Screenshot: Delete User

![Delete User](docs/images/um-delete.png)

## User Profile Management

### Profile Information Management

**What Admins Can Manage:**
- **Personal Details** - Name, email, phone number
- **Account Status** - Active, inactive, suspended
- **Role Assignments** - Change user roles and permissions
- **Avatar/Profile Pictures** - Upload, update, or remove user avatars
- **Password Management** - Reset passwords for users

### Profile Update Process

1. **Access User Profile** - Click on user from the list
2. **Edit Information** - Update necessary fields
3. **Save Changes** - Confirm updates
4. **Notify User** - System sends email notification of changes

### Avatar Management

**Uploading User Avatars:**
1. Go to user profile
2. Click **"Upload Avatar"** or **"Change Picture"**
3. Select image file (JPG, PNG, GIF supported)
4. Crop/resize if needed
5. Save changes

**Removing Avatars:**
1. Access user profile
2. Click **"Remove Avatar"**
3. Confirm removal
4. Default avatar will be displayed

### Password Management

**Resetting User Passwords:**
1. Find user in the list
2. Click **"Reset Password"**
3. Choose reset method:
   - **Email Reset Link** - User receives email with reset instructions
   - **Temporary Password** - Generate temporary password for user
4. Notify user of the reset

## Security and Account Policies

### Security Best Practices

- 🔐 **Regular Reviews** - Review user accounts monthly
- 🚫 **Remove Unused Accounts** - Deactivate accounts of former staff
- 👥 **Appropriate Access** - Only give necessary permissions
- 📝 **Document Changes** - Keep records of account modifications
- 🔄 **Regular Backups** - Ensure user data is backed up
- 🔍 **Audit Trail** - Monitor user management activities
- 🚨 **Security Alerts** - Set up notifications for suspicious activities

### User Account Policies

- **Staff Accounts** - Must use @macra.mw email addresses
- **Customer Accounts** - Can use any valid email address
- **Password Requirements** - Enforce strong password policies (8+ characters, mixed case, numbers, symbols)
- **Two-Factor Authentication** - Enabled for all staff accounts
- **Session Management** - 30-minute timeout for inactive sessions
- **Account Lockout** - Automatic lockout after failed login attempts

### Data Protection

- **Personal Information** - Handle user data according to privacy policies
- **Data Retention** - Follow data retention guidelines
- **Access Logs** - Maintain logs of who accessed what information
- **Data Export** - Provide data export capabilities when required

## Troubleshooting

### Common User Management Issues

#### "Cannot create user account"
- ✅ Check if email address already exists
- ✅ Verify email format is correct
- ✅ Ensure required fields are filled
- ✅ Check role permissions

#### "User cannot login after creation"
- ✅ Verify account status is "Active"
- ✅ Check if email verification is required
- ✅ Confirm password was set correctly
- ✅ Verify role assignments

#### "Cannot update user information"
- ✅ Check admin permissions
- ✅ Verify user exists in system
- ✅ Ensure no conflicting data (duplicate emails)
- ✅ Check for system validation errors

#### "Avatar upload fails"
- ✅ Check file size (max 5MB)
- ✅ Verify file format (JPG, PNG, GIF)
- ✅ Ensure stable internet connection
- ✅ Check server storage space

### Getting Help

**For User Management Issues:**
- **Email:** <EMAIL>
- **Phone:** +265 1 770 100
- **Internal Support:** IT Department

**For Technical Issues:**
- **Email:** <EMAIL>
- **Include:** User ID, error messages, steps taken

### Emergency Procedures

**Account Compromise:**
1. Immediately deactivate the affected account
2. Reset password
3. Review recent activity logs
4. Notify security team
5. Document the incident

**System Access Issues:**
1. Check system status
2. Verify network connectivity
3. Contact IT support
4. Use backup access methods if available

---

*This guide is for MACRA administrators only. For general account help, see the [Authentication Guide](authentication.md).*
