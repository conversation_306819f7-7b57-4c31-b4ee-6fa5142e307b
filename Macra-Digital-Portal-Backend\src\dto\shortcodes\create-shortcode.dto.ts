import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>al, MaxLength, IsIn, IsUUID, IsEnum, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ShortcodeAudience, ShortcodeCategory, ShortcodeStatus } from './shortcode-enums';

export class CreateShortcodeDto {
  @ApiProperty({
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString({message: 'Invalid characters in application ID!' })
  @IsUUID('4',{message: 'Invalid Application ID!'})
  @IsOptional()
  application_id?: string;

  @ApiProperty({
    description: 'Intended audience',
    example: 'community',
    maxLength: 10,
  })
  @IsString({ message: 'Invalid characters in audience!' })
  @IsNotEmpty({message: 'Intended audience is required' })
  @IsEnum(ShortcodeAudience, { message: 'Intended audience must be community, national, regional, or district' })
  audience: string;

  @ApiProperty({
    description: 'Shortcode status, default is inactive',
    example: 'inactive',
    maxLength: 10,
  })
  @IsString({ message: 'Invalid characters in status!' })
  @IsNotEmpty({message: 'Shortcode status is required' })
  @IsEnum(ShortcodeStatus, { message: 'Shortcode status must be active or inactive' })
  status: string;

  @ApiProperty({
    description: 'Category of shortcode, default is reserved',
    example: 'reserved',
    maxLength: 20,
  })
  @IsString({ message: 'Invalid category selected' })
  @IsNotEmpty({ message: 'Shortcode category is required' })
  @IsEnum(ShortcodeCategory, { message: 'Category must be reserved, lifeline_services, data_voice_ussd, customer_care, life_and_safety, or future_use' })
  category: string;

  @ApiProperty({
    description: 'Description',
    example: 'Community shortcode requested by Airtel to provide scam alerts',
    maxLength: 255,
  })
  @IsString({ message: 'Invalid characters in description!' })
  @IsNotEmpty({message: 'Description is required' })
  @MaxLength(255)
  description: string;

  @ApiProperty({
    description: 'Notes',
    example: 'This shortcode will be used to provide scam alerts to the public. It was previously assigned on 200 but changed to 345.',
    maxLength: 255,
  })
  @IsString({ message: 'Invalid characters in notes!' })
  @IsOptional()
  @MaxLength(255)
  notes?: string;

  @ApiProperty({
    description: 'Preferred shortcode length',
    example: 4,
    enum: [3, 4],
  })
  @Type(() => Number)
  @IsNumber({}, { message: 'Shortcode length must be a number!' })
  @IsNotEmpty({message: 'Preferred shortcode length is required' })
  @IsIn([3, 4], { message: 'Shortcode length must be 3 or 4 digits' })
  shortcode_length: number;
}

