import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reateDate<PERSON>olumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsUUID, IsString, IsOptional, IsBoolean, IsDateString, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';
import { Applications } from '../../entities/applications.entity';

import { CeirCertificationBodies } from './ceir-certification-bodies.entity';
import { CeirTechnicalStandards } from './ceir-technical-standards.entity';

// Test type constants for validation
export const TEST_TYPES = [
  'rf_performance',
  'sar_testing',
  'emc_testing',
  'safety_testing',
  'environmental_testing',
  'security_testing',
  'interoperability_testing',
  'conformance_testing',
] as const;

// Test result constants for validation
export const TEST_RESULTS = [
  'pass',
  'fail',
  'conditional_pass',
  'not_applicable',
  'pending',
] as const;

// Report status constants for validation
export const REPORT_STATUSES = [
  'draft',
  'under_review',
  'approved',
  'rejected',
  'expired',
] as const;

@Entity('ceir_test_reports')
export class CeirTestReports {
  @ApiProperty({
    description: 'Unique identifier for the test report',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID()
  report_id: string;

  @ApiProperty({
    description: 'Unique report number',
    example: 'TR-2024-001-RF'
  })
  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  report_number: string;

  @ApiProperty({
    description: 'Application ID this report belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  application_id?: string;

  @ApiProperty({
    description: 'Device ID this report is for',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  device_id: string;

  @ApiProperty({
    description: 'Certification body that conducted the test',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  certification_body_id: string;

  @ApiProperty({
    description: 'Technical standard tested against',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  technical_standard_id: string;

  @ApiProperty({
    description: 'Type of test performed',
    example: 'rf_performance'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  test_type: string;

  @ApiProperty({
    description: 'Title of the test report',
    example: 'RF Performance Test Report for Samsung Galaxy S21'
  })
  @Column({ type: 'varchar', length: 255 })
  @IsString()
  report_title: string;

  @ApiProperty({
    description: 'Date when testing was conducted',
    example: '2024-01-15'
  })
  @Column({ type: 'date' })
  @IsDateString()
  test_date: Date;

  @ApiProperty({
    description: 'Date when the report was issued',
    example: '2024-01-20'
  })
  @Column({ type: 'date' })
  @IsDateString()
  report_date: Date;

  @ApiProperty({
    description: 'Date when the report expires',
    example: '2027-01-20'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  expiry_date?: Date;

  @ApiProperty({
    description: 'Overall test result',
    example: 'pass'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  test_result: string;

  @ApiProperty({
    description: 'Current status of the report',
    example: 'approved'
  })
  @Column({ type: 'varchar', length: 50, default: 'draft' })
  @IsString()
  report_status: string;

  @ApiProperty({
    description: 'Test methods used',
    example: ['Conducted spurious emissions', 'Radiated spurious emissions']
  })
  @Column({ type: 'simple-array', nullable: true })
  test_methods?: string[];

  @ApiProperty({
    description: 'Frequency bands tested',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100']
  })
  @Column({ type: 'simple-array', nullable: true })
  tested_frequency_bands?: string[];

  @ApiProperty({
    description: 'Maximum measured power in dBm',
    example: 32.5
  })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  max_measured_power_dbm?: number;

  @ApiProperty({
    description: 'SAR value if applicable (W/kg)',
    example: 1.2
  })
  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  sar_value_wkg?: number;

  @ApiProperty({
    description: 'Test conditions and environment',
    example: 'Temperature: 23°C, Humidity: 45%, Atmospheric pressure: 86-106 kPa'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  test_conditions?: string;

  @ApiProperty({
    description: 'Equipment used for testing',
    example: 'Rohde & Schwarz FSW Signal Analyzer, Keysight E5071C Network Analyzer'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  test_equipment?: string;

  @ApiProperty({
    description: 'Detailed test results and measurements',
    example: 'All measurements within acceptable limits as per standard requirements'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  test_results_details?: string;

  @ApiProperty({
    description: 'Deviations or non-conformities found',
    example: 'Minor deviation in spurious emission at 1.8 GHz band, within acceptable tolerance'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  deviations?: string;

  @ApiProperty({
    description: 'Recommendations or corrective actions',
    example: 'No corrective actions required. Equipment meets all requirements.'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  recommendations?: string;

  @ApiProperty({
    description: 'Name of the test engineer',
    example: 'Dr. John Smith'
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString()
  test_engineer?: string;

  @ApiProperty({
    description: 'Name of the report reviewer',
    example: 'Prof. Jane Doe'
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString()
  reviewed_by?: string;

  @ApiProperty({
    description: 'Name of the report approver',
    example: 'Dr. Michael Johnson'
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString()
  approved_by?: string;

  @ApiProperty({
    description: 'File path to the report document',
    example: '/documents/test-reports/TR-2024-001-RF.pdf'
  })
  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @IsString()
  report_file_path?: string;

  @ApiProperty({
    description: 'Whether this report is currently valid',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_valid: boolean;

  @ApiProperty({
    description: 'Additional notes or comments',
    example: 'Report generated using automated test system'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;

  @ManyToOne('Device', { nullable: false })
  @JoinColumn({ name: 'device_id' })
  device: any;

  @ManyToOne(() => CeirCertificationBodies, { nullable: false })
  @JoinColumn({ name: 'certification_body_id' })
  certification_body: CeirCertificationBodies;

  @ManyToOne(() => CeirTechnicalStandards, { nullable: false })
  @JoinColumn({ name: 'technical_standard_id' })
  technical_standard: CeirTechnicalStandards;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.report_id) {
      this.report_id = uuidv4();
    }
  }
}
