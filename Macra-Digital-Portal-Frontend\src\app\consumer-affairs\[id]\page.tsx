'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { consumerAffairsService } from '@/services/consumer-affairs';
import { ConsumerAffairsComplaint, ComplaintStatus } from '@/types';
import Loader from '@/components/Loader';
import TextArea from '@/components/forms/TextArea';
import Select from '@/components/forms/Select';
import AttachmentManager from '@/components/consumer-affairs/AttachmentManager';
import { useIsConsumerAffairsStaff } from '@/hooks/useIsConsumerAffairsStaff';
import { useIsConsumerAffairsManager } from '@/hooks/useIsConsumerAffairsManager';
import { taskService } from '@/services/task-assignment';
import { TaskPriority, TaskType } from '@/types/task';


const ComplaintDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { showSuccess, showError } = useToast();
  const isCAStaff = useIsConsumerAffairsStaff();
  const isCAManager = useIsConsumerAffairsManager();

  const complaintId = params.id as string;

  const [complaint, setComplaint] = useState<ConsumerAffairsComplaint | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  
  // Form states
  const [statusForm, setStatusForm] = useState({
    status: '',
    comment: ''
  });




  const fetchComplaintDetails = useCallback(async () => {
    if (!isAuthenticated || !complaintId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await consumerAffairsService.getComplaint(complaintId);
      setComplaint(response);
      
      // Set initial status form values
      setStatusForm({
        status: response.status,
        comment: ''
      });
      

    } catch (err: any) {
      console.error('❌ Error fetching complaint details:', err);
      const errorMessage = err.response?.data?.message || err.message || 'Failed to load complaint details';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, complaintId, showError]);

  useEffect(() => {
    fetchComplaintDetails();
  }, [fetchComplaintDetails]);

  const handleStatusUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!statusForm.status || !statusForm.comment.trim()) {
      showError('Please select a status and provide a comment');
      return;
    }

    setIsUpdatingStatus(true);
    try {
      await consumerAffairsService.updateComplaintStatus(
        complaintId,
        statusForm.status as ComplaintStatus,
        statusForm.comment.trim()
      );
      
      showSuccess('Complaint status updated successfully');
      
      // Reset comment but keep status
      setStatusForm(prev => ({ ...prev, comment: '' }));
      
      // Refresh complaint details
      await fetchComplaintDetails();
      
    } catch (err: any) {
      console.error('❌ Error updating status:', err);
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update status';
      showError(errorMessage);
    } finally {
      setIsUpdatingStatus(false);
    }
  };



  const getStatusColor = (status: string) => {
    return consumerAffairsService.getStatusColor(status);
  };

  const getPriorityColor = (priority: string) => {
    return consumerAffairsService.getPriorityColor(priority);
  };

  if (!isAuthenticated) {
    return (
      <div className="p-6 min-h-full bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400">Please log in to view complaint details.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-6 min-h-full bg-gray-50 dark:bg-gray-900">
        <Loader message="Loading complaint details..." />
      </div>
    );
  }

  if (error || !complaint) {
    return (
      <div className="p-6 min-h-full bg-gray-50 dark:bg-gray-900">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-2"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Complaint
              </h3>
              <p className="text-red-700 dark:text-red-300 mt-1">
                {error || 'Complaint not found'}
              </p>
              <button
                onClick={() => router.back()}
                className="mt-2 text-sm text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300"
              >
                ← Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 min-h-full bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-2"
            >
              <i className="ri-arrow-left-line mr-1"></i>
              Back to Complaints
            </button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Complaint #{complaint.complaint_number || complaint.complaint_id}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Submitted on {new Date(complaint.created_at).toLocaleDateString()}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(complaint.status)}`}>
              {complaint.status.replace('_', ' ').toUpperCase()}
            </span>
            <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getPriorityColor(complaint.priority)}`}>
              {complaint.priority.toUpperCase()} PRIORITY
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Complaint Details */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Complaint Details
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title
                </h3>
                <p className="text-gray-900 dark:text-gray-100">{complaint.title}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </h3>
                <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {complaint.category}
                </span>
              </div>

              {complaint.complainee_reg_number && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Complainee Registration Number
                  </h3>
                  <p className="text-gray-900 dark:text-gray-100">{complaint.complainee_reg_number}</p>
                </div>
              )}
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {complaint.description}
                  </p>
                </div>
              </div>
              
              {complaint.resolution && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Resolution
                  </h3>
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-md p-3">
                    <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                      {complaint.resolution}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Status History */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Status History
            </h2>

            {complaint.status_history && complaint.status_history.length > 0 ? (
              <div className="space-y-4">
                {complaint.status_history.map((history, index) => (
                  <div key={history.history_id || index} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(history.status)}`}>
                        <i className="ri-time-line text-sm"></i>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Status changed to {history.status.replace('_', ' ').toUpperCase()}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(history.created_at).toLocaleString()}
                        </p>
                      </div>
                      {history.comment && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {history.comment}
                        </p>
                      )}
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        by {history.created_by || 'System'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No status history available.
              </p>
            )}
          </div>

          {/* Investigative Documents */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">

            <AttachmentManager
              complaintId={complaintId}
              attachments={complaint.attachments || []}
              onAttachmentsChange={(newAttachments) => {
                setComplaint(prev => prev ? { ...prev, attachments: newAttachments } : null);
              }}
              canUpload={isCAStaff}
              canDelete={isCAStaff}
              maxFiles={5}
              title="Investigative Documents"
            />
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Complainant Information */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Complainant Information
            </h2>

            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Name</p>
                <p className="text-gray-900 dark:text-gray-100">
                  {complaint.complainant?.first_name} {complaint.complainant?.last_name}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</p>
                <p className="text-gray-900 dark:text-gray-100">
                  {complaint.complainant?.email}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Submitted</p>
                <p className="text-gray-900 dark:text-gray-100">
                  {new Date(complaint.created_at).toLocaleString()}
                </p>
              </div>

              {complaint.updated_at !== complaint.created_at && (
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {new Date(complaint.updated_at).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Assignment Information */}
          {complaint.assignee && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Assigned Officer
              </h2>

              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Name</p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {complaint.assignee.first_name} {complaint.assignee.last_name}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {complaint.assignee.email}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Manager Actions and Update Status */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 space-y-6">
            {/* Manager-only actions */}
            {isCAManager && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Manager Actions
                </h2>
                <div className="space-y-3">
                  {/* Request Clarification when submitted complaint is incomplete: set pending + flag */}
                  {complaint.status === 'submitted' && (
                    <button
                      type="button"
                      onClick={async () => {
                        try {
                          await consumerAffairsService.updateComplaintStatus(
                            complaintId,
                            'under_review' as ComplaintStatus,
                            'Manager requested clarifications: complaint marked pending review.'
                          );
                          // Optionally flag via internal notes so UI shows badge
                          await consumerAffairsService.updateComplaint(complaintId, {
                            internal_notes: `[FLAG] Clarifications requested by manager on ${new Date().toLocaleString()}`
                          });
                          showSuccess('Clarification requested and status set to Under Review');
                          await fetchComplaintDetails();
                        } catch (err: any) {
                          showError(err.response?.data?.message || err.message || 'Failed to request clarification');
                        }
                      }}
                      className="w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-md"
                    >
                      <i className="ri-flag-2-line mr-2"></i>
                      Request Clarification (mark pending)
                    </button>
                  )}

                  {/* If reviewed complaint is incomplete: create task and assign officer for clarification */}
                  {complaint.status === 'under_review' && (
                    <button
                      type="button"
                      onClick={async () => {
                        try {
                          // Create a task for this complaint and assign to current officer if available
                          const assignedTo = complaint.assignee?.user_id || complaint.assigned_to;
                          if (!assignedTo) {
                            showError('No officer selected or assigned. Please assign an officer first.');
                            return;
                          }
                          await taskService.createTask({
                            task_type: TaskType.COMPLAINT,
                            title: `Clarification requested for Complaint #${complaint.complaint_number || complaint.complaint_id}`,
                            description: 'Manager deemed complaint incomplete; officer to request clarifications from submitter.',
                            priority: TaskPriority.MEDIUM,
                            entity_type: 'consumer_affairs_complaint',
                            entity_id: complaintId,
                            assigned_to: assignedTo,
                            status: 'pending'
                          });

                          await consumerAffairsService.updateComplaintStatus(
                            complaintId,
                            'under_review' as ComplaintStatus,
                            'Manager deemed incomplete; task created and officer assigned to request clarification.'
                          );
                          await consumerAffairsService.updateComplaint(complaintId, {
                            internal_notes: `[FLAG] Incomplete after review; clarification task created by manager on ${new Date().toLocaleString()}`
                          });
                          showSuccess('Task created and officer assigned for clarification');
                          await fetchComplaintDetails();
                        } catch (err: any) {
                          showError(err.response?.data?.message || err.message || 'Failed to create task and assign officer');
                        }
                      }}
                      className="w-full inline-flex items-center justify-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md"
                    >
                      <i className="ri-task-line mr-2"></i>
                      Create Task & Assign Officer
                    </button>
                  )}

                  {/* Enforcement guard note */}
                  {complaint.status !== 'resolved' && (
                    <div className="text-xs text-gray-600 dark:text-gray-300">
                      <i className="ri-shield-keyhole-line mr-1"></i>
                      Status must be resolved before enforcement.
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Update Status */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                {!isCAStaff ? 'Current Case Status' : 'Update Status'}
              </h2>

              <form onSubmit={handleStatusUpdate} className="space-y-4">
                <Select
                  label="Status"
                  value={statusForm.status}
                  onChange={(e) => setStatusForm(prev => ({ ...prev, status: e.target.value }))}
                  options={consumerAffairsService.getStatusOptions()}
                  required
                  disabled={!isCAStaff}
                  helperText={!isCAStaff ? 'Only Consumer Affairs staff can update status.' : undefined}
                />

                <TextArea
                  label="Comment"
                  value={statusForm.comment}
                  onChange={(e) => setStatusForm(prev => ({ ...prev, comment: e.target.value }))}
                  placeholder="Add a comment about this status change..."
                  required
                  rows={4}
                  disabled={!isCAStaff}
                  helperText={!isCAStaff ? 'Updating status is restricted to Consumer Affairs.' : undefined}
                />

                <button
                  type="submit"
                  disabled={!isCAStaff || isUpdatingStatus || !statusForm.status || !statusForm.comment.trim()}
                  className="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 disabled:cursor-not-allowed text-white text-sm font-medium rounded-md transition-colors"
                  title={!isCAStaff ? 'Only Consumer Affairs staff can update status' : undefined}
                >
                  {isUpdatingStatus ? (
                    <>
                      <i className="ri-loader-4-line animate-spin mr-2"></i>
                      Updating...
                    </>
                  ) : (
                    <>
                      <i className="ri-refresh-line mr-2"></i>
                      {!isCAStaff ? 'View' : 'Update Status'}
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>


        </div>
      </div>
    </div>
  );
};

export default ComplaintDetailPage;
