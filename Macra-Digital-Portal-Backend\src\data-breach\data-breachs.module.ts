import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataBreachReportController } from './data-breachs.controller';
import { DataBreachReportService } from './data-breachs.service';
import {
  DataBreachReport,
} from 'src/entities/data-breachs.entity';
import { User } from 'src/entities/user.entity';
import { NotificationsModule } from 'src/notifications/notifications.module';
import { TasksModule } from 'src/tasks/tasks.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DataBreachReport,
      User,
    ]),
    NotificationsModule,
    forwardRef(() => TasksModule),
  ],
  controllers: [DataBreachReportController],
  providers: [DataBreachReportService],
  exports: [DataBreachReportService],
})
export class DataBreachModule {}
