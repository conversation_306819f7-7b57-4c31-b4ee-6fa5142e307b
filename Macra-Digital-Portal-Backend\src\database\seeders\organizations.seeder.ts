import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from 'src/entities/organization.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class OrganizationSeederService {
  constructor(
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  async seedOrganizations(): Promise<void> {
    console.log('🌱 Seeding organizations...');

    const existing = await this.organizationRepository.count();
    if (existing > 0) {
      console.log('✅ Organizations already exist, skipping...');
      return;
    }

    const organizations: Partial<Organization>[] = [
      {
        organization_id: uuidv4(),
        name: 'Malawi Telecom Ltd',
        registration_number: 'MW-REG-001',
        website: 'https://www.mtl.mw',
        email: '<EMAIL>',
        phone: '+26511234567',
        fax: '+26511234568',
        date_incorporation: new Date('2000-01-15'),
        place_incorporation: 'Blantyre',
      },
      {
        organization_id: uuidv4(),
        name: 'Blantyre Electronics Inc',
        registration_number: 'MW-REG-002',
        website: 'https://www.bei.mw',
        email: '<EMAIL>',
        phone: '+26512345678',
        date_incorporation: new Date('2005-06-23'),
        place_incorporation: 'Blantyre',
      },
      {
        organization_id: uuidv4(),
        name: 'Lilongwe Digital Solutions',
        registration_number: 'MW-REG-003',
        website: 'https://www.lilodigi.mw',
        email: '<EMAIL>',
        phone: '+26519876543',
        fax: '+26519876544',
        date_incorporation: new Date('2012-09-10'),
        place_incorporation: 'Lilongwe',
      },
      {
        organization_id: uuidv4(),
        name: 'Zomba Communication Group',
        registration_number: 'MW-REG-004',
        website: 'https://www.zcg.mw',
        email: '<EMAIL>',
        phone: '+26514567890',
        date_incorporation: new Date('2018-03-05'),
        place_incorporation: 'Zomba',
      },
      {
        organization_id: uuidv4(),
        name: 'Mzuzu Broadcasting Corp',
        registration_number: 'MW-REG-005',
        website: 'https://www.mbc.mw',
        email: '<EMAIL>',
        phone: '+26510987654',
        date_incorporation: new Date('2020-11-30'),
        place_incorporation: 'Mzuzu',
      }
    ];

    const inserts = organizations.map((entry) =>
      this.organizationRepository.create(entry),
    );

    await this.organizationRepository.save(inserts);
    console.log(`✅ Seeded ${inserts.length} organizations.`);
  }

  async seedAll(): Promise<void> {
    await this.seedOrganizations();
  }

  async clearAll(): Promise<void> {
    await this.organizationRepository.clear();
    console.log('🗑️ Cleared organizations');
  }
}
