import { DataSource } from 'typeorm';
import { CeirEquipmentTypeCategories, EQUIPMENT_CATEGORIES, FREQUENCY_BANDS } from '../../ceir/entities/ceir-equipment-type-categories.entity';
import { User } from '../../entities/user.entity';
import { v4 as uuidv4 } from 'uuid';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class CeirEquipmentTypeCategoriesSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    const categoryRepository = dataSource.getRepository(CeirEquipmentTypeCategories);
    const userRepository = dataSource.getRepository(User);

    // Check if categories already exist
    const existingCount = await categoryRepository.count();
    if (existingCount > 0) {
      console.log('CEIR Equipment Type Categories already exist, skipping seeder...');
      return;
    }

    // Try to get a system user for created_by (optional)
    let systemUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    let systemUserId: string | undefined = undefined;

    if (!systemUser) {
      try {
        // Try to create a system user if it doesn't exist
        systemUser = userRepository.create({
          email: '<EMAIL>',
          first_name: 'System',
          last_name: 'Administrator',
          phone: '+265999000000',
          password: 'system123', // This should be hashed in real implementation
          status: 'active' as any,
        });
        systemUser = await userRepository.save(systemUser);
        systemUserId = systemUser.user_id;
        console.log('✅ Created system user for seeding');
      } catch (error) {
        console.log('⚠️ Could not create system user, proceeding with null created_by');
        systemUserId = undefined;
      }
    } else {
      systemUserId = systemUser.user_id;
      console.log('✅ Found existing system user for seeding');
    }

    const categories = [
      {
        category_id: uuidv4(),
        category_type: 'mobile_phone',
        category_name: 'Mobile Phone',
        description: 'Basic mobile phones for voice communication and SMS',
        ceir_standard_code: 'CEIR-MP-001',
        supported_frequency_bands: ['gsm_900', 'gsm_1800'],
        required_standards: ['3GPP TS 51.010-1', 'ETSI EN 301 511', 'IEC 62209-1'],
        required_test_procedures: ['RF Performance Testing', 'SAR Testing', 'EMC Testing'],
        max_transmit_power_dbm: 33.0,
        requires_sar_testing: true,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'smartphone',
        category_name: 'Smartphone',
        description: 'Advanced mobile devices with data capabilities and applications',
        ceir_standard_code: 'CEIR-SP-001',
        supported_frequency_bands: [
          'gsm_900',
          'gsm_1800',
          'umts_2100',
          'lte_800',
          'lte_1800',
          'lte_2600',
          'wifi_2_4ghz',
          'wifi_5ghz',
          'bluetooth',
        ],
        required_standards: ['3GPP TS 36.521', 'ETSI EN 301 908', 'IEEE 802.11', 'IEC 62209-2'],
        required_test_procedures: ['RF Performance Testing', 'SAR Testing', 'EMC Testing', 'Security Testing'],
        max_transmit_power_dbm: 23.0,
        requires_sar_testing: true,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'tablet',
        category_name: 'Tablet Device',
        description: 'Portable computing devices with cellular connectivity',
        ceir_standard_code: 'CEIR-TB-001',
        supported_frequency_bands: [
          'lte_800',
          'lte_1800',
          'lte_2600',
          'wifi_2_4ghz',
          'wifi_5ghz',
          'bluetooth',
        ],
        required_standards: ['3GPP TS 36.521', 'IEEE 802.11', 'IEC 62209-2'],
        required_test_procedures: ['RF Performance Testing', 'SAR Testing', 'EMC Testing'],
        max_transmit_power_dbm: 23.0,
        requires_sar_testing: true,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'modem',
        category_name: 'Cellular Modem',
        description: 'Data communication devices for internet connectivity',
        ceir_standard_code: 'CEIR-MD-001',
        supported_frequency_bands: [
          'umts_2100',
          'lte_800',
          'lte_1800',
          'lte_2600',
        ],
        required_standards: ['3GPP TS 36.521', 'ETSI EN 301 908'],
        required_test_procedures: ['RF Performance Testing', 'EMC Testing'],
        max_transmit_power_dbm: 23.0,
        requires_sar_testing: false,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'router',
        category_name: 'Cellular Router',
        description: 'Network routing devices with cellular backhaul',
        ceir_standard_code: 'CEIR-RT-001',
        supported_frequency_bands: [
          'lte_800',
          'lte_1800',
          'lte_2600',
          'wifi_2_4ghz',
          'wifi_5ghz',
        ],
        required_standards: ['3GPP TS 36.521', 'IEEE 802.11', 'ETSI EN 301 908'],
        required_test_procedures: ['RF Performance Testing', 'EMC Testing'],
        max_transmit_power_dbm: 23.0,
        requires_sar_testing: false,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'iot_device',
        category_name: 'IoT Device',
        description: 'Internet of Things devices with cellular connectivity',
        ceir_standard_code: 'CEIR-IOT-001',
        supported_frequency_bands: [
          'lte_800',
          'lte_1800',
          'bluetooth',
        ],
        required_standards: ['3GPP TS 36.521', 'ETSI EN 300 328'],
        required_test_procedures: ['RF Performance Testing', 'EMC Testing'],
        max_transmit_power_dbm: 20.0,
        requires_sar_testing: false,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'wearable',
        category_name: 'Wearable Device',
        description: 'Wearable technology with wireless connectivity',
        ceir_standard_code: 'CEIR-WR-001',
        supported_frequency_bands: [
          'bluetooth',
          'wifi_2_4ghz',
          'nfc_13_56mhz',
        ],
        required_standards: ['ETSI EN 300 328', 'ETSI EN 300 330', 'IEC 62209-2'],
        required_test_procedures: ['RF Performance Testing', 'SAR Testing', 'EMC Testing'],
        max_transmit_power_dbm: 10.0,
        requires_sar_testing: true,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
      {
        category_id: uuidv4(),
        category_type: 'satellite_terminal',
        category_name: 'Satellite Terminal',
        description: 'Satellite communication terminals and equipment',
        ceir_standard_code: 'CEIR-SAT-001',
        supported_frequency_bands: [
          'satellite_l_band',
          'satellite_ku_band',
        ],
        required_standards: ['ETSI EN 301 428', 'ETSI EN 302 186'],
        required_test_procedures: ['RF Performance Testing', 'EMC Testing'],
        max_transmit_power_dbm: 40.0,
        requires_sar_testing: false,
        requires_emc_testing: true,
        requires_rf_testing: true,
        approval_validity_months: 60,
        is_active: true,
        created_by: systemUserId,
      },
    ];

    console.log('Seeding CEIR Equipment Type Categories...');
    
    for (const categoryData of categories) {
      const category = categoryRepository.create(categoryData);
      await categoryRepository.save(category);
      console.log(`✅ Created equipment category: ${categoryData.category_name}`);
    }

    console.log('CEIR Equipment Type Categories seeding completed!');
  }
}
