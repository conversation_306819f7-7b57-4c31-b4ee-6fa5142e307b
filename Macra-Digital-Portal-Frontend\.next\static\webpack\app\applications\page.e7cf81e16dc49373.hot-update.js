"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/applications/page",{

/***/ "(app-pages-browser)/./src/components/common/AssignModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/common/AssignModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AssignModal = (param)=>{\n    let { isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess, task, onReassignSuccess } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [officers, setOfficers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assigning, setAssigning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOfficer, setSelectedOfficer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loadTask, setTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReassignMode, setIsReassignMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssignModal.useEffect\": ()=>{\n            if (isOpen) {\n                fetchOfficers();\n                fetchExistingTasks();\n                // For reassignment, pre-select the current assignee\n                setSelectedOfficer(isReassignMode ? (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assigned_to) || '' : '');\n                setComment('');\n                // For reassignment, pre-fill the current due date if available\n                setDueDate(isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.due_date) ? loadTask.due_date.split('T')[0] : '');\n            }\n        }\n    }[\"AssignModal.useEffect\"], [\n        isOpen,\n        isReassignMode,\n        task\n    ]);\n    const fetchExistingTasks = async ()=>{\n        if (task) {\n            setTask(task);\n            setIsReassignMode(true);\n            return;\n        }\n        if (itemId && itemType === 'application') {\n            const loadTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getTaskForApplication(itemId);\n            if (loadTask && loadTask.task_id) {\n                setTask(loadTask);\n                setIsReassignMode(true);\n            } else {\n                setIsReassignMode(false);\n                setTask(null);\n            }\n        }\n    };\n    const fetchOfficers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getOfficers();\n            const officersData = response.data || [];\n            setOfficers(officersData);\n            if (officersData.length === 0) {\n                console.warn('No officers found for task assignment');\n            }\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            setOfficers([]);\n            showError('Failed to load officers. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getTaskTypeFromItemType = (itemType)=>{\n        switch(itemType){\n            case 'application':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.EVALUATION;\n            case 'data_breach':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DATA_BREACH;\n            case 'complaint':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.COMPLAINT;\n            case 'inspection':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.INSPECTION;\n            case 'document_review':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DOCUMENT_REVIEW;\n            case 'task':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION; // Default for existing tasks\n            default:\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION;\n        }\n    };\n    const getTaskTitle = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'Untitled';\n        switch(itemType){\n            case 'application':\n                return \"Application Evaluation: \".concat(baseTitle);\n            case 'data_breach':\n                return \"Data Breach Investigation: \".concat(baseTitle);\n            case 'complaint':\n                return \"Complaint Review: \".concat(baseTitle);\n            case 'inspection':\n                return \"Inspection Task: \".concat(baseTitle);\n            case 'document_review':\n                return \"Document Review: \".concat(baseTitle);\n            default:\n                return \"Task: \".concat(baseTitle);\n        }\n    };\n    const getTaskDescription = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'item';\n        switch(itemType){\n            case 'application':\n                return \"Evaluate and review application \".concat(baseTitle, \" for compliance and approval.\");\n            case 'data_breach':\n                return \"Investigate and assess data breach report \".concat(baseTitle, \" for regulatory compliance.\");\n            case 'complaint':\n                return \"Review and resolve complaint \".concat(baseTitle, \" according to regulatory procedures.\");\n            case 'inspection':\n                return \"Conduct inspection for \".concat(baseTitle, \" to ensure regulatory compliance.\");\n            case 'document_review':\n                return \"Review and validate document \".concat(baseTitle, \" for accuracy and compliance.\");\n            default:\n                return \"Process and review \".concat(baseTitle, \".\");\n        }\n    };\n    const handleAssign = async ()=>{\n        if (!selectedOfficer) {\n            showError('Please select an officer');\n            return;\n        }\n        if (!dueDate) {\n            showError('Please select a due date');\n            return;\n        }\n        if (!isReassignMode && !itemId) {\n            showError('Item ID is missing');\n            return;\n        }\n        setAssigning(true);\n        try {\n            if (isReassignMode && loadTask) {\n                // Reassign existing task\n                await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.reassignTask(loadTask.task_id, {\n                    assignedTo: selectedOfficer,\n                    comment: comment.trim() || undefined,\n                    due_date: dueDate,\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM\n                });\n                showSuccess('Task reassigned successfully');\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            } else {\n                const taskData = {\n                    task_type: getTaskTypeFromItemType(itemType),\n                    title: getTaskTitle(itemType, itemTitle),\n                    description: getTaskDescription(itemType, itemTitle),\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM,\n                    status: _types__WEBPACK_IMPORTED_MODULE_4__.TaskStatus.PENDING,\n                    entity_type: itemType,\n                    entity_id: itemId,\n                    assigned_to: selectedOfficer,\n                    due_date: dueDate,\n                    metadata: {\n                        comment: comment.trim() || undefined,\n                        original_item_title: itemTitle,\n                        assignment_context: 'manual_assignment'\n                    }\n                };\n                // For other entity types, create a new task\n                await createNewTask(taskData);\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error \".concat(isReassignMode ? 'reassigning' : 'creating assignment', \" task:\"), error);\n            showError(\"Failed to \".concat(isReassignMode ? 'reassign' : 'assign', \" task\"));\n        } finally{\n            setAssigning(false);\n        }\n    };\n    // Helper function to create a new task\n    async function createNewTask(taskData) {\n        const createdTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.createTask(taskData);\n        showSuccess(\"Successfully assigned \".concat(itemType.replace('_', ' '), \" to officer\"));\n        onAssignSuccess === null || onAssignSuccess === void 0 ? void 0 : onAssignSuccess();\n    }\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: isReassignMode ? 'Reassign Task' : \"Create Task for \".concat(itemType.replace('_', ' ').toUpperCase())\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-task-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isReassignMode ? 'Task Details:' : 'Task to be Created:'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300 font-medium\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.title : getTaskTitle(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.description : getTaskDescription(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Task #\",\n                                        loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assignee) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Currently assigned to: \",\n                                        loadTask.assignee.first_name,\n                                        \" \",\n                                        loadTask.assignee.last_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                                    children: \"Loading officers...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedOfficer,\n                                    onChange: (e)=>setSelectedOfficer(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select an officer...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        officers.map((officer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: officer.user_id,\n                                                children: [\n                                                    officer.first_name,\n                                                    \" \",\n                                                    officer.last_name,\n                                                    \" - \",\n                                                    officer.email,\n                                                    officer.department ? \" (\".concat(typeof officer.department === 'string' ? officer.department : officer.department.name, \")\") : ''\n                                                ]\n                                            }, officer.user_id, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Due Date *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    value: dueDate,\n                                    onChange: (e)=>setDueDate(e.target.value),\n                                    min: new Date().toISOString().split('T')[0],\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: comment,\n                                    onChange: (e)=>setComment(e.target.value),\n                                    placeholder: isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...',\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedOfficer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-green-900 dark:text-green-100 mb-2\",\n                                    children: \"Selected Officer:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200\",\n                                    children: (()=>{\n                                        const officer = officers.find((o)=>o.user_id === selectedOfficer);\n                                        return officer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                officer.first_name,\n                                                \" \",\n                                                officer.last_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                officer.email,\n                                                officer.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \"Department: \",\n                                                        typeof officer.department === 'string' ? officer.department : officer.department.name\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true) : 'Officer not found';\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined),\n                                dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Due Date:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" \",\n                                        new Date(dueDate).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleAssign,\n                            disabled: !selectedOfficer || !dueDate || assigning,\n                            className: \"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                            children: assigning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassigning...' : 'Creating Task...'\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassign Task' : 'Create Task'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AssignModal, \"R0FJPMujrbn0r85siFN9zWWyY8M=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AssignModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AssignModal);\nvar _c;\n$RefreshReg$(_c, \"AssignModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9Bc3NpZ25Nb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0E7QUFDTTtBQUN5QjtBQTBCbEYsTUFBTVEsY0FBMEM7UUFBQyxFQUMvQ0MsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLGVBQWUsRUFDZkMsSUFBSSxFQUNKQyxpQkFBaUIsRUFDbEI7O0lBQ0MsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHZixnRUFBUUE7SUFDM0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHbkIsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNvQixTQUFTQyxXQUFXLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQixXQUFXQyxhQUFhLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN3QixpQkFBaUJDLG1CQUFtQixHQUFHekIsK0NBQVFBLENBQVM7SUFDL0QsTUFBTSxDQUFDMEIsU0FBU0MsV0FBVyxHQUFHM0IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDNEIsU0FBU0MsV0FBVyxHQUFHN0IsK0NBQVFBLENBQVM7SUFDL0MsTUFBTSxDQUFDOEIsVUFBVUMsUUFBUSxHQUFHL0IsK0NBQVFBLENBQWM7SUFDbEQsTUFBTSxDQUFDZ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2pDLCtDQUFRQSxDQUFVO0lBQzlEQyxnREFBU0E7aUNBQUM7WUFDUixJQUFJTyxRQUFRO2dCQUNWMEI7Z0JBQ0FDO2dCQUNBLG9EQUFvRDtnQkFDcERWLG1CQUFtQk8saUJBQWtCRixDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVNLFdBQVcsS0FBSSxLQUFNO2dCQUNwRVQsV0FBVztnQkFDWCwrREFBK0Q7Z0JBQy9ERSxXQUFXRyxtQkFBa0JGLHFCQUFBQSwrQkFBQUEsU0FBVU8sUUFBUSxJQUFHUCxTQUFTTyxRQUFRLENBQUNDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHO1lBQ3RGO1FBQ0Y7Z0NBQUc7UUFBQzlCO1FBQVF3QjtRQUFnQmxCO0tBQUs7SUFHakMsTUFBTXFCLHFCQUFxQjtRQUN6QixJQUFHckIsTUFBTTtZQUNQaUIsUUFBUWpCO1lBQ1JtQixrQkFBa0I7WUFDbEI7UUFDRjtRQUVBLElBQUl2QixVQUFVQyxhQUFhLGVBQWU7WUFDeEMsTUFBTW1CLFdBQVcsTUFBTTNCLGtFQUFXQSxDQUFDb0MscUJBQXFCLENBQUM3QjtZQUN6RCxJQUFHb0IsWUFBWUEsU0FBU1UsT0FBTyxFQUFFO2dCQUMvQlQsUUFBUUQ7Z0JBQ1JHLGtCQUFrQjtZQUNwQixPQUFPO2dCQUNMQSxrQkFBa0I7Z0JBQ2xCRixRQUFRO1lBQ1Y7UUFDRjtJQUVGO0lBRUEsTUFBTUcsZ0JBQWdCO1FBQ3BCYixXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1vQixXQUFXLE1BQU10QyxrRUFBV0EsQ0FBQ3VDLFdBQVc7WUFDOUMsTUFBTUMsZUFBZUYsU0FBU0csSUFBSSxJQUFJLEVBQUU7WUFDeEN6QixZQUFZd0I7WUFFWixJQUFJQSxhQUFhRSxNQUFNLEtBQUssR0FBRztnQkFDN0JDLFFBQVFDLElBQUksQ0FBQztZQUNmO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDN0IsWUFBWSxFQUFFO1lBQ2RGLFVBQVU7UUFDWixTQUFVO1lBQ1JJLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTRCLDBCQUEwQixDQUFDdEM7UUFDL0IsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU9QLDRDQUFRQSxDQUFDOEMsVUFBVTtZQUM1QixLQUFLO2dCQUNILE9BQU85Qyw0Q0FBUUEsQ0FBQytDLFdBQVc7WUFDN0IsS0FBSztnQkFDSCxPQUFPL0MsNENBQVFBLENBQUNnRCxTQUFTO1lBQzNCLEtBQUs7Z0JBQ0gsT0FBT2hELDRDQUFRQSxDQUFDaUQsVUFBVTtZQUM1QixLQUFLO2dCQUNILE9BQU9qRCw0Q0FBUUEsQ0FBQ2tELGVBQWU7WUFDakMsS0FBSztnQkFDSCxPQUFPbEQsNENBQVFBLENBQUNtRCxXQUFXLEVBQUUsNkJBQTZCO1lBQzVEO2dCQUNFLE9BQU9uRCw0Q0FBUUEsQ0FBQ21ELFdBQVc7UUFDL0I7SUFDRjtJQUVBLE1BQU1DLGVBQWUsQ0FBQzdDLFVBQWtCQztRQUN0QyxNQUFNNkMsWUFBWTdDLGFBQWE7UUFDL0IsT0FBUUQ7WUFDTixLQUFLO2dCQUNILE9BQU8sMkJBQXFDLE9BQVY4QztZQUNwQyxLQUFLO2dCQUNILE9BQU8sOEJBQXdDLE9BQVZBO1lBQ3ZDLEtBQUs7Z0JBQ0gsT0FBTyxxQkFBK0IsT0FBVkE7WUFDOUIsS0FBSztnQkFDSCxPQUFPLG9CQUE4QixPQUFWQTtZQUM3QixLQUFLO2dCQUNILE9BQU8sb0JBQThCLE9BQVZBO1lBQzdCO2dCQUNFLE9BQU8sU0FBbUIsT0FBVkE7UUFDcEI7SUFDRjtJQUVBLE1BQU1DLHFCQUFxQixDQUFDL0MsVUFBa0JDO1FBQzVDLE1BQU02QyxZQUFZN0MsYUFBYTtRQUMvQixPQUFRRDtZQUNOLEtBQUs7Z0JBQ0gsT0FBTyxtQ0FBNkMsT0FBVjhDLFdBQVU7WUFDdEQsS0FBSztnQkFDSCxPQUFPLDZDQUF1RCxPQUFWQSxXQUFVO1lBQ2hFLEtBQUs7Z0JBQ0gsT0FBTyxnQ0FBMEMsT0FBVkEsV0FBVTtZQUNuRCxLQUFLO2dCQUNILE9BQU8sMEJBQW9DLE9BQVZBLFdBQVU7WUFDN0MsS0FBSztnQkFDSCxPQUFPLGdDQUEwQyxPQUFWQSxXQUFVO1lBQ25EO2dCQUNFLE9BQU8sc0JBQWdDLE9BQVZBLFdBQVU7UUFDM0M7SUFDRjtJQUVBLE1BQU1FLGVBQWU7UUFDbkIsSUFBSSxDQUFDbkMsaUJBQWlCO1lBQ3BCUCxVQUFVO1lBQ1Y7UUFDRjtRQUVBLElBQUksQ0FBQ1csU0FBUztZQUNaWCxVQUFVO1lBQ1Y7UUFDRjtRQUVBLElBQUksQ0FBQ2Usa0JBQWtCLENBQUN0QixRQUFRO1lBQzlCTyxVQUFVO1lBQ1Y7UUFDRjtRQUVBTSxhQUFhO1FBR2IsSUFBSTtZQUNGLElBQUlTLGtCQUFrQkYsVUFBVTtnQkFDOUIseUJBQXlCO2dCQUN6QixNQUFNM0Isa0VBQVdBLENBQUN5RCxZQUFZLENBQUM5QixTQUFTVSxPQUFPLEVBQUU7b0JBQy9DcUIsWUFBWXJDO29CQUNaRSxTQUFTQSxRQUFRb0MsSUFBSSxNQUFNQztvQkFDM0IxQixVQUFVVDtvQkFDVm9DLFVBQVUzRCxnREFBWUEsQ0FBQzRELE1BQU07Z0JBQy9CO2dCQUNBakQsWUFBWTtnQkFDWkQsOEJBQUFBLHdDQUFBQTtZQUVGLE9BQU87Z0JBQ0wsTUFBTW1ELFdBQTJCO29CQUMvQkMsV0FBV2xCLHdCQUF3QnRDO29CQUNuQ3lELE9BQU9aLGFBQWE3QyxVQUFVQztvQkFDOUJ5RCxhQUFhWCxtQkFBbUIvQyxVQUFVQztvQkFDMUNvRCxVQUFVM0QsZ0RBQVlBLENBQUM0RCxNQUFNO29CQUM3QkssUUFBUWhFLDhDQUFVQSxDQUFDaUUsT0FBTztvQkFDMUJDLGFBQWE3RDtvQkFDYjhELFdBQVcvRDtvQkFDWDBCLGFBQWFaO29CQUNiYSxVQUFVVDtvQkFDVjhDLFVBQVU7d0JBQ1JoRCxTQUFTQSxRQUFRb0MsSUFBSSxNQUFNQzt3QkFDM0JZLHFCQUFxQi9EO3dCQUNyQmdFLG9CQUFvQjtvQkFDdEI7Z0JBQ0Y7Z0JBQ0EsNENBQTRDO2dCQUM1QyxNQUFNQyxjQUFjWDtnQkFDcEJuRCw4QkFBQUEsd0NBQUFBO1lBQ0Y7WUFFQU47UUFDRixFQUFFLE9BQU91QyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxTQUFnRSxPQUF2RGhCLGlCQUFpQixnQkFBZ0IsdUJBQXNCLFdBQVNnQjtZQUN2Ri9CLFVBQVUsYUFBb0QsT0FBdkNlLGlCQUFpQixhQUFhLFVBQVM7UUFDaEUsU0FBVTtZQUNSVCxhQUFhO1FBQ2Y7SUFDRjtJQUVFLHVDQUF1QztJQUN6QyxlQUFlc0QsY0FBY1gsUUFBdUI7UUFDbEQsTUFBTVksY0FBYyxNQUFNM0Usa0VBQVdBLENBQUM0RSxVQUFVLENBQUNiO1FBQ2pEbEQsWUFBWSx5QkFBb0QsT0FBM0JMLFNBQVNxRSxPQUFPLENBQUMsS0FBSyxNQUFLO1FBQ2hFbkUsNEJBQUFBLHNDQUFBQTtJQUNGO0lBRUEsSUFBSSxDQUFDTCxRQUFRLE9BQU87SUFFcEIscUJBQ0UsOERBQUN5RTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUVELFdBQVdsRCxpQkFBaUIsMkNBQTJDOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUc5RSw4REFBQ2lEOztzREFDQyw4REFBQ0c7NENBQUdGLFdBQVU7c0RBQ1hsRCxpQkFBaUIsa0JBQWtCLG1CQUE0RCxPQUF6Q3JCLFNBQVNxRSxPQUFPLENBQUMsS0FBSyxLQUFLSyxXQUFXOzs7Ozs7c0RBRS9GLDhEQUFDQzs0Q0FBRUosV0FBVTtzREFDVmxELGlCQUFpQixxQ0FBcUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJN0QsOERBQUN1RDs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBU2hGOzRCQUNUeUUsV0FBVTtzQ0FFViw0RUFBQ0M7Z0NBQUVELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUtqQiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNRO29DQUFHUixXQUFVOztzREFDWiw4REFBQ0M7NENBQUVELFdBQVU7Ozs7Ozt3Q0FDWmxELGlCQUFpQixrQkFBa0I7Ozs7Ozs7OENBRXRDLDhEQUFDc0Q7b0NBQUVKLFdBQVU7OENBQ1ZsRCxpQkFBaUJGLHFCQUFBQSwrQkFBQUEsU0FBVXNDLEtBQUssR0FBR1osYUFBYTdDLFVBQVVDOzs7Ozs7OENBRTdELDhEQUFDMEU7b0NBQUVKLFdBQVU7OENBQ1ZsRCxpQkFBaUJGLHFCQUFBQSwrQkFBQUEsU0FBVXVDLFdBQVcsR0FBR1gsbUJBQW1CL0MsVUFBVUM7Ozs7OztnQ0FFeEVvQixtQkFBa0JGLHFCQUFBQSwrQkFBQUEsU0FBVTZELFdBQVcsbUJBQ3RDLDhEQUFDTDtvQ0FBRUosV0FBVTs7d0NBQWdEO3dDQUNwRHBELHFCQUFBQSwrQkFBQUEsU0FBVTZELFdBQVc7Ozs7Ozs7Z0NBRy9CM0QsbUJBQWtCRixxQkFBQUEsK0JBQUFBLFNBQVU4RCxRQUFRLG1CQUNuQyw4REFBQ047b0NBQUVKLFdBQVU7O3dDQUFnRDt3Q0FDbkNwRCxTQUFTOEQsUUFBUSxDQUFDQyxVQUFVO3dDQUFDO3dDQUFFL0QsU0FBUzhELFFBQVEsQ0FBQ0UsU0FBUzs7Ozs7Ozs7Ozs7OztzQ0FNeEYsOERBQUNiOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2E7b0NBQU1iLFdBQVU7OENBQ2RsRCxpQkFBaUIsMEJBQTBCOzs7Ozs7Z0NBRTdDWix3QkFDQyw4REFBQzZEO29DQUFJQyxXQUFVOzhDQUFvRDs7Ozs7OERBSW5FLDhEQUFDYztvQ0FDQ0MsT0FBT3pFO29DQUNQMEUsVUFBVSxDQUFDQyxJQUFNMUUsbUJBQW1CMEUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29DQUNsRGYsV0FBVTs7c0RBRVYsOERBQUNtQjs0Q0FBT0osT0FBTTtzREFBRzs7Ozs7O3dDQUNoQi9FLFNBQVNvRixHQUFHLENBQUMsQ0FBQ0Msd0JBQ2IsOERBQUNGO2dEQUE2QkosT0FBT00sUUFBUUMsT0FBTzs7b0RBQ2pERCxRQUFRVixVQUFVO29EQUFDO29EQUFFVSxRQUFRVCxTQUFTO29EQUFDO29EQUFJUyxRQUFRRSxLQUFLO29EQUN4REYsUUFBUUcsVUFBVSxHQUFHLEtBQTJGLE9BQXRGLE9BQU9ILFFBQVFHLFVBQVUsS0FBSyxXQUFXSCxRQUFRRyxVQUFVLEdBQUdILFFBQVFHLFVBQVUsQ0FBQ0MsSUFBSSxFQUFDLE9BQUs7OytDQUYzR0osUUFBUUMsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXBDLDhEQUFDdkI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FBTWIsV0FBVTs4Q0FBa0U7Ozs7Ozs4Q0FHbkYsOERBQUMwQjtvQ0FDQ3BCLE1BQUs7b0NBQ0xTLE9BQU9yRTtvQ0FDUHNFLFVBQVUsQ0FBQ0MsSUFBTXRFLFdBQVdzRSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzFDWSxLQUFLLElBQUlDLE9BQU9DLFdBQVcsR0FBR3pFLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQ0FDM0M0QyxXQUFVOzs7Ozs7Ozs7Ozs7c0NBS2QsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2E7b0NBQU1iLFdBQVU7OENBQ2RsRCxpQkFBaUIsa0NBQWtDOzs7Ozs7OENBRXRELDhEQUFDZ0Y7b0NBQ0NmLE9BQU92RTtvQ0FDUHdFLFVBQVUsQ0FBQ0MsSUFBTXhFLFdBQVd3RSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzFDZ0IsYUFBYWpGLGlCQUFpQiw2Q0FBNkM7b0NBQzNFa0YsTUFBTTtvQ0FDTmhDLFdBQVU7Ozs7Ozs7Ozs7Ozt3QkFLYjFELGlDQUNDLDhEQUFDeUQ7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUTtvQ0FBR1IsV0FBVTs4Q0FBOEQ7Ozs7Ozs4Q0FHNUUsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaLENBQUM7d0NBQ0EsTUFBTXFCLFVBQVVyRixTQUFTaUcsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFWixPQUFPLEtBQUtoRjt3Q0FDakQsT0FBTytFLHdCQUNMOztnREFDR0EsUUFBUVYsVUFBVTtnREFBQztnREFBRVUsUUFBUVQsU0FBUzs4REFDdkMsOERBQUN1Qjs7Ozs7Z0RBQ0FkLFFBQVFFLEtBQUs7Z0RBQ2JGLFFBQVFHLFVBQVUsa0JBQ2pCOztzRUFDRSw4REFBQ1c7Ozs7O3dEQUFLO3dEQUNPLE9BQU9kLFFBQVFHLFVBQVUsS0FBSyxXQUFXSCxRQUFRRyxVQUFVLEdBQUdILFFBQVFHLFVBQVUsQ0FBQ0MsSUFBSTs7OzsyREFJdEc7b0NBQ047Ozs7OztnQ0FFRC9FLHlCQUNDLDhEQUFDcUQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDb0M7c0RBQU87Ozs7Ozt3Q0FBa0I7d0NBQUUsSUFBSVIsS0FBS2xGLFNBQVMyRixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUTFFLDhEQUFDdEM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBU2hGOzRCQUNUeUUsV0FBVTtzQ0FDWDs7Ozs7O3NDQUdELDhEQUFDSzs0QkFDQ0MsTUFBSzs0QkFDTEMsU0FBUzlCOzRCQUNUNkQsVUFBVSxDQUFDaEcsbUJBQW1CLENBQUNJLFdBQVdOOzRCQUMxQzRELFdBQVU7c0NBRVQ1RCwwQkFDQzs7a0RBQ0UsOERBQUMyRDt3Q0FBSUMsV0FBVTs7Ozs7O29DQUNkbEQsaUJBQWlCLG1CQUFtQjs7NkRBR3ZDOztrREFDRSw4REFBQ21EO3dDQUFFRCxXQUFXbEQsaUJBQWlCLDZCQUE2Qjs7Ozs7O29DQUMzREEsaUJBQWlCLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFwRDtHQWxYTXpCOztRQVUrQkwsNERBQVFBOzs7S0FWdkNLO0FBb1hOLGlFQUFlQSxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJEOlxcTWVtb3J5IEJ1c2luZXNzIFNvbHV0aW9pbnNcXFByb2plY3RzXFxNQUNSQVxccHJvamVjdFxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcY29tbW9uXFxBc3NpZ25Nb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb250ZXh0cy9Ub2FzdENvbnRleHQnO1xyXG5pbXBvcnQgeyB0YXNrU2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvdGFzay1hc3NpZ25tZW50JztcclxuaW1wb3J0IHsgVGFzaywgVGFza1R5cGUsIFRhc2tQcmlvcml0eSwgVGFza1N0YXR1cywgQ3JlYXRlVGFza0R0byB9IGZyb20gJ0AvdHlwZXMnO1xyXG5cclxuaW50ZXJmYWNlIE9mZmljZXIge1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBkZXBhcnRtZW50Pzoge1xyXG4gICAgZGVwYXJ0bWVudF9pZDogc3RyaW5nO1xyXG4gICAgbmFtZTogc3RyaW5nO1xyXG4gICAgY29kZTogc3RyaW5nO1xyXG4gIH0gfCBzdHJpbmc7IC8vIENhbiBiZSBlaXRoZXIgYSBkZXBhcnRtZW50IG9iamVjdCBvciBzdHJpbmcgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxufVxyXG5cclxuaW50ZXJmYWNlIEFzc2lnbk1vZGFsUHJvcHMge1xyXG4gIGlzT3BlbjogYm9vbGVhbjtcclxuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xyXG4gIGl0ZW1JZDogc3RyaW5nIHwgbnVsbDtcclxuICBpdGVtVHlwZTogc3RyaW5nO1xyXG4gIGl0ZW1UaXRsZT86IHN0cmluZztcclxuICBvbkFzc2lnblN1Y2Nlc3M/OiAoKSA9PiB2b2lkO1xyXG4gIC8vIFJlYXNzaWdubWVudCBtb2RlIHByb3BzXHJcbiAgdGFzaz86IFRhc2sgfCBudWxsOyAvLyBGb3IgcmVhc3NpZ25tZW50IG1vZGVcclxuICBvblJlYXNzaWduU3VjY2Vzcz86ICgpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmNvbnN0IEFzc2lnbk1vZGFsOiBSZWFjdC5GQzxBc3NpZ25Nb2RhbFByb3BzPiA9ICh7XHJcbiAgaXNPcGVuLFxyXG4gIG9uQ2xvc2UsXHJcbiAgaXRlbUlkLFxyXG4gIGl0ZW1UeXBlLFxyXG4gIGl0ZW1UaXRsZSxcclxuICBvbkFzc2lnblN1Y2Nlc3MsXHJcbiAgdGFzayxcclxuICBvblJlYXNzaWduU3VjY2Vzc1xyXG59KSA9PiB7XHJcbiAgY29uc3QgeyBzaG93U3VjY2Vzcywgc2hvd0Vycm9yIH0gPSB1c2VUb2FzdCgpO1xyXG4gIGNvbnN0IFtvZmZpY2Vycywgc2V0T2ZmaWNlcnNdID0gdXNlU3RhdGU8T2ZmaWNlcltdPihbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFthc3NpZ25pbmcsIHNldEFzc2lnbmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkT2ZmaWNlciwgc2V0U2VsZWN0ZWRPZmZpY2VyXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xyXG4gIGNvbnN0IFtjb21tZW50LCBzZXRDb21tZW50XSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbZHVlRGF0ZSwgc2V0RHVlRGF0ZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcclxuICBjb25zdCBbbG9hZFRhc2ssIHNldFRhc2tdID0gdXNlU3RhdGU8VGFzayB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc1JlYXNzaWduTW9kZSwgc2V0SXNSZWFzc2lnbk1vZGVdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaXNPcGVuKSB7XHJcbiAgICAgIGZldGNoT2ZmaWNlcnMoKTtcclxuICAgICAgZmV0Y2hFeGlzdGluZ1Rhc2tzKCk7XHJcbiAgICAgIC8vIEZvciByZWFzc2lnbm1lbnQsIHByZS1zZWxlY3QgdGhlIGN1cnJlbnQgYXNzaWduZWVcclxuICAgICAgc2V0U2VsZWN0ZWRPZmZpY2VyKGlzUmVhc3NpZ25Nb2RlID8gKGxvYWRUYXNrPy5hc3NpZ25lZF90byB8fCAnJykgOiAnJyk7XHJcbiAgICAgIHNldENvbW1lbnQoJycpO1xyXG4gICAgICAvLyBGb3IgcmVhc3NpZ25tZW50LCBwcmUtZmlsbCB0aGUgY3VycmVudCBkdWUgZGF0ZSBpZiBhdmFpbGFibGVcclxuICAgICAgc2V0RHVlRGF0ZShpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaz8uZHVlX2RhdGUgPyBsb2FkVGFzay5kdWVfZGF0ZS5zcGxpdCgnVCcpWzBdIDogJycpO1xyXG4gICAgfVxyXG4gIH0sIFtpc09wZW4sIGlzUmVhc3NpZ25Nb2RlLCB0YXNrXSk7XHJcblxyXG5cclxuICBjb25zdCBmZXRjaEV4aXN0aW5nVGFza3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZih0YXNrKSB7XHJcbiAgICAgIHNldFRhc2sodGFzayk7XHJcbiAgICAgIHNldElzUmVhc3NpZ25Nb2RlKHRydWUpO1xyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICBpZiAoaXRlbUlkICYmIGl0ZW1UeXBlID09PSAnYXBwbGljYXRpb24nKSB7XHJcbiAgICAgIGNvbnN0IGxvYWRUYXNrID0gYXdhaXQgdGFza1NlcnZpY2UuZ2V0VGFza0ZvckFwcGxpY2F0aW9uKGl0ZW1JZClcclxuICAgICAgaWYobG9hZFRhc2sgJiYgbG9hZFRhc2sudGFza19pZCkge1xyXG4gICAgICAgIHNldFRhc2sobG9hZFRhc2spXHJcbiAgICAgICAgc2V0SXNSZWFzc2lnbk1vZGUodHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0SXNSZWFzc2lnbk1vZGUoZmFsc2UpO1xyXG4gICAgICAgIHNldFRhc2sobnVsbCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgfVxyXG5cclxuICBjb25zdCBmZXRjaE9mZmljZXJzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGFza1NlcnZpY2UuZ2V0T2ZmaWNlcnMoKTtcclxuICAgICAgY29uc3Qgb2ZmaWNlcnNEYXRhID0gcmVzcG9uc2UuZGF0YSB8fCBbXTtcclxuICAgICAgc2V0T2ZmaWNlcnMob2ZmaWNlcnNEYXRhKTtcclxuXHJcbiAgICAgIGlmIChvZmZpY2Vyc0RhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdObyBvZmZpY2VycyBmb3VuZCBmb3IgdGFzayBhc3NpZ25tZW50Jyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG9mZmljZXJzOicsIGVycm9yKTtcclxuICAgICAgc2V0T2ZmaWNlcnMoW10pO1xyXG4gICAgICBzaG93RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG9mZmljZXJzLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0VGFza1R5cGVGcm9tSXRlbVR5cGUgPSAoaXRlbVR5cGU6IHN0cmluZyk6IFRhc2tUeXBlID0+IHtcclxuICAgIHN3aXRjaCAoaXRlbVR5cGUpIHtcclxuICAgICAgY2FzZSAnYXBwbGljYXRpb24nOlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5FVkFMVUFUSU9OO1xyXG4gICAgICBjYXNlICdkYXRhX2JyZWFjaCc6XHJcbiAgICAgICAgcmV0dXJuIFRhc2tUeXBlLkRBVEFfQlJFQUNIO1xyXG4gICAgICBjYXNlICdjb21wbGFpbnQnOlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5DT01QTEFJTlQ7XHJcbiAgICAgIGNhc2UgJ2luc3BlY3Rpb24nOlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5JTlNQRUNUSU9OO1xyXG4gICAgICBjYXNlICdkb2N1bWVudF9yZXZpZXcnOlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5ET0NVTUVOVF9SRVZJRVc7XHJcbiAgICAgIGNhc2UgJ3Rhc2snOlxyXG4gICAgICAgIHJldHVybiBUYXNrVHlwZS5BUFBMSUNBVElPTjsgLy8gRGVmYXVsdCBmb3IgZXhpc3RpbmcgdGFza3NcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gVGFza1R5cGUuQVBQTElDQVRJT047XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0VGFza1RpdGxlID0gKGl0ZW1UeXBlOiBzdHJpbmcsIGl0ZW1UaXRsZT86IHN0cmluZyk6IHN0cmluZyA9PiB7XHJcbiAgICBjb25zdCBiYXNlVGl0bGUgPSBpdGVtVGl0bGUgfHwgJ1VudGl0bGVkJztcclxuICAgIHN3aXRjaCAoaXRlbVR5cGUpIHtcclxuICAgICAgY2FzZSAnYXBwbGljYXRpb24nOlxyXG4gICAgICAgIHJldHVybiBgQXBwbGljYXRpb24gRXZhbHVhdGlvbjogJHtiYXNlVGl0bGV9YDtcclxuICAgICAgY2FzZSAnZGF0YV9icmVhY2gnOlxyXG4gICAgICAgIHJldHVybiBgRGF0YSBCcmVhY2ggSW52ZXN0aWdhdGlvbjogJHtiYXNlVGl0bGV9YDtcclxuICAgICAgY2FzZSAnY29tcGxhaW50JzpcclxuICAgICAgICByZXR1cm4gYENvbXBsYWludCBSZXZpZXc6ICR7YmFzZVRpdGxlfWA7XHJcbiAgICAgIGNhc2UgJ2luc3BlY3Rpb24nOlxyXG4gICAgICAgIHJldHVybiBgSW5zcGVjdGlvbiBUYXNrOiAke2Jhc2VUaXRsZX1gO1xyXG4gICAgICBjYXNlICdkb2N1bWVudF9yZXZpZXcnOlxyXG4gICAgICAgIHJldHVybiBgRG9jdW1lbnQgUmV2aWV3OiAke2Jhc2VUaXRsZX1gO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiBgVGFzazogJHtiYXNlVGl0bGV9YDtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRUYXNrRGVzY3JpcHRpb24gPSAoaXRlbVR5cGU6IHN0cmluZywgaXRlbVRpdGxlPzogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICAgIGNvbnN0IGJhc2VUaXRsZSA9IGl0ZW1UaXRsZSB8fCAnaXRlbSc7XHJcbiAgICBzd2l0Y2ggKGl0ZW1UeXBlKSB7XHJcbiAgICAgIGNhc2UgJ2FwcGxpY2F0aW9uJzpcclxuICAgICAgICByZXR1cm4gYEV2YWx1YXRlIGFuZCByZXZpZXcgYXBwbGljYXRpb24gJHtiYXNlVGl0bGV9IGZvciBjb21wbGlhbmNlIGFuZCBhcHByb3ZhbC5gO1xyXG4gICAgICBjYXNlICdkYXRhX2JyZWFjaCc6XHJcbiAgICAgICAgcmV0dXJuIGBJbnZlc3RpZ2F0ZSBhbmQgYXNzZXNzIGRhdGEgYnJlYWNoIHJlcG9ydCAke2Jhc2VUaXRsZX0gZm9yIHJlZ3VsYXRvcnkgY29tcGxpYW5jZS5gO1xyXG4gICAgICBjYXNlICdjb21wbGFpbnQnOlxyXG4gICAgICAgIHJldHVybiBgUmV2aWV3IGFuZCByZXNvbHZlIGNvbXBsYWludCAke2Jhc2VUaXRsZX0gYWNjb3JkaW5nIHRvIHJlZ3VsYXRvcnkgcHJvY2VkdXJlcy5gO1xyXG4gICAgICBjYXNlICdpbnNwZWN0aW9uJzpcclxuICAgICAgICByZXR1cm4gYENvbmR1Y3QgaW5zcGVjdGlvbiBmb3IgJHtiYXNlVGl0bGV9IHRvIGVuc3VyZSByZWd1bGF0b3J5IGNvbXBsaWFuY2UuYDtcclxuICAgICAgY2FzZSAnZG9jdW1lbnRfcmV2aWV3JzpcclxuICAgICAgICByZXR1cm4gYFJldmlldyBhbmQgdmFsaWRhdGUgZG9jdW1lbnQgJHtiYXNlVGl0bGV9IGZvciBhY2N1cmFjeSBhbmQgY29tcGxpYW5jZS5gO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiBgUHJvY2VzcyBhbmQgcmV2aWV3ICR7YmFzZVRpdGxlfS5gO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUFzc2lnbiA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghc2VsZWN0ZWRPZmZpY2VyKSB7XHJcbiAgICAgIHNob3dFcnJvcignUGxlYXNlIHNlbGVjdCBhbiBvZmZpY2VyJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWR1ZURhdGUpIHtcclxuICAgICAgc2hvd0Vycm9yKCdQbGVhc2Ugc2VsZWN0IGEgZHVlIGRhdGUnKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICghaXNSZWFzc2lnbk1vZGUgJiYgIWl0ZW1JZCkge1xyXG4gICAgICBzaG93RXJyb3IoJ0l0ZW0gSUQgaXMgbWlzc2luZycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0QXNzaWduaW5nKHRydWUpO1xyXG5cclxuICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmIChpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaykge1xyXG4gICAgICAgIC8vIFJlYXNzaWduIGV4aXN0aW5nIHRhc2tcclxuICAgICAgICBhd2FpdCB0YXNrU2VydmljZS5yZWFzc2lnblRhc2sobG9hZFRhc2sudGFza19pZCwge1xyXG4gICAgICAgICAgYXNzaWduZWRUbzogc2VsZWN0ZWRPZmZpY2VyLFxyXG4gICAgICAgICAgY29tbWVudDogY29tbWVudC50cmltKCkgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgICAgZHVlX2RhdGU6IGR1ZURhdGUsXHJcbiAgICAgICAgICBwcmlvcml0eTogVGFza1ByaW9yaXR5Lk1FRElVTVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHNob3dTdWNjZXNzKCdUYXNrIHJlYXNzaWduZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgICAgb25SZWFzc2lnblN1Y2Nlc3M/LigpO1xyXG5cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zdCB0YXNrRGF0YTogQ3JlYXRlVGFza0R0byAgPSB7XHJcbiAgICAgICAgICB0YXNrX3R5cGU6IGdldFRhc2tUeXBlRnJvbUl0ZW1UeXBlKGl0ZW1UeXBlKSxcclxuICAgICAgICAgIHRpdGxlOiBnZXRUYXNrVGl0bGUoaXRlbVR5cGUsIGl0ZW1UaXRsZSksXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZ2V0VGFza0Rlc2NyaXB0aW9uKGl0ZW1UeXBlLCBpdGVtVGl0bGUpLFxyXG4gICAgICAgICAgcHJpb3JpdHk6IFRhc2tQcmlvcml0eS5NRURJVU0sXHJcbiAgICAgICAgICBzdGF0dXM6IFRhc2tTdGF0dXMuUEVORElORyxcclxuICAgICAgICAgIGVudGl0eV90eXBlOiBpdGVtVHlwZSxcclxuICAgICAgICAgIGVudGl0eV9pZDogaXRlbUlkISxcclxuICAgICAgICAgIGFzc2lnbmVkX3RvOiBzZWxlY3RlZE9mZmljZXIsXHJcbiAgICAgICAgICBkdWVfZGF0ZTogZHVlRGF0ZSxcclxuICAgICAgICAgIG1ldGFkYXRhOiB7XHJcbiAgICAgICAgICAgIGNvbW1lbnQ6IGNvbW1lbnQudHJpbSgpIHx8IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgb3JpZ2luYWxfaXRlbV90aXRsZTogaXRlbVRpdGxlLFxyXG4gICAgICAgICAgICBhc3NpZ25tZW50X2NvbnRleHQ6ICdtYW51YWxfYXNzaWdubWVudCdcclxuICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIC8vIEZvciBvdGhlciBlbnRpdHkgdHlwZXMsIGNyZWF0ZSBhIG5ldyB0YXNrXHJcbiAgICAgICAgYXdhaXQgY3JlYXRlTmV3VGFzayh0YXNrRGF0YSk7XHJcbiAgICAgICAgb25SZWFzc2lnblN1Y2Nlc3M/LigpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBvbkNsb3NlKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciAke2lzUmVhc3NpZ25Nb2RlID8gJ3JlYXNzaWduaW5nJyA6ICdjcmVhdGluZyBhc3NpZ25tZW50J30gdGFzazpgLCBlcnJvcik7XHJcbiAgICAgIHNob3dFcnJvcihgRmFpbGVkIHRvICR7aXNSZWFzc2lnbk1vZGUgPyAncmVhc3NpZ24nIDogJ2Fzc2lnbid9IHRhc2tgKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldEFzc2lnbmluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY3JlYXRlIGEgbmV3IHRhc2tcclxuICBhc3luYyBmdW5jdGlvbiBjcmVhdGVOZXdUYXNrKHRhc2tEYXRhOiBDcmVhdGVUYXNrRHRvKSB7XHJcbiAgICBjb25zdCBjcmVhdGVkVGFzayA9IGF3YWl0IHRhc2tTZXJ2aWNlLmNyZWF0ZVRhc2sodGFza0RhdGEpO1xyXG4gICAgc2hvd1N1Y2Nlc3MoYFN1Y2Nlc3NmdWxseSBhc3NpZ25lZCAke2l0ZW1UeXBlLnJlcGxhY2UoJ18nLCAnICcpfSB0byBvZmZpY2VyYCk7XHJcbiAgICBvbkFzc2lnblN1Y2Nlc3M/LigpO1xyXG4gIH1cclxuICBcclxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIHAtNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyB3LWZ1bGwgbWF4LXctMnhsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbCBzaGFkb3ctMnhsXCI+XHJcbiAgICAgICAgey8qIEhlYWRlciAtIEZpeGVkICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktOTAwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9e2lzUmVhc3NpZ25Nb2RlID8gXCJyaS11c2VyLXNoYXJlZC1saW5lIHRleHQtd2hpdGUgdGV4dC1sZ1wiIDogXCJyaS10YXNrLWxpbmUgdGV4dC13aGl0ZSB0ZXh0LWxnXCJ9PjwvaT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxyXG4gICAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gJ1JlYXNzaWduIFRhc2snIDogYENyZWF0ZSBUYXNrIGZvciAke2l0ZW1UeXBlLnJlcGxhY2UoJ18nLCAnICcpLnRvVXBwZXJDYXNlKCl9YH1cclxuICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdUcmFuc2ZlciB0YXNrIHRvIGFub3RoZXIgb2ZmaWNlcicgOiAnQXNzaWduIHRoaXMgaXRlbSB0byBhbiBvZmZpY2VyIGZvciBwcm9jZXNzaW5nJ31cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMzAwXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktY2xvc2UtbGluZSB0ZXh0LXhsXCI+PC9pPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBDb250ZW50IC0gU2Nyb2xsYWJsZSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02IGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgey8qIFRhc2sgRGV0YWlscyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctYmx1ZS01MCBkYXJrOmJnLWJsdWUtOTAwLzIwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCBkYXJrOmJvcmRlci1ibHVlLTgwMFwiPlxyXG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIGRhcms6dGV4dC1ibHVlLTEwMCBtYi0yIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwicmktdGFzay1saW5lIG1yLTJcIj48L2k+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gJ1Rhc2sgRGV0YWlsczonIDogJ1Rhc2sgdG8gYmUgQ3JlYXRlZDonfVxyXG4gICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDAgZGFyazp0ZXh0LWJsdWUtMzAwIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gbG9hZFRhc2s/LnRpdGxlIDogZ2V0VGFza1RpdGxlKGl0ZW1UeXBlLCBpdGVtVGl0bGUpfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMSBicmVhay13b3JkcyB3aGl0ZXNwYWNlLXByZS13cmFwIG92ZXJmbG93LXdyYXAtYW55d2hlcmUgaHlwaGVucy1hdXRvXCI+XHJcbiAgICAgICAgICAgICAge2lzUmVhc3NpZ25Nb2RlID8gbG9hZFRhc2s/LmRlc2NyaXB0aW9uIDogZ2V0VGFza0Rlc2NyaXB0aW9uKGl0ZW1UeXBlLCBpdGVtVGl0bGUpfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaz8udGFza19udW1iZXIgJiYgKFxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgVGFzayAje2xvYWRUYXNrPy50YXNrX251bWJlcn1cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSAmJiBsb2FkVGFzaz8uYXNzaWduZWUgJiYgKFxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgQ3VycmVudGx5IGFzc2lnbmVkIHRvOiB7bG9hZFRhc2suYXNzaWduZWUuZmlyc3RfbmFtZX0ge2xvYWRUYXNrLmFzc2lnbmVlLmxhc3RfbmFtZX1cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogT2ZmaWNlciBTZWxlY3Rpb24gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cclxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdSZWFzc2lnbiB0byBPZmZpY2VyIConIDogJ0Fzc2lnbiB0byBPZmZpY2VyIConfVxyXG4gICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTQgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgIExvYWRpbmcgb2ZmaWNlcnMuLi5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRPZmZpY2VyfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZE9mZmljZXIoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXByaW1hcnkgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3QgYW4gb2ZmaWNlci4uLjwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAge29mZmljZXJzLm1hcCgob2ZmaWNlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17b2ZmaWNlci51c2VyX2lkfSB2YWx1ZT17b2ZmaWNlci51c2VyX2lkfT5cclxuICAgICAgICAgICAgICAgICAgICB7b2ZmaWNlci5maXJzdF9uYW1lfSB7b2ZmaWNlci5sYXN0X25hbWV9IC0ge29mZmljZXIuZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAge29mZmljZXIuZGVwYXJ0bWVudCA/IGAgKCR7dHlwZW9mIG9mZmljZXIuZGVwYXJ0bWVudCA9PT0gJ3N0cmluZycgPyBvZmZpY2VyLmRlcGFydG1lbnQgOiBvZmZpY2VyLmRlcGFydG1lbnQubmFtZX0pYCA6ICcnfVxyXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIER1ZSBEYXRlICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICBEdWUgRGF0ZSAqXHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17ZHVlRGF0ZX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldER1ZURhdGUoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIG1pbj17bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19IC8vIFByZXZlbnQgcGFzdCBkYXRlc1xyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci1wcmltYXJ5IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMTAwXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBDb21tZW50ICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICB7aXNSZWFzc2lnbk1vZGUgPyAnUmVhc3NpZ25tZW50IE5vdGVzIChPcHRpb25hbCknIDogJ1Rhc2sgTm90ZXMgKE9wdGlvbmFsKSd9XHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgICAgIHZhbHVlPXtjb21tZW50fVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29tbWVudChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2lzUmVhc3NpZ25Nb2RlID8gJ0FkZCBhbnkgbm90ZXMgYWJvdXQgdGhpcyByZWFzc2lnbm1lbnQuLi4nIDogJ0FkZCBhbnkgc3BlY2lmaWMgaW5zdHJ1Y3Rpb25zIG9yIGNvbnRleHQgZm9yIHRoaXMgbG9hZFRhc2suLi4nfVxyXG4gICAgICAgICAgICAgIHJvd3M9ezN9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXByaW1hcnkgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFNlbGVjdGVkIE9mZmljZXIgU3VtbWFyeSAqL31cclxuICAgICAgICAgIHtzZWxlY3RlZE9mZmljZXIgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWdyZWVuLTUwIGRhcms6YmctZ3JlZW4tOTAwLzIwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTgwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi05MDAgZGFyazp0ZXh0LWdyZWVuLTEwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBTZWxlY3RlZCBPZmZpY2VyOlxyXG4gICAgICAgICAgICAgIDwvaDQ+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cclxuICAgICAgICAgICAgICAgIHsoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBvZmZpY2VyID0gb2ZmaWNlcnMuZmluZChvID0+IG8udXNlcl9pZCA9PT0gc2VsZWN0ZWRPZmZpY2VyKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIG9mZmljZXIgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgIHtvZmZpY2VyLmZpcnN0X25hbWV9IHtvZmZpY2VyLmxhc3RfbmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDxiciAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAge29mZmljZXIuZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICB7b2ZmaWNlci5kZXBhcnRtZW50ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBEZXBhcnRtZW50OiB7dHlwZW9mIG9mZmljZXIuZGVwYXJ0bWVudCA9PT0gJ3N0cmluZycgPyBvZmZpY2VyLmRlcGFydG1lbnQgOiBvZmZpY2VyLmRlcGFydG1lbnQubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICApIDogJ09mZmljZXIgbm90IGZvdW5kJztcclxuICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAge2R1ZURhdGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPkR1ZSBEYXRlOjwvc3Ryb25nPiB7bmV3IERhdGUoZHVlRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBGb290ZXIgLSBGaXhlZCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zIHAtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktOTAwXCI+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS02MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBc3NpZ259XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRPZmZpY2VyIHx8ICFkdWVEYXRlIHx8IGFzc2lnbmluZ31cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBiZy1wcmltYXJ5IGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgcm91bmRlZC1tZCBob3ZlcjpiZy1wcmltYXJ5LWRhcmsgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHthc3NpZ25pbmcgPyAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTJcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgIHtpc1JlYXNzaWduTW9kZSA/ICdSZWFzc2lnbmluZy4uLicgOiAnQ3JlYXRpbmcgVGFzay4uLid9XHJcbiAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT17aXNSZWFzc2lnbk1vZGUgPyBcInJpLXVzZXItc2hhcmVkLWxpbmUgbXItMlwiIDogXCJyaS10YXNrLWxpbmUgbXItMlwifT48L2k+XHJcbiAgICAgICAgICAgICAgICB7aXNSZWFzc2lnbk1vZGUgPyAnUmVhc3NpZ24gVGFzaycgOiAnQ3JlYXRlIFRhc2snfVxyXG4gICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFzc2lnbk1vZGFsO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVRvYXN0IiwidGFza1NlcnZpY2UiLCJUYXNrVHlwZSIsIlRhc2tQcmlvcml0eSIsIlRhc2tTdGF0dXMiLCJBc3NpZ25Nb2RhbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJpdGVtSWQiLCJpdGVtVHlwZSIsIml0ZW1UaXRsZSIsIm9uQXNzaWduU3VjY2VzcyIsInRhc2siLCJvblJlYXNzaWduU3VjY2VzcyIsInNob3dTdWNjZXNzIiwic2hvd0Vycm9yIiwib2ZmaWNlcnMiLCJzZXRPZmZpY2VycyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiYXNzaWduaW5nIiwic2V0QXNzaWduaW5nIiwic2VsZWN0ZWRPZmZpY2VyIiwic2V0U2VsZWN0ZWRPZmZpY2VyIiwiY29tbWVudCIsInNldENvbW1lbnQiLCJkdWVEYXRlIiwic2V0RHVlRGF0ZSIsImxvYWRUYXNrIiwic2V0VGFzayIsImlzUmVhc3NpZ25Nb2RlIiwic2V0SXNSZWFzc2lnbk1vZGUiLCJmZXRjaE9mZmljZXJzIiwiZmV0Y2hFeGlzdGluZ1Rhc2tzIiwiYXNzaWduZWRfdG8iLCJkdWVfZGF0ZSIsInNwbGl0IiwiZ2V0VGFza0ZvckFwcGxpY2F0aW9uIiwidGFza19pZCIsInJlc3BvbnNlIiwiZ2V0T2ZmaWNlcnMiLCJvZmZpY2Vyc0RhdGEiLCJkYXRhIiwibGVuZ3RoIiwiY29uc29sZSIsIndhcm4iLCJlcnJvciIsImdldFRhc2tUeXBlRnJvbUl0ZW1UeXBlIiwiRVZBTFVBVElPTiIsIkRBVEFfQlJFQUNIIiwiQ09NUExBSU5UIiwiSU5TUEVDVElPTiIsIkRPQ1VNRU5UX1JFVklFVyIsIkFQUExJQ0FUSU9OIiwiZ2V0VGFza1RpdGxlIiwiYmFzZVRpdGxlIiwiZ2V0VGFza0Rlc2NyaXB0aW9uIiwiaGFuZGxlQXNzaWduIiwicmVhc3NpZ25UYXNrIiwiYXNzaWduZWRUbyIsInRyaW0iLCJ1bmRlZmluZWQiLCJwcmlvcml0eSIsIk1FRElVTSIsInRhc2tEYXRhIiwidGFza190eXBlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInN0YXR1cyIsIlBFTkRJTkciLCJlbnRpdHlfdHlwZSIsImVudGl0eV9pZCIsIm1ldGFkYXRhIiwib3JpZ2luYWxfaXRlbV90aXRsZSIsImFzc2lnbm1lbnRfY29udGV4dCIsImNyZWF0ZU5ld1Rhc2siLCJjcmVhdGVkVGFzayIsImNyZWF0ZVRhc2siLCJyZXBsYWNlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaSIsImgzIiwidG9VcHBlckNhc2UiLCJwIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJoNCIsInRhc2tfbnVtYmVyIiwiYXNzaWduZWUiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwibGFiZWwiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9wdGlvbiIsIm1hcCIsIm9mZmljZXIiLCJ1c2VyX2lkIiwiZW1haWwiLCJkZXBhcnRtZW50IiwibmFtZSIsImlucHV0IiwibWluIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidGV4dGFyZWEiLCJwbGFjZWhvbGRlciIsInJvd3MiLCJmaW5kIiwibyIsImJyIiwic3Ryb25nIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/AssignModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/activityNotesService.ts":
/*!**********************************************!*\
  !*** ./src/services/activityNotesService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityNotesService: () => (/* binding */ activityNotesService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n\n\nclass ActivityNotesService {\n    async create(data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(this.baseUrl, data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findAll(query) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(this.baseUrl, {\n            params: query\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntity(entityType, entityId) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findByEntityAndStep(entityType, entityId, step) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/entity/\").concat(entityType, \"/\").concat(entityId, \"/step/\").concat(step));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async findOne(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/\").concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async update(id, data) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id), data);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async archive(id) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"\".concat(this.baseUrl, \"/\").concat(id, \"/archive\"));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async softDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/soft\"));\n    }\n    async hardDelete(id) {\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id, \"/hard\"));\n    }\n    // Specialized methods for evaluation workflow\n    async createEvaluationComment(applicationId, step, comment, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/evaluation-comment\"), {\n            applicationId,\n            step,\n            comment,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    async createStatusUpdate(applicationId, statusChange, metadata) {\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/status-update\"), {\n            applicationId,\n            statusChange,\n            metadata\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n    }\n    // Helper methods for common use cases\n    async getEvaluationComments(applicationId, step) {\n        const query = {\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'evaluation_comment',\n            status: 'active'\n        };\n        if (step) {\n            query.step = step;\n        }\n        return this.findAll(query);\n    }\n    async getApplicationNotes(applicationId) {\n        return this.findByEntity('application', applicationId);\n    }\n    async getApplicationStatusUpdates(applicationId) {\n        return this.findAll({\n            entity_type: 'application',\n            entity_id: applicationId,\n            note_type: 'status_update',\n            status: 'active'\n        });\n    }\n    constructor(){\n        this.baseUrl = '/activity-notes';\n    }\n}\nconst activityNotesService = new ActivityNotesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/activityNotesService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/task-assignment.ts":
/*!*****************************************!*\
  !*** ./src/services/task-assignment.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taskAssignmentService: () => (/* binding */ taskAssignmentService),\n/* harmony export */   taskService: () => (/* binding */ taskService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n\n\nconst taskService = {\n    // Main task CRUD operations\n    getTasks: async (params)=>{\n        // Handle assignment_status filter by using different endpoints or filters\n        if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'unassigned') {\n            // Use the unassigned tasks endpoint\n            return taskService.getUnassignedTasks(params);\n        } else if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'assigned') {\n            // Use the assigned tasks endpoint instead of recursive call\n            return taskService.getAssignedTasks(params);\n        }\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        if (params === null || params === void 0 ? void 0 : params.assigned_to) searchParams.append('filter.assigned_to', params.assigned_to);\n        const queryString = searchParams.toString();\n        const url = \"/tasks\".concat(queryString ? \"?\".concat(queryString) : '');\n        console.log('🔗 Making API call to:', url);\n        console.log('📊 Request params:', params);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        console.log('✅ API response:', response.data);\n        return response.data;\n    },\n    getUnassignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getAssignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getMyTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskStats: async ()=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/tasks/stats');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTask: async (taskId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/\".concat(taskId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    createTask: async (taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/tasks', taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    updateTask: async (taskId, taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.patch(\"/tasks/\".concat(taskId), taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskForApplication: async (applicationId)=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/application/\".concat(applicationId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return null; // No task found for application\n            }\n            throw error;\n        }\n    },\n    assignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/assign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    reassignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/reassign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    deleteTask: async (taskId)=>{\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete(\"/tasks/\".concat(taskId));\n    },\n    // Get users for assignment (officers only, exclude customers)\n    getUsers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                params: {\n                    limit: 100,\n                    filter: {\n                        exclude_customers: true\n                    }\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching users:', error);\n        }\n    },\n    // Get officers specifically (non-customer users)\n    getOfficers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users/list/officers');\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            // Fallback to regular users endpoint with filtering\n            try {\n                const fallbackResponse = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                    params: {\n                        limit: 100,\n                        filter: {\n                            exclude_customers: true\n                        }\n                    }\n                });\n                return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(fallbackResponse);\n            } catch (fallbackError) {\n                console.error('Error fetching users as fallback:', fallbackError);\n            }\n        }\n    }\n};\n// Legacy service for backward compatibility\nconst taskAssignmentService = {\n    // Generic task management methods\n    getUnassignedTasks: taskService.getUnassignedTasks,\n    getAssignedTasks: taskService.getAssignedTasks,\n    assignTask: taskService.assignTask,\n    getTaskById: taskService.getTask,\n    // Legacy application-specific methods (for backward compatibility)\n    getUnassignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get all applications (including assigned)\n    getAllApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get applications assigned to current user\n    getMyAssignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get officers for assignment\n    getOfficers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users');\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            return {\n                data: []\n            };\n        }\n    },\n    // Assign application to officer\n    assignApplication: async (applicationId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/applications/\".concat(applicationId, \"/assign\"), assignData);\n        return response.data;\n    },\n    // Get application details\n    getApplication: async (applicationId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/applications/\".concat(applicationId));\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/task-assignment.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib */ \"(app-pages-browser)/./src/lib/index.ts\");\n\n\n// Re-export for backward compatibility\nconst userService = {\n    // Get all users with pagination\n    async getUsers () {\n        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (query.page) params.set('page', query.page.toString());\n        if (query.limit) params.set('limit', query.limit.toString());\n        if (query.search) params.set('search', query.search);\n        if (query.sortBy) {\n            query.sortBy.forEach((sort)=>params.append('sortBy', sort));\n        }\n        if (query.searchBy) {\n            query.searchBy.forEach((search)=>params.append('searchBy', search));\n        }\n        if (query.filter) {\n            Object.entries(query.filter).forEach((param)=>{\n                let [key, value] = param;\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(\"filter.\".concat(key), v));\n                } else {\n                    params.set(\"filter.\".concat(key), value);\n                }\n            });\n        }\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"?\".concat(params.toString()));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user by ID\n    async getUser (id) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get(\"/\".concat(id));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user emails for predictive input\n    async getUserEmails (searchTerm) {\n        try {\n            var _usersData_data;\n            const params = new URLSearchParams();\n            if (searchTerm) params.set('search', searchTerm);\n            params.set('limit', '20'); // Limit to 20 suggestions\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/users?\".concat(params.toString()));\n            const usersData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n            // Extract emails from users\n            const emails = ((_usersData_data = usersData.data) === null || _usersData_data === void 0 ? void 0 : _usersData_data.map((user)=>user.email).filter(Boolean)) || [];\n            return emails;\n        } catch (error) {\n            console.warn('Failed to fetch user emails:', error);\n            return [];\n        }\n    },\n    // Get user by ID (alias for consistency)\n    async getUserById (id) {\n        return this.getUser(id);\n    },\n    // Get current user profile\n    async getProfile () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Create new user\n    async createUser (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update user\n    async updateUser (id, userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put(\"/\".concat(id), userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Update current user profile\n    async updateProfile (userData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile', userData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Change password\n    async changePassword (passwordData) {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.put('/profile/password', passwordData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload avatar\n    async uploadAvatar (file) {\n        console.log('userService: uploadAvatar called', {\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type\n        });\n        const formData = new FormData();\n        formData.append('avatar', file);\n        try {\n            const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.post('/profile/avatar', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _axiosError_response, _axiosError_response1, _axiosError_response2;\n            const axiosError = error;\n            console.error('userService: Upload failed', {\n                status: (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status,\n                statusText: (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.statusText,\n                data: (_axiosError_response2 = axiosError.response) === null || _axiosError_response2 === void 0 ? void 0 : _axiosError_response2.data,\n                message: axiosError.message\n            });\n            throw error;\n        }\n    },\n    // Remove avatar\n    async removeAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Get user avatar document\n    async getUserAvatar () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.get('/profile/avatar');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user\n    async deleteUser (id) {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.usersApiClient.delete(\"/\".concat(id));\n    },\n    // Get user signature\n    async getUserSignature () {\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/documents/user/signature');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Upload user signature\n    async uploadSignature (file) {\n        const formData = new FormData();\n        formData.append('signature', file);\n        const response = await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/documents/user/signature', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    // Delete user signature\n    async deleteSignature () {\n        await _lib__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete('/documents/user/signature');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/userService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy90eXBlcy90YXNrLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUtPLHNDQUFLQTs7Ozs7Ozs7Ozs7O1dBQUFBO01BWVg7QUFFTSx3Q0FBS0M7Ozs7OztXQUFBQTtNQU1YO0FBRU0sMENBQUtDOzs7OztXQUFBQTtNQUtYIiwic291cmNlcyI6WyJEOlxcTWVtb3J5IEJ1c2luZXNzIFNvbHV0aW9pbnNcXFByb2plY3RzXFxNQUNSQVxccHJvamVjdFxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcdHlwZXNcXHRhc2sudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVudGl0eSwgVXNlclJlZmVyZW5jZSB9IGZyb20gJy4vaW5kZXgnO1xyXG5cclxuLy8gRGVmaW5lIHByb3BlciB0eXBlcyBmb3IgdGFzayBtZXRhZGF0YVxyXG5leHBvcnQgdHlwZSBUYXNrTWV0YWRhdGEgPSBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBudW1iZXIgfCBib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZD47XHJcblxyXG5leHBvcnQgZW51bSBUYXNrVHlwZSB7XHJcbiAgQVBQTElDQVRJT04gPSAnYXBwbGljYXRpb24nLFxyXG4gIENPTVBMQUlOVCA9ICdjb21wbGFpbnQnLFxyXG4gIERBVEFfQlJFQUNIID0gJ2RhdGFfYnJlYWNoJyxcclxuICBFVkFMVUFUSU9OID0gJ2V2YWx1YXRpb24nLFxyXG4gIElOU1BFQ1RJT04gPSAnaW5zcGVjdGlvbicsXHJcbiAgRE9DVU1FTlRfUkVWSUVXID0gJ2RvY3VtZW50X3JldmlldycsXHJcbiAgQ09NUExJQU5DRV9DSEVDSyA9ICdjb21wbGlhbmNlX2NoZWNrJyxcclxuICBGT0xMT1dfVVAgPSAnZm9sbG93X3VwJyxcclxuICBQQVlNRU5UX1ZFUklGSUNBVElPTiA9ICdwYXltZW50X3ZlcmlmaWNhdGlvbicsXHJcbiAgVVNFUiA9ICd1c2VyJyxcclxuICBPVEhFUiA9ICdvdGhlcicsXHJcbn1cclxuXHJcbmV4cG9ydCBlbnVtIFRhc2tTdGF0dXMge1xyXG4gIFBFTkRJTkcgPSAncGVuZGluZycsXHJcbiAgSU5fUFJPR1JFU1MgPSAnaW5fcHJvZ3Jlc3MnLFxyXG4gIENPTVBMRVRFRCA9ICdjb21wbGV0ZWQnLFxyXG4gIENBTkNFTExFRCA9ICdjYW5jZWxsZWQnLFxyXG4gIE9OX0hPTEQgPSAnb25faG9sZCdcclxufVxyXG5cclxuZXhwb3J0IGVudW0gVGFza1ByaW9yaXR5IHtcclxuICBMT1cgPSAnbG93JyxcclxuICBNRURJVU0gPSAnbWVkaXVtJyxcclxuICBISUdIID0gJ2hpZ2gnLFxyXG4gIFVSR0VOVCA9ICd1cmdlbnQnXHJcbn1cclxuXHJcblxyXG4vLyBUYXNrIGludGVyZmFjZXNcclxuZXhwb3J0IGludGVyZmFjZSBUYXNrIGV4dGVuZHMgQmFzZUVudGl0eSB7XHJcbiAgdGFza19pZDogc3RyaW5nO1xyXG4gIHRhc2tfbnVtYmVyOiBzdHJpbmc7XHJcbiAgdGFza190eXBlOiBUYXNrVHlwZTtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHN0YXR1czogVGFza1N0YXR1cztcclxuICBwcmlvcml0eTogVGFza1ByaW9yaXR5O1xyXG4gIGVudGl0eV90eXBlPzogc3RyaW5nO1xyXG4gIGVudGl0eV9pZD86IHN0cmluZztcclxuICBhc3NpZ25lZF90bz86IHN0cmluZztcclxuICBhc3NpZ25lZF9ieT86IHN0cmluZztcclxuICBhc3NpZ25lZF9hdD86IHN0cmluZztcclxuICBkdWVfZGF0ZT86IHN0cmluZztcclxuICBjb21wbGV0ZWRfYXQ/OiBzdHJpbmc7XHJcbiAgcmV2aWV3X25vdGVzPzogc3RyaW5nO1xyXG4gIG1ldGFkYXRhPzogVGFza01ldGFkYXRhO1xyXG4gIFxyXG4gIC8vIFJlbGF0aW9uc1xyXG4gIGFzc2lnbmVlPzogVXNlclJlZmVyZW5jZTtcclxuICBhc3NpZ25lcj86IFVzZXJSZWZlcmVuY2U7XHJcbiAgY3JlYXRvcj86IFVzZXJSZWZlcmVuY2U7XHJcbiAgdXBkYXRlcj86IFVzZXJSZWZlcmVuY2U7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlVGFza0R0byB7XHJcbiAgdGFza190eXBlOiBUYXNrVHlwZTtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHByaW9yaXR5Pzogc3RyaW5nO1xyXG4gIHN0YXR1cz86IHN0cmluZztcclxuICBlbnRpdHlfdHlwZT86IHN0cmluZztcclxuICBlbnRpdHlfaWQ/OiBzdHJpbmc7XHJcbiAgYXNzaWduZWRfdG8/OiBzdHJpbmc7XHJcbiAgZHVlX2RhdGU/OiBzdHJpbmc7XHJcbiAgbWV0YWRhdGE/OiBUYXNrTWV0YWRhdGE7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXBkYXRlVGFza0R0byB7XHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgc3RhdHVzPzogc3RyaW5nO1xyXG4gIHByaW9yaXR5Pzogc3RyaW5nO1xyXG4gIGFzc2lnbmVkX3RvPzogc3RyaW5nO1xyXG4gIGR1ZV9kYXRlPzogc3RyaW5nO1xyXG4gIHJldmlld19ub3Rlcz86IHN0cmluZztcclxuICBjb21wbGV0aW9uX25vdGVzPzogc3RyaW5nO1xyXG4gIGVudGl0eV90eXBlPzogc3RyaW5nO1xyXG4gIGVudGl0eV9pZD86IHN0cmluZztcclxuICBtZXRhZGF0YT86IFRhc2tNZXRhZGF0YTtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBc3NpZ25UYXNrRHRvIHtcclxuICBhc3NpZ25lZFRvOiBzdHJpbmc7XHJcbiAgY29tbWVudD86IHN0cmluZztcclxuICBhc3NpZ25tZW50X25vdGVzPzogc3RyaW5nO1xyXG4gIGR1ZV9kYXRlPzogc3RyaW5nO1xyXG4gIHByaW9yaXR5PzogVGFza1ByaW9yaXR5O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tGaWx0ZXJzIHtcclxuICBzZWFyY2g/OiBzdHJpbmc7XHJcbiAgc3RhdHVzPzogVGFza1N0YXR1cztcclxuICBwcmlvcml0eT86IFRhc2tQcmlvcml0eTtcclxuICB0YXNrX3R5cGU/OiBUYXNrVHlwZTtcclxuICBlbnRpdHlfdHlwZT86IHN0cmluZztcclxuICBhc3NpZ25lZF90bz86IHN0cmluZztcclxuICBhc3NpZ25lZF9ieT86IHN0cmluZztcclxuICBhc3NpZ25tZW50X3N0YXR1cz86IHN0cmluZztcclxuICBkdWVfZGF0ZT86IHN0cmluZztcclxuICBvdmVyZHVlPzogYm9vbGVhbjtcclxufVxyXG5cclxuLy8gVGFzayByZXNwb25zZXNcclxuZXhwb3J0IHR5cGUgVGFza3NSZXNwb25zZSA9IHtcclxuICBkYXRhOiBUYXNrW107XHJcbiAgbWV0YToge1xyXG4gICAgaXRlbXNQZXJQYWdlOiBudW1iZXI7XHJcbiAgICB0b3RhbEl0ZW1zPzogbnVtYmVyO1xyXG4gICAgY3VycmVudFBhZ2U/OiBudW1iZXI7XHJcbiAgICB0b3RhbFBhZ2VzPzogbnVtYmVyO1xyXG4gICAgc29ydEJ5OiBbc3RyaW5nLCBzdHJpbmddW107XHJcbiAgICBzZWFyY2hCeTogc3RyaW5nW107XHJcbiAgICBzZWFyY2g6IHN0cmluZztcclxuICAgIHNlbGVjdDogc3RyaW5nW107XHJcbiAgICBmaWx0ZXI/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBzdHJpbmdbXT47XHJcbiAgfTtcclxuICBsaW5rczoge1xyXG4gICAgZmlyc3Q/OiBzdHJpbmc7XHJcbiAgICBwcmV2aW91cz86IHN0cmluZztcclxuICAgIGN1cnJlbnQ6IHN0cmluZztcclxuICAgIG5leHQ/OiBzdHJpbmc7XHJcbiAgICBsYXN0Pzogc3RyaW5nO1xyXG4gIH07XHJcbn07XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tTdGF0aXN0aWNzIHtcclxuICB0b3RhbDogbnVtYmVyO1xyXG4gIHBlbmRpbmc6IG51bWJlcjtcclxuICBpbl9wcm9ncmVzczogbnVtYmVyO1xyXG4gIGNvbXBsZXRlZDogbnVtYmVyO1xyXG4gIG92ZXJkdWU6IG51bWJlcjtcclxuICBieV9wcmlvcml0eToge1xyXG4gICAgbG93OiBudW1iZXI7XHJcbiAgICBtZWRpdW06IG51bWJlcjtcclxuICAgIGhpZ2g6IG51bWJlcjtcclxuICAgIHVyZ2VudDogbnVtYmVyO1xyXG4gIH07XHJcbiAgYnlfdHlwZTogUmVjb3JkPFRhc2tUeXBlLCBudW1iZXI+O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tOYXZpZ2F0aW9uSW5mbyB7XHJcbiAgdGFzazogVGFzaztcclxuICBjYW5OYXZpZ2F0ZVRvRW50aXR5OiBib29sZWFuO1xyXG59XHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcbi8vIFVzZXIgaW50ZXJmYWNlIGZvciB0YXNrIGFzc2lnbm1lbnRzXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGFza1VzZXIge1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxufVxyXG4vLyBMZWdhY3kgdHlwZSBhbGlhcyBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG5leHBvcnQgdHlwZSBHZW5lcmljVGFzayA9IFRhc2s7XHJcblxyXG4vLyBMZWdhY3kgaW50ZXJmYWNlIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XHJcbmV4cG9ydCBpbnRlcmZhY2UgVGFza0Fzc2lnbm1lbnRBcHBsaWNhdGlvbiB7XHJcbiAgYXBwbGljYXRpb25faWQ6IHN0cmluZztcclxuICBhcHBsaWNhdGlvbl9udW1iZXI6IHN0cmluZztcclxuICBzdGF0dXM6IHN0cmluZztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIGFwcGxpY2FudDoge1xyXG4gICAgYXBwbGljYW50X2lkOiBzdHJpbmc7XHJcbiAgICBjb21wYW55X25hbWU6IHN0cmluZztcclxuICAgIGZpcnN0X25hbWU6IHN0cmluZztcclxuICAgIGxhc3RfbmFtZTogc3RyaW5nO1xyXG4gIH07XHJcbiAgbGljZW5zZV9jYXRlZ29yeToge1xyXG4gICAgbGljZW5zZV9jYXRlZ29yeV9pZDogc3RyaW5nO1xyXG4gICAgbmFtZTogc3RyaW5nO1xyXG4gICAgbGljZW5zZV90eXBlOiB7XHJcbiAgICAgIGxpY2Vuc2VfdHlwZV9pZDogc3RyaW5nO1xyXG4gICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gIH07XHJcbiAgYXNzaWduZWU/OiB7XHJcbiAgICB1c2VyX2lkOiBzdHJpbmc7XHJcbiAgICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgICBsYXN0X25hbWU6IHN0cmluZztcclxuICAgIGVtYWlsOiBzdHJpbmc7XHJcbiAgfTtcclxuICBhc3NpZ25lZF9hdD86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBUYXNrQXNzaWdubWVudE9mZmljZXIge1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBkZXBhcnRtZW50Pzogc3RyaW5nO1xyXG59XHJcblxyXG5cclxuXHJcblxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBUYXNrU3RhdHMge1xyXG4gIHRvdGFsOiBudW1iZXI7XHJcbiAgcGVuZGluZzogbnVtYmVyO1xyXG4gIGluX3Byb2dyZXNzOiBudW1iZXI7XHJcbiAgY29tcGxldGVkOiBudW1iZXI7XHJcbiAgY2FuY2VsbGVkOiBudW1iZXI7XHJcbiAgb25faG9sZDogbnVtYmVyO1xyXG4gIHVuYXNzaWduZWQ6IG51bWJlcjtcclxuICBhc3NpZ25lZDogbnVtYmVyO1xyXG4gIG92ZXJkdWU6IG51bWJlcjtcclxufVxyXG5cclxuLy8gTGVnYWN5IGludGVyZmFjZXMgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuZXhwb3J0IGludGVyZmFjZSBBc3NpZ25BcHBsaWNhdGlvblJlcXVlc3Qge1xyXG4gIGFzc2lnbmVkVG86IHN0cmluZztcclxufSJdLCJuYW1lcyI6WyJUYXNrVHlwZSIsIlRhc2tTdGF0dXMiLCJUYXNrUHJpb3JpdHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});