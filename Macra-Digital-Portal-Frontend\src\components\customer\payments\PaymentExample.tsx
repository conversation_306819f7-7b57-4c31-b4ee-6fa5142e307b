'use client';

import React, { useState } from 'react';
import UploadProofOfPaymentModal from './UploadProofOfPaymentModal';
import ViewInvoiceModal from './ViewInvoiceModal';
import { formatAmount, formatDate } from '@/utils/formatters';

// Example component to demonstrate both modals
const PaymentExample: React.FC = () => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);

  // Mock invoice data
  const mockInvoices = [
    {
      invoice_id: 'inv-001',
      invoice_number: 'INV-2024-001',
      amount: 150000,
      due_date: '2024-02-15T00:00:00Z',
      status: 'pending',
      description: 'Type Approval Application Fee - Smartphone Model XYZ',
      created_at: '2024-01-15T10:30:00Z',
      balance: 150000
    },
    {
      invoice_id: 'inv-002',
      invoice_number: 'INV-2024-002',
      amount: 75000,
      due_date: '2024-02-20T00:00:00Z',
      status: 'overdue',
      description: 'Equipment Testing and Certification Fee',
      created_at: '2024-01-10T14:20:00Z',
      balance: 75000
    },
    {
      invoice_id: 'inv-003',
      invoice_number: 'INV-2024-003',
      amount: 200000,
      due_date: '2024-03-01T00:00:00Z',
      status: 'pending',
      description: 'Annual License Renewal Fee - Telecommunications Equipment',
      created_at: '2024-01-20T09:15:00Z',
      balance: 100000 // Partially paid
    }
  ];

  const handleUploadProof = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowModal(true);
  };

  const handleUploadSuccess = () => {
    console.log('Payment proof uploaded successfully');
    // Refresh invoice list or update status
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Payment Management - Upload Proof Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Click "Upload Proof" on any invoice to see the modal in action
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Outstanding Invoices
          </h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Invoice
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {mockInvoices.map((invoice) => (
                <tr key={invoice.invoice_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {invoice.invoice_number}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        ID: {invoice.invoice_id}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 dark:text-gray-100 max-w-xs">
                      {invoice.description}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatAmount(invoice.amount, 'MWK')}
                    </div>
                    {invoice.balance !== invoice.amount && (
                      <div className="text-xs text-orange-600 dark:text-orange-400">
                        Balance: {formatAmount(invoice.balance, 'MWK')}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {formatDate(invoice.due_date)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(invoice.status)}`}>
                      {invoice.status.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleUploadProof(invoice)}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                    >
                      <i className="ri-upload-line mr-1"></i>
                      Upload Proof
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Features Overview */}
      <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
          Modal Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Design Features:</h4>
            <ul className="space-y-1 text-blue-700 dark:text-blue-300">
              <li>• Fixed header and footer (like AssignModal)</li>
              <li>• Scrollable content area only</li>
              <li>• Responsive design for all screen sizes</li>
              <li>• Dark mode support</li>
              <li>• Loading states and validation</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Functionality:</h4>
            <ul className="space-y-1 text-blue-700 dark:text-blue-300">
              <li>• Complete invoice details display</li>
              <li>• Payment method selection</li>
              <li>• File upload with validation</li>
              <li>• Form validation and error handling</li>
              <li>• Success/error notifications</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Technical Notes */}
      <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-start">
          <i className="ri-information-line text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2"></i>
          <div>
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
              Implementation Notes
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              This modal follows the same structure as the AssignModal with fixed header/footer and scrollable content.
              The file upload includes validation for file type (JPEG, PNG, PDF) and size (max 5MB).
              Form validation ensures all required fields are completed before submission.
            </p>
          </div>
        </div>
      </div>

      {/* Modal */}
      <UploadProofOfPaymentModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedInvoice(null);
        }}
        invoice={selectedInvoice}
        onSuccess={handleUploadSuccess}
      />
    </div>
  );
};

export default PaymentExample;
