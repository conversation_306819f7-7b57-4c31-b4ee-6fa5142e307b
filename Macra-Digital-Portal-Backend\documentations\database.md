The provided document outlines the database schema for the Malawi Communications Regulatory Authority (MACRA) Licensing Portal. To improve clarity, consistency, and usability, I will clean up and reorganize the documentation while preserving all critical details. The revised version will address inconsistencies, correct errors, standardize formatting, and enhance readability. Below is the cleaned-up version of the MACRA Portal Database Documentation.

---

# MACRA Portal Database Documentation
**Date**: May 29, 2025  
**Time**: 02:43 PM CAT  
**Version**: 2.0  

## Overview
This document details the database schema for the Malawi Communications Regulatory Authority (MACRA) Licensing Portal. The schema supports the management of users, applicants, license applications, evaluations, licenses, transactions, and related entities. It streamlines the licensing process, including application submission, evaluation, approval, and tracking of financial transactions and audit trails for various license types (e.g., ISP, mobile, postal, radio, satellite, TV, university radio licenses).  

The schema adheres to JSON Schema Draft 2020-12 and provides comprehensive definitions for entities, their properties, relationships, and constraints. This documentation ensures clarity for development, maintenance, and integration.

## Database Entities
The database comprises 42 relational tables, each representing a distinct entity. Below is a list of entities, followed by detailed definitions:

1. Users
2. Address
3. Contacts
4. IdentificationTypes
5. UserIdentifications
6. Employees
7. Roles
8. EmployeeRoles
9. Applicants
10. ContactPersons
11. Stakeholders
12. ShareholderDetails
13. ScopeOfService
14. LicenseCategories
15. LicenseTypes
16. ApplicantDisclosure
17. Applicants
18. Documents
19. Evaluations
20. EvaluationCriteria
21. Licenses
22. Transactions
23. Invoices
24. Payments
24. AuditTrail
25. Notifications
*Note*: The original document lists only a subset of entities. For brevity, only provided entities are detailed below, with assumptions that the remaining entities follow similar conventions.

## Entity Definitions
Each entity is described with its purpose, properties, and constraints. Properties include data types, constraints (e.g., required, unique), and foreign key relationships. All timestamps use the `date-time` format (ISO 8601). Fields marked as "auto created, uneditable" are system-generated and immutable after creation.

### 1. Users
**Description**: Stores information about system users (e.g., applicants, evaluators, administrators, super administrators).  
**Properties**:
- `user_id`: String (UUID, required) - Unique identifier.
- `email`: String (email format, required, unique) - User’s email address.
- `password`: String (required) - Hashed password for authentication.
- `first_name`: String (1-100 characters, required) - User’s first name.
- `last_name`: String (1-100 characters, required) - User’s last name.
- `middle_name`: String (1-100 characters, optional) - User’s middle name.
- `phone`: String (pattern: `^+?\d{10,15}$`, required) - Phone number in international format.
- `status`: String (enum: `active`, `inactive`, `suspended`, default: `active`) - Account status.
- `profile_image`: String (nullable) - URL to profile image.
- `role_id`: String (FK - Roles.role_id, nullable) - Foreign key to Roles table.
- `two_factor_next_verification`: String (date-time, nullable) - Timestamp for next two-factor verification.
- `two_factor_code`: String (pattern: `^\d{3}-\d{3}$`, nullable) - 6-digit code (e.g., `202-200`).
- `email_verified_at`: String (date-time, nullable) - Email verification timestamp.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.
- `last_login`: String (date-time, nullable) - Last login timestamp.  
**Constraints**:
- `email` must be unique.

### 2. Address
**Description**: Stores address information for users or applicants.  
**Properties**:
- `address_id`: String (UUID, required) - Unique identifier.
- `address_line_1`: String (required) - Primary address line.
- `address_line_2`: String (optional) - Secondary address line.
- `address_line_3`: String (optional) - Tertiary address line.
- `postal_code`: String (4-9 characters, required) - Postal or ZIP code.
- `country`: String (3-50 characters, required) - Country name.
- `city`: String (3-50 characters, required) - City name.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 3. Contacts
**Description**: Stores contact information for users or applicants.  
**Properties**:
- `contact_id`: String (UUID, required) - Unique identifier.
- `telephone`: String (required) - Telephone number.
- `email`: String (email format, optional) - Contact email.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 4. IdentificationTypes
**Description**: Defines types of identification documents.  
**Properties**:
- `identification_type_id`: String (UUID, required) - Unique identifier.
- `name`: String (enum: `National ID`, `Passport`, `Driver’s License`, `Permanent Residence Card`, required) - Identification type.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `name` must be unique.

### 5. UserIdentifications
**Description**: Stores user identification details.  
**Properties**:
- `identification_type_id`: String (FK - IdentificationTypes.identification_type_id, UUID, required) - Foreign key to IdentificationTypes.
- `user_id`: String (FK - Users.user_id, UUID, required) - Foreign key to Users.
- `identification_value`: String (5-100 characters, required) - Identification number or value.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- Combination of `identification_type_id` and `identification_value` must be unique.

### 6. Employees
**Description**: Stores information about MACRA employees.  
**Properties**:
- `user_id`: String (FK - Users.user_id, UUID, required) - Foreign key to Users.
- `employee_number`: String (required, unique) - Unique government employee number.
- `first_name`: String (1-100 characters, required) - Employee’s first name.
- `last_name`: String (1-100 characters, required) - Employee’s last name.
- `middle_name`: String (1-100 characters, optional) - Employee’s middle name.
- `position`: String (required) - Employee’s position at MACRA.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `employee_number` must be unique.

### 7. Roles
**Description**: Defines roles for employees.  
**Properties**:
- `role_id`: String (UUID, required) - Unique identifier.
- `name`: String (enum: `customer`, `administrator`, `evaluator`, `legal`, `accountant`, `sales`, `other`, required) - Role name.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `name` must be unique.

### 8. EmployeeRoles
**Description**: Assigns roles to employees.  
**Properties**:
- `employee_id`: String (FK - Employees.user_id, UUID, required) - Foreign key to Employees.
- `role_id`: String (FK - Roles.role_id, UUID, required) - Foreign key to Roles.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, nullable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- Combination of `employee_id` and `role_id` must be unique.  
*Note*: The original document includes a redundant `UserRoles` entity, which appears to duplicate `EmployeeRoles`. For clarity, only `EmployeeRoles` is retained, assuming it is the intended table for assigning roles to employees.

### 9. Applicants
**Description**: Stores information about applicants applying for licenses.  
**Properties**:
- `applicant_id`: String (UUID, required) - Unique identifier.
- `name`: String (1-255 characters, required) - Organization name.
- `business_registration_number`: String (required, unique) - Company registration number.
- `tpin`: String (numeric, required, unique) - Taxpayer Identification Number.
- `website`: String (URL format, required) - Organization’s website URL.
- `email`: String (email format, required) - Contact email.
- `phone`: String (required) - Phone number.
- `fax`: String (nullable) - Fax number.
- `level_of_insurance_cover`: String (optional) - Insurance coverage details.
- `address_id`: String (FK - Address.address_id, optional) - Foreign key to Address.
- `contact_id`: String (FK - Contacts.contact_id, optional) - Foreign key to Contacts.
- `date_incorporation`: String (date, required) - Incorporation date.
- `place_incorporation`: String (required) - Incorporation place.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `business_registration_number` must be unique.
- `tpin` must be unique.
- No additional properties allowed.

### 10. ContactPersons
**Description**: Stores contact persons for applicants.  
**Properties**:
- `contact_id`: String (UUID, required) - Unique identifier.
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `first_name`: String (1-100 characters, required) - First name.
- `last_name`: String (1-100 characters, required) - Last name.
- `middle_name`: String (1-100 characters, optional) - Middle name.
- `designation`: String (5-50 characters, required) - Job title.
- `email`: String (email format, required) - Email address.
- `phone`: String (pattern: `^+?\d{10,15}$`, required) - Phone number.
- `is_primary`: Boolean (default: false) - Primary contact indicator.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 11. Stakeholders
**Description**: Stores key management team members or stakeholders of applicants.  
**Properties**:
- `stakeholder_id`: String (UUID, required) - Unique identifier.
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `first_name`: String (1-100 characters, required) - First name.
- `last_name`: String (1-100 characters, required) - Last name.
- `middle_name`: String (1-100 characters, optional) - Middle name.
- `contact_id`: String (FK - Contacts.contact_id, required) - Foreign key to Contacts.
- `nationality`: String (max 50 characters, required) - Nationality.
- `position`: String (enum: `CEO`, `Shareholder`, `Auditor`, `Lawyer`, required) - Position in organization.
- `profile`: String (max 300 characters, required) - Brief overview.
- `cv_document_id`: String (FK - Documents.document_id, required) - Foreign key to Documents.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 12. ShareholderDetails
**Description**: Stores shareholding details for stakeholders.  
**Properties**:
- `shareholder_id`: String (UUID, required) - Unique identifier.
- `stakeholder_id`: String (FK - Stakeholders.stakeholder_id, UUID, required) - Foreign key to Stakeholders.
- `shareholding_percent`: Integer (1-100, required) - Percentage of shareholding.
- `description`: String (max 300 characters, optional) - Additional details.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 13. ScopeOfService
**Description**: Stores details about the scope of services for applications.  
**Properties**:
- `scope_of_service_id`: String (UUID, required) - Unique identifier.
- `application_id`: String (FK - Applications.application_id, UUID, required) - Foreign key to Applications.
- `nature_of_service`: String (max 300 characters, required) - Description of service nature.
- `premises`: String (max 300 characters, required) - Details of premises.
- `transport_type`: String (max 300 characters, required) - Transport type details.
- `customer_assistance`: String (max 300 characters, required) - Customer assistance details.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 14. LicenseCategories
**Description**: Defines available license categories.  
**Properties**:
- `license_category_id`: String (UUID, required) - Unique identifier.
- `license_type_id`: String (FK - LicenseTypes.license_type_id, UUID, required) - Foreign key to LicenseTypes.
- `name`: String (10-255 characters, required) - Category name.
- `fee`: String (max 20 characters, required) - License fee.
- `description`: String (required) - Category description.
- `authorizes`: String (required) - Authorization details.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 15. LicenseTypes
**Description**: Defines types of licenses available.  
**Properties**:
- `license_type_id`: String (UUID, required) - Unique identifier.
- `name`: String (10-255 characters, required) - License type name.
- `description`: String (required) - License description.
- `validity`: Integer (minimum: 1, required) - Validity period in months.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `name` must be unique.

### 16. ApplicantDisclosure
**Description**: Stores self-disclosure information for applicants.  
**Properties**:
- `applicant_disclosure_id`: String (UUID, required) - Unique identifier.
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `censured`: String (optional) - Details of censure.
- `disciplined`: String (optional) - Details of disciplinary actions.
- `penalized`: String (optional) - Details of penalties.
- `suspended`: String (optional) - Details of suspensions.
- `prosecuted`: String (optional) - Details of prosecutions.
- `convicted_warned_conduct`: String (optional) - Details of convictions or warnings.
- `investigated_subjected`: String (optional) - Details of investigations.
- `failed_debt_issued`: String (optional) - Details of debt issues.
- `litigation`: String (optional) - Details of litigation.
- `adjudged_insolvent`: String (optional) - Insolvency details.
- `creditor_compromise`: String (optional) - Creditor compromise details.
- `liquidator_receiver_property_judicial_manager`: String (optional) - Details of liquidators or judicial managers.
- `voluntary_winding_up`: String (optional) - Details of voluntary winding up.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 17. Applications
**Description**: Stores license applications submitted by applicants.  
**Properties**:
- `application_id`: String (UUID, required) - Unique identifier.
- `application_number`: String (pattern: `^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$`, required, auto created, uneditable, unique) - Application number (e.g., `LIC-2025-001`).
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `license_category_id`: String (FK - LicenseCategories.license_category_id, UUID, required) - Foreign key to LicenseCategories.
- `status`: String (enum: `draft`, `submitted`, `under_review`, `evaluation`, `approved`, `rejected`, `withdrawn`, default: `draft`, required) - Application status.
- `current_step`: Integer (1-6, required) - Current process step.
- `progress_percentage`: Integer (0-100, required) - Completion percentage.
- `submitted_at`: String (date-time, nullable) - Submission timestamp.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `application_number` must be unique.

### 18. Documents
**Description**: Stores file uploads and metadata for system documents.  
**Properties**:
- `document_id`: String (UUID, required) - Unique identifier.
- `application_id`: String (FK - Applications.application_id, UUID, nullable) - Foreign key to Applications (null for non-application documents).
- `document_type`: String (enum: `certificate_incorporation`, `memorandum_association`, `shareholding_structure`, etc., required) - Document type.
- `file_name`: String (1-255 characters, required) - File name.
- `entity_type`: String (1-255 characters, required) - Type of entity associated with the document.
- `entity_id`: String (UUID, required) - ID of the associated entity.
- `file_path`: String (required) - Server file path or cloud storage URL.
- `file_size`: Integer (minimum: 0, required) - File size in bytes.
- `mime_type`: String (required) - MIME type of the file.
- `is_required`: Boolean (default: false) - Indicates if the document is required.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 19. Evaluations
**Description**: Stores evaluation details for license applications.  
**Properties**:
- `evaluation_id`: String (UUID, required) - Unique identifier.
- `application_id`: String (FK - Applications.application_id, UUID, required) - Foreign key to Applications.
- `evaluator_id`: String (FK - Users.user_id, UUID, required) - Foreign key to Users (evaluator).
- `evaluation_type`: String (enum: `individual_license_a`, `class_license_b`, `network_service`, etc., required) - Evaluation type.
- `status`: String (enum: `draft`, `completed`, `approved`, `rejected`, default: `draft`, required) - Evaluation status.
- `total_score`: Number (0-100, required) - Overall evaluation score.
- `recommendation`: String (enum: `approve`, `conditional_approve`, `reject`, required) - Evaluator’s recommendation.
- `evaluators_notes`: String (nullable) - Additional notes.
- `shareholding_compliance`: Boolean (nullable) - Shareholding requirements compliance.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.
- `completed_at`: String (date-time, nullable) - Evaluation completion timestamp.  
**Constraints**:
- No additional properties allowed.

### 20. EvaluationCriteria
**Description**: Stores individual evaluation criteria scores for license applications.  
**Properties**:
- `criteria_id`: String (UUID, required) - Unique identifier.
- `evaluation_id`: String (FK - Evaluations.evaluation_id, UUID, required) - Foreign key to Evaluations.
- `category`: String (required) - Main evaluation category (e.g., `business_plan`, `financial_capacity`).
- `subcategory`: String (required) - Specific criterion within the category.
- `score`: Number (0-100, required) - Score for this criterion.
- `weight`: Number (0-1, required) - Weight in overall score.
- `max_marks`: Integer (minimum: 0, nullable) - Maximum marks for this criterion.
- `awarded_marks`: Integer (minimum: 0, nullable) - Awarded marks for this criterion.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 21. Licenses
**Description**: Stores information about issued licenses.  
**Properties**:
- `license_id`: String (UUID, required) - Unique identifier.
- `license_number`: String (pattern: `^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$`, required, unique) - License number (e.g., `LIC-2024-001-001`).
- `application_id`: String (FK - Applications.application_id, UUID, required) - Foreign key to Applications.
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `license_type_id`: String (FK - LicenseTypes.license_type_id, UUID, required) - Foreign key to LicenseTypes.
- `status`: String (enum: `active`, `expired`, `suspended`, `revoked`, `under_review`, default: `active`, required) - License status.
- `issue_date`: String (date, required) - License issuance date.
- `expiry_date`: String (date, required) - License expiry date.
- `issued_by`: String (FK - Users.user_id, UUID, required) - User who issued the license.
- `conditions`: String (nullable) - Special license conditions.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `license_number` must be unique.

### 22. Payments
**Description**: Stores financial transactions related to license fees and payments.  
**Properties**:
- `payment_id`: String (UUID, required) - Unique identifier.
- `transaction_number`: String (required, unique) - Human-readable transaction reference.
- `application_id`: String (FK - Applications.application_id, UUID, nullable) - Foreign key to Applications.
- `license_id`: String (FK - Licenses.license_id, UUID, nullable) - Foreign key to Licenses.
- `applicant_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `transaction_type`: String (enum: `application_fee`, `license_fee`, `renewal_fee`, `penalty`, `refund`, required) - Transaction type.
- `amount`: Number (minimum: 0, required) - Transaction amount.
- `currency`: String (default: `MWK`, required) - Currency code.
- `status`: String (enum: `pending`, `completed`, `failed`, `cancelled`, `refunded`, default: `pending`, required) - Transaction status.
- `payment_method`: String (enum: `bank_transfer`, `mobile_money`, `cash`, `cheque`, `online`, nullable) - Payment method.
- `reference_number`: String (nullable) - External payment reference.
- `description`: String (required) - Transaction description.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.
- `completed_at`: String (date-time, nullable) - Transaction completion timestamp.  


### 23. Invoices
**Description**: Stores invoices issued for pending payments.  
**Properties**:
- `invoice_id`: String (UUID, required) - Unique identifier.
- `client_id`: String (FK - Applicants.applicant_id, UUID, required) - Foreign key to Applicants.
- `invoice_number`: String (required, unique) - Human-readable invoice number.
- `amount`: Number (decimal, minimum: 0, required) - Total amount due.
- `status`: String (enum: `draft`, `sent`, `paid`, `overdue`, `cancelled`, required) - Invoice status.
- `issue_date`: String (date-time, required) - Invoice issuance date.
- `due_date`: String (date-time, required) - Payment due date.
- `description`: String (required) - Description of invoiced services.
- `items`: Array of objects (nullable) - List of invoiced items with properties:
  - `item_id`: String (UUID, required)
  - `description`: String (required)
  - `quantity`: Integer (minimum: 1, required)
  - `unit_price`: Number (decimal, minimum: 0, required)
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- `invoice_number` must be unique.

### 24. AuditTrail
**Description**: Tracks system actions for auditing purposes.  
**Properties**:
- `audit_id`: String (UUID, required) - Unique identifier.
- `user_id`: String (FK - Users.user_id, UUID, required) - Foreign key to Users.
- `action`: String (enum: `create`, `update`, `delete`, `view`, `approve`, `reject`, `submit`, `login`, `logout`, required) - Action performed.
- `module`: String (enum: `license`, `spectrum`, `transaction`, `user`, `evaluation`, `application`, required) - Affected system module.
- `entity_type`: String (required) - Type of entity affected.
- `entity_id`: String (UUID, nullable) - ID of the affected entity.
- `old_values`: Object (nullable) - Previous values (JSON object).
- `new_values`: Object (nullable) - New values (JSON object).
- `ip_address`: String (nullable) - User’s IP address.
- `user_agent`: String (nullable) - Browser user agent.
- `description`: String (required) - Action description.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.  
**Constraints**:
- No additional properties allowed.

### 25. Notifications
**Description**: Stores system notifications for users.  
**Properties**:
- `notification_id`: String (UUID, required) - Unique identifier.
- `user_id`: String (FK - Users.user_id, UUID, required) - Foreign key to Users.
- `type`: String (enum: `application_status`, `evaluation_assigned`, `payment_due`, `license_expiry`, `system_alert`, required) - Notification type.
- `title`: String (1-255 characters, required) - Notification title.
- `message`: String (required) - Notification content.
- `is_read`: Boolean (default: false) - Read status.
- `priority`: String (enum: `low`, `medium`, `high`, `urgent`, default: `medium`) - Notification priority.
- `related_entity_type`: String (nullable) - Type of related entity (e.g., `application`, `license`).
- `related_entity_id`: String (UUID, nullable) - ID of related entity.
- `action_url`: String (nullable) - URL for notification action.
- `expires_at`: String (date-time, nullable) - Notification expiry timestamp.
- `created_at`: String (date-time, required, auto created, uneditable) - Creation timestamp.
- `created_by`: String (FK - Users.user_id, required, auto created, uneditable) - User who created the record.
- `updated_at`: String (date-time, nullable) - Last update timestamp.
- `updated_by`: String (FK - Users.user_id, nullable) - User who updated the record.
- `deleted_at`: String (date-time, nullable) - Soft deletion timestamp.
- `read_at`: String (date-time, nullable) - Timestamp when notification was read.  
**Constraints**:
- No additional properties allowed.

## Cleanup Notes
The following issues in the original document were addressed:
1. **Inconsistent Entity Naming**: The original document used `applicants` in the entity list but referred to `Applicants` in entity definitions. Standardized to `Applicants` for consistency.
2. **Redundant Entity**: Removed `UserRoles` as it duplicated `EmployeeRoles`. Assumed `EmployeeRoles` is the intended table for role assignments.
3. **Inconsistent Foreign Keys**: Corrected foreign key references (e.g., `applicant_id` to `applicant_id` in `ContactPersons` and `Stakeholders`).
4. **Data Type Clarifications**: Specified `String` instead of `Text` where appropriate and clarified numeric types (e.g., `amount` as `Number (decimal)`).
5. **Enum Lists**: Ensured enum values were consistent and complete (e.g., added missing document types in `Documents.document_type`).
6. **Constraint Clarity**: Explicitly stated unique constraints and removed ambiguous "no additional properties" where redundant.
7. **Formatting**: Standardized property descriptions, used consistent terminology, and organized entities sequentially.

## Recommendations for Further Improvement
- **Add Missing Entities**: The original document lists 42 entities but details only a subset. Ensure all entities (e.g., `ManagementTeam`) are fully documented.
- **Define Relationships**: Include an entity-relationship diagram to visualize foreign key dependencies.
- **Validation Rules**: Specify additional validation rules (e.g., regex for `tpin`, maximum file size for `Documents`).
- **Indexing**: Recommend indexes for frequently queried fields (e.g., `application_number`, `license_number`).
- **Audit Trail Enhancements**: Consider adding `context` or `session_id` to `AuditTrail` for better traceability.

This cleaned-up documentation provides a clear, consistent, and comprehensive reference for the MACRA Licensing Portal database schema. Let me know if you need further refinements or additional details!