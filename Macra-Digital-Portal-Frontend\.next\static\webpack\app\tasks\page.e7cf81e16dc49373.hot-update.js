"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/page",{

/***/ "(app-pages-browser)/./src/app/tasks/page.tsx":
/*!********************************!*\
  !*** ./src/app/tasks/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/common/DataTable */ \"(app-pages-browser)/./src/components/common/DataTable.tsx\");\n/* harmony import */ var _components_common_Select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/common/Select */ \"(app-pages-browser)/./src/components/common/Select.tsx\");\n/* harmony import */ var _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/useTaskNavigation */ \"(app-pages-browser)/./src/hooks/useTaskNavigation.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/tasks/TaskModal */ \"(app-pages-browser)/./src/components/tasks/TaskModal.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/ConfirmationModal */ \"(app-pages-browser)/./src/components/common/ConfirmationModal.tsx\");\n/* harmony import */ var _components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/tasks/ReassignTaskModal */ \"(app-pages-browser)/./src/components/tasks/ReassignTaskModal.tsx\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/formatters */ \"(app-pages-browser)/./src/utils/formatters.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction TasksPage() {\n    _s();\n    const { showSuccess } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTask, setEditingTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tasksData, setTasksData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToDelete, setTaskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReassignModal, setShowReassignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToReassign, setTaskToReassign] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING // Default to showing pending tasks\n    });\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10\n    });\n    // Add task navigation hook\n    const { openTaskView, isLoading: isNavigating } = (0,_hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation)();\n    const handleEditTask = (task)=>{\n        setEditingTask(task);\n        setIsModalOpen(true);\n    };\n    const handleCreateTask = ()=>{\n        setEditingTask(null);\n        setIsModalOpen(true);\n    };\n    const handleModalClose = ()=>{\n        setIsModalOpen(false);\n        setEditingTask(null);\n    };\n    const handleTaskSaved = ()=>{\n        if (editingTask) {\n            showSuccess('Task updated successfully!');\n        } else {\n            showSuccess('Task created successfully!');\n        }\n        // Reload tasks to show updated data\n        loadTasks(currentQuery);\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === '' ? undefined : value\n            }));\n    };\n    const handleDeleteTask = (task)=>{\n        setTaskToDelete(task);\n        setShowDeleteModal(true);\n    };\n    const handleCancelDelete = ()=>{\n        setShowDeleteModal(false);\n        setTaskToDelete(null);\n    };\n    const handleReassignTask = (task)=>{\n        setTaskToReassign(task);\n        setShowReassignModal(true);\n    };\n    const handleCancelReassign = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n    };\n    const handleReassignSuccess = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n        // Reload tasks to show updated assignment\n        loadTasks(currentQuery);\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!taskToDelete) return;\n        setIsDeleting(true);\n        try {\n            await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.deleteTask(taskToDelete.task_id);\n            setShowDeleteModal(false);\n            setTaskToDelete(null);\n            // Reload tasks\n            loadTasks(currentQuery);\n        } catch (err) {\n            console.error('Error deleting task:', err);\n            setError('Failed to delete task');\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const loadTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[loadTasks]\": async (query)=>{\n            try {\n                setLoading(true);\n                setError(null);\n                setCurrentQuery(query);\n                // Combine query with current filters\n                const params = {\n                    ...query,\n                    ...filters\n                };\n                const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getTasks(params);\n                setTasksData(response);\n            } catch (err) {\n                console.error('Error loading tasks:', err);\n                let errorMessage = 'Failed to load tasks. Please try again.';\n                if (err && typeof err === 'object') {\n                    if ('response' in err && err.response && typeof err.response === 'object') {\n                        if ('status' in err.response) {\n                            const status = err.response.status;\n                            if (status === 401) {\n                                errorMessage = 'Authentication required. Please log in again.';\n                            } else if (status === 403) {\n                                errorMessage = 'You do not have permission to view tasks.';\n                            } else if (status === 500) {\n                                errorMessage = 'Server error. Please try again later.';\n                            } else if ('data' in err.response && err.response.data && typeof err.response.data === 'object' && 'message' in err.response.data && typeof err.response.data.message === 'string') {\n                                errorMessage = err.response.data.message;\n                            }\n                        }\n                    } else if ('message' in err && typeof err.message === 'string') {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setTasksData({\n                    data: [],\n                    meta: {\n                        itemsPerPage: query.limit || 10,\n                        totalItems: 0,\n                        currentPage: query.page || 1,\n                        totalPages: 0,\n                        sortBy: [],\n                        searchBy: [],\n                        search: '',\n                        select: []\n                    },\n                    links: {\n                        current: ''\n                    }\n                });\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"TasksPage.useCallback[loadTasks]\"], [\n        filters\n    ]);\n    // Load tasks when component mounts or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            console.log('🔄 Filters changed, loading tasks:', filters);\n            loadTasks({\n                page: 1,\n                limit: 10\n            });\n        }\n    }[\"TasksPage.useEffect\"], [\n        filters,\n        loadTasks\n    ]);\n    // Load users on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"TasksPage.useEffect\"], []);\n    // Handler for DataTable query changes (pagination, search, sorting)\n    const handleQueryChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[handleQueryChange]\": (query)=>{\n            loadTasks(query);\n        }\n    }[\"TasksPage.useCallback[handleQueryChange]\"], [\n        loadTasks\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            const usersResponse = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getOfficers();\n            setUsers(usersResponse.data);\n        } catch (err) {\n            console.error('Error loading users:', err);\n            setUsers([]);\n        }\n    };\n    const taskColumns = [\n        {\n            key: 'task_number',\n            label: 'Task Number',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\",\n                    onClick: ()=>openTaskView(task.task_id),\n                    title: \"Click to view task\",\n                    children: String(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\",\n                            onClick: ()=>openTaskView(task.task_id),\n                            title: \"Click to view task\",\n                            children: String(value)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'task_type',\n            label: 'Type',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100 capitalize\",\n                    children: String(value).replace('_', ' ')\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'status',\n            label: 'Status',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat((0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.getStatusColor)(value)),\n                    children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.formatStatus)(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'assignee',\n            label: 'Assigned To',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100\",\n                    children: task.assignee ? \"\".concat(task.assignee.first_name, \" \").concat(task.assignee.last_name) : 'Unassigned'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'due_date',\n            label: 'Due Date',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                    children: value ? new Date(String(value)).toLocaleDateString() : 'No due date'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'actions',\n            label: 'Actions',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        task.assigned_at ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>openTaskView(task.task_id),\n                            disabled: isNavigating,\n                            className: \"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50\",\n                            title: \"Open in new tab\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-external-link-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                \" View\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this) : null,\n                        task.status !== _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleReassignTask(task),\n                            className: \"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900\",\n                            title: \"Reassign task\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-user-shared-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                task.assignee ? 'Reassign' : 'Assign'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 italic flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-check-circle-line mr-1 text-green-600 dark:text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                \"Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: \"Task Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Manage and track tasks across your organization\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: handleCreateTask,\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-add-line w-5 h-5 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        \"Add Task\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Status\",\n                                value: filters.status || '',\n                                onChange: (value)=>handleFilterChange('status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Statuses'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                                        label: 'Pending'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED,\n                                        label: 'Completed'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.CANCELLED,\n                                        label: 'Cancelled'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.ON_HOLD,\n                                        label: 'On Hold'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Priority\",\n                                value: filters.priority || '',\n                                onChange: (value)=>handleFilterChange('priority', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Priorities'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.LOW,\n                                        label: 'Low'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.MEDIUM,\n                                        label: 'Medium'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                                        label: 'High'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.URGENT,\n                                        label: 'Urgent'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Assignment Status\",\n                                value: filters.assigned_to || '',\n                                onChange: (value)=>handleFilterChange('assignment_status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Tasks'\n                                    },\n                                    {\n                                        value: 'assigned',\n                                        label: 'Assigned'\n                                    },\n                                    {\n                                        value: 'unassigned',\n                                        label: 'Unassigned'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                columns: taskColumns,\n                data: tasksData,\n                loading: loading,\n                onQueryChange: handleQueryChange,\n                searchPlaceholder: \"Search tasks by title, description, or task number...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDeleteModal,\n                onClose: handleCancelDelete,\n                onConfirm: handleConfirmDelete,\n                title: \"Delete Task\",\n                message: taskToDelete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: [\n                                \"Are you sure you want to delete task \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: taskToDelete.task_number\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 54\n                                }, void 0),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"This action cannot be undone. All data associated with this task will be permanently removed.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 13\n                }, void 0) : 'Are you sure you want to delete this task?',\n                confirmText: \"Yes, Delete Task\",\n                cancelText: \"Cancel\",\n                confirmVariant: \"danger\",\n                loading: isDeleting\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showReassignModal,\n                onClose: handleCancelReassign,\n                task: taskToReassign,\n                onReassignSuccess: handleReassignSuccess\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: handleModalClose,\n                onSave: handleTaskSaved,\n                task: editingTask\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, this);\n}\n_s(TasksPage, \"D8uyOwLrwxEwiFXOHL7G6/RqEak=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation\n    ];\n});\n_c = TasksPage;\nvar _c;\n$RefreshReg$(_c, \"TasksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/common/AssignModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/common/AssignModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AssignModal = (param)=>{\n    let { isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess, task, onReassignSuccess } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [officers, setOfficers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assigning, setAssigning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOfficer, setSelectedOfficer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loadTask, setTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReassignMode, setIsReassignMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssignModal.useEffect\": ()=>{\n            if (isOpen) {\n                fetchOfficers();\n                fetchExistingTasks();\n                // For reassignment, pre-select the current assignee\n                setSelectedOfficer(isReassignMode ? (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assigned_to) || '' : '');\n                setComment('');\n                // For reassignment, pre-fill the current due date if available\n                setDueDate(isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.due_date) ? loadTask.due_date.split('T')[0] : '');\n            }\n        }\n    }[\"AssignModal.useEffect\"], [\n        isOpen,\n        isReassignMode,\n        task\n    ]);\n    const fetchExistingTasks = async ()=>{\n        if (task) {\n            setTask(task);\n            setIsReassignMode(true);\n            return;\n        }\n        if (itemId && itemType === 'application') {\n            const loadTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getTaskForApplication(itemId);\n            if (loadTask && loadTask.task_id) {\n                setTask(loadTask);\n                setIsReassignMode(true);\n            } else {\n                setIsReassignMode(false);\n                setTask(null);\n            }\n        }\n    };\n    const fetchOfficers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getOfficers();\n            const officersData = response.data || [];\n            setOfficers(officersData);\n            if (officersData.length === 0) {\n                console.warn('No officers found for task assignment');\n            }\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            setOfficers([]);\n            showError('Failed to load officers. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getTaskTypeFromItemType = (itemType)=>{\n        switch(itemType){\n            case 'application':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.EVALUATION;\n            case 'data_breach':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DATA_BREACH;\n            case 'complaint':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.COMPLAINT;\n            case 'inspection':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.INSPECTION;\n            case 'document_review':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DOCUMENT_REVIEW;\n            case 'task':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION; // Default for existing tasks\n            default:\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION;\n        }\n    };\n    const getTaskTitle = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'Untitled';\n        switch(itemType){\n            case 'application':\n                return \"Application Evaluation: \".concat(baseTitle);\n            case 'data_breach':\n                return \"Data Breach Investigation: \".concat(baseTitle);\n            case 'complaint':\n                return \"Complaint Review: \".concat(baseTitle);\n            case 'inspection':\n                return \"Inspection Task: \".concat(baseTitle);\n            case 'document_review':\n                return \"Document Review: \".concat(baseTitle);\n            default:\n                return \"Task: \".concat(baseTitle);\n        }\n    };\n    const getTaskDescription = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'item';\n        switch(itemType){\n            case 'application':\n                return \"Evaluate and review application \".concat(baseTitle, \" for compliance and approval.\");\n            case 'data_breach':\n                return \"Investigate and assess data breach report \".concat(baseTitle, \" for regulatory compliance.\");\n            case 'complaint':\n                return \"Review and resolve complaint \".concat(baseTitle, \" according to regulatory procedures.\");\n            case 'inspection':\n                return \"Conduct inspection for \".concat(baseTitle, \" to ensure regulatory compliance.\");\n            case 'document_review':\n                return \"Review and validate document \".concat(baseTitle, \" for accuracy and compliance.\");\n            default:\n                return \"Process and review \".concat(baseTitle, \".\");\n        }\n    };\n    const handleAssign = async ()=>{\n        if (!selectedOfficer) {\n            showError('Please select an officer');\n            return;\n        }\n        if (!dueDate) {\n            showError('Please select a due date');\n            return;\n        }\n        if (!isReassignMode && !itemId) {\n            showError('Item ID is missing');\n            return;\n        }\n        setAssigning(true);\n        try {\n            if (isReassignMode && loadTask) {\n                // Reassign existing task\n                await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.reassignTask(loadTask.task_id, {\n                    assignedTo: selectedOfficer,\n                    comment: comment.trim() || undefined,\n                    due_date: dueDate,\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM\n                });\n                showSuccess('Task reassigned successfully');\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            } else {\n                const taskData = {\n                    task_type: getTaskTypeFromItemType(itemType),\n                    title: getTaskTitle(itemType, itemTitle),\n                    description: getTaskDescription(itemType, itemTitle),\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM,\n                    status: _types__WEBPACK_IMPORTED_MODULE_4__.TaskStatus.PENDING,\n                    entity_type: itemType,\n                    entity_id: itemId,\n                    assigned_to: selectedOfficer,\n                    due_date: dueDate,\n                    metadata: {\n                        comment: comment.trim() || undefined,\n                        original_item_title: itemTitle,\n                        assignment_context: 'manual_assignment'\n                    }\n                };\n                // For other entity types, create a new task\n                await createNewTask(taskData);\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error \".concat(isReassignMode ? 'reassigning' : 'creating assignment', \" task:\"), error);\n            showError(\"Failed to \".concat(isReassignMode ? 'reassign' : 'assign', \" task\"));\n        } finally{\n            setAssigning(false);\n        }\n    };\n    // Helper function to create a new task\n    async function createNewTask(taskData) {\n        const createdTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.createTask(taskData);\n        showSuccess(\"Successfully assigned \".concat(itemType.replace('_', ' '), \" to officer\"));\n        onAssignSuccess === null || onAssignSuccess === void 0 ? void 0 : onAssignSuccess();\n    }\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: isReassignMode ? 'Reassign Task' : \"Create Task for \".concat(itemType.replace('_', ' ').toUpperCase())\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-task-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isReassignMode ? 'Task Details:' : 'Task to be Created:'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300 font-medium\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.title : getTaskTitle(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.description : getTaskDescription(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Task #\",\n                                        loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assignee) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Currently assigned to: \",\n                                        loadTask.assignee.first_name,\n                                        \" \",\n                                        loadTask.assignee.last_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                                    children: \"Loading officers...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedOfficer,\n                                    onChange: (e)=>setSelectedOfficer(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select an officer...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        officers.map((officer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: officer.user_id,\n                                                children: [\n                                                    officer.first_name,\n                                                    \" \",\n                                                    officer.last_name,\n                                                    \" - \",\n                                                    officer.email,\n                                                    officer.department ? \" (\".concat(typeof officer.department === 'string' ? officer.department : officer.department.name, \")\") : ''\n                                                ]\n                                            }, officer.user_id, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Due Date *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    value: dueDate,\n                                    onChange: (e)=>setDueDate(e.target.value),\n                                    min: new Date().toISOString().split('T')[0],\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: comment,\n                                    onChange: (e)=>setComment(e.target.value),\n                                    placeholder: isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...',\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedOfficer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-green-900 dark:text-green-100 mb-2\",\n                                    children: \"Selected Officer:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200\",\n                                    children: (()=>{\n                                        const officer = officers.find((o)=>o.user_id === selectedOfficer);\n                                        return officer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                officer.first_name,\n                                                \" \",\n                                                officer.last_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                officer.email,\n                                                officer.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \"Department: \",\n                                                        typeof officer.department === 'string' ? officer.department : officer.department.name\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true) : 'Officer not found';\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined),\n                                dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Due Date:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" \",\n                                        new Date(dueDate).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleAssign,\n                            disabled: !selectedOfficer || !dueDate || assigning,\n                            className: \"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                            children: assigning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassigning...' : 'Creating Task...'\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassign Task' : 'Create Task'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AssignModal, \"R0FJPMujrbn0r85siFN9zWWyY8M=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AssignModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AssignModal);\nvar _c;\n$RefreshReg$(_c, \"AssignModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/AssignModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/task-assignment.ts":
/*!*****************************************!*\
  !*** ./src/services/task-assignment.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taskAssignmentService: () => (/* binding */ taskAssignmentService),\n/* harmony export */   taskService: () => (/* binding */ taskService)\n/* harmony export */ });\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/apiClient */ \"(app-pages-browser)/./src/lib/apiClient.ts\");\n\n\nconst taskService = {\n    // Main task CRUD operations\n    getTasks: async (params)=>{\n        // Handle assignment_status filter by using different endpoints or filters\n        if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'unassigned') {\n            // Use the unassigned tasks endpoint\n            return taskService.getUnassignedTasks(params);\n        } else if ((params === null || params === void 0 ? void 0 : params.assignment_status) === 'assigned') {\n            // Use the assigned tasks endpoint instead of recursive call\n            return taskService.getAssignedTasks(params);\n        }\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        if (params === null || params === void 0 ? void 0 : params.assigned_to) searchParams.append('filter.assigned_to', params.assigned_to);\n        const queryString = searchParams.toString();\n        const url = \"/tasks\".concat(queryString ? \"?\".concat(queryString) : '');\n        console.log('🔗 Making API call to:', url);\n        console.log('📊 Request params:', params);\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        console.log('✅ API response:', response.data);\n        return response.data;\n    },\n    getUnassignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getAssignedTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        // Use nestjs-paginate filter format: filter.columnName=value\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('filter.task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('filter.status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('filter.priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    getMyTasks: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        if (params === null || params === void 0 ? void 0 : params.task_type) searchParams.append('task_type', params.task_type);\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.priority) searchParams.append('priority', params.priority);\n        const queryString = searchParams.toString();\n        const url = \"/tasks/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskStats: async ()=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/tasks/stats');\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTask: async (taskId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/\".concat(taskId));\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    createTask: async (taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.post('/tasks', taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    updateTask: async (taskId, taskData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.patch(\"/tasks/\".concat(taskId), taskData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    getTaskForApplication: async (applicationId)=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/tasks/application/\".concat(applicationId));\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return null; // No task found for application\n            }\n            throw error;\n        }\n    },\n    assignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/assign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    reassignTask: async (taskId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/tasks/\".concat(taskId, \"/reassign\"), assignData);\n        return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n    },\n    deleteTask: async (taskId)=>{\n        await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.delete(\"/tasks/\".concat(taskId));\n    },\n    // Get users for assignment (officers only, exclude customers)\n    getUsers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                params: {\n                    limit: 100,\n                    filter: {\n                        exclude_customers: true\n                    }\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching users:', error);\n        }\n    },\n    // Get officers specifically (non-customer users)\n    getOfficers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users/list/officers');\n            return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(response);\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            // Fallback to regular users endpoint with filtering\n            try {\n                const fallbackResponse = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users', {\n                    params: {\n                        limit: 100,\n                        filter: {\n                            exclude_customers: true\n                        }\n                    }\n                });\n                return (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_0__.processApiResponse)(fallbackResponse);\n            } catch (fallbackError) {\n                console.error('Error fetching users as fallback:', fallbackError);\n            }\n        }\n    }\n};\n// Legacy service for backward compatibility\nconst taskAssignmentService = {\n    // Generic task management methods\n    getUnassignedTasks: taskService.getUnassignedTasks,\n    getAssignedTasks: taskService.getAssignedTasks,\n    assignTask: taskService.assignTask,\n    getTaskById: taskService.getTask,\n    // Legacy application-specific methods (for backward compatibility)\n    getUnassignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/unassigned\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get all applications (including assigned)\n    getAllApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get applications assigned to current user\n    getMyAssignedApplications: async (params)=>{\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const queryString = searchParams.toString();\n        const url = \"/applications/assigned/me\".concat(queryString ? \"?\".concat(queryString) : '');\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(url);\n        return response.data;\n    },\n    // Get officers for assignment\n    getOfficers: async ()=>{\n        try {\n            const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get('/users');\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            return {\n                data: []\n            };\n        }\n    },\n    // Assign application to officer\n    assignApplication: async (applicationId, assignData)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.put(\"/applications/\".concat(applicationId, \"/assign\"), assignData);\n        return response.data;\n    },\n    // Get application details\n    getApplication: async (applicationId)=>{\n        const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_1__.apiClient.get(\"/applications/\".concat(applicationId));\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/task-assignment.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});