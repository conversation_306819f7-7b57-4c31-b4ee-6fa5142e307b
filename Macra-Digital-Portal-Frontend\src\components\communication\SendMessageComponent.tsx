'use client';

import React, { useState, useRef, useEffect } from 'react';
import { PaperAirplaneIcon, PaperClipIcon, XMarkIcon, UserIcon, UsersIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/hooks/useToast';

export interface MessageRecipient {
  id: string;
  name: string;
  email: string;
  role?: string;
  avatar?: string;
}

export interface MessageAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
}

export interface SendMessageData {
  recipients: string[];
  subject: string;
  message: string;
  attachments: File[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  messageType: 'email' | 'notification' | 'announcement';
}

interface SendMessageComponentProps {
  onSendMessage: (messageData: SendMessageData) => Promise<void>;
  availableRecipients: MessageRecipient[];
  defaultRecipients?: MessageRecipient[];
  defaultSubject?: string;
  defaultMessage?: string;
  messageType?: 'email' | 'notification' | 'announcement';
  allowAttachments?: boolean;
  allowPriority?: boolean;
  allowMultipleRecipients?: boolean;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const SendMessageComponent: React.FC<SendMessageComponentProps> = ({
  onSendMessage,
  availableRecipients,
  defaultRecipients = [],
  defaultSubject = '',
  defaultMessage = '',
  messageType = 'email',
  allowAttachments = true,
  allowPriority = true,
  allowMultipleRecipients = true,
  placeholder = 'Type your message here...',
  className = '',
  disabled = false
}) => {
  const { showSuccess, showError } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Form state
  const [selectedRecipients, setSelectedRecipients] = useState<MessageRecipient[]>(defaultRecipients);
  const [subject, setSubject] = useState(defaultSubject);
  const [message, setMessage] = useState(defaultMessage);
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'urgent'>('normal');
  const [attachments, setAttachments] = useState<MessageAttachment[]>([]);
  const [isSending, setIsSending] = useState(false);
  
  // UI state
  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);
  const [recipientSearch, setRecipientSearch] = useState('');

  // Filter recipients based on search
  const filteredRecipients = availableRecipients.filter(recipient =>
    !selectedRecipients.find(selected => selected.id === recipient.id) &&
    (recipient.name.toLowerCase().includes(recipientSearch.toLowerCase()) ||
     recipient.email.toLowerCase().includes(recipientSearch.toLowerCase()))
  );

  const handleAddRecipient = (recipient: MessageRecipient) => {
    if (allowMultipleRecipients) {
      setSelectedRecipients(prev => [...prev, recipient]);
    } else {
      setSelectedRecipients([recipient]);
    }
    setRecipientSearch('');
    setShowRecipientDropdown(false);
  };

  const handleRemoveRecipient = (recipientId: string) => {
    setSelectedRecipients(prev => prev.filter(r => r.id !== recipientId));
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newAttachments: MessageAttachment[] = files.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      file
    }));
    
    setAttachments(prev => [...prev, ...newAttachments]);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(a => a.id !== attachmentId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSendMessage = async () => {
    // Validation
    if (selectedRecipients.length === 0) {
      showError('Please select at least one recipient');
      return;
    }

    if (!message.trim()) {
      showError('Please enter a message');
      return;
    }

    if (messageType === 'email' && !subject.trim()) {
      showError('Please enter a subject for the email');
      return;
    }

    setIsSending(true);
    try {
      const messageData: SendMessageData = {
        recipients: selectedRecipients.map(r => r.id),
        subject: subject.trim(),
        message: message.trim(),
        attachments: attachments.map(a => a.file),
        priority,
        messageType
      };

      await onSendMessage(messageData);
      
      // Reset form after successful send
      setSelectedRecipients([]);
      setSubject('');
      setMessage('');
      setAttachments([]);
      setPriority('normal');
      
      showSuccess('Message sent successfully!');
    } catch (error) {
      console.error('Error sending message:', error);
      showError('Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'normal': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-6">
        <div className="space-y-4">
          {/* Recipients Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {allowMultipleRecipients ? 'Recipients' : 'Recipient'}
            </label>
            
            {/* Selected Recipients */}
            {selectedRecipients.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {selectedRecipients.map(recipient => (
                  <div
                    key={recipient.id}
                    className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                  >
                    <UserIcon className="h-4 w-4" />
                    <span>{recipient.name}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveRecipient(recipient.id)}
                      className="text-blue-500 hover:text-blue-700"
                      disabled={disabled}
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Recipient Search */}
            <div className="relative">
              <input
                type="text"
                value={recipientSearch}
                onChange={(e) => {
                  setRecipientSearch(e.target.value);
                  setShowRecipientDropdown(true);
                }}
                onFocus={() => setShowRecipientDropdown(true)}
                placeholder="Search and select recipients..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={disabled}
              />
              
              {/* Recipient Dropdown */}
              {showRecipientDropdown && filteredRecipients.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {filteredRecipients.map(recipient => (
                    <button
                      key={recipient.id}
                      type="button"
                      onClick={() => handleAddRecipient(recipient)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      disabled={disabled}
                    >
                      <UserIcon className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium text-gray-900">{recipient.name}</div>
                        <div className="text-sm text-gray-500">{recipient.email}</div>
                        {recipient.role && (
                          <div className="text-xs text-gray-400">{recipient.role}</div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Subject (for emails) */}
          {messageType === 'email' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject
              </label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter email subject..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={disabled}
              />
            </div>
          )}

          {/* Priority */}
          {allowPriority && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as any)}
                className={`px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${getPriorityColor(priority)}`}
                disabled={disabled}
              >
                <option value="low">Low</option>
                <option value="normal">Normal</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          )}

          {/* Message Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={placeholder}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              disabled={disabled}
            />
          </div>

          {/* Attachments */}
          {allowAttachments && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Attachments
              </label>
              
              {/* File Input */}
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                disabled={disabled}
              />
              
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="inline-flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={disabled}
              >
                <PaperClipIcon className="h-4 w-4" />
                Add Files
              </button>

              {/* Attachment List */}
              {attachments.length > 0 && (
                <div className="mt-3 space-y-2">
                  {attachments.map(attachment => (
                    <div
                      key={attachment.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <PaperClipIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">{attachment.name}</span>
                        <span className="text-xs text-gray-500">({formatFileSize(attachment.size)})</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveAttachment(attachment.id)}
                        className="text-red-500 hover:text-red-700"
                        disabled={disabled}
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Send Button */}
          <div className="flex justify-end pt-4">
            <button
              type="button"
              onClick={handleSendMessage}
              disabled={disabled || isSending || selectedRecipients.length === 0 || !message.trim()}
              className="inline-flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
              {isSending ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendMessageComponent;
