export enum DataBreachCategory {
  UNAUTHORIZED_ACCESS = 'Unauthorized Data Access',
  DATA_MISUSE = 'Data Misuse or Sharing',
  PRIVACY_VIOLATIONS = 'Privacy Violations',
  IDENTITY_THEFT = 'Identity Theft',
  PHISHING_ATTEMPTS = 'Phishing Attempts',
  DATA_LOSS = 'Data Loss or Theft',
  CONSENT_VIOLATIONS = 'Consent Violations',
  OTHER = 'Other',
}

export enum DataBreachPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum DataBreachStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum DataBreachSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface DataBreachReportResponseDto {
  report_id: string;
  report_number: string;
  reporter_id: string;
  title: string;
  description: string;
  category: string;
  severity: string;
  status: string;
  priority: string;
  incident_date: Date;
  organization_involved: string;
  respondent_reg_number?: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
  
  // Related data
  reporter?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  // TO-DO Add respondent here
  /*
  respondent?: {
    organization_id: string;
    name: string;
    email: string;
  };
  */
  
  attachments?: {
    attachment_id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    uploaded_at: Date;
  }[];
  
  status_history?: {
    history_id: string;
    status: DataBreachStatus;
    comment?: string;
    created_at: Date;
    creator: {
      user_id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}
