'use client';

import React, { useState, useEffect } from 'react';
import { DataBreachReport } from '@/types/data-breach';
import { dataBreachService } from '@/services/data-breach';
import DataBreachActions from './DataBreachActions';
import DataBreachActivityNotes from './DataBreachActivityNotes';
import { useToast } from '@/contexts/ToastContext';
import Loader from '@/components/Loader';

interface DataBreachManagementProps {
  reportId: string;
  className?: string;
}

const DataBreachManagement: React.FC<DataBreachManagementProps> = ({
  reportId,
  className = ''
}) => {
  const { showError } = useToast();
  const [report, setReport] = useState<DataBreachReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    setLoading(true);
    try {
      const reportData = await dataBreachService.getReportById(reportId);
      setReport(reportData);
    } catch (error) {
      console.error('Error fetching report:', error);
      showError('Failed to load data breach report');
    } finally {
      setLoading(false);
    }
  };

  const handleReportUpdate = () => {
    // Refresh the report data
    fetchReport();
    // Trigger activity notes refresh
    setRefreshTrigger(prev => prev + 1);
  };

  const handleActivityRefresh = () => {
    // Trigger activity notes refresh
    setRefreshTrigger(prev => prev + 1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'investigating':
        return 'bg-orange-100 text-orange-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <Loader message="Loading data breach report..." />
      </div>
    );
  }

  if (!report) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <i className="ri-error-warning-line text-4xl text-gray-400 mb-4"></i>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Report Not Found</h3>
        <p className="text-gray-500">The requested data breach report could not be found.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Report Header */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {report.title}
            </h2>
            <div className="flex items-center gap-3 mb-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getSeverityColor(report.severity)}`}>
                {report.severity.toUpperCase()} SEVERITY
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(report.status)}`}>
                {report.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Report ID: {report.report_id} | Category: {report.category}
            </p>
          </div>
          <div className="text-right text-sm text-gray-600 dark:text-gray-400">
            <p>Submitted: {formatDate(report.created_at)}</p>
            <p>Updated: {formatDate(report.updated_at)}</p>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h4>
          <p className="text-gray-700 dark:text-gray-300">{report.description}</p>
        </div>

        {/* Additional Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Incident Details</h5>
            <div className="space-y-1 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Incident Date:</span>
                <span className="ml-2 text-gray-900 dark:text-gray-100">
                  {report.incident_date ? formatDate(report.incident_date) : 'Not specified'}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Organization:</span>
                <span className="ml-2 text-gray-900 dark:text-gray-100">
                  {report.organization_involved || 'Not specified'}
                </span>
              </div>
            </div>
          </div>
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Data Information</h5>
            <div className="space-y-1 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Affected Data:</span>
                <span className="ml-2 text-gray-900 dark:text-gray-100">
                  {report.affected_data_types || 'Not specified'}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Priority:</span>
                <span className="ml-2 text-gray-900 dark:text-gray-100">
                  {report.priority?.toUpperCase() || 'NORMAL'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Management Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Actions Panel */}
        <DataBreachActions
          report={report}
          onUpdate={handleReportUpdate}
          onRefreshActivity={handleActivityRefresh}
        />

        {/* Activity Notes Panel */}
        <DataBreachActivityNotes
          reportId={report.report_id}
          refreshTrigger={refreshTrigger}
        />
      </div>
    </div>
  );
};

export default DataBreachManagement;
