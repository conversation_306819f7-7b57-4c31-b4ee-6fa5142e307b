"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/page",{

/***/ "(app-pages-browser)/./src/app/tasks/page.tsx":
/*!********************************!*\
  !*** ./src/app/tasks/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/common/DataTable */ \"(app-pages-browser)/./src/components/common/DataTable.tsx\");\n/* harmony import */ var _components_common_Select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/common/Select */ \"(app-pages-browser)/./src/components/common/Select.tsx\");\n/* harmony import */ var _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/useTaskNavigation */ \"(app-pages-browser)/./src/hooks/useTaskNavigation.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/tasks/TaskModal */ \"(app-pages-browser)/./src/components/tasks/TaskModal.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/ConfirmationModal */ \"(app-pages-browser)/./src/components/common/ConfirmationModal.tsx\");\n/* harmony import */ var _components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/tasks/ReassignTaskModal */ \"(app-pages-browser)/./src/components/tasks/ReassignTaskModal.tsx\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/formatters */ \"(app-pages-browser)/./src/utils/formatters.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TasksPage() {\n    _s();\n    const { showSuccess } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTask, setEditingTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tasksData, setTasksData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToDelete, setTaskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReassignModal, setShowReassignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [taskToReassign, setTaskToReassign] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING // Default to showing pending tasks\n    });\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    // Add task navigation hook\n    const { openTaskView, isLoading: isNavigating } = (0,_hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation)();\n    const handleCreateTask = ()=>{\n        setEditingTask(null);\n        setIsModalOpen(true);\n    };\n    const canViewTask = (task)=>{\n        if (task.assigned_at && task.assigned_to == (user === null || user === void 0 ? void 0 : user.user_id)) return true;\n        return false;\n    };\n    const handleModalClose = ()=>{\n        setIsModalOpen(false);\n        setEditingTask(null);\n    };\n    const handleTaskSaved = ()=>{\n        if (editingTask) {\n            showSuccess('Task updated successfully!');\n        } else {\n            showSuccess('Task created successfully!');\n        }\n        // Reload tasks to show updated data\n        loadTasks(currentQuery);\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === '' ? undefined : value\n            }));\n    };\n    const handleDeleteTask = (task)=>{\n        setTaskToDelete(task);\n        setShowDeleteModal(true);\n    };\n    const handleCancelDelete = ()=>{\n        setShowDeleteModal(false);\n        setTaskToDelete(null);\n    };\n    const handleReassignTask = (task)=>{\n        setTaskToReassign(task);\n        setShowReassignModal(true);\n    };\n    const handleCancelReassign = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n    };\n    const handleReassignSuccess = ()=>{\n        setShowReassignModal(false);\n        setTaskToReassign(null);\n        // Reload tasks to show updated assignment\n        loadTasks(currentQuery);\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!taskToDelete) return;\n        setIsDeleting(true);\n        try {\n            await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.deleteTask(taskToDelete.task_id);\n            setShowDeleteModal(false);\n            setTaskToDelete(null);\n            // Reload tasks\n            loadTasks(currentQuery);\n        } catch (err) {\n            console.error('Error deleting task:', err);\n            setError('Failed to delete task');\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const loadTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[loadTasks]\": async (query)=>{\n            try {\n                setLoading(true);\n                setError(null);\n                setCurrentQuery(query);\n                // Combine query with current filters\n                const params = {\n                    ...query,\n                    ...filters\n                };\n                const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getTasks(params);\n                setTasksData(response);\n            } catch (err) {\n                console.error('Error loading tasks:', err);\n                let errorMessage = 'Failed to load tasks. Please try again.';\n                if (err && typeof err === 'object') {\n                    if ('response' in err && err.response && typeof err.response === 'object') {\n                        if ('status' in err.response) {\n                            const status = err.response.status;\n                            if (status === 401) {\n                                errorMessage = 'Authentication required. Please log in again.';\n                            } else if (status === 403) {\n                                errorMessage = 'You do not have permission to view tasks.';\n                            } else if (status === 500) {\n                                errorMessage = 'Server error. Please try again later.';\n                            } else if ('data' in err.response && err.response.data && typeof err.response.data === 'object' && 'message' in err.response.data && typeof err.response.data.message === 'string') {\n                                errorMessage = err.response.data.message;\n                            }\n                        }\n                    } else if ('message' in err && typeof err.message === 'string') {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setTasksData({\n                    data: [],\n                    meta: {\n                        itemsPerPage: query.limit || 10,\n                        totalItems: 0,\n                        currentPage: query.page || 1,\n                        totalPages: 0,\n                        sortBy: [],\n                        searchBy: [],\n                        search: '',\n                        select: []\n                    },\n                    links: {\n                        current: ''\n                    }\n                });\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"TasksPage.useCallback[loadTasks]\"], [\n        filters\n    ]);\n    // Load tasks when component mounts or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            console.log('🔄 Filters changed, loading tasks:', filters);\n            loadTasks({\n                page: 1,\n                limit: 10\n            });\n        }\n    }[\"TasksPage.useEffect\"], [\n        filters,\n        loadTasks\n    ]);\n    // Load users on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"TasksPage.useEffect\"], []);\n    // Handler for DataTable query changes (pagination, search, sorting)\n    const handleQueryChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TasksPage.useCallback[handleQueryChange]\": (query)=>{\n            loadTasks(query);\n        }\n    }[\"TasksPage.useCallback[handleQueryChange]\"], [\n        loadTasks\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            const usersResponse = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_8__.taskService.getOfficers();\n            setUsers(usersResponse.data);\n        } catch (err) {\n            console.error('Error loading users:', err);\n            setUsers([]);\n        }\n    };\n    const taskColumns = [\n        {\n            key: 'task_number',\n            label: 'Task Number',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: canViewTask(task) ? \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\" : \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                    onClick: ()=>canViewTask(task) ? openTaskView(task.task_id) : null,\n                    title: \"Click to view task\",\n                    children: String(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            render: (value, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: canViewTask(task) ? \"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\" : \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            onClick: ()=>canViewTask(task) ? openTaskView(task.task_id) : null,\n                            title: \"Click to view task\",\n                            children: String(value)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'task_type',\n            label: 'Type',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100 capitalize\",\n                    children: String(value).replace('_', ' ')\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'status',\n            label: 'Status',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat((0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.getStatusColor)(value)),\n                    children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_11__.formatStatus)(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'assignee',\n            label: 'Assigned To',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-900 dark:text-gray-100\",\n                    children: task.assignee ? \"\".concat(task.assignee.first_name, \" \").concat(task.assignee.last_name) : 'Unassigned'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'due_date',\n            label: 'Due Date',\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                    children: value ? new Date(String(value)).toLocaleDateString() : 'No due date'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'actions',\n            label: 'Actions',\n            render: (_, task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        canViewTask(task) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>openTaskView(task.task_id),\n                            disabled: isNavigating,\n                            className: \"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50\",\n                            title: \"Open in new tab\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-external-link-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                \" View\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this) : null,\n                        task.status !== _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleReassignTask(task),\n                            className: \"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900\",\n                            title: \"Reassign task\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-user-shared-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                task.assignee ? 'Reassign' : 'Assign'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400 italic flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-check-circle-line mr-1 text-green-600 dark:text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                \"Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: \"Task Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Manage and track tasks across your organization\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: handleCreateTask,\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-add-line w-5 h-5 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        \"Add Task\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Status\",\n                                value: filters.status || '',\n                                onChange: (value)=>handleFilterChange('status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Statuses'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                                        label: 'Pending'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED,\n                                        label: 'Completed'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.CANCELLED,\n                                        label: 'Cancelled'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.ON_HOLD,\n                                        label: 'On Hold'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Priority\",\n                                value: filters.priority || '',\n                                onChange: (value)=>handleFilterChange('priority', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Priorities'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.LOW,\n                                        label: 'Low'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.MEDIUM,\n                                        label: 'Medium'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                                        label: 'High'\n                                    },\n                                    {\n                                        value: _types__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.URGENT,\n                                        label: 'Urgent'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Select__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"Assignment Status\",\n                                value: filters.assigned_to || '',\n                                onChange: (value)=>handleFilterChange('assignment_status', value),\n                                options: [\n                                    {\n                                        value: '',\n                                        label: 'All Tasks'\n                                    },\n                                    {\n                                        value: 'assigned',\n                                        label: 'Assigned'\n                                    },\n                                    {\n                                        value: 'unassigned',\n                                        label: 'Unassigned'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_DataTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                columns: taskColumns,\n                data: tasksData,\n                loading: loading,\n                onQueryChange: handleQueryChange,\n                searchPlaceholder: \"Search tasks by title, description, or task number...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConfirmationModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDeleteModal,\n                onClose: handleCancelDelete,\n                onConfirm: handleConfirmDelete,\n                title: \"Delete Task\",\n                message: taskToDelete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: [\n                                \"Are you sure you want to delete task \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: taskToDelete.task_number\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 54\n                                }, void 0),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"This action cannot be undone. All data associated with this task will be permanently removed.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 13\n                }, void 0) : 'Are you sure you want to delete this task?',\n                confirmText: \"Yes, Delete Task\",\n                cancelText: \"Cancel\",\n                confirmVariant: \"danger\",\n                loading: isDeleting\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_ReassignTaskModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showReassignModal,\n                onClose: handleCancelReassign,\n                task: taskToReassign,\n                onReassignSuccess: handleReassignSuccess\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 432,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_TaskModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: handleModalClose,\n                onSave: handleTaskSaved,\n                task: editingTask\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n_s(TasksPage, \"BnF9w3bz05KinPghCnyHZ4vWRu8=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth,\n        _hooks_useTaskNavigation__WEBPACK_IMPORTED_MODULE_5__.useTaskNavigation\n    ];\n});\n_c = TasksPage;\nvar _c;\n$RefreshReg$(_c, \"TasksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/common/AssignModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/common/AssignModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/task-assignment */ \"(app-pages-browser)/./src/services/task-assignment.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AssignModal = (param)=>{\n    let { isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess, task, onReassignSuccess } = param;\n    _s();\n    const { showSuccess, showError } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [officers, setOfficers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assigning, setAssigning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOfficer, setSelectedOfficer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loadTask, setTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReassignMode, setIsReassignMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssignModal.useEffect\": ()=>{\n            if (isOpen) {\n                fetchOfficers();\n                fetchExistingTasks();\n                // For reassignment, pre-select the current assignee\n                setSelectedOfficer(isReassignMode ? (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assigned_to) || '' : '');\n                setComment('');\n                // For reassignment, pre-fill the current due date if available\n                setDueDate(isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.due_date) ? loadTask.due_date.split('T')[0] : '');\n            }\n        }\n    }[\"AssignModal.useEffect\"], [\n        isOpen,\n        isReassignMode,\n        task\n    ]);\n    const fetchExistingTasks = async ()=>{\n        if (task) {\n            setTask(task);\n            setIsReassignMode(true);\n            return;\n        }\n        if (itemId && itemType === 'application') {\n            const loadTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getTaskForApplication(itemId);\n            if (loadTask && loadTask.task_id) {\n                setTask(loadTask);\n                setIsReassignMode(true);\n            } else {\n                setIsReassignMode(false);\n                setTask(null);\n            }\n        }\n    };\n    const fetchOfficers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.getOfficers();\n            const officersData = response.data || [];\n            setOfficers(officersData);\n            if (officersData.length === 0) {\n                console.warn('No officers found for task assignment');\n            }\n        } catch (error) {\n            console.error('Error fetching officers:', error);\n            setOfficers([]);\n            showError('Failed to load officers. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getTaskTypeFromItemType = (itemType)=>{\n        switch(itemType){\n            case 'application':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.EVALUATION;\n            case 'data_breach':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DATA_BREACH;\n            case 'complaint':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.COMPLAINT;\n            case 'inspection':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.INSPECTION;\n            case 'document_review':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.DOCUMENT_REVIEW;\n            case 'task':\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION; // Default for existing tasks\n            default:\n                return _types__WEBPACK_IMPORTED_MODULE_4__.TaskType.APPLICATION;\n        }\n    };\n    const getTaskTitle = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'Untitled';\n        switch(itemType){\n            case 'application':\n                return \"Application Evaluation: \".concat(baseTitle);\n            case 'data_breach':\n                return \"Data Breach Investigation: \".concat(baseTitle);\n            case 'complaint':\n                return \"Complaint Review: \".concat(baseTitle);\n            case 'inspection':\n                return \"Inspection Task: \".concat(baseTitle);\n            case 'document_review':\n                return \"Document Review: \".concat(baseTitle);\n            default:\n                return \"Task: \".concat(baseTitle);\n        }\n    };\n    const getTaskDescription = (itemType, itemTitle)=>{\n        const baseTitle = itemTitle || 'item';\n        switch(itemType){\n            case 'application':\n                return \"Evaluate and review application \".concat(baseTitle, \" for compliance and approval.\");\n            case 'data_breach':\n                return \"Investigate and assess data breach report \".concat(baseTitle, \" for regulatory compliance.\");\n            case 'complaint':\n                return \"Review and resolve complaint \".concat(baseTitle, \" according to regulatory procedures.\");\n            case 'inspection':\n                return \"Conduct inspection for \".concat(baseTitle, \" to ensure regulatory compliance.\");\n            case 'document_review':\n                return \"Review and validate document \".concat(baseTitle, \" for accuracy and compliance.\");\n            default:\n                return \"Process and review \".concat(baseTitle, \".\");\n        }\n    };\n    const handleAssign = async ()=>{\n        if (!selectedOfficer) {\n            showError('Please select an officer');\n            return;\n        }\n        if (!dueDate) {\n            showError('Please select a due date');\n            return;\n        }\n        if (!isReassignMode && !itemId) {\n            showError('Item ID is missing');\n            return;\n        }\n        setAssigning(true);\n        try {\n            if (isReassignMode && loadTask) {\n                // Reassign existing task\n                await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.reassignTask(loadTask.task_id, {\n                    assignedTo: selectedOfficer,\n                    comment: comment.trim() || undefined,\n                    due_date: dueDate,\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM\n                });\n                showSuccess('Task reassigned successfully');\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            } else {\n                const taskData = {\n                    task_type: getTaskTypeFromItemType(itemType),\n                    title: getTaskTitle(itemType, itemTitle),\n                    description: getTaskDescription(itemType, itemTitle),\n                    priority: _types__WEBPACK_IMPORTED_MODULE_4__.TaskPriority.MEDIUM,\n                    status: _types__WEBPACK_IMPORTED_MODULE_4__.TaskStatus.PENDING,\n                    entity_type: itemType,\n                    entity_id: itemId,\n                    assigned_to: selectedOfficer,\n                    due_date: dueDate,\n                    metadata: {\n                        comment: comment.trim() || undefined,\n                        original_item_title: itemTitle,\n                        assignment_context: 'manual_assignment'\n                    }\n                };\n                // For other entity types, create a new task\n                await createNewTask(taskData);\n                onReassignSuccess === null || onReassignSuccess === void 0 ? void 0 : onReassignSuccess();\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error \".concat(isReassignMode ? 'reassigning' : 'creating assignment', \" task:\"), error);\n            showError(\"Failed to \".concat(isReassignMode ? 'reassign' : 'assign', \" task\"));\n        } finally{\n            setAssigning(false);\n        }\n    };\n    // Helper function to create a new task\n    async function createNewTask(taskData) {\n        const createdTask = await _services_task_assignment__WEBPACK_IMPORTED_MODULE_3__.taskService.createTask(taskData);\n        showSuccess(\"Successfully assigned \".concat(itemType.replace('_', ' '), \" to officer\"));\n        onAssignSuccess === null || onAssignSuccess === void 0 ? void 0 : onAssignSuccess();\n    }\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: isReassignMode ? 'Reassign Task' : \"Create Task for \".concat(itemType.replace('_', ' ').toUpperCase())\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-task-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isReassignMode ? 'Task Details:' : 'Task to be Created:'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300 font-medium\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.title : getTaskTitle(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\",\n                                    children: isReassignMode ? loadTask === null || loadTask === void 0 ? void 0 : loadTask.description : getTaskDescription(itemType, itemTitle)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Task #\",\n                                        loadTask === null || loadTask === void 0 ? void 0 : loadTask.task_number\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, undefined),\n                                isReassignMode && (loadTask === null || loadTask === void 0 ? void 0 : loadTask.assignee) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                    children: [\n                                        \"Currently assigned to: \",\n                                        loadTask.assignee.first_name,\n                                        \" \",\n                                        loadTask.assignee.last_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                                    children: \"Loading officers...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedOfficer,\n                                    onChange: (e)=>setSelectedOfficer(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select an officer...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        officers.map((officer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: officer.user_id,\n                                                children: [\n                                                    officer.first_name,\n                                                    \" \",\n                                                    officer.last_name,\n                                                    \" - \",\n                                                    officer.email,\n                                                    officer.department ? \" (\".concat(typeof officer.department === 'string' ? officer.department : officer.department.name, \")\") : ''\n                                                ]\n                                            }, officer.user_id, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Due Date *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    value: dueDate,\n                                    onChange: (e)=>setDueDate(e.target.value),\n                                    min: new Date().toISOString().split('T')[0],\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: comment,\n                                    onChange: (e)=>setComment(e.target.value),\n                                    placeholder: isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...',\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedOfficer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-green-900 dark:text-green-100 mb-2\",\n                                    children: \"Selected Officer:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200\",\n                                    children: (()=>{\n                                        const officer = officers.find((o)=>o.user_id === selectedOfficer);\n                                        return officer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                officer.first_name,\n                                                \" \",\n                                                officer.last_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                officer.email,\n                                                officer.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \"Department: \",\n                                                        typeof officer.department === 'string' ? officer.department : officer.department.name\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true) : 'Officer not found';\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined),\n                                dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 dark:text-green-200 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Due Date:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" \",\n                                        new Date(dueDate).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleAssign,\n                            disabled: !selectedOfficer || !dueDate || assigning,\n                            className: \"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                            children: assigning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassigning...' : 'Creating Task...'\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isReassignMode ? 'Reassign Task' : 'Create Task'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\common\\\\AssignModal.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AssignModal, \"R0FJPMujrbn0r85siFN9zWWyY8M=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AssignModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AssignModal);\nvar _c;\n$RefreshReg$(_c, \"AssignModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/AssignModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(app-pages-browser)/./src/lib/authUtils.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/userService */ \"(app-pages-browser)/./src/services/userService.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [twoFa, setTwoFa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    // Check for existing token on mount with validation\n    const initAuth = async ()=>{\n        // Get saved data from cookies\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n        console.log(1 + \"=======\");\n        console.log(1 + \"=======\" + savedToken);\n        console.log(1 + \"=======\" + savedUser);\n        if (savedToken && savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                // Validate token is not expired\n                if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                    setToken(savedToken);\n                    setUser(user);\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                } else {\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                }\n            } catch (error) {\n                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                console.error('AuthContext: User data parse failed', error);\n            }\n            try {\n                const saved2faUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('2fa_login_user');\n                const twoFaUser = JSON.parse(saved2faUser || '');\n                setTwoFa(twoFaUser);\n            } catch (e) {\n                console.error('AuthContext: 2FA user parse failed', e);\n            }\n        }\n        setLoading(false);\n    };\n    const login = async function(email, password) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            // Validate response structure for normal login\n            if (!response || !response.user) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Store remember me preference for 2FA completion\n            if (rememberMe) {\n                sessionStorage.setItem('remember_me', 'true');\n            } else {\n                sessionStorage.removeItem('remember_me');\n            }\n            // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\n            const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\n            // Check if email verification is required (new field from backend)\n            const requiresEmailVerification = response.requires_2fa && response.action === 'register';\n            if (!requires2FA && !requiresEmailVerification) {\n                // No 2FA or email verification required, set auth context immediately\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(response.user));\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token || '');\n                if (response.user.two_factor_enabled == true || response.user.two_factor_enabled == 'true') {\n                    await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.generateTwoFactorCode(response.user.user_id, 'login');\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                }\n                await initAuth();\n            } else if (requiresEmailVerification) {\n                // Email verification required - store user data for verification page\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('2fa_login_user', JSON.stringify(response.user));\n                console.log('AuthContext: Email verification required for user:', response.user.email);\n            }\n            return response;\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async function(token, userData) {\n        let rememberMe = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const user = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', user);\n            }\n            setToken(token);\n            setUser(user);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(user), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (result.user) {\n            login(userData.email, userData.password);\n        }\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        setTwoFa(null);\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        _services_userService__WEBPACK_IMPORTED_MODULE_5__.userService.getUserById(updatedUser.user_id).then((user)=>{\n            user.isCustomer = roles.includes('customer');\n            if (true) {}\n            setUser(user);\n            // Update cookies with new user data - use existing token expiration\n            const existingToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n            if (existingToken) {\n                // Try to determine original expiration from token or use default\n                const cookieExpiration = 1; // Default to 1 day for user updates\n                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(updatedUser), {\n                    expires: cookieExpiration\n                });\n            }\n        });\n    // Ensure isAdmin property is set for backward compatibility\n    };\n    const value = {\n        user,\n        token,\n        login,\n        twoFa,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, undefined);\n};\n_s1(AuthProvider, \"I6iMVdqB1Xt36iGFqqbhy/pRq+U=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/task.ts":
/*!***************************!*\
  !*** ./src/types/task.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType)\n/* harmony export */ });\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"APPLICATION\"] = \"application\";\n    TaskType[\"COMPLAINT\"] = \"complaint\";\n    TaskType[\"DATA_BREACH\"] = \"data_breach\";\n    TaskType[\"EVALUATION\"] = \"evaluation\";\n    TaskType[\"INSPECTION\"] = \"inspection\";\n    TaskType[\"DOCUMENT_REVIEW\"] = \"document_review\";\n    TaskType[\"COMPLIANCE_CHECK\"] = \"compliance_check\";\n    TaskType[\"FOLLOW_UP\"] = \"follow_up\";\n    TaskType[\"PAYMENT_VERIFICATION\"] = \"payment_verification\";\n    TaskType[\"USER\"] = \"user\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"ON_HOLD\"] = \"on_hold\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/task.ts\n"));

/***/ })

});