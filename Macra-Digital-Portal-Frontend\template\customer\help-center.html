
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Customer Dashboard - Digital Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }

      .license-card {
        transition: transform 0.3s ease;
      }

      .license-card:hover {
        transform: translateY(-4px);
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }

      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }

      .side-nav::-webkit-scrollbar {
        display: none;
      }

      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      .tab-button {
        position: relative;
        z-index: 1;
      }

      .tab-button.active {
        color: #e02b20;
        font-weight: 500;
      }

      .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e02b20;
      }

      .tab-content {
        display: block;
      }

      .tab-content.hidden {
        display: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Applications
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                    placeholder="Search for licenses or applications..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>


        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6 md:p-8">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-8">
              <h1 class="text-2xl font-bold text-gray-900">Help & Support</h1>
              <p class="mt-2 text-sm text-gray-600">
                Find answers to common questions and get support for MACRA Digital Portal
              </p>
            </div>

            <!-- Help & Support Content -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Left sidebar with categories -->
              <div class="md:col-span-1">
                <div class="bg-white rounded-lg shadow overflow-hidden">
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Help Categories</h3>
                  </div>
                  <div class="divide-y divide-gray-200">
                    <button onclick="showCategory('getting-started')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-primary hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-rocket-line"></i>
                      </div>
                      Getting Started
                    </button>
                    <button onclick="showCategory('license-management')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-gray-700 hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-key-line"></i>
                      </div>
                      License Management
                    </button>
                    <button onclick="showCategory('spectrum-management')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-gray-700 hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-wifi-line"></i>
                      </div>
                      Spectrum Management
                    </button>
                    <button onclick="showCategory('financial-transactions')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-gray-700 hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-bank-card-line"></i>
                      </div>
                      Financial Transactions
                    </button>
                    <button onclick="showCategory('reports-analytics')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-gray-700 hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-bar-chart-line"></i>
                      </div>
                      Reports & Analytics
                    </button>
                    <button onclick="showCategory('account-settings')" class="w-full px-4 py-4 flex items-center text-left text-sm font-medium text-gray-700 hover:bg-gray-50">
                      <div class="w-5 h-5 flex items-center justify-center mr-3">
                        <i class="ri-settings-line"></i>
                      </div>
                      Account Settings
                    </button>
                  </div>
                </div>

                <!-- Contact Support Card -->
                <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Need More Help?</h3>
                  </div>
                  <div class="p-4">
                    <p class="text-sm text-gray-600 mb-4">
                      Can't find what you're looking for? Our support team is here to help.
                    </p>
                    <a href="#" class="block w-full bg-primary text-white text-center py-2 px-4 rounded-md hover:bg-red-700 transition duration-150">
                      Contact Support
                    </a>
                    <div class="mt-4 flex items-center justify-center">
                      <div class="flex items-center text-gray-600 text-sm">
                        <i class="ri-phone-line mr-2"></i>
                        <span>+****************</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Main content area -->
              <div class="md:col-span-2">
                <!-- Getting Started Section -->
                <div id="getting-started" class="category-content bg-white rounded-lg shadow overflow-hidden">
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">Getting Started with MACRA Digital Portal</h2>
                  </div>
                  <div class="p-6">
                    <div class="space-y-6">

                      
                      <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">How to Navigate the Dashboard</h3>
                        <p class="text-gray-600 mb-4">
                          The dashboard provides an overview of your licenses, spectrum allocations, and recent transactions. Here's how to navigate:
                        </p>
                        <ul class="list-disc pl-5 text-gray-600 space-y-2">
                          <li>Use the sidebar menu to access different sections</li>
                          <li>The top search bar allows you to quickly find specific items</li>
                          <li>Dashboard cards show key metrics and status updates</li>
                          <li>Click on any card to view more detailed information</li>
                        </ul>
                      </div>
                      
                      <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Setting Up Your Account</h3>
                        <p class="text-gray-600 mb-4">
                          To get the most out of MACRA Digital Portal, follow these steps to set up your account:
                        </p>
                        <ol class="list-decimal pl-5 text-gray-600 space-y-2">
                          <li>Complete your profile information</li>
                          <li>Set up notification preferences</li>
                          <li>Add team members and assign roles</li>
                          <li>Configure your organization settings</li>
                          <li>Import existing licenses and spectrum data</li>
                        </ol>
                      </div>
                      
                      <!-- <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Video Tutorials</h3>
                        <p class="text-gray-600 mb-4">
                          Watch these video tutorials to quickly learn how to use the platform:
                        </p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <div class="aspect-w-16 aspect-h-9 bg-gray-100 flex items-center justify-center">
                              <div class="text-gray-400">
                                <i class="ri-video-line text-4xl"></i>
                              </div>
                            </div>
                            <div class="p-3">
                              <h4 class="font-medium text-gray-900">Dashboard Overview</h4>
                              <p class="text-sm text-gray-600">3:45 min</p>
                            </div>
                          </div>
                          <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <div class="aspect-w-16 aspect-h-9 bg-gray-100 flex items-center justify-center">
                              <div class="text-gray-400">
                                <i class="ri-video-line text-4xl"></i>
                              </div>
                            </div>
                            <div class="p-3">
                              <h4 class="font-medium text-gray-900">License Management</h4>
                              <p class="text-sm text-gray-600">5:12 min</p>
                            </div>
                          </div>
                        </div>
                      </div> -->
                    </div>
                  </div>
                </div>

                <!-- License Management Section (Hidden by default) -->
                <div id="license-management" class="category-content hidden bg-white rounded-lg shadow overflow-hidden">
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">License Management Help</h2>
                  </div>
                  <div class="p-6">
                    <div class="space-y-6">
                      <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">How to Apply for a New License</h3>
                        <p class="text-gray-600">
                          Learn how to submit applications for new telecommunications licenses through the portal.
                        </p>
                      </div>
                      
                      <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Renewing Existing Licenses</h3>
                        <p class="text-gray-600">
                          Step-by-step guide for renewing your existing licenses before they expire.
                        </p>
                      </div>
                      
                      <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">License Status Monitoring</h3>
                        <p class="text-gray-600">
                          How to track the status of your licenses and set up alerts for upcoming renewals.
                        </p>
                      </div>
                      
                      <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Frequently Asked Questions</h3>
                        <div class="mt-4 space-y-4">
                          <div>
                            <h4 class="font-medium text-gray-900">What documents are required for license application?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                              License applications typically require business registration documents, technical specifications, and proof of financial capability.
                            </p>
                          </div>
                          <div>
                            <h4 class="font-medium text-gray-900">How long does license approval take?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                              Standard license applications are typically processed within 30 business days, while expedited applications may be processed in 10-15 business days.
                            </p>
                          </div>
                          <div>
                            <h4 class="font-medium text-gray-900">Can I transfer a license to another entity?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                              Yes, licenses can be transferred subject to regulatory approval. The transfer process can be initiated through the License Management section.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Other category sections would be added here with 'hidden' class -->
                <div id="spectrum-management" class="category-content hidden bg-white rounded-lg shadow overflow-hidden">
                  <!-- Spectrum Management content -->
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">Spectrum Management Help</h2>
                  </div>
                  <div class="p-6">
                    <p class="text-gray-600">Content for spectrum management help will be available soon.</p>
                  </div>
                </div>

                <div id="financial-transactions" class="category-content hidden bg-white rounded-lg shadow overflow-hidden">
                  <!-- Financial Transactions content -->
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">Financial Transactions Help</h2>
                  </div>
                  <div class="p-6">
                    <p class="text-gray-600">Content for financial transactions help will be available soon.</p>
                  </div>
                </div>

                <div id="reports-analytics" class="category-content hidden bg-white rounded-lg shadow overflow-hidden">
                  <!-- Reports & Analytics content -->
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">Reports & Analytics Help</h2>
                  </div>
                  <div class="p-6">
                    <p class="text-gray-600">Content for reports and analytics help will be available soon.</p>
                  </div>
                </div>

                <div id="account-settings" class="category-content hidden bg-white rounded-lg shadow overflow-hidden">
                  <!-- Account Settings content -->
                  <div class="px-4 py-5 border-b border-gray-200">
                    <h2 class="text-xl font-medium text-gray-900">Account Settings Help</h2>
                  </div>
                  <div class="p-6">
                    <p class="text-gray-600">Content for account settings help will be available soon.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>
