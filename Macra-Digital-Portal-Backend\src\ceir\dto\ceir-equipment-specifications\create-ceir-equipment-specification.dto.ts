import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
  MaxLength,
  IsArray,
  IsIn,
  IsDateString,
  <PERSON>N<PERSON>ber,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { 
  NETWORK_TECHNOLOGIES, 
  ANTENNA_TYPES 
} from '../../entities/ceir-equipment-specifications.entity';

export class CreateCeirEquipmentSpecificationDto {
  @ApiProperty({
    description: 'Device ID this specification belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID('4', { message: 'Device ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Device ID is required' })
  device_id: string;

  @ApiProperty({
    description: 'Equipment category ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID('4', { message: 'Equipment category ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Equipment category ID is required' })
  equipment_category_id: string;

  @ApiProperty({
    description: 'Hardware version',
    example: 'HW v2.1',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Hardware version must be a string' })
  @MaxLength(100, { message: 'Hardware version must not exceed 100 characters' })
  hardware_version?: string;

  @ApiProperty({
    description: 'Software version',
    example: 'SW v1.5.2',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Software version must be a string' })
  @MaxLength(100, { message: 'Software version must not exceed 100 characters' })
  software_version?: string;

  @ApiProperty({
    description: 'Firmware version',
    example: 'FW v3.2.1',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Firmware version must be a string' })
  @MaxLength(100, { message: 'Firmware version must not exceed 100 characters' })
  firmware_version?: string;

  @ApiProperty({
    description: 'Supported network technologies',
    type: 'array',
    items: { enum: [...NETWORK_TECHNOLOGIES] },
    example: ['gsm', 'umts', 'lte'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Supported network technologies must be an array' })
  @IsString({ each: true, message: 'Each network technology must be a string' })
  @IsIn(NETWORK_TECHNOLOGIES, { each: true, message: 'Each network technology must be a valid technology' })
  supported_network_technologies?: string[];

  @ApiProperty({
    description: 'Operating frequency bands',
    type: 'array',
    example: ['GSM 900 (880-915 MHz)', 'GSM 1800 (1710-1785 MHz)', 'UMTS 2100 (1920-1980 MHz)'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Operating frequency bands must be an array' })
  @IsString({ each: true, message: 'Each frequency band must be a string' })
  operating_frequency_bands?: string[];

  @ApiProperty({
    description: 'Maximum transmit power in dBm',
    example: 33.0,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Maximum transmit power must be a number' })
  @Min(-50, { message: 'Maximum transmit power must be at least -50 dBm' })
  @Max(50, { message: 'Maximum transmit power must not exceed 50 dBm' })
  max_transmit_power_dbm?: number;

  @ApiProperty({
    description: 'Receiver sensitivity in dBm',
    example: -102.0,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Receiver sensitivity must be a number' })
  @Min(-150, { message: 'Receiver sensitivity must be at least -150 dBm' })
  @Max(0, { message: 'Receiver sensitivity must not exceed 0 dBm' })
  receiver_sensitivity_dbm?: number;

  @ApiProperty({
    description: 'Antenna type',
    enum: ANTENNA_TYPES,
    example: 'internal',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Antenna type must be a string' })
  @IsIn(ANTENNA_TYPES, { message: 'Antenna type must be one of the valid antenna types' })
  antenna_type?: string;

  @ApiProperty({
    description: 'Antenna gain in dBi',
    example: 2.15,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Antenna gain must be a number' })
  @Min(-20, { message: 'Antenna gain must be at least -20 dBi' })
  @Max(20, { message: 'Antenna gain must not exceed 20 dBi' })
  antenna_gain_dbi?: number;

  @ApiProperty({
    description: 'SAR value (head) in W/kg',
    example: 0.98,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'SAR head value must be a number' })
  @Min(0, { message: 'SAR head value must be non-negative' })
  @Max(10, { message: 'SAR head value must not exceed 10 W/kg' })
  sar_head_wkg?: number;

  @ApiProperty({
    description: 'SAR value (body) in W/kg',
    example: 1.15,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'SAR body value must be a number' })
  @Min(0, { message: 'SAR body value must be non-negative' })
  @Max(10, { message: 'SAR body value must not exceed 10 W/kg' })
  sar_body_wkg?: number;

  @ApiProperty({
    description: 'Operating temperature range',
    example: '-10°C to +55°C',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Operating temperature range must be a string' })
  @MaxLength(100, { message: 'Operating temperature range must not exceed 100 characters' })
  operating_temperature_range?: string;

  @ApiProperty({
    description: 'Storage temperature range',
    example: '-20°C to +70°C',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Storage temperature range must be a string' })
  @MaxLength(100, { message: 'Storage temperature range must not exceed 100 characters' })
  storage_temperature_range?: string;

  @ApiProperty({
    description: 'Operating humidity range',
    example: '5% to 95% RH (non-condensing)',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Operating humidity range must be a string' })
  @MaxLength(100, { message: 'Operating humidity range must not exceed 100 characters' })
  operating_humidity_range?: string;

  @ApiProperty({
    description: 'Power supply voltage',
    example: '3.7V DC (Li-ion battery)',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Power supply voltage must be a string' })
  @MaxLength(100, { message: 'Power supply voltage must not exceed 100 characters' })
  power_supply_voltage?: string;

  @ApiProperty({
    description: 'Power consumption in watts',
    example: 2.5,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Power consumption must be a number' })
  @Min(0, { message: 'Power consumption must be non-negative' })
  @Max(1000, { message: 'Power consumption must not exceed 1000 watts' })
  power_consumption_watts?: number;

  @ApiProperty({
    description: 'Battery capacity in mAh',
    example: 4000,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Battery capacity must be a number' })
  @Min(0, { message: 'Battery capacity must be non-negative' })
  @Max(100000, { message: 'Battery capacity must not exceed 100000 mAh' })
  battery_capacity_mah?: number;

  @ApiProperty({
    description: 'Physical dimensions (L x W x H)',
    example: '158.2 x 73.7 x 7.9 mm',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Physical dimensions must be a string' })
  @MaxLength(100, { message: 'Physical dimensions must not exceed 100 characters' })
  physical_dimensions?: string;

  @ApiProperty({
    description: 'Weight in grams',
    example: 169,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Weight must be a number' })
  @Min(0, { message: 'Weight must be non-negative' })
  @Max(10000, { message: 'Weight must not exceed 10000 grams' })
  weight_grams?: number;

  @ApiProperty({
    description: 'IP rating for water/dust protection',
    example: 'IP68',
    maxLength: 20,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'IP rating must be a string' })
  @MaxLength(20, { message: 'IP rating must not exceed 20 characters' })
  ip_rating?: string;

  @ApiProperty({
    description: 'Supported SIM card types',
    type: 'array',
    example: ['Nano-SIM', 'eSIM'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Supported SIM types must be an array' })
  @IsString({ each: true, message: 'Each SIM type must be a string' })
  supported_sim_types?: string[];

  @ApiProperty({
    description: 'Memory specifications',
    example: '8GB RAM, 128GB Storage, microSD up to 1TB',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Memory specifications must be a string' })
  memory_specifications?: string;

  @ApiProperty({
    description: 'Display specifications',
    example: '6.1" Super AMOLED, 2400x1080, 421 PPI',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Display specifications must be a string' })
  display_specifications?: string;

  @ApiProperty({
    description: 'Camera specifications',
    example: 'Triple rear: 64MP main + 12MP ultra-wide + 5MP macro, 32MP front',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Camera specifications must be a string' })
  camera_specifications?: string;

  @ApiProperty({
    description: 'Connectivity features',
    type: 'array',
    example: ['Wi-Fi 6', 'Bluetooth 5.2', 'NFC', 'USB-C', '3.5mm headphone jack'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Connectivity features must be an array' })
  @IsString({ each: true, message: 'Each connectivity feature must be a string' })
  connectivity_features?: string[];

  @ApiProperty({
    description: 'Security features',
    type: 'array',
    example: ['Fingerprint scanner', 'Face recognition', 'Knox security platform'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Security features must be an array' })
  @IsString({ each: true, message: 'Each security feature must be a string' })
  security_features?: string[];

  @ApiProperty({
    description: 'Compliance certifications',
    type: 'array',
    example: ['CE', 'FCC', 'IC', 'RoHS', 'REACH'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Compliance certifications must be an array' })
  @IsString({ each: true, message: 'Each compliance certification must be a string' })
  compliance_certifications?: string[];

  @ApiProperty({
    description: 'Date when specifications were last updated',
    example: '2024-01-15',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Specification date must be a valid ISO 8601 date string' })
  specification_date?: string;

  @ApiProperty({
    description: 'Additional technical notes',
    example: 'Supports carrier aggregation, VoLTE, and Wi-Fi calling',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Technical notes must be a string' })
  technical_notes?: string;

  @ApiProperty({
    description: 'Whether these specifications are currently active',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  is_active: boolean = true;

  @ApiProperty({
    description: 'User ID who is creating this specification (optional, can be extracted from JWT)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided' })
  created_by?: string;
}
