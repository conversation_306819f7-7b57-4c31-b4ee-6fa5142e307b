import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from '@nestjs-modules/mailer';
import { UnauthorizedException } from '@nestjs/common';
import { LoginDto } from '../dto/auth/login.dto';
import { User } from '../entities/user.entity';
import { UserStatus } from "../entities/user.entity";
import { Role } from "../entities/role.entity";

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let mailerService: jest.Mocked<MailerService>;

  const mockRole: Role = {
  role_id: 'role-id-123',
  name: 'admin',
  created_at: new Date(),
  updated_at: new Date(),
  users: [],            // assuming you don't need users/permissions populated
  permissions: [],
};


  const mockUser: User = {
    user_id: '123',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    password: 'hashedpassword',
    phone: '+265999123456',
    two_factor_enabled: true,
    roles: [mockRole],
    identifications: [],
    employee_records: [],
    status: UserStatus.ACTIVE,
    two_factor_code: undefined,
    two_factor_temp: undefined,
    two_factor_next_verification: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByEmail: jest.fn(),
            validatePassword: jest.fn(),
            updateLastLogin: jest.fn(),
            create: jest.fn(),
            updatePassword: jest.fn(),
            findById: jest.fn(),
            clearTwoFactorCode: jest.fn(),
            clearTempTwoFactorCode: jest.fn(),
            setTempTwoFactorCode: jest.fn(),
            setTwoFactorCode: jest.fn(),
            enableTwoFactor: jest.fn(),
            verifyEmail: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
    mailerService = module.get(MailerService);
  });

  describe('login', () => {
    it('should return access token and user if credentials are valid', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      usersService.findByEmail.mockResolvedValue(mockUser);
      usersService.validatePassword.mockResolvedValue(true);
      usersService.updateLastLogin.mockResolvedValue(undefined);
      jwtService.sign.mockReturnValue('jwt-token');

      const result = await service.login(loginDto);

      expect(usersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(usersService.validatePassword).toHaveBeenCalledWith('password123', 'hashedpassword');
      expect(jwtService.sign).toHaveBeenCalledWith({
        email: mockUser.email,
        sub: mockUser.user_id,
        roles: ['admin'],
      });
      expect(result).toEqual({
        access_token: 'jwt-token',
        user: {
          user_id: '123',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          two_factor_enabled: true,
          roles: ['admin'],
        },
      });
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      usersService.findByEmail.mockResolvedValue(mockUser);
      usersService.validatePassword.mockResolvedValue(false);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const inactiveUser = { ...mockUser, status: UserStatus.INACTIVE };
      usersService.findByEmail.mockResolvedValue(inactiveUser);
      usersService.validatePassword.mockResolvedValue(true);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if user is not found', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      usersService.findByEmail.mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });
});
