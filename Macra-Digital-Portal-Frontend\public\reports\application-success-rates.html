<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Success Rates Report - MACRA</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .success-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metric-card.overall { border-left: 4px solid #28a745; }
        .metric-card.first-time { border-left: 4px solid #007bff; }
        .metric-card.resubmission { border-left: 4px solid #ffc107; }
        .metric-card.rejection { border-left: 4px solid #dc3545; }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-card.overall .metric-value { color: #28a745; }
        .metric-card.first-time .metric-value { color: #007bff; }
        .metric-card.resubmission .metric-value { color: #ffc107; }
        .metric-card.rejection .metric-value { color: #dc3545; }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .success-rate {
            font-weight: bold;
            text-align: center;
        }
        
        .rate-excellent { color: #28a745; }
        .rate-good { color: #007bff; }
        .rate-average { color: #ffc107; }
        .rate-poor { color: #dc3545; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .progress-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
        .progress-good { background: linear-gradient(90deg, #007bff, #17a2b8); }
        .progress-average { background: linear-gradient(90deg, #ffc107, #fd7e14); }
        .progress-poor { background: linear-gradient(90deg, #dc3545, #e83e8c); }
        
        .rejection-reason {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .reason-documentation { background: #fff3cd; color: #856404; }
        .reason-technical { background: #f8d7da; color: #721c24; }
        .reason-compliance { background: #d1ecf1; color: #0c5460; }
        .reason-financial { background: #e2e3e5; color: #383d41; }
        
        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            position: relative;
        }
        
        canvas {
            height: 250px !important;
        }
        
        .chart-container h3 {
            margin-top: 15px;
            color: #333;
            text-align: center;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            canvas { max-width: 100%; height: auto; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Application Success Rates Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> August 11, 2025, 04:29 PM CAT</div>
                <div><strong>Period:</strong> Q4 2024 (Oct-Dec)</div>
                <div><strong>Generated By:</strong> Quality Assurance Team</div>
            </div>
        </div>

        <!-- Success Rate Metrics -->
        <div class="success-metrics">
            <div class="metric-card overall">
                <div class="metric-value">78.5%</div>
                <div class="metric-label">Overall Success Rate</div>
            </div>
            <div class="metric-card first-time">
                <div class="metric-value">65.2%</div>
                <div class="metric-label">First-Time Approval</div>
            </div>
            <div class="metric-card resubmission">
                <div class="metric-value">13.3%</div>
                <div class="metric-label">Approved After Resubmission</div>
            </div>
            <div class="metric-card rejection">
                <div class="metric-value">21.5%</div>
                <div class="metric-label">Final Rejection Rate</div>
            </div>
        </div>

        <!-- Success Rates by License Type -->
        <div class="section">
            <h2 class="section-title">Success Rates by License Type</h2>
            <div class="chart-container">
                <h3 style="color: #333; margin-bottom: 15px; text-align: center;">Application Success Rate Comparison</h3>
                <!-- Success Rate Progress Bars -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <!-- Telecommunications -->
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #007bff;">Telecommunications</span>
                                <span style="font-weight: bold; color: #28a745;">82.1%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill progress-excellent" style="width: 82.1%;"></div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">128/156 approved ✓ Excellent</div>
                        </div>
                        <!-- Broadcasting -->
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #28a745;">Broadcasting</span>
                                <span style="font-weight: bold; color: #28a745;">80.6%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill progress-excellent" style="width: 80.6%;"></div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">108/134 approved ✓ Excellent</div>
                        </div>
                        <!-- Postal Services -->
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #007bff;">Postal Services</span>
                                <span style="font-weight: bold; color: #007bff;">75.3%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill progress-good" style="width: 75.3%;"></div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">67/89 approved ✓ Good</div>
                        </div>
                        <!-- Standards -->
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #ffc107;">Standards</span>
                                <span style="font-weight: bold; color: #ffc107;">67.2%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill progress-average" style="width: 67.2%;"></div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">45/67 approved ⚠ Average</div>
                        </div>
                    </div>
                </div>
                <!-- Summary Cards -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="font-weight: bold; color: #28a745;">Telecommunications</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">82.1%</div>
                        <div style="font-size: 12px; color: #666;">128/156 approved</div>
                        <div style="color: #28a745; font-size: 12px;">✓ Excellent</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="font-weight: bold; color: #28a745;">Broadcasting</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">80.6%</div>
                        <div style="font-size: 12px; color: #666;">108/134 approved</div>
                        <div style="color: #28a745; font-size: 12px;">✓ Excellent</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                        <div style="font-weight: bold; color: #007bff;">Postal Services</div>
                        <div style="font-size: 18px; color: #007bff; margin: 5px 0;">75.3%</div>
                        <div style="font-size: 12px; color: #666;">67/89 approved</div>
                        <div style="color: #007bff; font-size: 12px;">✓ Good</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <div style="font-weight: bold; color: #ffc107;">Standards</div>
                        <div style="font-size: 18px; color: #ffc107; margin: 5px 0;">67.2%</div>
                        <div style="font-size: 12px; color: #666;">45/67 approved</div>
                        <div style="color: #ffc107; font-size: 12px;">⚠ Average</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rejection Reasons Analysis -->
        <div class="section">
            <h2 class="section-title">Rejection Reasons Analysis</h2>
            <div class="chart-container">
                <canvas id="rejectionReasonsChart"></canvas>
                <h3>Common Rejection Reasons</h3>
                <div style="text-align: center; font-size: 14px; color: #333; margin-top: 10px;">
                    Total Rejections: <strong>107 applications</strong>
                </div>
            </div>
        </div>

        <!-- Monthly Success Rate Trends -->
        <div class="section">
            <h2 class="section-title">Monthly Success Rate Trends</h2>
            <div class="chart-container">
                <canvas id="monthlyTrendsChart"></canvas>
                <h3>Success Rate Trend Analysis</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #007bff;">October 2024</div>
                        <div style="font-size: 18px; color: #007bff; margin: 5px 0;">75.8%</div>
                        <div style="font-size: 12px; color: #666;">125/165 approved</div>
                        <div style="color: #28a745; font-size: 12px;">+2.3% vs prev</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #28a745;">November 2024</div>
                        <div style="font-size: 18px; color: #28a745; margin: 5px 0;">79.7%</div>
                        <div style="font-size: 12px; color: #666;">118/148 approved</div>
                        <div style="color: #28a745; font-size: 12px;">+3.9% vs prev</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-weight: bold; color: #ffc107;">December 2024</div>
                        <div style="font-size: 18px; color: #ffc107; margin: 5px 0;">78.9%</div>
                        <div style="font-size: 12px; color: #666;">105/133 approved</div>
                        <div style="color: #dc3545; font-size: 12px;">-0.8% vs prev</div>
                    </div>
                </div>
                <div style="text-align: center; font-size: 14px; color: #333; margin-top: 15px;">
                    Target: <strong>80%</strong> | Current Achievement: <strong>98.6%</strong>
                </div>
            </div>
        </div>

        <!-- Success Rate Trends -->
        <div class="section">
            <h2 class="section-title">Success Rate Trends</h2>
            <div class="chart-container">
                <canvas id="quarterlyTrendsChart"></canvas>
                <h3>Quarterly Success Rate Comparison</h3>
                <div style="text-align: center; font-size: 14px; color: #28a745; font-weight: bold; margin-top: 10px;">
                    ↑ 4.3% improvement over quarter
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This report helps identify areas for process improvement and applicant guidance.</p>
        </div>
    </div>

    <script>
        // Rejection Reasons Chart (Pie Chart)
        const rejectionReasonsChart = new Chart(document.getElementById('rejectionReasonsChart'), {
            type: 'pie',
            data: {
                labels: ['Documentation', 'Technical', 'Compliance', 'Financial', 'Other'],
                datasets: [{
                    label: 'Rejection Reasons',
                    data: [42.1, 26.2, 16.8, 11.2, 3.7],
                    backgroundColor: ['#ffc107', '#dc3545', '#17a2b8', '#6c757d', '#28a745'],
                    borderColor: ['#ffffff', '#ffffff', '#ffffff', '#ffffff', '#ffffff'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.raw}%`;
                            }
                        }
                    }
                }
            }
        });

        // Monthly Success Rate Trends (Line Chart)
        const monthlyTrendsChart = new Chart(document.getElementById('monthlyTrendsChart'), {
            type: 'line',
            data: {
                labels: ['Oct', 'Nov', 'Dec'],
                datasets: [
                    {
                        label: 'Success Rate',
                        data: [75.8, 79.7, 78.9],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 6,
                        pointBackgroundColor: '#28a745'
                    },
                    {
                        label: 'Target (80%)',
                        data: [80, 80, 80],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.2)',
                        fill: false,
                        tension: 0,
                        pointRadius: 0,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 70,
                        max: 85,
                        title: {
                            display: true,
                            text: 'Success Rate (%)',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            stepSize: 5
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw}%`;
                            }
                        }
                    }
                }
            }
        });

        // Quarterly Success Rate Trends (Bar Chart)
        const quarterlyTrendsChart = new Chart(document.getElementById('quarterlyTrendsChart'), {
            type: 'bar',
            data: {
                labels: ['Q3 2024', 'Q4 2024'],
                datasets: [{
                    label: 'Success Rate',
                    data: [74.2, 78.5],
                    backgroundColor: 'rgba(0, 123, 255, 0.6)',
                    borderColor: '#007bff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 70,
                        max: 80,
                        title: {
                            display: true,
                            text: 'Success Rate (%)',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            stepSize: 2
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Quarter',
                            font: {
                                size: 16
                            }
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 16
                            },
                            boxWidth: 12,
                            boxHeight: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw}%`;
                            }
                        }
                    },
                    annotation: {
                        annotations: {
                            improvementLabel: {
                                type: 'label',
                                xValue: 1.5,
                                yValue: 78,
                                content: ['↑ 4.3% improvement'],
                                color: '#28a745',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                position: 'center'
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>