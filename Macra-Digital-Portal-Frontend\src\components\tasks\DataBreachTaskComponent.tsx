'use client';

import React, { useState, useEffect } from 'react';
import { dataBreachService, DataBreachReport } from '@/services/data-breach';
import { useToast } from '@/contexts/ToastContext';
import DataBreachCard from '../data-breach/DataBreachCard';
import DataBreachActions from '../data-breach/DataBreachActions';
import ActivityNotesModal from '@/components/evaluation/ActivityNotesModal';
import ActivityHistory from '@/components/common/ActivityHistory';
import { Task } from '@/types/task';

interface DataBreachTaskComponentProps {
  task: Task;
  onTaskUpdate?: () => void;
}

const DataBreachTaskComponent: React.FC<DataBreachTaskComponentProps> = ({
  task,
  onTaskUpdate
}) => {
  const { showError } = useToast();
  const [report, setReport] = useState<DataBreachReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isActivityNotesModalOpen, setIsActivityNotesModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (task.entity_id) {
      fetchDataBreachReport();
    }
  }, [task.entity_id]);

  const fetchDataBreachReport = async () => {
    if (!task.entity_id) {
      setError('No data breach report ID provided');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await dataBreachService.getReportById(task.entity_id);
      setReport(response);
    } catch (err: unknown) {
      console.error('Error fetching data breach report:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load data breach report: ${errorMessage}`);
      showError(`Failed to load data breach report: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleReportUpdate = () => {
    // Refresh the report data
    fetchDataBreachReport();
    // Trigger task update if callback provided
    if (onTaskUpdate) {
      onTaskUpdate();
    }
  };

  const handleRefreshActivity = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">Loading data breach report...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex">
          <i className="ri-error-warning-line text-red-400 mr-2"></i>
          <div>
            <p className="text-red-700 dark:text-red-200">{error}</p>
            <button
              onClick={fetchDataBreachReport}
              className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="text-center py-12">
        <i className="ri-file-warning-line text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
        <p className="text-gray-500 dark:text-gray-400">No data breach report found</p>
        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
          Report ID: {task.entity_id}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Task Information */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-start">
          <i className="ri-task-line text-blue-600 dark:text-blue-400 mr-3 mt-1"></i>
          <div>
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100">
              Data Breach Investigation Task
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mt-1">
              <strong>Task:</strong> {task.title}
            </p>
            <p className="text-blue-700 dark:text-blue-300">
              <strong>Description:</strong> {task.description}
            </p>
            <div className="flex items-center mt-2 text-sm text-blue-600 dark:text-blue-400">
              <span className="mr-4">
                <strong>Status:</strong> {task.status?.replace('_', ' ').toUpperCase()}
              </span>
              <span className="mr-4">
                <strong>Priority:</strong> {task.priority?.toUpperCase()}
              </span>
              <span>
                <strong>Due:</strong> {task.due_date ? new Date(task.due_date).toLocaleDateString() : 'Not set'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Data Breach Card */}
      <DataBreachCard
        report={report}
        showEmptyFields={true}
        defaultCollapsed={false}
      />

      {/* Activity Notes Section */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Activity History</h3>
          <button
            onClick={() => setIsActivityNotesModalOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 flex items-center"
          >
            <i className="ri-mail-line mr-2"></i>
            Send Message
          </button>
        </div>
        <div className="p-4">
          <ActivityHistory
            entityType="data-breach"
            entityId={report.report_id}
            title=""
            showSearch={true}
            showFilters={false}
            maxHeight="max-h-96"
            refreshTrigger={refreshTrigger}
            className="border-0 rounded-none"
          />
        </div>
      </div>

      {/* Data Breach Actions */}
      <DataBreachActions
        report={report}
        onUpdate={handleReportUpdate}
        onRefreshActivity={handleRefreshActivity}
      />

      {/* Activity Notes Modal */}
      <ActivityNotesModal
        isOpen={isActivityNotesModalOpen}
        onClose={() => {
          setIsActivityNotesModalOpen(false);
          handleRefreshActivity();
        }}
        entityId={report.report_id}
        entityType="data-breach"
        initialEmails={report.reporter?.email || ''}
        title={`Data Breach Report - ${report.report_number}`}
      />
    </div>
  );
};

export default DataBreachTaskComponent;
