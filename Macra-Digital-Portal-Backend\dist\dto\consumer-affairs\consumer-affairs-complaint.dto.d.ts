import { ComplaintCategory, ComplaintStatus, ComplaintPriority } from 'src/consumer-affairs/consumer-affairs-constants';
export declare class CreateConsumerAffairsComplaintDto {
    title: string;
    description: string;
    category: ComplaintCategory;
    priority?: ComplaintPriority;
}
declare const UpdateConsumerAffairsComplaintDto_base: import("@nestjs/common").Type<Partial<CreateConsumerAffairsComplaintDto>>;
export declare class UpdateConsumerAffairsComplaintDto extends UpdateConsumerAffairsComplaintDto_base {
    status?: ComplaintStatus;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
}
export declare class UpdateConsumerAffairsComplaintStatusDto {
    status: ComplaintStatus;
    comment?: string;
}
export declare class UpdateConsumerAffairsComplaineeDto {
    complainee_reg_number: string;
}
export declare class ConsumerAffairsComplaintFilterDto {
    category?: ComplaintCategory;
    status?: ComplaintStatus;
    priority?: ComplaintPriority;
    complainant_id?: string;
    assigned_to?: string;
    from_date?: string;
    to_date?: string;
    search?: string;
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: 'ASC' | 'DESC';
}
export {};
