import { UserStatus } from '../../entities/user.entity';

export class UserResponseDto {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  status: UserStatus;
  profile_image?: string;
  roles?: string[];
  email_verified_at?: Date;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export class LoginResponseDto {
  access_token: string;
  refresh_token: string;
  user: UserResponseDto;
  requires_two_factor?: boolean;
}

export class RefreshTokenResponseDto {
  access_token: string;
  refresh_token: string;
}
