import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum ActivityNoteType {
  EVALUATION_COMMENT = 'evaluation_comment',
  STATUS_UPDATE = 'status_update',
  GENERAL_NOTE = 'general_note',
  SYSTEM_LOG = 'system_log',
  REVIEW_NOTE = 'review_note',
  APPROVAL_NOTE = 'approval_note',
  REJECTION_NOTE = 'rejection_note',
}

export enum ActivityNoteStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

@Entity('activity_notes')
@Index(['entity_type', 'entity_id'])
@Index(['created_by'])
@Index(['note_type'])
@Index(['status'])
export class ActivityNote {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Polymorphic relationship fields
  @Column({ type: 'varchar', length: 100 })
  entity_type: string; // 'application', 'evaluation', 'document', etc.

  @Column({ type: 'uuid' })
  entity_id: string; // ID of the related entity

  // Note content
  @Column({ type: 'text' })
  note: string;

  @Column({
    type: 'varchar',
    default: ActivityNoteType.GENERAL_NOTE,
  })
  note_type: string;

  @Column({
    type: 'varchar',
    default: ActivityNoteStatus.ACTIVE,
  })
  status: string;

  // Additional metadata
  @Column({ type: 'varchar', length: 100, nullable: true })
  category: string; // Optional categorization

  @Column({ type: 'varchar', length: 100, nullable: true })
  step: string; // For evaluation steps like 'applicant-info', 'documents', etc.

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional flexible data

  // Priority/importance level
  @Column({ type: 'varchar', length: 20, default: 'normal' })
  priority: string; // 'low', 'normal', 'high', 'critical'

  // Visibility settings
  @Column({ type: 'boolean', default: true })
  is_visible: boolean;

  @Column({ type: 'boolean', default: false })
  is_internal: boolean; // Internal notes vs public notes

  // User relationships
  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  // Timestamps
  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  archived_at: Date;

  // Soft delete support
  @Column({ type: 'timestamp', nullable: true })
  deleted_at: Date;
}
