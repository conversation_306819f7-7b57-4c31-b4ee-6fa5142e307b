import {
  <PERSON><PERSON><PERSON>,
  Column,
  CreateDate<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { Applications } from './applications.entity';
import { User } from './user.entity';

@Entity('application_status_history')
export class ApplicationStatusHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  history_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  application_id: string;

  @Column({
    type: 'varchar',
    length: 30,
  })
  @IsString()
  status: string;

  @Column({
    type: 'varchar',
    length: 30,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  previous_status?: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  @IsOptional()
  comments?: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  @IsOptional()
  reason?: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  changed_by: string;

  @CreateDateColumn()
  changed_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  estimated_completion_date?: Date;

  // Relations
  @ManyToOne(() => Applications)
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'changed_by' })
  user: User;

  @BeforeInsert()
  generateId() {
    if (!this.history_id) {
      this.history_id = uuidv4();
    }
  }
}
