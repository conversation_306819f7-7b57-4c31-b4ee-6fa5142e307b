import { PartialType } from '@nestjs/swagger';
import { CreateShortcodeDto } from './create-shortcode.dto';
import { IsEnum, IsOptional, IsUUID, MaxLength, ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments, Validate } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ShortcodeStatus } from './shortcode-enums';

@ValidatorConstraint({ name: 'assignedToRequiredForActive', async: false })
export class AssignedToRequiredForActiveConstraint implements ValidatorConstraintInterface {
  validate(assignedTo: string, args: ValidationArguments): boolean {
    const object = args.object as any;
    // If status is 'active', assigned_to is required
    if (object.status === ShortcodeStatus.ACTIVE) {
      return !!(assignedTo && assignedTo.trim() !== '');
    }
    // If status is not 'active', assigned_to is optional
    return true;
  }

  defaultMessage(): string {
    return 'An active short code has to be assigned';
  }
}

export class UpdateShortcodeDto extends PartialType(CreateShortcodeDto) {
  @ApiProperty({
    description: 'User ID who is updating this shortcode',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Invalid updater!' })
  updated_by?: string;


  @ApiProperty({
    description: 'Organization ID who is assigned this shortcode',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Invalid assignee!' })
  @Validate(AssignedToRequiredForActiveConstraint)
  assigned_to?: string;

  @ApiProperty({
    description: 'Edit pregenerated shortcode. Must not exceed 4 digits',
    example: '1234',
    required: false
  })
  @IsOptional()
  @MaxLength(4, { message: 'Shortcode must not exceed 4 digits' })
  shortcode?: string;
}
