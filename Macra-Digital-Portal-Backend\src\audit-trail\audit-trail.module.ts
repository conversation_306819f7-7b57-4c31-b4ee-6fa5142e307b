import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditTrailService } from './audit-trail.service';
import { AuditTrailController } from './audit-trail.controller';
import { AuditTrail } from '../entities/audit-trail.entity';
import { AdminAlert } from 'src/entities/admin_alerts.entity';
import { User } from 'src/entities';

@Module({
  imports: [TypeOrmModule.forFeature([AuditTrail, AdminAlert, User])],
  controllers: [AuditTrailController],
  providers: [AuditTrailService],
  exports: [AuditTrailService],
})
export class AuditTrailModule {}
