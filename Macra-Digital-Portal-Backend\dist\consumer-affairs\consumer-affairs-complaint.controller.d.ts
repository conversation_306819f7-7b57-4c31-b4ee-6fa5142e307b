import { ConsumerAffairsComplaintService } from 'src/consumer-affairs/consumer-affairs-complaint.service';
import { PaginateQuery } from 'nestjs-paginate';
import { ConsumerAffairsComplaintFilterDto, CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintStatusDto, UpdateConsumerAffairsComplaineeDto } from 'src/dto/consumer-affairs/consumer-affairs-complaint.dto';
export declare class ConsumerAffairsComplaintController {
    private readonly complaintService;
    constructor(complaintService: ConsumerAffairsComplaintService);
    create(createDto: CreateConsumerAffairsComplaintDto, files: Express.Multer.File[], req: any): Promise<import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto | {
        attachments: {
            attachment_id: any;
            file_name: any;
            file_type: any;
            file_size: any;
            uploaded_at: any;
        }[];
        complaint_id: string;
        complaint_number: string;
        complainant_id: string;
        complainee_reg_number?: string;
        title: string;
        description: string;
        category: string;
        status: string;
        priority: string;
        assigned_to?: string;
        resolution?: string;
        resolved_at?: Date;
        created_at?: Date;
        updated_at?: Date;
        complainant?: {
            user_id: string;
            first_name: string;
            last_name: string;
            email: string;
        };
        assignee?: {
            user_id: string;
            first_name: string;
            last_name: string;
            email: string;
        };
        status_history?: {
            history_id: string;
            status: string;
            comment?: string;
            created_at: Date;
            creator: {
                user_id: string;
                first_name: string;
                last_name: string;
            };
        }[];
    } | {
        warning: string;
        complaint_id: string;
        complaint_number: string;
        complainant_id: string;
        complainee_reg_number?: string;
        title: string;
        description: string;
        category: string;
        status: string;
        priority: string;
        assigned_to?: string;
        resolution?: string;
        resolved_at?: Date;
        created_at?: Date;
        updated_at?: Date;
        complainant?: {
            user_id: string;
            first_name: string;
            last_name: string;
            email: string;
        };
        assignee?: {
            user_id: string;
            first_name: string;
            last_name: string;
            email: string;
        };
        attachments?: {
            attachment_id: string;
            file_name: string;
            file_type: string;
            file_size: number;
            uploaded_at: Date;
        }[];
        status_history?: {
            history_id: string;
            status: string;
            comment?: string;
            created_at: Date;
            creator: {
                user_id: string;
                first_name: string;
                last_name: string;
            };
        }[];
    }>;
    findAll(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<import("../entities").ConsumerAffairsComplaint>>;
    findOne(id: string, req: any): Promise<import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto>;
    update(id: string, updateDto: UpdateConsumerAffairsComplaintDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto;
    }>;
    delete(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    updateStatus(id: string, statusDto: UpdateConsumerAffairsComplaintStatusDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto;
    }>;
    updateComplainee(id: string, complaineeDto: UpdateConsumerAffairsComplaineeDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto;
    }>;
    assignComplaint(id: string, assignedTo: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-constants").ConsumerAffairsComplaintResponseDto;
    }>;
    getStatsSummary(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            by_status: {};
            by_category: {};
            by_priority: {};
        };
    }>;
    exportToCsv(filterDto: ConsumerAffairsComplaintFilterDto, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    addAttachments(id: string, files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            document_id: any;
            file_name: any;
            mime_type: any;
            file_size: any;
            created_at: any;
        }[];
    }>;
    getAttachments(id: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            attachment_id: any;
            file_name: any;
            file_type: any;
            file_size: any;
            uploaded_at: any;
            uploader: {
                user_id: any;
                first_name: any;
                last_name: any;
            } | undefined;
        }[];
    }>;
    deleteAttachment(id: string, attachmentId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    downloadAttachment(id: string, attachmentId: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            download_url: string;
            file_name: any;
            file_type: any;
            file_size: any;
            expires_in: number;
        };
    }>;
}
