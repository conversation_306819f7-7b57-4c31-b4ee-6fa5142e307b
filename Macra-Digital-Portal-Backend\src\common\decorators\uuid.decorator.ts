import { PrimaryGeneratedColumn, BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

/**
 * Custom UUID Primary Column decorator that works across all database types
 * without requiring database-specific extensions
 */
export function UuidPrimaryColumn() {
  return function (target: any, propertyKey: string) {
    // Apply the PrimaryGeneratedColumn decorator with uuid type
    PrimaryGeneratedColumn('uuid')(target, propertyKey);
    
    // Add a BeforeInsert hook to generate UUID if not provided
    const originalBeforeInsert = target.constructor.prototype.beforeInsert;
    
    target.constructor.prototype.beforeInsert = function() {
      // Generate UUID using Node.js uuid library if not already set
      if (!this[propertyKey]) {
        this[propertyKey] = uuidv4();
      }
      
      // Call original beforeInsert if it exists
      if (originalBeforeInsert) {
        originalBeforeInsert.call(this);
      }
    };
    
    // Ensure the BeforeInsert decorator is applied
    BeforeInsert()(target, 'beforeInsert');
  };
}

/**
 * Alternative approach: Use regular string column with UUID generation
 * This works reliably across all database types
 */
export function UuidColumn() {
  return function (target: any, propertyKey: string) {
    // Import Column decorator
    const { Column } = require('typeorm');
    
    // Apply Column decorator with appropriate type
    Column({
      type: 'varchar',
      length: 36,
      primary: true,
      unique: true,
    })(target, propertyKey);
    
    // Add BeforeInsert hook
    const originalBeforeInsert = target.constructor.prototype.beforeInsert;
    
    target.constructor.prototype.beforeInsert = function() {
      if (!this[propertyKey]) {
        this[propertyKey] = uuidv4();
      }
      
      if (originalBeforeInsert) {
        originalBeforeInsert.call(this);
      }
    };
    
    BeforeInsert()(target, 'beforeInsert');
  };
}
