<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Renewals - Admin Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style type="text/tailwindcss">
      @layer components {
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6 sm:items-start md:items-start lg:items-center;
        }
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
        }

        .enhanced-select {
          @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }

        .main-button {
          @apply inline-flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }

        .license-card {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1;
        }

        .license-card-icon {
          @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl;
        }

        .license-card-button {
          @apply w-full bg-primary text-white px-4 py-3 rounded-lg font-medium hover:bg-primary-subtle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 transform hover:scale-105;
        }

        .category-section {
          @apply mb-12;
        }

        .category-header {
          @apply flex items-center mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border-l-4 border-primary;
        }

        .category-icon {
          @apply w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center mr-4;
        }

        .fade-in {
          animation: fadeIn 0.6s ease-in-out;
        }

        .slide-up {
          animation: slideUp 0.8s ease-out;
        }

        .inner-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2 border-b border-gray-200 pb-4;
        }

        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }

        .sub-heading {
          @apply text-gray-700;
        }

        .card-bg {
          @apply bg-white rounded-lg border border-gray-200 hover:border-primary transition-all duration-200 hover:shadow-md;
        }

        .status-badge {
          @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }

        .status-expiring-soon {
          @apply bg-red-100 text-red-800;
        }

        .status-expiring-warning {
          @apply bg-yellow-100 text-yellow-800;
        }

        .status-renewal-submitted {
          @apply bg-blue-100 text-blue-800;
        }

        .status-renewal-approved {
          @apply bg-green-100 text-green-800;
        }

        .status-active {
          @apply bg-green-100 text-green-800;
        }

        .status-expired {
          @apply bg-red-100 text-red-800;
        }

        /* License Management Dropdown Styles */
        .license-management-dropdown .rotate-180 {
          transform: rotate(180deg);
        }

        .license-management-dropdown button:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .renewal-card {
          @apply bg-white rounded-lg border border-gray-200 p-6 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md;
        }

        .renewal-card-expired {
          @apply bg-red-50 rounded-lg border border-red-200 p-6 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md;
        }

        .renewal-card-warning {
          @apply bg-yellow-50 rounded-lg border border-yellow-200 p-6 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md;
        }

        .expiry-badge {
          @apply relative inline-flex items-center justify-center w-8 h-8 rounded-full text-lg;
        }

        .expiry-critical {
          @apply bg-red-100 text-red-600;
        }

        .expiry-warning {
          @apply bg-yellow-100 text-yellow-600;
        }

        .expiry-good {
          @apply bg-green-100 text-green-600;
        }

        .expiry-badge .days-subscript {
          @apply absolute -bottom-1 -right-3 bg-white text-xs font-bold rounded-full w-7 h-5 flex items-center justify-center border-2;
        }

        .expiry-critical .days-subscript {
          @apply border-red-600 text-red-600;
        }

        .expiry-warning .days-subscript {
          @apply border-yellow-600 text-yellow-600;
        }

        .expiry-good .days-subscript {
          @apply border-green-600 text-green-600;
        }

        .view-doc-btn {
          @apply inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-secondary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary ml-2;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }

      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .stagger-animation {
        animation-delay: calc(var(--stagger) * 0.1s);
      }

      /* Notification animations */
      .notification-toast {
        transition: transform 0.3s ease-in-out;
      }

      /* Loading spinner animation */
      .animate-spin {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      /* Urgent indicator pulse */
      .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: .5;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <!-- License Management with Dropdown -->
            <div class="license-management-dropdown">
              <button
                onclick="toggleLicenseDropdown()"
                class="flex items-center justify-between w-full px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none"
              >
                <div class="flex items-center">
                  <div class="w-5 h-5 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  License Management
                </div>
                <div class="w-4 h-4 flex items-center justify-center">
                  <i class="ri-arrow-down-s-line transition-transform duration-200" id="licenseDropdownIcon"></i>
                </div>
              </button>

              <!-- Dropdown Menu -->
              <div id="licenseDropdownMenu" class="ml-8 mt-1 space-y-1 hidden">
                <a
                  href="license-management.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-list-check-line"></i>
                  </div>
                  Manage Licenses
                </a>
                <a
                  href="approve_licenses.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-file-check-line"></i>
                  </div>
                  License Approvals
                </a>
                <a
                  href="renew_licenses.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-refresh-line"></i>
                  </div>
                  License Renewals
                </a>
              </div>
            </div>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>
              </div>
              Spectrum Management
            </a>
          <a
              href="../financial/accounts-finance.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>
              Accounts & Finance
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>
              </div>
              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>
                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Admin User</p>
              <p class="text-xs text-gray-500">MACRA Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="tab-heading">
              <div>
                <h1 class="text-3xl font-bold text-gray-900">License Renewal Management</h1>
                <p class="mt-2 text-gray-600">Monitor and manage license renewals, track expiration dates, and process renewal applications.</p>
              </div>
              <div class="flex space-x-3">
                <select class="enhanced-select">
                  <option value="all">All Categories</option>
                  <option value="application">Application Service</option>
                  <option value="content">Content Service</option>
                  <option value="network-facility">Network Facility</option>
                  <option value="network-service">Network Service</option>
                  <option value="postal">Postal Services</option>
                </select>
                <select class="enhanced-select">
                  <option value="all">All Status</option>
                  <option value="expiring-soon">Expiring Soon</option>
                  <option value="expiring-warning">Expiring Warning</option>
                  <option value="renewal-submitted">Renewal Submitted</option>
                  <option value="renewal-approved">Renewal Approved</option>
                  <option value="active">Active</option>
                  <option value="expired">Expired</option>
                </select>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="ri-alarm-warning-line text-2xl text-red-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Expiring Soon</p>
                    <p class="text-2xl font-bold text-gray-900">8</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="ri-time-line text-2xl text-yellow-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Renewal Pending</p>
                    <p class="text-2xl font-bold text-gray-900">15</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="ri-check-line text-2xl text-green-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Renewed Today</p>
                    <p class="text-2xl font-bold text-gray-900">3</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="ri-refresh-line text-2xl text-blue-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Active</p>
                    <p class="text-2xl font-bold text-gray-900">127</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Renewal Alert -->
            <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-8">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-alarm-warning-line text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Critical Renewals Required</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p><strong>8 licenses</strong> are expiring within the next 30 days and require immediate attention. <strong>2 licenses</strong> have already expired.</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- License Renewals by Category -->
            <div class="space-y-8">
              <!-- Application Service License Renewals -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-global-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Application Service License Renewals</h2>
                    <p class="text-sm text-gray-600">ISP and Individual license renewals</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-expiring-soon">3 Expiring Soon</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- TechConnect ISP License - Critical -->
                  <div class="renewal-card-expired">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-global-line text-blue-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">TechConnect ISP</h3>
                          <p class="text-sm text-gray-500">License #ASL-2022-001</p>
                        </div>
                      </div>
                      <span class="expiry-badge expiry-critical">
                        <i class="ri-alarm-warning-line"></i>
                        <span class="days-subscript">5</span>
                      </span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Issue Date:</span>
                        <span class="font-medium">Jan 15, 2022</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Expiry Date:</span>
                        <span class="font-medium text-red-600">Jan 20, 2025</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Category:</span>
                        <span class="font-medium">Application Service</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Renewal Fee:</span>
                        <span class="font-medium text-primary">MWK 30,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Status:</span>
                        <span class="status-badge status-expiring-soon">Expiring Soon</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewRenewalDetails('ASL-2022-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="processRenewal('ASL-2022-001')" class="main-button flex-1">
                        <i class="ri-refresh-line mr-2"></i>Process Renewal
                      </button>
                    </div>
                  </div>

                  <!-- John Banda Individual License - Warning -->
                  <div class="renewal-card-warning">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-user-line text-green-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">John Banda</h3>
                          <p class="text-sm text-gray-500">License #ASL-2023-002</p>
                        </div>
                      </div>
                      <span class="expiry-badge expiry-warning">
                        <i class="ri-time-line"></i>
                        <span class="days-subscript">45</span>
                      </span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Issue Date:</span>
                        <span class="font-medium">Mar 10, 2023</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Expiry Date:</span>
                        <span class="font-medium text-yellow-600">Mar 10, 2025</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Category:</span>
                        <span class="font-medium">Application Service</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Renewal Fee:</span>
                        <span class="font-medium text-primary">MWK 30,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Status:</span>
                        <span class="status-badge status-renewal-submitted">Renewal Submitted</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewRenewalDetails('ASL-2023-002')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="approveRenewal('ASL-2023-002')" class="main-button flex-1">
                        <i class="ri-check-line mr-2"></i>Approve Renewal
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content Service License Renewals -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-tv-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Content Service License Renewals</h2>
                    <p class="text-sm text-gray-600">Radio and Television broadcasting license renewals</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-expiring-warning">2 Expiring</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Malawi University Radio License - Critical -->
                  <div class="renewal-card-expired">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-radio-line text-red-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">Malawi University</h3>
                          <p class="text-sm text-gray-500">License #CSL-2022-001</p>
                        </div>
                      </div>
                      <span class="expiry-badge expiry-critical">
                        <i class="ri-alarm-warning-line"></i>
                        <span class="days-subscript">2</span>
                      </span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Issue Date:</span>
                        <span class="font-medium">Jan 23, 2022</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Expiry Date:</span>
                        <span class="font-medium text-red-600">Jan 17, 2025</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Category:</span>
                        <span class="font-medium">Content Service</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Renewal Fee:</span>
                        <span class="font-medium text-primary">MWK 6,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Status:</span>
                        <span class="status-badge status-expired">Expired</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewRenewalDetails('CSL-2022-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="urgentRenewal('CSL-2022-001')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-alarm-warning-line mr-2"></i>Urgent Renewal
                      </button>
                    </div>
                  </div>

                  <!-- Blantyre Broadcasting TV License - Warning -->
                  <div class="renewal-card-warning">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-tv-line text-purple-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">Blantyre Broadcasting</h3>
                          <p class="text-sm text-gray-500">License #CSL-2022-002</p>
                        </div>
                      </div>
                      <span class="expiry-badge expiry-warning">
                        <i class="ri-time-line"></i>
                        <span class="days-subscript">60</span>
                      </span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Issue Date:</span>
                        <span class="font-medium">May 30, 2022</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Expiry Date:</span>
                        <span class="font-medium text-yellow-600">Mar 15, 2025</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Category:</span>
                        <span class="font-medium">Content Service</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Renewal Fee:</span>
                        <span class="font-medium text-primary">MWK 6,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Status:</span>
                        <span class="status-badge status-active">Active</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewRenewalDetails('CSL-2022-002')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="initiateRenewal('CSL-2022-002')" class="main-button flex-1">
                        <i class="ri-refresh-line mr-2"></i>Initiate Renewal
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Network Service License Renewals -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-smartphone-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Network Service License Renewals</h2>
                    <p class="text-sm text-gray-600">Mobile network and telecommunications service renewals</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-renewal-approved">1 Approved</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- MobileNet Solutions License - Approved -->
                  <div class="renewal-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-smartphone-line text-green-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">MobileNet Solutions</h3>
                          <p class="text-sm text-gray-500">License #NSL-2022-001</p>
                        </div>
                      </div>
                      <span class="expiry-badge expiry-good">
                        <i class="ri-check-line"></i>
                        <span class="days-subscript">✓</span>
                      </span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Issue Date:</span>
                        <span class="font-medium">Apr 8, 2022</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Expiry Date:</span>
                        <span class="font-medium text-green-600">Apr 8, 2027</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Category:</span>
                        <span class="font-medium">Network Service</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Renewal Fee:</span>
                        <span class="font-medium text-primary">MWK 1,800,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Status:</span>
                        <span class="status-badge status-renewal-approved">Renewal Approved</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewRenewalDetails('NSL-2022-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="generateNewLicense('NSL-2022-001')" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-file-text-line mr-2"></i>Generate License
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Renewal Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
              <!-- Renewal Requirements -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-file-list-line mr-2 text-primary"></i>
                  Renewal Requirements
                </h3>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Updated Financial Statements</p>
                      <p class="text-xs text-gray-500">Latest audited financial statements</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Compliance Certificate</p>
                      <p class="text-xs text-gray-500">Certificate of compliance with regulations</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Technical Assessment</p>
                      <p class="text-xs text-gray-500">Updated technical capacity documentation</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Renewal Application Form</p>
                      <p class="text-xs text-gray-500">Completed renewal application</p>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="ri-check-line text-green-500 mr-3 mt-0.5"></i>
                    <div>
                      <p class="text-sm font-medium text-gray-900">Payment Confirmation</p>
                      <p class="text-xs text-gray-500">Proof of renewal fee payment</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Renewal Timeline -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i class="ri-calendar-line mr-2 text-primary"></i>
                  Renewal Timeline
                </h3>
                <div class="space-y-4">
                  <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">Application Submission</p>
                      <p class="text-xs text-gray-500">Customer submits renewal application</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">Document Review</p>
                      <p class="text-xs text-gray-500">5-10 days for document verification</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                    <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">Technical Assessment</p>
                      <p class="text-xs text-gray-500">10-15 days for technical evaluation</p>
                    </div>
                  </div>
                  <div class="flex items-center p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">4</div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">License Issued</p>
                      <p class="text-xs text-gray-500">New license certificate generated</p>
                    </div>
                  </div>
                  <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-700">
                      <strong>Important:</strong> Submit renewal applications at least 60 days before expiration to ensure continuity of service.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Renewal Details Modal -->
    <div id="renewalDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
      <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-semibold text-gray-900" id="renewalModalTitle">License Renewal Details</h3>
            <button onclick="closeRenewalDetailsModal()" class="text-gray-400 hover:text-gray-600">
              <i class="ri-close-line text-xl"></i>
            </button>
          </div>

          <!-- Modal Body - Scrollable -->
          <div class="py-4 max-h-96 overflow-y-auto">
            <!-- License Basic Info -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg border-2">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-key-line mr-2"></i>License Information
              </h4>
              <div id="renewalLicenseInfo" class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <!-- Content will be populated here -->
              </div>
            </div>

            <!-- Renewal Status -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg border-2">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-refresh-line mr-2"></i>Renewal Status
              </h4>
              <div id="renewalStatusInfo" class="space-y-3">
                <!-- Content will be populated here -->
              </div>
            </div>

            <!-- Required Documents -->
            <div class="mb-6 p-4 bg-yellow-50 rounded-lg border-2">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-file-list-line mr-2"></i>Required Documents
              </h4>
              <div id="renewalDocuments" class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <!-- Documents will be populated here -->
              </div>
            </div>

            <!-- Payment Information -->
            <div class="mb-4 p-4 bg-green-50 rounded-lg border-2">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-money-dollar-circle-line mr-2"></i>Payment Information
              </h4>
              <div id="renewalPaymentInfo" class="text-sm text-gray-700">
                <!-- Payment info will be populated here -->
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end pt-4 border-t space-x-3">
            <button onclick="closeRenewalDetailsModal()" class="secondary-main-button">
              Close
            </button>
            <button onclick="downloadRenewalReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              <i class="ri-download-line mr-2"></i>Download Report
            </button>
            <button onclick="processRenewalFromModal()" class="main-button">
              <i class="ri-refresh-line mr-2"></i>Process Renewal
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Toggle license dropdown
      function toggleLicenseDropdown() {
        const dropdown = document.getElementById('licenseDropdownMenu');
        const icon = document.getElementById('licenseDropdownIcon');

        if (dropdown.classList.contains('hidden')) {
          dropdown.classList.remove('hidden');
          icon.classList.add('rotate-180');
        } else {
          dropdown.classList.add('hidden');
          icon.classList.remove('rotate-180');
        }
      }

      // Enhanced filtering functionality
      function filterLicenses() {
        const categoryFilter = document.querySelector('select[class*="enhanced-select"]:first-of-type').value;
        const statusFilter = document.querySelector('select[class*="enhanced-select"]:last-of-type').value;

        const licenseCards = document.querySelectorAll('.renewal-card, .renewal-card-expired, .renewal-card-warning');

        licenseCards.forEach(card => {
          let showCard = true;

          // Category filtering
          if (categoryFilter !== 'all') {
            const categoryText = card.querySelector('.text-sm.text-gray-500').textContent;
            const cardCategory = getCategoryFromLicense(categoryText);
            if (cardCategory !== categoryFilter) {
              showCard = false;
            }
          }

          // Status filtering
          if (statusFilter !== 'all') {
            const statusBadge = card.querySelector('.status-badge');
            if (statusBadge) {
              const cardStatus = getStatusFromBadge(statusBadge.textContent.trim());
              if (cardStatus !== statusFilter) {
                showCard = false;
              }
            }
          }

          card.style.display = showCard ? 'block' : 'none';
        });

        updateStatistics();
      }

      // Helper function to get category from license text
      function getCategoryFromLicense(licenseText) {
        if (licenseText.includes('ASL')) return 'application';
        if (licenseText.includes('CSL')) return 'content';
        if (licenseText.includes('NSL')) return 'network-service';
        if (licenseText.includes('NFL')) return 'network-facility';
        return 'all';
      }

      // Helper function to get status from badge text
      function getStatusFromBadge(badgeText) {
        const statusMap = {
          'Expiring Soon': 'expiring-soon',
          'Expiring Warning': 'expiring-warning',
          'Renewal Submitted': 'renewal-submitted',
          'Renewal Approved': 'renewal-approved',
          'Active': 'active',
          'Expired': 'expired'
        };
        return statusMap[badgeText] || 'all';
      }

      // Update statistics based on visible cards
      function updateStatistics() {
        const visibleCards = document.querySelectorAll('.renewal-card:not([style*="display: none"]), .renewal-card-expired:not([style*="display: none"]), .renewal-card-warning:not([style*="display: none"])');

        let expiringSoon = 0;
        let renewalPending = 0;
        let renewedToday = 0;
        let totalActive = 0;

        visibleCards.forEach(card => {
          const statusBadge = card.querySelector('.status-badge');
          if (statusBadge) {
            const status = statusBadge.textContent.trim();
            switch(status) {
              case 'Expiring Soon':
              case 'Expired':
                expiringSoon++;
                break;
              case 'Renewal Submitted':
                renewalPending++;
                break;
              case 'Renewal Approved':
                renewedToday++;
                break;
              case 'Active':
                totalActive++;
                break;
            }
          }
        });

        // Update the statistics cards
        const statCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-4 .text-2xl.font-bold');
        if (statCards.length >= 4) {
          statCards[0].textContent = expiringSoon;
          statCards[1].textContent = renewalPending;
          statCards[2].textContent = renewedToday;
          statCards[3].textContent = totalActive;
        }
      }

      // View renewal details
      function viewRenewalDetails(licenseNumber) {
        const modal = document.getElementById('renewalDetailsModal');
        const title = document.getElementById('renewalModalTitle');
        const licenseInfo = document.getElementById('renewalLicenseInfo');
        const statusInfo = document.getElementById('renewalStatusInfo');
        const documents = document.getElementById('renewalDocuments');
        const paymentInfo = document.getElementById('renewalPaymentInfo');

        // Enhanced sample data with more license types
        const renewalData = {
          'ASL-2022-001': {
            title: 'TechConnect ISP License Renewal',
            licenseInfo: {
              'License Number': 'ASL-2022-001',
              'License Type': 'Application Service License',
              'Licensee': 'TechConnect ISP Ltd.',
              'Issue Date': 'Jan 15, 2022',
              'Expiry Date': 'Jan 20, 2025',
              'Days Until Expiry': '5 days',
              'Renewal Fee': 'MWK 30,000,000'
            },
            status: {
              'Current Status': 'Expiring Soon',
              'Renewal Application': 'Not Submitted',
              'Last Contact': 'Jan 10, 2025',
              'Reminder Sent': 'Yes (3 times)'
            },
            documents: [
              'Certificate of Incorporation',
              'Audited Financial Statements',
              'Technical Capacity Report',
              'Compliance Certificate',
              'Business Plan Update',
              'Payment Receipt'
            ],
            payment: {
              'Renewal Fee': 'MWK 30,000,000',
              'Payment Status': 'Pending',
              'Payment Method': 'Bank Transfer',
              'Due Date': 'Jan 20, 2025'
            }
          },
          'ASL-2023-002': {
            title: 'John Banda Individual License Renewal',
            licenseInfo: {
              'License Number': 'ASL-2023-002',
              'License Type': 'Application Service License',
              'Licensee': 'John Banda',
              'Issue Date': 'Mar 10, 2023',
              'Expiry Date': 'Mar 10, 2025',
              'Days Until Expiry': '45 days',
              'Renewal Fee': 'MWK 30,000,000'
            },
            status: {
              'Current Status': 'Renewal Submitted',
              'Renewal Application': 'Submitted on Jan 5, 2025',
              'Review Status': 'Under Review',
              'Assigned Evaluator': 'Sarah Mwale'
            },
            documents: [
              'Renewal Application Form ✓',
              'Updated Financial Statements ✓',
              'Technical Assessment ✓',
              'Compliance Certificate ✓',
              'Payment Receipt ✓'
            ],
            payment: {
              'Renewal Fee': 'MWK 30,000,000',
              'Payment Status': 'Paid',
              'Payment Method': 'Bank Transfer',
              'Payment Date': 'Jan 5, 2025'
            }
          },
          'CSL-2022-001': {
            title: 'Malawi University Radio License Renewal',
            licenseInfo: {
              'License Number': 'CSL-2022-001',
              'License Type': 'Content Service License',
              'Licensee': 'Malawi University',
              'Issue Date': 'Jan 23, 2022',
              'Expiry Date': 'Jan 17, 2025',
              'Days Until Expiry': '2 days',
              'Renewal Fee': 'MWK 6,000,000'
            },
            status: {
              'Current Status': 'Expired',
              'Renewal Application': 'Not Submitted',
              'Last Contact': 'Jan 15, 2025',
              'Reminder Sent': 'Yes (5 times)'
            },
            documents: [
              'Certificate of Incorporation',
              'Programming Schedule',
              'Technical Equipment List',
              'Compliance Certificate',
              'Financial Statements'
            ],
            payment: {
              'Renewal Fee': 'MWK 6,000,000',
              'Payment Status': 'Overdue',
              'Payment Method': 'Bank Transfer',
              'Due Date': 'Jan 17, 2025'
            }
          },
          'CSL-2022-002': {
            title: 'Blantyre Broadcasting TV License Renewal',
            licenseInfo: {
              'License Number': 'CSL-2022-002',
              'License Type': 'Content Service License',
              'Licensee': 'Blantyre Broadcasting',
              'Issue Date': 'May 30, 2022',
              'Expiry Date': 'Mar 15, 2025',
              'Days Until Expiry': '60 days',
              'Renewal Fee': 'MWK 6,000,000'
            },
            status: {
              'Current Status': 'Active',
              'Renewal Application': 'Not Required Yet',
              'Last Contact': 'Dec 20, 2024',
              'Reminder Sent': 'No'
            },
            documents: [
              'Certificate of Incorporation',
              'Broadcasting License',
              'Technical Equipment List',
              'Programming Schedule',
              'Financial Statements'
            ],
            payment: {
              'Renewal Fee': 'MWK 6,000,000',
              'Payment Status': 'Not Due',
              'Payment Method': 'Bank Transfer',
              'Due Date': 'Mar 15, 2025'
            }
          },
          'NSL-2022-001': {
            title: 'MobileNet Solutions License Renewal',
            licenseInfo: {
              'License Number': 'NSL-2022-001',
              'License Type': 'Network Service License',
              'Licensee': 'MobileNet Solutions',
              'Issue Date': 'Apr 8, 2022',
              'Expiry Date': 'Apr 8, 2027',
              'Days Until Expiry': '800+ days',
              'Renewal Fee': 'MWK 1,800,000'
            },
            status: {
              'Current Status': 'Renewal Approved',
              'Renewal Application': 'Approved on Dec 15, 2024',
              'Review Status': 'Complete',
              'Assigned Evaluator': 'Michael Phiri'
            },
            documents: [
              'Renewal Application Form ✓',
              'Network Infrastructure Report ✓',
              'Financial Statements ✓',
              'Technical Assessment ✓',
              'Payment Receipt ✓'
            ],
            payment: {
              'Renewal Fee': 'MWK 1,800,000',
              'Payment Status': 'Paid',
              'Payment Method': 'Bank Transfer',
              'Payment Date': 'Dec 10, 2024'
            }
          }
        };

        const data = renewalData[licenseNumber];
        if (!data) return;

        title.textContent = data.title;

        // Populate license info
        licenseInfo.innerHTML = '';
        Object.entries(data.licenseInfo).forEach(([key, value]) => {
          licenseInfo.innerHTML += `
            <div class="flex justify-between">
              <span class="text-gray-600">${key}:</span>
              <span class="font-medium">${value}</span>
            </div>
          `;
        });

        // Populate status info
        statusInfo.innerHTML = '';
        Object.entries(data.status).forEach(([key, value]) => {
          statusInfo.innerHTML += `
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">${key}:</span>
              <span class="font-medium">${value}</span>
            </div>
          `;
        });

        // Populate documents
        documents.innerHTML = '';
        data.documents.forEach(doc => {
          documents.innerHTML += `
            <div class="flex items-center text-sm">
              <i class="ri-file-text-line mr-2 text-gray-500"></i>
              <span>${doc}</span>
            </div>
          `;
        });

        // Populate payment info
        paymentInfo.innerHTML = '';
        Object.entries(data.payment).forEach(([key, value]) => {
          paymentInfo.innerHTML += `
            <div class="flex justify-between">
              <span class="text-gray-600">${key}:</span>
              <span class="font-medium">${value}</span>
            </div>
          `;
        });

        modal.classList.remove('hidden');
      }

      // Close renewal details modal
      function closeRenewalDetailsModal() {
        document.getElementById('renewalDetailsModal').classList.add('hidden');
      }

      // Enhanced process renewal with loading state
      function processRenewal(licenseNumber) {
        if (confirm(`Process renewal for license ${licenseNumber}?\n\nThis will initiate the renewal process and send notifications to the licensee.`)) {
          const button = event.target.closest('button');
          const originalText = button.innerHTML;

          // Show loading state
          button.innerHTML = '<i class="ri-loader-4-line mr-2 animate-spin"></i>Processing...';
          button.disabled = true;

          // Simulate API call
          setTimeout(() => {
            showNotification(`Renewal process initiated for ${licenseNumber}`, 'success');
            button.innerHTML = originalText;
            button.disabled = false;

            // Update card status
            updateCardStatus(licenseNumber, 'renewal-submitted');
          }, 2000);
        }
      }

      // Enhanced approve renewal with loading state
      function approveRenewal(licenseNumber) {
        if (confirm(`Approve renewal for license ${licenseNumber}?\n\nThis will approve the renewal application and generate a new license certificate.`)) {
          const button = event.target.closest('button');
          const originalText = button.innerHTML;

          // Show loading state
          button.innerHTML = '<i class="ri-loader-4-line mr-2 animate-spin"></i>Approving...';
          button.disabled = true;

          // Simulate API call
          setTimeout(() => {
            showNotification(`Renewal approved for ${licenseNumber}`, 'success');
            button.innerHTML = originalText;
            button.disabled = false;

            // Update card status
            updateCardStatus(licenseNumber, 'renewal-approved');
          }, 2000);
        }
      }

      // Enhanced urgent renewal with priority handling
      function urgentRenewal(licenseNumber) {
        if (confirm(`Mark ${licenseNumber} for urgent renewal processing?\n\nThis will prioritize this license for immediate review and processing.`)) {
          const button = event.target.closest('button');
          const originalText = button.innerHTML;

          // Show loading state
          button.innerHTML = '<i class="ri-loader-4-line mr-2 animate-spin"></i>Marking Urgent...';
          button.disabled = true;

          // Simulate API call
          setTimeout(() => {
            showNotification(`License ${licenseNumber} marked for urgent renewal`, 'warning');
            button.innerHTML = originalText;
            button.disabled = false;

            // Add urgent indicator to card
            addUrgentIndicator(licenseNumber);
          }, 1500);
        }
      }

      // Enhanced initiate renewal with notification tracking
      function initiateRenewal(licenseNumber) {
        if (confirm(`Send renewal notification for license ${licenseNumber}?\n\nThis will send an email and SMS notification to the licensee about upcoming renewal.`)) {
          const button = event.target.closest('button');
          const originalText = button.innerHTML;

          // Show loading state
          button.innerHTML = '<i class="ri-loader-4-line mr-2 animate-spin"></i>Sending...';
          button.disabled = true;

          // Simulate API call
          setTimeout(() => {
            showNotification(`Renewal notification sent for ${licenseNumber}`, 'info');
            button.innerHTML = originalText;
            button.disabled = false;

            // Update last contact date
            updateLastContact(licenseNumber);
          }, 1500);
        }
      }

      // Enhanced generate new license with download
      function generateNewLicense(licenseNumber) {
        if (confirm(`Generate new license certificate for ${licenseNumber}?\n\nThis will create a new license certificate and make it available for download.`)) {
          const button = event.target.closest('button');
          const originalText = button.innerHTML;

          // Show loading state
          button.innerHTML = '<i class="ri-loader-4-line mr-2 animate-spin"></i>Generating...';
          button.disabled = true;

          // Simulate API call
          setTimeout(() => {
            showNotification(`New license certificate generated for ${licenseNumber}`, 'success');
            button.innerHTML = originalText;
            button.disabled = false;

            // Trigger download
            downloadLicenseCertificate(licenseNumber);
          }, 3000);
        }
      }

      // Enhanced download renewal report with format options
      function downloadRenewalReport() {
        const format = prompt('Select report format:\n1. PDF\n2. Excel\n3. CSV\n\nEnter 1, 2, or 3:', '1');

        if (format && ['1', '2', '3'].includes(format)) {
          const formats = { '1': 'PDF', '2': 'Excel', '3': 'CSV' };
          const selectedFormat = formats[format];

          showNotification(`Generating ${selectedFormat} renewal report...`, 'info');

          // Simulate download
          setTimeout(() => {
            showNotification(`${selectedFormat} renewal report downloaded successfully`, 'success');
            // In real implementation, trigger actual download
            console.log(`Downloading renewal report in ${selectedFormat} format`);
          }, 2000);
        }
      }

      // Enhanced process renewal from modal
      function processRenewalFromModal() {
        const licenseNumber = document.getElementById('renewalModalTitle').textContent.match(/\w+-\d+-\d+/)?.[0];

        if (licenseNumber && confirm(`Process renewal for ${licenseNumber}?\n\nThis will initiate the renewal process.`)) {
          showNotification('Processing renewal from modal...', 'info');

          setTimeout(() => {
            showNotification(`Renewal processed for ${licenseNumber}`, 'success');
            closeRenewalDetailsModal();
            updateCardStatus(licenseNumber, 'renewal-submitted');
          }, 2000);
        }
      }

      // Helper function to show notifications
      function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification-toast');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification-toast fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // Set notification style based on type
        const styles = {
          success: 'bg-green-500 text-white',
          error: 'bg-red-500 text-white',
          warning: 'bg-yellow-500 text-white',
          info: 'bg-blue-500 text-white'
        };

        const icons = {
          success: 'ri-check-line',
          error: 'ri-error-warning-line',
          warning: 'ri-alarm-warning-line',
          info: 'ri-information-line'
        };

        notification.className += ` ${styles[type] || styles.info}`;
        notification.innerHTML = `
          <div class="flex items-center">
            <i class="${icons[type] || icons.info} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
              <i class="ri-close-line"></i>
            </button>
          </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
          notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
          if (notification.parentElement) {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
          }
        }, 5000);
      }

      // Helper function to update card status
      function updateCardStatus(licenseNumber, newStatus) {
        const cards = document.querySelectorAll('.renewal-card, .renewal-card-expired, .renewal-card-warning');

        cards.forEach(card => {
          const licenseText = card.querySelector('.text-sm.text-gray-500').textContent;
          if (licenseText.includes(licenseNumber)) {
            const statusBadge = card.querySelector('.status-badge');
            if (statusBadge) {
              // Update status badge
              const statusClasses = {
                'renewal-submitted': 'status-renewal-submitted',
                'renewal-approved': 'status-renewal-approved',
                'expiring-soon': 'status-expiring-soon',
                'active': 'status-active',
                'expired': 'status-expired'
              };

              const statusTexts = {
                'renewal-submitted': 'Renewal Submitted',
                'renewal-approved': 'Renewal Approved',
                'expiring-soon': 'Expiring Soon',
                'active': 'Active',
                'expired': 'Expired'
              };

              // Remove old status classes
              statusBadge.className = 'status-badge ' + statusClasses[newStatus];
              statusBadge.textContent = statusTexts[newStatus];
            }
          }
        });

        updateStatistics();
      }

      // Helper function to add urgent indicator
      function addUrgentIndicator(licenseNumber) {
        const cards = document.querySelectorAll('.renewal-card, .renewal-card-expired, .renewal-card-warning');

        cards.forEach(card => {
          const licenseText = card.querySelector('.text-sm.text-gray-500').textContent;
          if (licenseText.includes(licenseNumber)) {
            // Add urgent indicator if not already present
            if (!card.querySelector('.urgent-indicator')) {
              const urgentBadge = document.createElement('div');
              urgentBadge.className = 'urgent-indicator absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse';
              urgentBadge.innerHTML = '<i class="ri-alarm-warning-line mr-1"></i>URGENT';
              card.style.position = 'relative';
              card.appendChild(urgentBadge);
            }
          }
        });
      }

      // Helper function to update last contact
      function updateLastContact(licenseNumber) {
        const today = new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });

        // In a real implementation, this would update the backend
        console.log(`Updated last contact for ${licenseNumber} to ${today}`);
      }

      // Helper function to download license certificate
      function downloadLicenseCertificate(licenseNumber) {
        // Create a temporary download link
        const link = document.createElement('a');
        link.href = '#'; // In real implementation, this would be the actual file URL
        link.download = `License_Certificate_${licenseNumber}.pdf`;
        link.textContent = 'Download Certificate';

        // Simulate download
        console.log(`Downloading certificate for ${licenseNumber}`);
        showNotification(`Certificate download started for ${licenseNumber}`, 'info');
      }

      // Search functionality
      function searchLicenses(searchTerm) {
        const cards = document.querySelectorAll('.renewal-card, .renewal-card-expired, .renewal-card-warning');

        cards.forEach(card => {
          const licenseName = card.querySelector('.text-lg.font-semibold').textContent.toLowerCase();
          const licenseNumber = card.querySelector('.text-sm.text-gray-500').textContent.toLowerCase();

          const matches = licenseName.includes(searchTerm.toLowerCase()) ||
                         licenseNumber.includes(searchTerm.toLowerCase());

          card.style.display = matches ? 'block' : 'none';
        });

        updateStatistics();
      }

      // Initialize page
      document.addEventListener('DOMContentLoaded', function() {
        // Open the license dropdown by default since we're on the renewal page
        const licenseDropdown = document.getElementById('licenseDropdownMenu');
        const licenseIcon = document.getElementById('licenseDropdownIcon');
        if (licenseDropdown && licenseIcon) {
          licenseDropdown.classList.remove('hidden');
          licenseIcon.classList.add('rotate-180');
        }

        // Add event listeners for filter dropdowns
        const filterDropdowns = document.querySelectorAll('select.enhanced-select');
        filterDropdowns.forEach(dropdown => {
          dropdown.addEventListener('change', filterLicenses);
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
          // Ctrl/Cmd + F for search
          if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchTerm = prompt('Search licenses by name or number:');
            if (searchTerm) {
              searchLicenses(searchTerm);
            }
          }

          // Escape to close modal
          if (e.key === 'Escape') {
            const modal = document.getElementById('renewalDetailsModal');
            if (modal && !modal.classList.contains('hidden')) {
              closeRenewalDetailsModal();
            }
          }
        });

        // Initialize statistics
        updateStatistics();

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          });
        });
      });
    </script>
  </body>
</html>