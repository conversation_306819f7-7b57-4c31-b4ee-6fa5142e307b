"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/data-protection/page",{

/***/ "(app-pages-browser)/./src/app/customer/data-protection/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/customer/data-protection/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/customer/CustomerLayout */ \"(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\");\n/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loader */ \"(app-pages-browser)/./src/components/Loader.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/customer/DataBreachModal */ \"(app-pages-browser)/./src/components/customer/DataBreachModal.tsx\");\n/* harmony import */ var _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/customer/ComplaintStatusBar */ \"(app-pages-browser)/./src/components/customer/ComplaintStatusBar.tsx\");\n/* harmony import */ var _services_data_breach__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/data-breach */ \"(app-pages-browser)/./src/services/data-breach/index.ts\");\n/* harmony import */ var _components_data_breach_DataBreachViewModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-breach/DataBreachViewModal */ \"(app-pages-browser)/./src/components/data-breach/DataBreachViewModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DataProtectionPage = ()=>{\n    _s();\n    const { isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [complaints, setComplaints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [showDataBreachModal, setShowDataBreachModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedReportId, setSelectedReportId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Redirect to customer login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            if (!authLoading && !isAuthenticated) {\n                router.push('/customer/auth/login');\n            }\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Fetch data function\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataProtectionPage.useCallback[fetchData]\": async ()=>{\n            if (!isAuthenticated) {\n                console.log('❌ User not authenticated, skipping data fetch');\n                return;\n            }\n            console.log('✅ User authenticated, fetching data...');\n            try {\n                setIsLoading(true);\n                setError('');\n                // Fetch only data breach reports (this is the Data Breach page)\n                const dataBreachResponse = await _services_data_breach__WEBPACK_IMPORTED_MODULE_8__.dataBreachService.getReports({\n                    limit: 100\n                });\n                console.log('🔍 Data Breach Response:', dataBreachResponse);\n                console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);\n                console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);\n                // Ensure data is an array (services return data directly)\n                const dataBreachData = Array.isArray(dataBreachResponse.data) ? dataBreachResponse.data : [];\n                console.log('🔍 Data Breach Data Array:', dataBreachData);\n                // Transform only data breach reports\n                const combinedComplaints = dataBreachData.map({\n                    \"DataProtectionPage.useCallback[fetchData].combinedComplaints\": (report)=>{\n                        var _report_assignee, _report_assignee1;\n                        return {\n                            id: report.report_id,\n                            title: report.title,\n                            description: report.description,\n                            category: report.category,\n                            type: 'data_breach',\n                            priority: report.priority,\n                            status: report.status,\n                            submittedAt: report.created_at,\n                            updatedAt: report.updated_at,\n                            assignedTo: ((_report_assignee = report.assignee) === null || _report_assignee === void 0 ? void 0 : _report_assignee.first_name) && ((_report_assignee1 = report.assignee) === null || _report_assignee1 === void 0 ? void 0 : _report_assignee1.last_name) ? \"\".concat(report.assignee.first_name, \" \").concat(report.assignee.last_name) : undefined,\n                            resolution: report.resolution,\n                            number: report.report_number\n                        };\n                    }\n                }[\"DataProtectionPage.useCallback[fetchData].combinedComplaints\"]);\n                // Sort by creation date (newest first)\n                combinedComplaints.sort({\n                    \"DataProtectionPage.useCallback[fetchData]\": (a, b)=>new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()\n                }[\"DataProtectionPage.useCallback[fetchData]\"]);\n                setComplaints(combinedComplaints);\n            } catch (err) {\n                var _axiosError_response, _axiosError_response1;\n                console.error('Error fetching complaints:', err);\n                const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n                const isAxiosError = err && typeof err === 'object' && 'response' in err;\n                const axiosError = isAxiosError ? err : null;\n                const status = axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response = axiosError.response) === null || _axiosError_response === void 0 ? void 0 : _axiosError_response.status;\n                console.error('Error details:', {\n                    message: errorMessage,\n                    response: axiosError === null || axiosError === void 0 ? void 0 : (_axiosError_response1 = axiosError.response) === null || _axiosError_response1 === void 0 ? void 0 : _axiosError_response1.data,\n                    status: status\n                });\n                if (status === 401) {\n                    setError('Authentication required. Please log in again.');\n                } else if (status === 404) {\n                    setError('API endpoints not found. Please check if the backend is running.');\n                } else {\n                    setError(\"Failed to load complaints: \".concat(errorMessage));\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"DataProtectionPage.useCallback[fetchData]\"], [\n        isAuthenticated\n    ]);\n    // Fetch data on mount and when authentication changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataProtectionPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"DataProtectionPage.useEffect\"], [\n        isAuthenticated,\n        fetchData\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'submitted':\n                return 'bg-blue-100 text-blue-800';\n            case 'under_review':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'investigating':\n                return 'bg-orange-100 text-orange-800';\n            case 'resolved':\n                return 'bg-green-100 text-green-800';\n            case 'closed':\n                return 'bg-gray-100 text-gray-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'low':\n                return 'bg-gray-100 text-gray-800';\n            case 'medium':\n                return 'bg-blue-100 text-blue-800';\n            case 'high':\n                return 'bg-orange-100 text-orange-800';\n            case 'urgent':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const handleViewDetails = (reportId)=>{\n        setSelectedReportId(reportId);\n        setShowViewModal(true);\n    };\n    // All complaints are now data breach reports only\n    const dataBreachComplaints = complaints; // All complaints are data breach reports\n    // Calculate data breach statistics\n    const totalComplaints = complaints.length;\n    const pendingComplaints = complaints.filter((c)=>c.status === 'submitted' || c.status === 'under_review').length;\n    const investigatingComplaints = complaints.filter((c)=>c.status === 'investigating').length;\n    const resolvedComplaints = complaints.filter((c)=>c.status === 'resolved' || c.status === 'closed').length;\n    if (authLoading || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    message: \"Loading Data Breach...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-2 text-sm underline hover:no-underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                                    children: \"Data Breach\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Report and track data breach incidents and privacy violations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setShowDataBreachModal(true),\n                            className: \"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-shield-keyhole-line mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Report Data Breach\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200 dark:border-gray-700 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            {\n                                key: 'overview',\n                                label: 'Overview',\n                                icon: 'ri-dashboard-line'\n                            },\n                            {\n                                key: 'track',\n                                label: 'Track Complaints',\n                                icon: 'ri-search-eye-line',\n                                count: complaints.length\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab(tab.key),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center \".concat(activeTab === tab.key ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"\".concat(tab.icon, \" mr-2\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tab.label,\n                                    tab.count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\",\n                                        children: tab.count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, tab.key, true, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined),\n                activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-file-list-3-line text-2xl text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Total Complaints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: totalComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-time-line text-2xl text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: pendingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-search-eye-line text-2xl text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Investigating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: investigatingComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-check-double-line text-2xl text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                        children: \"Resolved\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                                        children: resolvedComplaints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-shield-keyhole-line text-3xl text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                    children: \"Data Breach Reporting\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"Report unauthorized access, misuse, or breach of your personal data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"What to Report:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Unauthorized data access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Data misuse or sharing\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Privacy violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Identity theft concerns\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined),\n                activeTab === 'track' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: complaints.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-file-search-line text-4xl text-gray-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                children: \"No complaints found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 dark:text-gray-400 mb-4\",\n                                children: \"You haven't submitted any complaints yet.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\",\n                                children: \"Submit Your First Complaint\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: complaints.map((complaint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3\",\n                                                            children: complaint.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(complaint.status)),\n                                                            children: complaint.status.replace('_', ' ').toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(complaint.priority)),\n                                                            children: complaint.priority.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 px-2 py-1 rounded-full text-xs font-medium \".concat(complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'),\n                                                            children: complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        complaint.id,\n                                                        \" | Category: \",\n                                                        complaint.category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300 mb-3\",\n                                                    children: complaint.description.length > 150 ? \"\".concat(complaint.description.substring(0, 150), \"...\") : complaint.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        currentStage: (0,_components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.getStageIndexFromStatus)(complaint.status),\n                                                        stages: complaint.type === 'consumer_affairs' ? _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.COMPLAINT_STAGES.CONSUMER_AFFAIRS : _components_customer_ComplaintStatusBar__WEBPACK_IMPORTED_MODULE_7__.COMPLAINT_STAGES.DATA_BREACH,\n                                                        status: complaint.status,\n                                                        size: \"sm\",\n                                                        variant: \"horizontal\",\n                                                        showPercentage: false,\n                                                        showStageNames: true,\n                                                        className: \"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Submitted: \",\n                                                                formatDate(complaint.submittedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mx-2\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated: \",\n                                                                formatDate(complaint.updatedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        complaint.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-2\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Assigned to: \",\n                                                                        complaint.assignedTo\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>handleViewDetails(complaint.id),\n                                            className: \"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200\",\n                                            children: \"View Details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, complaint.id, false, {\n                                fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 19\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-900 dark:text-red-100 mb-4\",\n                            children: \"Need Help with Data Breach Reporting?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Contact Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-phone-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"+265 1 770 100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-mail-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-time-line mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mon-Fri, 8:00 AM - 5:00 PM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-2\",\n                                            children: \"Data Breach Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm text-red-700 dark:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Unauthorized Access\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Misuse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Privacy Violations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Identity Theft\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Data Sharing Issues\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, undefined),\n                showDataBreachModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_DataBreachModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onClose: ()=>setShowDataBreachModal(false),\n                    onSubmit: (data)=>{\n                        console.log('Data breach report submitted:', data);\n                        setShowDataBreachModal(false);\n                        // Refresh complaints list without full page reload\n                        fetchData();\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 11\n                }, undefined),\n                showViewModal && selectedReportId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_breach_DataBreachViewModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    isOpen: showViewModal,\n                    onClose: ()=>{\n                        setShowViewModal(false);\n                        setSelectedReportId(null);\n                    },\n                    reportId: selectedReportId\n                }, void 0, false, {\n                    fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Memory Business Solutioins\\\\Projects\\\\MACRA\\\\project\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\data-protection\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataProtectionPage, \"s1xYu0qScp2g66mVdNqVu777hiw=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DataProtectionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataProtectionPage);\nvar _c;\n$RefreshReg$(_c, \"DataProtectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customer/data-protection/page.tsx\n"));

/***/ })

});