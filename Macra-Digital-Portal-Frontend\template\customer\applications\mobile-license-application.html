<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Mobile Network License Application - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link rel="stylesheet" href="../assets/main.css">
  <style type="text/tailwindcss">
    @layer components {
      .custom-form-label {
        @apply block text-sm font-medium text-gray-700 pb-2;
      }
      .enhanced-input {
        @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
      }

      .main-button {
        @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
      }

      .secondary-main-button {
        @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
      }

      .custom-input {
        @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
      }

      .form-section {
        @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
      }

      .inner-form-section {
        @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
      }

      .tab-heading {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
      }

      .form-group {
        @apply mb-6;
      }

      .step-indicator {
        @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium;
      }

      .step-indicator.active {
        @apply bg-primary text-white;
      }

      .step-indicator.completed {
        @apply bg-green-500 text-white;
      }

      .step-indicator.inactive {
        @apply bg-gray-200 text-gray-500;
      }

      .step-content {
        @apply hidden;
      }

      .step-content.active {
        @apply block;
      }

      .progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2;
      }

      .progress-fill {
        @apply bg-primary h-2 rounded-full transition-all duration-300;
      }

      .file-upload-area {
        @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary cursor-pointer transition-colors;
      }

      .tracking-status {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .status-draft {
        @apply bg-gray-100 text-gray-800;
      }

      .status-submitted {
        @apply bg-blue-100 text-blue-800;
      }

      .status-review {
        @apply bg-yellow-100 text-yellow-800;
      }

      .status-approved {
        @apply bg-green-100 text-green-800;
      }

      .status-rejected {
        @apply bg-red-100 text-red-800;
      }

      .evaluation-table {
        @apply w-full border-collapse border border-gray-300;
      }

      .evaluation-table th,
      .evaluation-table td {
        @apply border border-gray-300 px-4 py-2 text-left;
      }

      .evaluation-table th {
        @apply bg-gray-100 font-medium;
      }
    }

    @layer utilities {
      :root {
        --color-primary: #e02b20;
        --color-secondary: #20d5e0;
        --color-primary-subtle: #e4463c;
        --color-secondary-subtle: #abeff3;
      }
    }

  </style>

  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }

    .error-message {
      color: #dc2626;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .input-error {
      border-color: #dc2626 !important;
    }
  </style>
</head>
<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src=".docs/images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="index.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="new-application.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-add-line"></i>
            </div>
            New Application
          </a>
          <a href="my-licenses.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            My Licenses
          </a>
          <a href="invoices.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Invoices
          </a>
          <a href="payments.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-bank-card-line"></i>
            </div>
            Payments
          </a>
          <a href="documents.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-folder-line"></i>
            </div>
            Documents
          </a>
          <a href="contact-support.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-customer-service-line"></i>
            </div>
            Support
          </a>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">John Doe</p>
            <p class="text-xs text-gray-500">Customer</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                  placeholder="Search applications, licenses, or documents..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="help-center.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Help Center</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header-->
          <div class="tab-heading">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">Mobile Network License Application</h1>
              <p class="mt-1 text-sm text-gray-600">Complete your mobile network operator license application</p>
            </div>
            <div class="relative">
              <a href="new-application.html" class="secondary-main-button" role="button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-arrow-left-line"></i>
                </div>
                Back to Applications
              </a>
            </div>
          </div>

          <!-- Application Status & Tracking -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Application Status</h3>
                  <p class="text-sm text-gray-500">Application ID: <span id="applicationId" class="font-mono">MNO-2024-001</span></p>
                </div>
                <div>
                  <span id="applicationStatus" class="tracking-status status-draft">Draft</span>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                  <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
              </div>

              <!-- Step Indicators -->
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div id="step1Indicator" class="step-indicator active">1</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step2Indicator" class="step-indicator inactive">2</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step3Indicator" class="step-indicator inactive">3</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step4Indicator" class="step-indicator inactive">4</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step5Indicator" class="step-indicator inactive">5</div>
                </div>
              </div>

              <!-- Step Labels -->
              <div class="flex justify-between text-xs text-gray-500 mt-2">
                <span>Network Info</span>
                <span>Company Details</span>
                <span>Business Plan</span>
                <span>Technical Plan</span>
                <span>Evaluation & Undertaking</span>
              </div>
            </div>
          </div>

          <!-- Application Form -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <form id="mobileApplicationForm" class="space-y-8">

                <!-- Step 1: Mobile Network Information -->
                <div id="step1" class="step-content active">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Mobile Network Information</h3>

                    <div class="inner-form-section">
                      <!-- Network Category -->
                      <div>
                        <label for="networkCategory" class="custom-form-label">Network Category *</label>
                        <select id="networkCategory" name="networkCategory" class="custom-input" required>
                          <option value="">Select Network Category</option>
                          <option value="national">National Mobile Network</option>
                          <option value="regional">Regional Mobile Network</option>
                          <option value="district">District Mobile Network</option>
                          <option value="community">Community Mobile Network</option>
                        </select>
                      </div>

                      <!-- Service Type -->
                      <div>
                        <label for="serviceType" class="custom-form-label">Primary Service Type *</label>
                        <select id="serviceType" name="serviceType" class="custom-input" required>
                          <option value="">Select Service Type</option>
                          <option value="2g">2G Voice Services</option>
                          <option value="3g">3G Voice & Data Services</option>
                          <option value="4g">4G LTE Services</option>
                          <option value="5g">5G Network Services</option>
                          <option value="mvno">MVNO Services</option>
                        </select>
                      </div>

                      <!-- Coverage Area -->
                      <div>
                        <label for="coverageArea" class="custom-form-label">Proposed Coverage Area *</label>
                        <select id="coverageArea" name="coverageArea" class="custom-input" required>
                          <option value="">Select Coverage Area</option>
                          <option value="national">National Coverage</option>
                          <option value="regional">Regional Coverage</option>
                          <option value="urban">Urban Areas Only</option>
                          <option value="rural">Rural Areas Only</option>
                          <option value="mixed">Mixed Urban/Rural</option>
                        </select>
                      </div>

                      <!-- Spectrum Requirements -->
                      <div>
                        <label for="spectrumBands" class="custom-form-label">Required Spectrum Bands *</label>
                        <select id="spectrumBands" name="spectrumBands" class="custom-input" required>
                          <option value="">Select Spectrum Bands</option>
                          <option value="700mhz">700 MHz Band</option>
                          <option value="850mhz">850 MHz Band</option>
                          <option value="900mhz">900 MHz Band</option>
                          <option value="1800mhz">1800 MHz Band</option>
                          <option value="2100mhz">2100 MHz Band</option>
                          <option value="2600mhz">2600 MHz Band</option>
                          <option value="multiple">Multiple Bands</option>
                        </select>
                      </div>

                      <!-- Expected Subscribers -->
                      <div>
                        <label for="expectedSubscribers" class="custom-form-label">Expected Number of Subscribers (Year 1) *</label>
                        <input type="number" name="expectedSubscribers" id="expectedSubscribers" class="custom-input" required min="1" placeholder="e.g., 100000">
                      </div>

                      <!-- Target Market -->
                      <div>
                        <label for="targetMarket" class="custom-form-label">Target Market Segments *</label>
                        <select id="targetMarket" name="targetMarket" class="custom-input" required>
                          <option value="">Select Primary Target Market</option>
                          <option value="consumer">Consumer Market</option>
                          <option value="enterprise">Enterprise Market</option>
                          <option value="government">Government Sector</option>
                          <option value="iot">IoT & M2M Services</option>
                          <option value="mixed">Mixed Market</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Company Details -->
                <div id="step2" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Company Details</h3>

                    <div class="inner-form-section">
                      <!-- Company Name -->
                      <div class="sm:col-span-2">
                        <label for="companyName" class="custom-form-label">Company Name *</label>
                        <input type="text" name="companyName" id="companyName" class="custom-input" required>
                      </div>

                      <!-- Company Profile -->
                      <div class="sm:col-span-2">
                        <label for="companyProfile" class="custom-form-label">Company Profile *</label>
                        <textarea id="companyProfile" name="companyProfile" rows="4" class="custom-input" required placeholder="Provide a detailed description of your company's background, mission, and mobile network service objectives"></textarea>
                      </div>

                      <!-- Company Website -->
                      <div class="sm:col-span-2">
                        <label for="companyWebsite" class="custom-form-label">Company website *</label>
                        <input type="url" name="companyWebsite" id="companyWebsite" class="custom-input" required placeholder="https://www.company.com">
                      </div>

                      <!-- Contact Email -->
                      <div>
                        <label for="contactEmail" class="custom-form-label">Contact email *</label>
                        <input type="email" name="contactEmail" id="contactEmail" class="custom-input" required>
                      </div>

                      <!-- Contact Phone -->
                      <div>
                        <label for="contactPhone" class="custom-form-label">Contact phone *</label>
                        <input type="tel" name="contactPhone" id="contactPhone" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
                        <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
                      </div>

                      <!-- Registration Number -->
                      <div>
                        <label for="registrationNumber" class="custom-form-label">Company Registration Number *</label>
                        <input type="text" name="registrationNumber" id="registrationNumber" class="custom-input" required>
                      </div>

                      <!-- TPIN -->
                      <div>
                        <label for="companyTPIN" class="custom-form-label">Tax Payer Identification Number - TPIN *</label>
                        <input type="text" name="companyTPIN" id="companyTPIN" class="custom-input" required pattern="[0-9]+" placeholder="Enter digits only">
                        <p class="text-xs text-gray-500 mt-1">Enter digits only</p>
                      </div>

                      <!-- Business Address -->
                      <div class="sm:col-span-2">
                        <label for="businessAddress" class="custom-form-label">Business Address *</label>
                        <textarea id="businessAddress" name="businessAddress" rows="3" class="custom-input" required placeholder="Provide complete business address"></textarea>
                      </div>

                      <!-- Contact Person -->
                      <div>
                        <label for="contactPerson" class="custom-form-label">Contact Person Name *</label>
                        <input type="text" name="contactPerson" id="contactPerson" class="custom-input" required>
                      </div>

                      <!-- Contact Person Designation -->
                      <div>
                        <label for="contactPersonDesignation" class="custom-form-label">Contact Person Designation *</label>
                        <input type="text" name="contactPersonDesignation" id="contactPersonDesignation" class="custom-input" required>
                      </div>

                      <!-- Date of Incorporation -->
                      <div>
                        <label for="dateIncorporation" class="custom-form-label">Date of incorporation *</label>
                        <input type="date" name="dateIncorporation" id="dateIncorporation" class="custom-input" required>
                      </div>

                      <!-- Place of Incorporation -->
                      <div>
                        <label for="placeIncorporation" class="custom-form-label">Place of incorporation *</label>
                        <input type="text" name="placeIncorporation" id="placeIncorporation" class="custom-input" required>
                      </div>
                    </div>

                    <!-- Legal Documents -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Legal Documents</h4>
                      <p class="text-sm text-gray-600 mb-4">Please provide the following documents (Copies should be notarised):</p>

                      <div class="space-y-6">
                        <!-- Certificate of Incorporation -->
                        <div>
                          <label class="custom-form-label">Certificate of Incorporation *</label>
                          <div class="file-upload-area" onclick="document.getElementById('certificateIncorporation').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Certificate of Incorporation</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="certificateIncorporation" name="certificateIncorporation" accept=".pdf" class="hidden" required>
                          <div id="certificateIncorporationList" class="mt-2"></div>
                        </div>

                        <!-- Memorandum of Association -->
                        <div>
                          <label class="custom-form-label">Memorandum of Association *</label>
                          <div class="file-upload-area" onclick="document.getElementById('memorandumAssociation').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Memorandum of Association</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="memorandumAssociation" name="memorandumAssociation" accept=".pdf" class="hidden" required>
                          <div id="memorandumAssociationList" class="mt-2"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 3: Business Plan -->
                <div id="step3" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Business Plan</h3>

                    <div class="space-y-8">
                      <!-- Mobile Services Description -->
                      <div>
                        <label for="mobileServices" class="custom-form-label">Range of mobile network services to be provided and the components of the services *</label>
                        <textarea id="mobileServices" name="mobileServices" rows="6" class="custom-input" required placeholder="Describe in detail the range of mobile services you plan to provide, including voice, data, SMS, value-added services, and roaming capabilities"></textarea>
                      </div>

                      <!-- Market Assessment -->
                      <div>
                        <label class="custom-form-label">Detailed Market Assessment for Mobile Network Services *</label>
                        <div class="file-upload-area" onclick="document.getElementById('marketAssessment').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Market Assessment</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="marketAssessment" name="marketAssessment" accept=".pdf" class="hidden" required>
                        <div id="marketAssessmentList" class="mt-2"></div>
                      </div>

                      <!-- Financial Capacity -->
                      <div>
                        <label class="custom-form-label">Attach proof of financial capacity for mobile network operations *</label>
                        <div class="file-upload-area" onclick="document.getElementById('financialCapacity').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Proof of Financial Capacity</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="financialCapacity" name="financialCapacity" accept=".pdf" class="hidden" required>
                        <div id="financialCapacityList" class="mt-2"></div>
                      </div>

                      <!-- Business Projections -->
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label for="plannedLaunchDate" class="custom-form-label">Planned Network Launch Date *</label>
                          <input type="date" id="plannedLaunchDate" name="plannedLaunchDate" class="custom-input" required>
                        </div>
                        <div>
                          <label for="totalInvestment" class="custom-form-label">Total Investment Amount (MWK) *</label>
                          <input type="number" id="totalInvestment" name="totalInvestment" class="custom-input" required min="0" placeholder="e.g., 500000000">
                        </div>
                        <div>
                          <label for="employeeCount" class="custom-form-label">Planned Number of Employees *</label>
                          <input type="number" id="employeeCount" name="employeeCount" class="custom-input" required min="1" placeholder="e.g., 150">
                        </div>
                        <div>
                          <label for="projectedRevenue" class="custom-form-label">Projected Annual Revenue (USD) *</label>
                          <input type="number" id="projectedRevenue" name="projectedRevenue" class="custom-input" required min="0" placeholder="e.g., 1000000000">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 4: Technical Plan -->
                <div id="step4" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Technical Plan</h3>

                    <div class="space-y-8">
                      <!-- Technical Rollout Plan -->
                      <div>
                        <label class="custom-form-label">Technical and network rollout plan for the next five (5) years *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalRollout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical and Network Rollout Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalRollout" name="technicalRollout" accept=".pdf" class="hidden" required>
                        <div id="technicalRolloutList" class="mt-2"></div>
                      </div>

                      <!-- Technical Personnel -->
                      <div>
                        <label class="custom-form-label">Proposed Technical Personnel and their resumes *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalPersonnel').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical Personnel Resumes</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalPersonnel" name="technicalPersonnel" accept=".pdf" class="hidden" required>
                        <div id="technicalPersonnelList" class="mt-2"></div>
                      </div>

                      <!-- Network Layout -->
                      <div>
                        <label class="custom-form-label">Proposed Network layout i.e. base stations, transmission sites, and coverage areas *</label>
                        <div class="file-upload-area" onclick="document.getElementById('networkLayout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Network Layout</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="networkLayout" name="networkLayout" accept=".pdf" class="hidden" required>
                        <div id="networkLayoutList" class="mt-2"></div>
                      </div>

                      <!-- Implementation Schedule -->
                      <div>
                        <label class="custom-form-label">Implementation schedule and network expansion plan *</label>
                        <div class="file-upload-area" onclick="document.getElementById('implementationSchedule').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Implementation Schedule and Expansion Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="implementationSchedule" name="implementationSchedule" accept=".pdf" class="hidden" required>
                        <div id="implementationScheduleList" class="mt-2"></div>
                      </div>

                      <!-- Network Architecture -->
                      <div>
                        <label class="custom-form-label">Detailed information on network diagram/architecture/topology and configuration, description of equipment, and spectrum utilization plans *</label>
                        <div class="file-upload-area" onclick="document.getElementById('networkArchitecture').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Network Architecture Documentation</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="networkArchitecture" name="networkArchitecture" accept=".pdf" class="hidden" required>
                        <div id="networkArchitectureList" class="mt-2"></div>
                      </div>

                      <!-- Resource Requirements -->
                      <div>
                        <label class="custom-form-label">Resource requirements (spectrum, numbering, interconnection) *</label>
                        <div class="file-upload-area" onclick="document.getElementById('resourceRequirements').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Resource Requirements</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="resourceRequirements" name="resourceRequirements" accept=".pdf" class="hidden" required>
                        <div id="resourceRequirementsList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 5: Evaluation Criteria and Undertaking -->
                <div id="step5" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Evaluation Criteria</h3>

                    <div class="mb-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Mobile Network License Application Evaluation Criteria</h4>

                      <div class="overflow-x-auto">
                        <table class="evaluation-table">
                          <thead>
                            <tr>
                              <th class="text-left">Evaluation Category</th>
                              <th class="text-center">Weight (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Business Plan</td>
                              <td class="text-center">40%</td>
                            </tr>
                            <tr>
                              <td>Technical and Operational Capacity</td>
                              <td class="text-center">50%</td>
                            </tr>
                            <tr>
                              <td>Organization Set Up</td>
                              <td class="text-center">10%</td>
                            </tr>
                            <tr class="bg-gray-50 font-medium">
                              <td>Total</td>
                              <td class="text-center">100%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div class="space-y-4 mb-8">
                      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Minimum Score:</strong> The minimum score for consideration for mobile network license award shall be 55%
                        </p>
                      </div>

                      <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Note:</strong> Under organisation set up criteria, the Authority will not grant a licence where the Applicant fails to meet the shareholding requirements in the Communications Act, irrespective of how the Applicant scores in the other assessment categories.
                        </p>
                      </div>
                    </div>

                    <div class="border border-gray-200 rounded-md p-4 mb-8">
                      <div class="flex items-start">
                        <input type="checkbox" id="agreeEvaluationCriteria" name="agreeEvaluationCriteria" class="enhanced-checkbox mt-1" required>
                        <label for="agreeEvaluationCriteria" class="ml-3 text-sm text-gray-700">
                          I have read and agree to the evaluation criteria outlined above. I understand that my mobile network license application will be assessed based on these criteria and that the minimum score for consideration is 55%. *
                        </label>
                      </div>
                    </div>

                    <!-- Undertaking Section -->
                    <div class="mt-12">
                      <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Undertaking</h3>

                      <div class="space-y-6">
                        <!-- I/We Selection -->
                        <div>
                          <label for="iWe" class="custom-form-label">Declaration Type *</label>
                          <select id="iWe" name="iWe" class="custom-input" required onchange="updateUndertakingText()">
                            <option value="">Select Declaration Type</option>
                            <option value="I">I (Individual)</option>
                            <option value="We">We (Organization)</option>
                          </select>
                        </div>

                        <!-- Dynamic Undertaking Text -->
                        <div id="undertakingTextContainer" class="bg-gray-50 border border-gray-200 rounded-md p-6" style="display: none;">
                          <p id="undertakingText" class="text-sm text-gray-800 leading-relaxed mb-4">
                            <!-- Text will be dynamically updated based on I/We selection -->
                          </p>

                          <div class="border-t border-gray-200 pt-4 mt-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label class="text-sm font-medium text-gray-700">Signed:</label>
                                <input type="text" id="signedName" name="signedName" class="custom-input mt-1" readonly>
                              </div>
                              <div>
                                <label class="text-sm font-medium text-gray-700">Date:</label>
                                <input type="text" id="currentDate" name="currentDate" class="custom-input mt-1" readonly>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Company Stamp Upload -->
                        <div>
                          <label class="custom-form-label">Company Stamp *</label>
                          <p class="text-sm text-gray-600 mb-3">Upload your company's official rubber stamp</p>
                          <div class="file-upload-area" onclick="document.getElementById('companyStamp').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Company Stamp</p>
                            <p class="text-xs text-gray-500">JPG, JPEG, PNG up to 5MB</p>
                          </div>
                          <input type="file" id="companyStamp" name="companyStamp" accept=".jpg,.jpeg,.png" class="hidden" required>
                          <div id="companyStampList" class="mt-2"></div>

                          <!-- Stamp Preview -->
                          <div id="stampPreview" class="mt-4" style="display: none;">
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Company Stamp Preview:</h5>
                            <img id="stampImage" src="" alt="Company Stamp" class="max-w-xs max-h-32 border border-gray-300 rounded">
                          </div>
                        </div>

                        <!-- Important Notes -->
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                          <h4 class="text-md font-medium text-blue-900 mb-2">Important Notes:</h4>
                          <ul class="text-sm text-blue-800 space-y-1 list-disc list-inside">
                            <li>All photocopies must be duly certified as true copies of the original.</li>
                            <li>Any attachment to the application shall be initialised by the signatory.</li>
                            <li>Applicant company rubber stamp must be affixed on the last page of the application form.</li>
                          </ul>
                        </div>

                        <!-- Final Compliance Confirmation -->
                        <div class="border border-gray-200 rounded-md p-4">
                          <div class="flex items-start">
                            <input type="checkbox" id="confirmCompliance" name="confirmCompliance" class="enhanced-checkbox mt-1" required>
                            <label for="confirmCompliance" class="ml-3 text-sm text-gray-700">
                              I confirm that I have read and will comply with all the requirements stated above, including proper certification of documents, initialization of attachments, and affixing of company stamp. *
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between pt-8 border-t border-gray-200">
                  <button type="button" id="prevBtn" onclick="changeStep(-1)" class="secondary-main-button" style="display: none;">
                    <i class="ri-arrow-left-line mr-2"></i>
                    Previous
                  </button>
                  <div class="flex space-x-4">
                    <button type="button" onclick="saveDraft()" class="secondary-main-button">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>
                    <button type="button" id="nextBtn" onclick="changeStep(1)" class="main-button">
                      Next
                      <i class="ri-arrow-right-line ml-2"></i>
                    </button>
                    <button type="submit" id="submitBtn" class="main-button" style="display: none;">
                      <i class="ri-send-plane-line mr-2"></i>
                      Submit Application
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    let currentStep = 1;
    const totalSteps = 5;

    // Initialize the form
    document.addEventListener('DOMContentLoaded', function() {
      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      setCurrentDate();
      loadDraftData();
    });

    // Step navigation functions
    function changeStep(direction) {
      if (direction === 1 && !validateCurrentStep()) {
        return;
      }

      const currentStepElement = document.getElementById(`step${currentStep}`);
      currentStepElement.classList.remove('active');

      currentStep += direction;

      if (currentStep < 1) currentStep = 1;
      if (currentStep > totalSteps) currentStep = totalSteps;

      const newStepElement = document.getElementById(`step${currentStep}`);
      newStepElement.classList.add('active');

      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      saveDraftData();
    }

    function updateProgressBar() {
      const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
      document.getElementById('progressFill').style.width = `${progress}%`;
      document.getElementById('progressPercentage').textContent = `${Math.round(progress)}%`;

      // Update status
      const statusElement = document.getElementById('applicationStatus');
      if (progress === 0) {
        statusElement.textContent = 'Draft';
        statusElement.className = 'tracking-status status-draft';
      } else if (progress < 50) {
        statusElement.textContent = 'In Progress';
        statusElement.className = 'tracking-status status-submitted';
      } else if (progress < 100) {
        statusElement.textContent = 'Nearly Complete';
        statusElement.className = 'tracking-status status-review';
      } else {
        statusElement.textContent = 'Ready for Review';
        statusElement.className = 'tracking-status status-approved';
      }
    }

    function updateStepIndicators() {
      for (let i = 1; i <= totalSteps; i++) {
        const indicator = document.getElementById(`step${i}Indicator`);
        if (i < currentStep) {
          indicator.className = 'step-indicator completed';
        } else if (i === currentStep) {
          indicator.className = 'step-indicator active';
        } else {
          indicator.className = 'step-indicator inactive';
        }
      }
    }

    function updateNavigationButtons() {
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      const submitBtn = document.getElementById('submitBtn');

      prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';

      if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-flex';
      } else {
        nextBtn.style.display = 'inline-flex';
        submitBtn.style.display = 'none';
      }
    }

    // Validation functions
    function validateCurrentStep() {
      const currentStepElement = document.getElementById(`step${currentStep}`);
      const requiredFields = currentStepElement.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (field.type === 'checkbox') {
          if (!field.checked) {
            field.classList.add('input-error');
            isValid = false;
          } else {
            field.classList.remove('input-error');
          }
        } else if (!field.value.trim()) {
          field.classList.add('input-error');
          isValid = false;
        } else {
          field.classList.remove('input-error');

          // Special validations
          if (field.name === 'contactPhone') {
            if (!/^\d{10}$/.test(field.value)) {
              field.classList.add('input-error');
              isValid = false;
            }
          }

          if (field.name === 'companyTPIN') {
            if (!/^\d+$/.test(field.value)) {
              field.classList.add('input-error');
              isValid = false;
            }
          }
        }
      });

      if (!isValid) {
        alert('Please fill in all required fields correctly before proceeding.');
      }

      return isValid;
    }

    // Undertaking text update
    function updateUndertakingText() {
      const iWe = document.getElementById('iWe').value;
      const companyName = document.getElementById('companyName').value || '[Company Name]';
      const container = document.getElementById('undertakingTextContainer');
      const textElement = document.getElementById('undertakingText');
      const signedName = document.getElementById('signedName');

      if (iWe) {
        const pronouns = iWe === 'I' ? { subject: 'I', possessive: 'my', object: 'me' } : { subject: 'We', possessive: 'our', object: 'us' };

        textElement.innerHTML = `${pronouns.subject}, ${companyName} hereby declare that the information supplied above is true and correct to the best of ${pronouns.possessive} knowledge and belief. ${pronouns.subject} undertake that upon grant of the mobile network license, ${pronouns.subject} shall abide by the Terms and the Conditions upon which the license is granted. ${pronouns.subject} accept that the license may be revoked if it is established that the license was granted based on information found to be false or untrue or misleading.`;

        signedName.value = companyName;
        container.style.display = 'block';
      } else {
        container.style.display = 'none';
      }
    }

    // Set current date
    function setCurrentDate() {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('en-GB');
      document.getElementById('currentDate').value = formattedDate;
    }

    // File upload handling
    function handleFileUpload(input) {
      const file = input.files[0];
      if (file) {
        const listContainer = document.getElementById(input.id + 'List');
        listContainer.innerHTML = `
          <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
            <span class="text-sm text-gray-700">${file.name}</span>
            <span class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
          </div>
        `;

        // Handle stamp preview
        if (input.id === 'companyStamp') {
          const reader = new FileReader();
          reader.onload = function(e) {
            document.getElementById('stampImage').src = e.target.result;
            document.getElementById('stampPreview').style.display = 'block';
          };
          reader.readAsDataURL(file);
        }
      }
    }

    // Add event listeners for file inputs
    document.addEventListener('DOMContentLoaded', function() {
      const fileInputs = document.querySelectorAll('input[type="file"]');
      fileInputs.forEach(input => {
        input.addEventListener('change', function() {
          handleFileUpload(this);
        });
      });
    });

    // Auto-update undertaking text when company name changes
    document.addEventListener('DOMContentLoaded', function() {
      const companyNameInput = document.getElementById('companyName');
      if (companyNameInput) {
        companyNameInput.addEventListener('input', updateUndertakingText);
      }
    });

    // Draft saving and loading
    function saveDraftData() {
      const formData = new FormData(document.getElementById('mobileApplicationForm'));
      const data = {};
      for (let [key, value] of formData.entries()) {
        data[key] = value;
      }
      localStorage.setItem('mobileApplicationDraft', JSON.stringify(data));
    }

    function loadDraftData() {
      const savedData = localStorage.getItem('mobileApplicationDraft');
      if (savedData) {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(key => {
          const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
          if (element && element.type !== 'file') {
            element.value = data[key];
          }
        });
      }
    }

    function saveDraft() {
      saveDraftData();
      alert('Draft saved successfully!');
    }

    // Mobile sidebar toggle
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    // Dropdown toggle
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('[onclick="toggleDropdown()"]');

      if (!button && !dropdown.contains(event.target)) {
        dropdown.classList.remove('show');
      }
    });

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // Form submission
    document.getElementById('mobileApplicationForm').addEventListener('submit', function(e) {
      e.preventDefault();

      if (validateCurrentStep()) {
        // Here you would normally submit the form to your server
        alert('Mobile Network License application submitted successfully! You will receive a confirmation email shortly.');

        // Clear draft data
        localStorage.removeItem('mobileApplicationDraft');

        // Redirect to applications page
        window.location.href = 'my-licenses.html';
      }
    });
  </script>

</body>
</html>