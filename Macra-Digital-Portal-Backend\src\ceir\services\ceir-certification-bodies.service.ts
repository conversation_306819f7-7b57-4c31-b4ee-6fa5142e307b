import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirCertificationBodies } from '../entities/ceir-certification-bodies.entity';
import { CreateCeirCertificationBodyDto, UpdateCeirCertificationBodyDto } from '../dto/ceir-certification-bodies';

@Injectable()
export class CeirCertificationBodiesService {
  constructor(
    @InjectRepository(CeirCertificationBodies)
    private readonly certificationBodyRepository: Repository<CeirCertificationBodies>,
  ) {}

  async create(createDto: CreateCeirCertificationBodyDto, userId?: string): Promise<CeirCertificationBodies> {
    // Check if certification body with same registration number already exists
    const existingBody = await this.certificationBodyRepository.findOne({
      where: { registration_number: createDto.registration_number }
    });

    if (existingBody) {
      throw new ConflictException(`Certification body with registration number '${createDto.registration_number}' already exists`);
    }

    const certificationBody = this.certificationBodyRepository.create({
      ...createDto,
      created_by: createDto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      accreditation_date: createDto.accreditation_date ? new Date(createDto.accreditation_date) : undefined,
      accreditation_expiry_date: createDto.accreditation_expiry_date ? new Date(createDto.accreditation_expiry_date) : undefined,
    });

    return await this.certificationBodyRepository.save(certificationBody);
  }

  async findAll(): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { created_at: 'DESC' }
    });
  }

  async findAllActive(): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { is_active: true },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findOne(id: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.certificationBodyRepository.findOne({
      where: { certification_body_id: id },
      relations: ['creator', 'updater', 'address', 'contact']
    });

    if (!certificationBody) {
      throw new NotFoundException(`Certification body with ID '${id}' not found`);
    }

    return certificationBody;
  }

  async findByRegistrationNumber(registrationNumber: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.certificationBodyRepository.findOne({
      where: { registration_number: registrationNumber },
      relations: ['creator', 'updater', 'address', 'contact']
    });

    if (!certificationBody) {
      throw new NotFoundException(`Certification body with registration number '${registrationNumber}' not found`);
    }

    return certificationBody;
  }

  async update(id: string, updateDto: UpdateCeirCertificationBodyDto, userId?: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.findOne(id);

    // Check for conflicts if updating registration_number
    if (updateDto.registration_number && updateDto.registration_number !== certificationBody.registration_number) {
      const existingBody = await this.certificationBodyRepository.findOne({
        where: { registration_number: updateDto.registration_number }
      });

      if (existingBody && existingBody.certification_body_id !== id) {
        throw new ConflictException(`Certification body with registration number '${updateDto.registration_number}' already exists`);
      }
    }

    const updateData = {
      ...updateDto,
      updated_by: updateDto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
      accreditation_date: updateDto.accreditation_date ? new Date(updateDto.accreditation_date) : certificationBody.accreditation_date,
      accreditation_expiry_date: updateDto.accreditation_expiry_date ? new Date(updateDto.accreditation_expiry_date) : certificationBody.accreditation_expiry_date,
    };

    Object.assign(certificationBody, updateData);
    return await this.certificationBodyRepository.save(certificationBody);
  }

  async remove(id: string): Promise<void> {
    const certificationBody = await this.findOne(id);
    await this.certificationBodyRepository.softDelete(id);
  }

  async restore(id: string): Promise<CeirCertificationBodies> {
    await this.certificationBodyRepository.restore(id);
    return await this.findOne(id);
  }

  async activate(id: string, userId: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.findOne(id);
    certificationBody.is_active = true;
    certificationBody.updated_by = userId;
    return await this.certificationBodyRepository.save(certificationBody);
  }

  async deactivate(id: string, userId: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.findOne(id);
    certificationBody.is_active = false;
    certificationBody.updated_by = userId;
    return await this.certificationBodyRepository.save(certificationBody);
  }

  async updateAccreditationStatus(id: string, status: string, userId: string): Promise<CeirCertificationBodies> {
    const certificationBody = await this.findOne(id);
    certificationBody.accreditation_status = status;
    certificationBody.updated_by = userId;
    
    // If status is expired or revoked, deactivate the body
    if (status === 'expired' || status === 'revoked') {
      certificationBody.is_active = false;
    }
    
    return await this.certificationBodyRepository.save(certificationBody);
  }

  async findByType(bodyType: string): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { body_type: bodyType, is_active: true },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findByCountry(country: string): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { country, is_active: true },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findByAccreditationStatus(status: string): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { accreditation_status: status },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findByScope(scope: string): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository
      .createQueryBuilder('body')
      .where(':scope = ANY(body.certification_scopes)', { scope })
      .andWhere('body.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('body.creator', 'creator')
      .leftJoinAndSelect('body.updater', 'updater')
      .leftJoinAndSelect('body.address', 'address')
      .leftJoinAndSelect('body.contact', 'contact')
      .orderBy('body.organization_name', 'ASC')
      .getMany();
  }

  async findByEquipmentCategory(category: string): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository
      .createQueryBuilder('body')
      .where(':category = ANY(body.authorized_equipment_categories)', { category })
      .andWhere('body.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('body.creator', 'creator')
      .leftJoinAndSelect('body.updater', 'updater')
      .leftJoinAndSelect('body.address', 'address')
      .leftJoinAndSelect('body.contact', 'contact')
      .orderBy('body.organization_name', 'ASC')
      .getMany();
  }

  async findMacraRecognized(): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { is_macra_recognized: true, is_active: true },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findCeirCertifiers(): Promise<CeirCertificationBodies[]> {
    return await this.certificationBodyRepository.find({
      where: { can_issue_ceir_certificates: true, is_active: true },
      relations: ['creator', 'updater', 'address', 'contact'],
      order: { organization_name: 'ASC' }
    });
  }

  async findExpiringAccreditations(days: number = 30): Promise<CeirCertificationBodies[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return await this.certificationBodyRepository
      .createQueryBuilder('body')
      .where('body.accreditation_expiry_date IS NOT NULL')
      .andWhere('body.accreditation_expiry_date <= :futureDate', { futureDate })
      .andWhere('body.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('body.creator', 'creator')
      .leftJoinAndSelect('body.updater', 'updater')
      .leftJoinAndSelect('body.address', 'address')
      .leftJoinAndSelect('body.contact', 'contact')
      .orderBy('body.accreditation_expiry_date', 'ASC')
      .getMany();
  }

  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byType: Record<string, number>;
    byCountry: Record<string, number>;
    byAccreditationStatus: Record<string, number>;
    macraRecognized: number;
    ceirCertifiers: number;
    expiringAccreditations: number;
  }> {
    const [total, active, inactive, macraRecognized, ceirCertifiers] = await Promise.all([
      this.certificationBodyRepository.count(),
      this.certificationBodyRepository.count({ where: { is_active: true } }),
      this.certificationBodyRepository.count({ where: { is_active: false } }),
      this.certificationBodyRepository.count({ where: { is_macra_recognized: true } }),
      this.certificationBodyRepository.count({ where: { can_issue_ceir_certificates: true } }),
    ]);

    const expiringAccreditations = await this.findExpiringAccreditations(30);
    const bodies = await this.certificationBodyRepository.find();

    const byType = bodies.reduce((acc, body) => {
      acc[body.body_type] = (acc[body.body_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byCountry = bodies.reduce((acc, body) => {
      acc[body.country] = (acc[body.country] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byAccreditationStatus = bodies.reduce((acc, body) => {
      acc[body.accreditation_status] = (acc[body.accreditation_status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      inactive,
      byType,
      byCountry,
      byAccreditationStatus,
      macraRecognized,
      ceirCertifiers,
      expiringAccreditations: expiringAccreditations.length,
    };
  }
}
