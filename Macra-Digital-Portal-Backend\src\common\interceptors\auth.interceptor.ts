import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditTrailService } from '../../audit-trail/audit-trail.service';
import { AuditAction, AuditStatus } from '../../entities/audit-trail.entity';

@Injectable()
export class AuthInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuthInterceptor.name);

  constructor(private readonly auditTrailService: AuditTrailService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Extract request information
    const ipAddress = this.getClientIp(request);
    const userAgent = request.headers['user-agent'];
    const sessionId = request.sessionID || request.headers['x-session-id'];
    const method = request.method;
    const url = request.url;
    const startTime = Date.now();

    // Log authentication attempts for auth endpoints
    const isAuthEndpoint = url.includes('/auth/');
    const user = request.user;

    // Add standard headers before processing (when headers can still be set)
    this.addStandardHeaders(response);

    return next.handle().pipe(
      tap(async (responseData) => {
        // Log successful authentication events
        if (isAuthEndpoint && method === 'POST') {
          // Use setImmediate to avoid blocking the response
          setImmediate(async () => {
            try {
              await this.logAuthEvent(
                url,
                user?.user_id,
                ipAddress,
                userAgent,
                sessionId,
                AuditStatus.SUCCESS,
                undefined,
                {
                  responseTime: Date.now() - startTime,
                  method,
                  url,
                  statusCode: response.statusCode,
                }
              );
            } catch (auditError) {
              this.logger.error('Failed to log auth success event', auditError);
            }
          });
        }
      }),
      catchError((error) => {
        // Log failed authentication events
        if (isAuthEndpoint && method === 'POST') {
          // Use setImmediate to avoid blocking the error response
          setImmediate(async () => {
            try {
              await this.logAuthEvent(
                url,
                user?.user_id,
                ipAddress,
                userAgent,
                sessionId,
                AuditStatus.FAILURE,
                error.message,
                {
                  responseTime: Date.now() - startTime,
                  method,
                  url,
                  statusCode: error.status || 500,
                  errorStack: error.stack,
                }
              );
            } catch (auditError) {
              this.logger.error('Failed to log auth failure event', auditError);
            }
          });
        }

        // Log authentication errors
        if (error instanceof UnauthorizedException) {
          this.logger.warn(`Authentication failed for ${ipAddress}: ${error.message}`);
        }

        return throwError(() => error);
      }),
    );
  }

  private async logAuthEvent(
    url: string,
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    sessionId?: string,
    status: AuditStatus = AuditStatus.SUCCESS,
    errorMessage?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    try {
      let action: AuditAction;
      let description: string;

      // Determine action based on URL
      if (url.includes('/login')) {
        action = AuditAction.LOGIN;
        description = 'User login attempt';
      } else if (url.includes('/logout')) {
        action = AuditAction.LOGOUT;
        description = 'User logout';
      } else if (url.includes('/register')) {
        action = AuditAction.CREATE;
        description = 'User registration attempt';
      } else {
        action = AuditAction.VIEW;
        description = 'Authentication action';
      }

      await this.auditTrailService.logAuthEvent(
        action,
        userId,
        ipAddress,
        userAgent,
        sessionId,
        status,
        errorMessage,
        metadata,
      );
    } catch (auditError) {
      this.logger.error('Failed to log auth event to audit trail', auditError);
    }
  }

  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }

  private addStandardHeaders(response: any): void {
    try {
      // Check if headers can still be set
      if (response.headersSent) {
        this.logger.debug('Headers already sent, skipping header addition');
        return;
      }

      // Add standard security headers
      response.setHeader('X-Content-Type-Options', 'nosniff');
      response.setHeader('X-Frame-Options', 'DENY');
      response.setHeader('X-XSS-Protection', '1; mode=block');

      // Add API version header
      response.setHeader('X-API-Version', '1.0');

      // Add timestamp header
      response.setHeader('X-Timestamp', new Date().toISOString());
    } catch (error) {
      this.logger.debug('Failed to set headers (likely already sent)', error.message);
    }
  }
}
