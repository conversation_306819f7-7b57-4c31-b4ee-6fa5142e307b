"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const consumer_affairs_complaint_service_1 = require("./consumer-affairs-complaint.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const nestjs_paginate_1 = require("nestjs-paginate");
const consumer_affairs_complaint_dto_1 = require("../dto/consumer-affairs/consumer-affairs-complaint.dto");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const roles_guard_1 = require("../common/guards/roles.guard");
const role_entity_1 = require("../entities/role.entity");
let ConsumerAffairsComplaintController = class ConsumerAffairsComplaintController {
    constructor(complaintService) {
        this.complaintService = complaintService;
    }
    async create(createDto, files, req) {
        const complaint = await this.complaintService.create(createDto, req.user.user_id);
        if (files && files.length > 0) {
            try {
                const attachments = await this.complaintService.uploadAttachments(complaint.complaint_id, files, req.user.user_id);
                return {
                    ...complaint,
                    attachments: attachments.map(attachment => ({
                        attachment_id: attachment.attachment_id,
                        file_name: attachment.file_name,
                        file_type: attachment.file_type,
                        file_size: attachment.file_size,
                        uploaded_at: attachment.uploaded_at,
                    })),
                };
            }
            catch (error) {
                console.error('Failed to upload attachments during complaint creation:', error);
                return {
                    ...complaint,
                    warning: 'Complaint created but some attachments failed to upload',
                };
            }
        }
        return complaint;
    }
    async findAll(query, req) {
        return this.complaintService.findAll(query, req.user.user_id, req.user.isStaff || false);
    }
    async findOne(id, req) {
        return this.complaintService.findOne(id, req.user.user_id, req.user.isStaff || false);
    }
    async update(id, updateDto, req) {
        const complaint = await this.complaintService.update(id, updateDto, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaint updated successfully',
            data: complaint,
        };
    }
    async delete(id, req) {
        await this.complaintService.delete(id, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaint deleted successfully',
        };
    }
    async updateStatus(id, statusDto, req) {
        const complaint = await this.complaintService.updateStatus(id, statusDto, req.user.user_id);
        return {
            success: true,
            message: 'Complaint status updated successfully',
            data: complaint,
        };
    }
    async updateComplainee(id, complaineeDto, req) {
        const complaint = await this.complaintService.updateComplainee(id, complaineeDto, req.user.user_id);
        return {
            success: true,
            message: 'Complaint complainee updated successfully',
            data: complaint,
        };
    }
    async assignComplaint(id, assignedTo, req) {
        const complaint = await this.complaintService.update(id, { assigned_to: assignedTo }, req.user.user_id, true);
        return {
            success: true,
            message: 'Complaint assigned successfully',
            data: complaint,
        };
    }
    async getStatsSummary(req) {
        return {
            success: true,
            message: 'Statistics retrieved successfully',
            data: {
                total: 0,
                by_status: {},
                by_category: {},
                by_priority: {},
            },
        };
    }
    async exportToCsv(filterDto, req) {
        return {
            success: true,
            message: 'Export functionality not yet implemented',
        };
    }
    async addAttachments(id, files, req) {
        if (!files || files.length === 0) {
            throw new common_1.BadRequestException('No files provided');
        }
        const documents = await this.complaintService.uploadAttachments(id, files, req.user.user_id);
        return {
            success: true,
            message: `${documents.length} file(s) uploaded successfully`,
            data: documents.map(doc => ({
                document_id: doc.document_id,
                file_name: doc.file_name,
                mime_type: doc.mime_type,
                file_size: doc.file_size,
                created_at: doc.created_at,
            })),
        };
    }
    async getAttachments(id, req) {
        const documents = await this.complaintService.getAttachments(id, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Attachments retrieved successfully',
            data: documents.map(doc => ({
                attachment_id: doc.document_id,
                file_name: doc.file_name,
                file_type: doc.mime_type,
                file_size: doc.file_size,
                uploaded_at: doc.created_at,
                uploader: doc.creator ? {
                    user_id: doc.creator.user_id,
                    first_name: doc.creator.first_name,
                    last_name: doc.creator.last_name,
                } : undefined,
            })),
        };
    }
    async deleteAttachment(id, attachmentId, req) {
        await this.complaintService.deleteAttachment(attachmentId, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Attachment deleted successfully',
        };
    }
    async downloadAttachment(id, attachmentId, req) {
        const { downloadUrl, document } = await this.complaintService.getAttachmentDownloadUrl(attachmentId, req.user.user_id, req.user.isStaff || false, 3600);
        return {
            success: true,
            message: 'Download URL generated successfully',
            data: {
                download_url: downloadUrl,
                file_name: document.file_name,
                file_type: document.mime_type,
                file_size: document.file_size,
                expires_in: 3600,
            },
        };
    }
};
exports.ConsumerAffairsComplaintController = ConsumerAffairsComplaintController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new consumer affairs complaint' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Complaint created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('attachments', 5, {
        fileFilter: (req, file, callback) => {
            const allowedMimes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/png',
                'image/gif',
                'text/plain',
            ];
            if (allowedMimes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new Error('Invalid file type. Allowed types: PDF, Word, Excel, Images, Text'), false);
            }
        },
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
    })),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaint',
        description: 'Created new consumer affairs complaint',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consumer_affairs_complaint_dto_1.CreateConsumerAffairsComplaintDto, Array, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all consumer affairs complaints with pagination' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaints retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific consumer affairs complaint by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaint retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaint',
        description: 'Viewed consumer affairs complaint details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a consumer affairs complaint' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaint updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaint',
        description: 'Updated consumer affairs complaint',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, consumer_affairs_complaint_dto_1.UpdateConsumerAffairsComplaintDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a consumer affairs complaint' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Complaint deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaint',
        description: 'Deleted consumer affairs complaint',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "delete", null);
__decorate([
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update complaint status (Staff only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaint status updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaintStatus',
        description: 'Updated consumer affairs complaint status',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, consumer_affairs_complaint_dto_1.UpdateConsumerAffairsComplaintStatusDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "updateStatus", null);
__decorate([
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, common_1.Put)(':id/complainee'),
    (0, swagger_1.ApiOperation)({ summary: 'Update complaint complainee (Staff only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaint complainee updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid complainee registration number' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplainee',
        description: 'Updated consumer affairs complaint complainee',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, consumer_affairs_complaint_dto_1.UpdateConsumerAffairsComplaineeDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "updateComplainee", null);
__decorate([
    (0, roles_decorator_1.Roles)(role_entity_1.RoleName.MANAGER),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Put)(':id/assign'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign complaint to staff member (Manager only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complaint assigned successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaintAssignment',
        description: 'Assigned consumer affairs complaint to staff member',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('assigned_to', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "assignComplaint", null);
__decorate([
    (0, common_1.Get)('stats/summary'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "getStatsSummary", null);
__decorate([
    (0, common_1.Get)('export/csv'),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consumer_affairs_complaint_dto_1.ConsumerAffairsComplaintFilterDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "exportToCsv", null);
__decorate([
    (0, common_1.Post)(':id/attachments'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 5, {
        fileFilter: (req, file, callback) => {
            const allowedMimes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/png',
                'image/gif',
                'text/plain',
            ];
            if (allowedMimes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new Error('Invalid file type. Allowed types: PDF, Word, Excel, Images, Text'), false);
            }
        },
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
    })),
    (0, swagger_1.ApiOperation)({ summary: 'Upload attachments to a complaint' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Complaint UUID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Attachments uploaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid file type or size' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - can only upload to own complaints' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaintAttachment',
        description: 'Uploaded attachments to complaint',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "addAttachments", null);
__decorate([
    (0, common_1.Get)(':id/attachments'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all attachments for a complaint' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Complaint UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Attachments retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Complaint not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - can only view own complaint attachments' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "getAttachments", null);
__decorate([
    (0, common_1.Delete)(':id/attachments/:attachmentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an attachment from a complaint' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Complaint UUID' }),
    (0, swagger_1.ApiParam)({ name: 'attachmentId', description: 'Attachment UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Attachment deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Attachment not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - can only delete own attachments' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.CONSUMER_AFFAIRS,
        resourceType: 'ConsumerAffairsComplaintAttachment',
        description: 'Deleted complaint attachment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('attachmentId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "deleteAttachment", null);
__decorate([
    (0, common_1.Get)(':id/attachments/:attachmentId/download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download an attachment from a complaint' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Complaint UUID' }),
    (0, swagger_1.ApiParam)({ name: 'attachmentId', description: 'Attachment UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'File downloaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Attachment not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - can only download attachments from own complaints' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('attachmentId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "downloadAttachment", null);
exports.ConsumerAffairsComplaintController = ConsumerAffairsComplaintController = __decorate([
    (0, common_1.Controller)('consumer-affairs'),
    (0, swagger_1.ApiTags)('Consumer Affairs'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)(audit_interceptor_1.AuditInterceptor),
    __metadata("design:paramtypes", [consumer_affairs_complaint_service_1.ConsumerAffairsComplaintService])
], ConsumerAffairsComplaintController);
//# sourceMappingURL=consumer-affairs-complaint.controller.js.map