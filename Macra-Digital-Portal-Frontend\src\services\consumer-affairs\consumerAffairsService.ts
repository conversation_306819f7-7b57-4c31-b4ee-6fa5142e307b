import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { documentService } from '@/services/documentService';
import {
  PaginatedResponse,
  PaginateQuery,
  ConsumerAffairsComplaint,
  CreateConsumerAffairsComplaintData,
  UpdateConsumerAffairsComplaintData,
  ConsumerAffairsComplaintAttachment,
  ComplaintStatus,
  UpdateConsumerAffairsComplaineeData
} from '@/types';

export type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;

export const consumerAffairsService = {

  // Create new complaint
  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {
    try {
      console.log('🔄 Creating consumer affairs complaint:', {
        title: data.title,
        category: data.category,
        hasAttachments: data.attachments && data.attachments.length > 0
      });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);

      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Add attachments if provided
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post('/consumer-affairs', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Get all complaints with pagination
  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/consumer-affairs?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get complaint by ID
  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.get(`/consumer-affairs/${id}`);
    return processApiResponse(response);
  },

  // Get complaint by ID (alias for consistency)
  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {
    return this.getComplaint(id);
  },

  // Update complaint
  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs/${id}`, data);
    // Backend returns { success: true, message: string, data: complaint }
    const result = processApiResponse(response);
    return result.data || result;
  },

  // Delete complaint
  async deleteComplaint(id: string): Promise<void> {
    await apiClient.delete(`/consumer-affairs/${id}`);
  },

  // Update complaint status (for staff)
  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs/${id}/status`, {
      status,
      comment
    });
    const result = processApiResponse(response);
    return result.data || result;
  },

  // Update complainee (for staff)
  async updateComplainee(id: string, data: UpdateConsumerAffairsComplaineeData): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs/${id}/complainee`, data);
    const result = processApiResponse(response);
    return result.data || result;
  },

  // Assign complaint to staff member (for staff) - Updated to use the general update endpoint
  async assignComplaint(id: string, assignedTo: string): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs/${id}/assign`, {
      assigned_to: assignedTo
    });
    const result = processApiResponse(response);
    return result.data || result;
  },

  // Add single attachment to complaint (via consumer-affairs attachments endpoint)
  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment[]> {
    const formData = new FormData();
    formData.append('files', file);

    const response = await apiClient.post(`/consumer-affairs/${id}/attachments`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    const result = processApiResponse(response);

    // Backend returns minimal document info; map to attachment shape
    const payload = (result.data || result) as Array<any>;
    const attachments: ConsumerAffairsComplaintAttachment[] = (payload || []).map((doc: any) => ({
      attachment_id: doc.document_id || doc.attachment_id,
      complaint_id: id,
      file_name: doc.file_name,
      file_type: doc.mime_type || doc.file_type || 'application/octet-stream',
      file_size: doc.file_size || 0,
      file_path: doc.file_path || '',
      uploaded_at: doc.created_at || new Date().toISOString(),
      uploaded_by: doc.created_by || 'unknown',
    }));

    return attachments;
  },

  // Add multiple attachments to complaint (via consumer-affairs attachments endpoint)
  async addAttachments(id: string, files: File[]): Promise<ConsumerAffairsComplaintAttachment[]> {
    const formData = new FormData();
    files.forEach((file) => formData.append('files', file));

    const response = await apiClient.post(`/consumer-affairs/${id}/attachments`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    const result = processApiResponse(response);

    const payload = (result.data || result) as Array<any>;
    const attachments: ConsumerAffairsComplaintAttachment[] = (payload || []).map((doc: any) => ({
      attachment_id: doc.document_id || doc.attachment_id,
      complaint_id: id,
      file_name: doc.file_name,
      file_type: doc.mime_type || doc.file_type || 'application/octet-stream',
      file_size: doc.file_size || 0,
      file_path: doc.file_path || '',
      uploaded_at: doc.created_at || new Date().toISOString(),
      uploaded_by: doc.created_by || 'unknown',
    }));

    return attachments;
  },

  // Get all attachments for a complaint (via consumer-affairs attachments endpoint)
  async getAttachments(complaintId: string): Promise<ConsumerAffairsComplaintAttachment[]> {
    const response = await apiClient.get(`/consumer-affairs/${complaintId}/attachments`);
    const result = processApiResponse(response);
    const payload = result.data || result;

    // If backend already maps to attachments, return directly; else map fields
    const attachments: ConsumerAffairsComplaintAttachment[] = (payload || []).map((att: any) => ({
      attachment_id: att.attachment_id || att.document_id,
      complaint_id: complaintId,
      file_name: att.file_name,
      file_type: att.file_type || att.mime_type || 'application/octet-stream',
      file_size: att.file_size || 0,
      file_path: att.file_path || '',
      uploaded_at: att.uploaded_at || att.created_at || new Date().toISOString(),
      uploaded_by: att.uploaded_by || att.created_by || 'unknown',
    }));

    return attachments;
  },

  // Remove attachment from complaint (via generic documents API)
  async removeAttachment(_complaintId: string, attachmentId: string): Promise<void> {
    await documentService.deleteDocument(attachmentId);
  },

  // Download attachment from complaint (via generic documents API)
  async downloadAttachment(_complaintId: string, attachmentId: string): Promise<Blob> {
    return await documentService.downloadDocument(attachmentId);
  },

  // Get download URL: not supported in generic API; return a best-effort via blob URL
  async getAttachmentDownloadUrl(complaintId: string, attachmentId: string): Promise<{ downloadUrl: string; attachment: ConsumerAffairsComplaintAttachment }> {
    const blob = await documentService.downloadDocument(attachmentId);
    const downloadUrl = URL.createObjectURL(blob);
    // We don't have the Document here; return minimal shape for caller expectations
    return { downloadUrl, attachment: {
      attachment_id: attachmentId,
      complaint_id: complaintId,
      file_name: 'download',
      file_type: blob.type || 'application/octet-stream',
      file_size: blob.size || 0,
      file_path: '',
      uploaded_at: new Date().toISOString(),
      uploaded_by: 'unknown',
    }};
  },

  // Get statistics summary (for staff)
  async getStatsSummary(): Promise<{
    total: number;
    by_status: Record<string, number>;
    by_category: Record<string, number>;
    by_priority: Record<string, number>;
  }> {
    const response = await apiClient.get('/consumer-affairs/stats/summary');
    const result = processApiResponse(response);
    return result.data || result;
  },

  // Export complaints to CSV (for staff)
  async exportToCsv(filters?: any): Promise<Blob> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.set(key, value.toString());
        }
      });
    }

    const response = await apiClient.get(`/consumer-affairs/export/csv?${params.toString()}`, {
      responseType: 'blob'
    });

    return response.data;
  },

  // File validation utilities
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
    ];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size must be less than 10MB. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Allowed types: PDF, Word, Excel, Images (JPEG, PNG, GIF), Text files'
      };
    }

    return { isValid: true };
  },

  // Validate multiple files
  validateFiles(files: File[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (files.length > 5) {
      errors.push('Maximum 5 files allowed per upload');
    }

    files.forEach((file, index) => {
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        errors.push(`File ${index + 1} (${file.name}): ${validation.error}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Get file type icon for display
  getFileTypeIcon(fileType: string): string {
    const iconMap: Record<string, string> = {
      'application/pdf': 'ri-file-pdf-line',
      'application/msword': 'ri-file-word-line',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'ri-file-word-line',
      'application/vnd.ms-excel': 'ri-file-excel-line',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'ri-file-excel-line',
      'image/jpeg': 'ri-image-line',
      'image/png': 'ri-image-line',
      'image/gif': 'ri-image-line',
      'text/plain': 'ri-file-text-line',
    };

    return iconMap[fileType] || 'ri-file-line';
  },

  // Format file size for display
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Note: Investigative document methods are not yet implemented in the backend
  // These will be added when the backend supports investigative document management



  // Helper methods
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getPriorityColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'investigating', label: 'Investigating' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' }
    ];
  },

  getCategoryOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'Billing & Charges', label: 'Billing & Charges' },
      { value: 'Service Quality', label: 'Service Quality' },
      { value: 'Network Issues', label: 'Network Issues' },
      { value: 'Customer Service', label: 'Customer Service' },
      { value: 'Contract Disputes', label: 'Contract Disputes' },
      { value: 'Accessibility', label: 'Accessibility' },
      { value: 'Fraud & Scams', label: 'Fraud & Scams' },
      { value: 'Other', label: 'Other' }
    ];
  },

  getPriorityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ];
  },
};
