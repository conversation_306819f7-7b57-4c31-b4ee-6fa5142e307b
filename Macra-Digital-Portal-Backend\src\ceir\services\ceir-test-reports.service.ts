import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirTestReports } from '../entities/ceir-test-reports.entity';
import { CreateCeirTestReportDto, UpdateCeirTestReportDto } from '../dto/ceir-test-reports';

@Injectable()
export class CeirTestReportsService {
  constructor(
    @InjectRepository(CeirTestReports)
    private readonly testReportRepository: Repository<CeirTestReports>,
  ) {}

  async create(createDto: CreateCeirTestReportDto, userId?: string): Promise<CeirTestReports> {
    // Check if test report with same report number already exists
    const existingReport = await this.testReportRepository.findOne({
      where: { report_number: createDto.report_number }
    });

    if (existingReport) {
      throw new ConflictException(`Test report with number '${createDto.report_number}' already exists`);
    }

    const testReport = this.testReportRepository.create({
      ...createDto,
      created_by: createDto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      test_date: new Date(createDto.test_date),
      report_date: new Date(createDto.report_date),
      expiry_date: createDto.expiry_date ? new Date(createDto.expiry_date) : undefined,
    });

    return await this.testReportRepository.save(testReport);
  }

  async findAll(): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
  }

  async findAllValid(): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { is_valid: true, report_status: 'approved' },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findOne(id: string): Promise<CeirTestReports> {
    const testReport = await this.testReportRepository.findOne({
      where: { report_id: id },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater']
    });

    if (!testReport) {
      throw new NotFoundException(`Test report with ID '${id}' not found`);
    }

    return testReport;
  }

  async findByReportNumber(reportNumber: string): Promise<CeirTestReports> {
    const testReport = await this.testReportRepository.findOne({
      where: { report_number: reportNumber },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater']
    });

    if (!testReport) {
      throw new NotFoundException(`Test report with number '${reportNumber}' not found`);
    }

    return testReport;
  }

  async update(id: string, updateDto: UpdateCeirTestReportDto, userId?: string): Promise<CeirTestReports> {
    const testReport = await this.findOne(id);

    // Check for conflicts if updating report_number
    if (updateDto.report_number && updateDto.report_number !== testReport.report_number) {
      const existingReport = await this.testReportRepository.findOne({
        where: { report_number: updateDto.report_number }
      });

      if (existingReport && existingReport.report_id !== id) {
        throw new ConflictException(`Test report with number '${updateDto.report_number}' already exists`);
      }
    }

    const updateData = {
      ...updateDto,
      updated_by: updateDto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
      test_date: updateDto.test_date ? new Date(updateDto.test_date) : testReport.test_date,
      report_date: updateDto.report_date ? new Date(updateDto.report_date) : testReport.report_date,
      expiry_date: updateDto.expiry_date ? new Date(updateDto.expiry_date) : testReport.expiry_date,
    };

    Object.assign(testReport, updateData);
    return await this.testReportRepository.save(testReport);
  }

  async remove(id: string): Promise<void> {
    const testReport = await this.findOne(id);
    await this.testReportRepository.softDelete(id);
  }

  async restore(id: string): Promise<CeirTestReports> {
    await this.testReportRepository.restore(id);
    return await this.findOne(id);
  }

  async approve(id: string, userId: string, approvedBy?: string): Promise<CeirTestReports> {
    const testReport = await this.findOne(id);
    testReport.report_status = 'approved';
    testReport.is_valid = true;
    testReport.updated_by = userId;
    if (approvedBy) {
      testReport.approved_by = approvedBy;
    }
    return await this.testReportRepository.save(testReport);
  }

  async reject(id: string, userId: string): Promise<CeirTestReports> {
    const testReport = await this.findOne(id);
    testReport.report_status = 'rejected';
    testReport.is_valid = false;
    testReport.updated_by = userId;
    return await this.testReportRepository.save(testReport);
  }

  async validate(id: string, userId: string): Promise<CeirTestReports> {
    const testReport = await this.findOne(id);
    testReport.is_valid = true;
    testReport.updated_by = userId;
    return await this.testReportRepository.save(testReport);
  }

  async expire(id: string, userId: string): Promise<CeirTestReports> {
    const testReport = await this.findOne(id);
    testReport.report_status = 'expired';
    testReport.is_valid = false;
    testReport.updated_by = userId;
    return await this.testReportRepository.save(testReport);
  }

  async findByDevice(deviceId: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { device_id: deviceId },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findByApplication(applicationId: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { application_id: applicationId },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findByCertificationBody(certificationBodyId: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { certification_body_id: certificationBodyId },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findByTestType(testType: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { test_type: testType, is_valid: true },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findByTestResult(testResult: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { test_result: testResult },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findByStatus(reportStatus: string): Promise<CeirTestReports[]> {
    return await this.testReportRepository.find({
      where: { report_status: reportStatus },
      relations: ['application', 'device', 'certification_body', 'technical_standard', 'creator', 'updater'],
      order: { report_date: 'DESC' }
    });
  }

  async findExpiring(days: number = 30): Promise<CeirTestReports[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return await this.testReportRepository
      .createQueryBuilder('report')
      .where('report.expiry_date IS NOT NULL')
      .andWhere('report.expiry_date <= :futureDate', { futureDate })
      .andWhere('report.is_valid = :isValid', { isValid: true })
      .leftJoinAndSelect('report.application', 'application')
      .leftJoinAndSelect('report.device', 'device')
      .leftJoinAndSelect('report.certification_body', 'certification_body')
      .leftJoinAndSelect('report.technical_standard', 'technical_standard')
      .leftJoinAndSelect('report.creator', 'creator')
      .leftJoinAndSelect('report.updater', 'updater')
      .orderBy('report.expiry_date', 'ASC')
      .getMany();
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<CeirTestReports[]> {
    return await this.testReportRepository
      .createQueryBuilder('report')
      .where('report.test_date >= :startDate', { startDate })
      .andWhere('report.test_date <= :endDate', { endDate })
      .leftJoinAndSelect('report.application', 'application')
      .leftJoinAndSelect('report.device', 'device')
      .leftJoinAndSelect('report.certification_body', 'certification_body')
      .leftJoinAndSelect('report.technical_standard', 'technical_standard')
      .leftJoinAndSelect('report.creator', 'creator')
      .leftJoinAndSelect('report.updater', 'updater')
      .orderBy('report.test_date', 'DESC')
      .getMany();
  }

  async getStatistics(): Promise<{
    total: number;
    valid: number;
    invalid: number;
    byTestType: Record<string, number>;
    byTestResult: Record<string, number>;
    byStatus: Record<string, number>;
    expiring: number;
    thisMonth: number;
  }> {
    const [total, valid, invalid] = await Promise.all([
      this.testReportRepository.count(),
      this.testReportRepository.count({ where: { is_valid: true } }),
      this.testReportRepository.count({ where: { is_valid: false } }),
    ]);

    const expiring = await this.findExpiring(30);
    
    const thisMonthStart = new Date();
    thisMonthStart.setDate(1);
    thisMonthStart.setHours(0, 0, 0, 0);
    
    const thisMonthEnd = new Date();
    thisMonthEnd.setMonth(thisMonthEnd.getMonth() + 1);
    thisMonthEnd.setDate(0);
    thisMonthEnd.setHours(23, 59, 59, 999);
    
    const thisMonthReports = await this.findByDateRange(thisMonthStart, thisMonthEnd);
    
    const reports = await this.testReportRepository.find();

    const byTestType = reports.reduce((acc, report) => {
      acc[report.test_type] = (acc[report.test_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byTestResult = reports.reduce((acc, report) => {
      acc[report.test_result] = (acc[report.test_result] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byStatus = reports.reduce((acc, report) => {
      acc[report.report_status] = (acc[report.report_status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      valid,
      invalid,
      byTestType,
      byTestResult,
      byStatus,
      expiring: expiring.length,
      thisMonth: thisMonthReports.length,
    };
  }
}
