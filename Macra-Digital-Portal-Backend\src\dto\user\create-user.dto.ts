import { IsEmail, IsString, IsOptional, IsEnum, IsUUID, IsArray, MinLength, MaxLength, Matches, ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments, Validate } from 'class-validator';
import { UserStatus } from '../../entities/user.entity';

@ValidatorConstraint({ name: 'departmentRequiredForMacra', async: false })
export class DepartmentRequiredForMacraConstraint implements ValidatorConstraintInterface {
  validate(departmentId: string, args: ValidationArguments) {
    const object = args.object as any;
    // If email doesn't end with @macra.mw, department_id is optional
    return true;
  }

  defaultMessage() {
    return 'Department ID is required for MACRA staff';
  }
}

export class CreateUserDto {
  @IsEmail()
  @IsString({ message: 'Invalid email address!' })
  email: string;

  @IsString({ message: 'Invalid characters in password!' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(128, { message: 'Password must not exceed 128 characters' })
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number or special character', // eslint-disable-line
  })
  password: string;

  @IsString({ message: 'Invalid characters in first name!' })
  @MinLength(3, { message: 'First name must be at least 3 characters long' })
  @MaxLength(100, { message: 'First name must not exceed 100 characters' })
  first_name: string;

  @IsString({ message: 'Invalid characters in last name!' })
  @MinLength(3, { message: 'Last name must be at least 3 characters long' })
  @MaxLength(100, { message: 'Last name must not exceed 100 characters' })
  last_name: string;

  @IsOptional()
  @IsString({ message: 'Invalid characters in middle name!' })
  @MaxLength(100)
  middle_name?: string;

  @IsString({ message: 'Invalid characters in phone number!' })
  @Matches(/^\+?[1-9]\d{1,14}$/, {
    message: 'Phone number must be a valid international format: +XXXYYYY...',
  })
  phone: string;

  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @IsOptional()
  @IsString({ message: 'Invalid characters in profile image!' })
  profile_image?: string;

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true, message: 'Invalid role ID!' })
  role_ids?: string[];

  @IsOptional()
  @IsUUID('4', {message: 'Invalid department ID!'})
  @Validate(DepartmentRequiredForMacraConstraint)
  department_id?: string;

  @IsOptional()
  @IsUUID('4', {message: 'Invalid organization ID!'})
  organization_id?: string;
}
