import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DashboardService } from './dashboard.service';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('dashboard')
@Controller('dashboard')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('overview')
  @ApiOperation({ summary: 'Get dashboard overview statistics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard overview retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'Dashboard',
    description: 'Viewed dashboard overview',
  })
  async getOverview(@Request() req: any) {
    return this.dashboardService.getOverviewStats(req.user.userId, req.user.roles);
  }

  @Get('licenses/stats')
  @ApiOperation({ summary: 'Get license statistics' })
  @ApiResponse({
    status: 200,
    description: 'License statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'LicenseStats',
    description: 'Viewed license statistics',
  })
  async getLicenseStats(@Request() req: any) {
    return this.dashboardService.getLicenseStats(req.user.userId, req.user.roles);
  }

  @Get('users/stats')
  @ApiOperation({ summary: 'Get user statistics' })
  @ApiResponse({
    status: 200,
    description: 'User statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'UserStats',
    description: 'Viewed user statistics',
  })
  async getUserStats(@Request() req: any) {
    return this.dashboardService.getUserStats(req.user.userId, req.user.roles);
  }

  @Get('financial/stats')
  @ApiOperation({ summary: 'Get financial statistics' })
  @ApiResponse({
    status: 200,
    description: 'Financial statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'FinancialStats',
    description: 'Viewed financial statistics',
  })
  async getFinancialStats(@Request() req: any) {
    return this.dashboardService.getFinancialStats(req.user.userId, req.user.roles);
  }

  @Get('applications/recent')
  @ApiOperation({ summary: 'Get recent applications' })
  @ApiResponse({
    status: 200,
    description: 'Recent applications retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'RecentApplications',
    description: 'Viewed recent applications',
  })
  async getRecentApplications(@Request() req: any) {
    return this.dashboardService.getRecentApplications(req.user.userId, req.user.roles);
  }

  @Get('activities/recent')
  @ApiOperation({ summary: 'Get recent activities' })
  @ApiResponse({
    status: 200,
    description: 'Recent activities retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DASHBOARD,
    resourceType: 'RecentActivities',
    description: 'Viewed recent activities',
  })
  async getRecentActivities(@Request() req: any) {
    return this.dashboardService.getRecentActivities(req.user.userId, req.user.roles);
  }
}
