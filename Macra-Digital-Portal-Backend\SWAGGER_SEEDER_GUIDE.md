# 📚 Swagger Documentation & Database Seeder Guide

## 🎯 Overview

This guide covers the implementation of:
1. **Swagger API Documentation** - Interactive API documentation
2. **Database Seeders** - Populate database with initial data

## 📖 Swagger Documentation

### **Features Added:**
- ✅ Interactive API documentation at `/api/docs`
- ✅ JWT Bearer token authentication
- ✅ Detailed endpoint descriptions
- ✅ Request/response examples
- ✅ Data validation schemas

### **Access Swagger UI:**
```
http://localhost:3001/api/docs
```

### **API Endpoints Documented:**

#### **Authentication Endpoints:**
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Reset password
- `POST /auth/verify-email` - Email verification
- `POST /auth/generate-2fa` - Generate 2FA
- `POST /auth/verify-2fa` - Verify 2FA
- `POST /auth/refresh` - Refresh token

#### **User Management Endpoints:**
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `GET /users` - List all users
- `GET /users/:id` - Get user by ID
- `POST /users` - Create new user
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user

### **JWT Authentication:**
- Click "Authorize" button in Swagger UI
- Enter: `Bearer YOUR_JWT_TOKEN`
- Test protected endpoints

## 🌱 Database Seeders

### **Features Added:**
- ✅ Role seeding with predefined roles
- ✅ User seeding with admin, evaluator, and test accounts
- ✅ Random user generation with faker
- ✅ Password hashing
- ✅ Proper entity relationships

### **Available Commands:**

#### **Seed Database:**
```bash
npm run seed
```

#### **Clear Seeded Data:**
```bash
npm run seed:clear
```

#### **Reset (Clear + Seed):**
```bash
npm run seed:reset
```

### **Default Accounts Created:**

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Administrator | <EMAIL> | Admin123! | Full system access |
| Evaluator | <EMAIL> | Evaluator123! | Evaluation access |
| Customer | <EMAIL> | Password123! | Test user account |

### **Roles Available:**
- `ADMINISTRATOR` - System Administrator
- `CUSTOMER` - Regular Customer
- `EVALUATOR` - Application Evaluator
- `LEGAL` - Legal Department
- `ACCOUNTANT` - Accounting Department
- `SALES` - Sales Department
- `OTHER` - Other roles

### **Random Data Generated:**
- 10 additional random users
- Realistic names using Faker.js
- Valid phone numbers (+265 format)
- Random status (ACTIVE/INACTIVE)
- Random email verification status

## 🚀 Usage Instructions

### **1. Start the Backend:**
```bash
cd Backend-Nestjs
npm run start:dev
# or
node dist/main.js
```

### **2. Access Swagger Documentation:**
Open browser: `http://localhost:3001/api/docs`

### **3. Seed the Database:**
```bash
npm run seed
```

### **4. Test API with Swagger:**
1. Register a new user or use default accounts
2. Login to get JWT token
3. Click "Authorize" in Swagger UI
4. Enter: `Bearer YOUR_JWT_TOKEN`
5. Test protected endpoints

### **5. Test Authentication Flow:**
```bash
# Register new user
curl -X POST http://localhost:3001/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!",
    "first_name": "New",
    "last_name": "User",
    "phone": "+************"
  }'

# Login
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'

# Use token for protected endpoints
curl -X GET http://localhost:3001/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📋 File Structure

```
Backend-Nestjs/
├── src/
│   ├── database/
│   │   └── seeders/
│   │       ├── seeder.service.ts    # Main seeder logic
│   │       ├── seeder.module.ts     # Seeder module
│   │       └── seed.ts              # CLI script
│   ├── dto/
│   │   └── auth/
│   │       ├── login.dto.ts         # Login DTO with Swagger
│   │       └── register.dto.ts      # Register DTO with Swagger
│   ├── auth/
│   │   └── auth.controller.ts       # Auth controller with Swagger
│   └── main.ts                      # Swagger configuration
└── package.json                     # Seeder scripts
```

## 🔧 Customization

### **Add More Swagger Documentation:**
```typescript
@ApiOperation({ summary: 'Your endpoint description' })
@ApiResponse({ status: 200, description: 'Success response' })
@ApiResponse({ status: 400, description: 'Bad request' })
```

### **Add More Seed Data:**
Edit `src/database/seeders/seeder.service.ts`:
```typescript
// Add more roles
const rolesData = [
  { name: RoleName.YOUR_NEW_ROLE },
  // ...
];

// Add more users
const customUser = this.usersRepository.create({
  email: '<EMAIL>',
  // ...
});
```

### **Custom Faker Data:**
```typescript
// Generate custom data
const customData = {
  name: faker.person.fullName(),
  email: faker.internet.email(),
  phone: `+265${faker.string.numeric(9)}`,
  address: faker.location.streetAddress(),
};
```

## 🎉 Success Indicators

- ✅ Swagger UI loads at `/api/docs`
- ✅ All endpoints documented with examples
- ✅ JWT authentication works in Swagger
- ✅ Seeder creates default accounts
- ✅ Can login with seeded accounts
- ✅ Database populated with test data

## 🔍 Troubleshooting

### **Swagger Issues:**
- Ensure `@nestjs/swagger` is installed
- Check main.ts configuration
- Verify controller decorators

### **Seeder Issues:**
- Ensure database is running
- Check entity relationships
- Verify enum values match entities
- Run `npm run build` before seeding

### **Authentication Issues:**
- Use correct JWT format: `Bearer TOKEN`
- Ensure token is valid and not expired
- Check user permissions for endpoints

The Swagger documentation and database seeders are now fully implemented and ready to use! 🚀
