import { IsEnum, IsOptional, IsString, IsUUID, IsObject, IsIP } from 'class-validator';
import { AuditAction, AuditModule, AuditStatus } from '../../entities/audit-trail.entity';

export class CreateAuditTrailDto {
  @IsEnum(AuditAction)
  action: AuditAction;

  @IsEnum(AuditModule)
  module: AuditModule;

  @IsEnum(AuditStatus)
  status: AuditStatus;

  @IsString()
  resourceType: string;

  @IsOptional()
  @IsString()
  resourceId?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  oldValues?: Record<string, any>;

  @IsOptional()
  @IsObject()
  newValues?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsIP()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsString()
  errorMessage?: string;

  @IsOptional()
  @IsUUID()
  userId?: string;
}
