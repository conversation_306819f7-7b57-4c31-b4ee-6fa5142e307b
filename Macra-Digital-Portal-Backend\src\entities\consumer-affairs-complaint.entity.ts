import {
  <PERSON><PERSON>ty,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { User } from './user.entity';
import { ComplaintStatus } from 'src/consumer-affairs/consumer-affairs-constants';
import { Documents } from './documents.entity';

@Entity('consumer_affairs_complaints')
export class ConsumerAffairsComplaint {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  complaint_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  complaint_number: string; // Pattern: COMP-YYYY-XXX

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  complainant_id: string; // References User who submitted the complaint

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  complainee_reg_number?: string; // References the User against whom the complaint is filed by business registration number

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({
    type: 'varchar',
    length: 50,
  })
  category: string;

  @Column({ type: 'varchar', length: 50, default: ComplaintStatus.SUBMITTED })
  status: string;

  @Column({ type: 'varchar', length: 50})
  priority: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  assigned_to?: string; // Staff member assigned to handle the complaint

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  resolution?: string; // Resolution details when complaint is resolved

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  internal_notes?: string; // Internal notes for staff

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  resolved_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  created_by: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'complainant_id' })
  complainant: User;

  // TO-DO: Add relation for complainee, either organization/ applicant
  
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany(() => Documents, (attachment) => attachment.entity_id)
  attachments: Documents[];

  @OneToMany(() => ConsumerAffairsComplaintStatusHistory, (history) => history.complaint)
  status_history: ConsumerAffairsComplaintStatusHistory[];

  @BeforeInsert()
  generateId() {
    if (!this.complaint_id) {
      this.complaint_id = uuidv4();
    }
    if (!this.complaint_number) {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 999) + 1;
      this.complaint_number = `COMP-${year}-${randomNum.toString().padStart(3, '0')}`;
    }
  }
}


// Status history entity for tracking complaint status changes
@Entity('consumer_affairs_complaint_status_history')
export class ConsumerAffairsComplaintStatusHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  history_id: string;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  complaint_id: string;

  @Column({ type: 'varchar', length: 50, default: ComplaintStatus.SUBMITTED })
  status: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  comment?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'varchar', length: 36 })
  @IsUUID()
  created_by: string;

  // Relations
  @ManyToOne(() => ConsumerAffairsComplaint, (complaint) => complaint.status_history)
  @JoinColumn({ name: 'complaint_id' })
  complaint: ConsumerAffairsComplaint;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @BeforeInsert()
  generateId() {
    if (!this.history_id) {
      this.history_id = uuidv4();
    }
  }
}
