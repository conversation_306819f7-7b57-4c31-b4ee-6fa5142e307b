import { PartialType } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString, MinLength, MaxLength, ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments, Validate } from 'class-validator';
import { DataBreachCategory, DataBreachSeverity, DataBreachStatus, DataBreachPriority } from 'src/data-breach/data-breachs-constants';

@ValidatorConstraint({ name: 'respondentRequiredForInvestigating', async: false })
export class RespondentRequiredForInvestigatingConstraint implements ValidatorConstraintInterface {
  validate(respondentRegNumber: string, args: ValidationArguments): boolean {
    const object = args.object as any;
    // If status is 'investigating', respondent_reg_number is required
    if (object.status === DataBreachStatus.INVESTIGATING) {
      return !!(respondentRegNumber && respondentRegNumber.trim() !== '');
    }
    // If status is not 'investigating', respondent_reg_number is optional
    return true;
  }

  defaultMessage(): string {
    return 'Respondent registration number is required when status is investigating';
  }
}

export class CreateDataBreachReportDto {
  @IsString()
  @MinLength(5, { message: 'Title must be at least 5 characters long' })
  @MaxLength(255, { message: 'Title must not exceed 255 characters' })
  title: string;

  @IsString()
  @MinLength(20, { message: 'Description must be at least 20 characters long' })
  description: string;

  @IsEnum(DataBreachCategory, { message: 'Invalid breach category' })
  category: DataBreachCategory;

  @IsEnum(DataBreachSeverity, { message: 'Invalid severity level' })
  severity: DataBreachSeverity;

  @IsDateString({}, { message: 'Invalid incident date format' })
  incident_date: string;

  @IsString()
  @MinLength(2, { message: 'Organization name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Organization name must not exceed 100 characters' })
  organization_involved: string;

  @IsOptional()
  @IsString()
  affected_data_types?: string;

  @IsOptional()
  @IsString()
  contact_attempts?: string;

  @IsOptional()
  @IsEnum(DataBreachPriority, { message: 'Invalid priority level' })
  priority?: DataBreachPriority;
}

export class UpdateDataBreachReportDto extends PartialType(CreateDataBreachReportDto) {
  @IsOptional()
  @IsEnum(DataBreachStatus, { message: 'Invalid breach status' })
  status?: DataBreachStatus;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsString()
  @Validate(RespondentRequiredForInvestigatingConstraint)
  respondent_reg_number?: string;

  @IsOptional()
  @IsString()
  resolution?: string;

  @IsOptional()
  @IsString()
  internal_notes?: string;

  @IsOptional()
  @IsDateString()
  resolved_at?: Date;
}

// CreateDataBreachReportAttachmentDto removed - using polymorphic Documents system instead

export class UpdateDataBreachReportStatusDto {
  @IsEnum(DataBreachStatus, { message: 'Invalid breach status' })
  status: DataBreachStatus;

  @IsOptional()
  @IsString()
  comment?: string;
}

export class DataBreachReportFilterDto {
  @IsOptional()
  @IsEnum(DataBreachCategory, { message: 'Invalid breach category' })
  category?: DataBreachCategory;

  @IsOptional()
  @IsEnum(DataBreachSeverity, { message: 'Invalid severity level' })
  severity?: DataBreachSeverity;

  @IsOptional()
  @IsEnum(DataBreachStatus, { message: 'Invalid breach status' })
  status?: DataBreachStatus;

  @IsOptional()
  @IsEnum(DataBreachPriority, { message: 'Invalid priority level' })
  priority?: DataBreachPriority;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid reporter ID' })
  reporter_id?: string;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for from_date' })
  from_date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for to_date' })
  to_date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for incident_from_date' })
  incident_from_date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for incident_to_date' })
  incident_to_date?: string;

  @IsOptional()
  @IsString()
  search?: string; // For searching in title, description, and organization

  @IsOptional()
  page?: number;

  @IsOptional()
  limit?: number;

  @IsOptional()
  sort_by?: string;

  @IsOptional()
  sort_order?: 'ASC' | 'DESC';
}
