import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,

} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ConsumerAffairsComplaintService } from 'src/consumer-affairs/consumer-affairs-complaint.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { ConsumerAffairsComplaintFilterDto, CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintStatusDto, UpdateConsumerAffairsComplaineeDto } from 'src/dto/consumer-affairs/consumer-affairs-complaint.dto';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Audit, AuditInterceptor } from 'src/common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from 'src/entities/audit-trail.entity';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { RoleName } from 'src/entities/role.entity';



@Controller('consumer-affairs')
@ApiTags('Consumer Affairs')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class ConsumerAffairsComplaintController {
  constructor(
    private readonly complaintService: ConsumerAffairsComplaintService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new consumer affairs complaint' })
  @ApiResponse({ status: 201, description: 'Complaint created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(FilesInterceptor('attachments', 5, {
    fileFilter: (req, file, callback) => {
      // Allow common document and image types
      const allowedMimes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'image/gif',
        'text/plain',
      ];

      if (allowedMimes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Allowed types: PDF, Word, Excel, Images, Text'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaint',
    description: 'Created new consumer affairs complaint',
  })
  async create(
    @Body(ValidationPipe) createDto: CreateConsumerAffairsComplaintDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.create(createDto, req.user.user_id);

    // Handle file uploads if any
    if (files && files.length > 0) {
      try {
        const attachments = await this.complaintService.uploadAttachments(
          complaint.complaint_id,
          files,
          req.user.user_id
        );

        return {
          ...complaint,
          attachments: attachments.map(attachment => ({
            attachment_id: attachment.attachment_id,
            file_name: attachment.file_name,
            file_type: attachment.file_type,
            file_size: attachment.file_size,
            uploaded_at: attachment.uploaded_at,
          })),
        };
      } catch (error) {
        // If file upload fails, still return the complaint but log the error
        console.error('Failed to upload attachments during complaint creation:', error);
        return {
          ...complaint,
          warning: 'Complaint created but some attachments failed to upload',
        };
      }
    }

    return complaint;
  }

  @Get()
  @ApiOperation({ summary: 'Get all consumer affairs complaints with pagination' })
  @ApiResponse({ status: 200, description: 'Complaints retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Request() req: any,
  ) {
    return this.complaintService.findAll(
      query,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific consumer affairs complaint by ID' })
  @ApiResponse({ status: 200, description: 'Complaint retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaint',
    description: 'Viewed consumer affairs complaint details',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    return this.complaintService.findOne(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a consumer affairs complaint' })
  @ApiResponse({ status: 200, description: 'Complaint updated successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaint',
    description: 'Updated consumer affairs complaint',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateConsumerAffairsComplaintDto,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.update(
      id,
      updateDto,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Consumer affairs complaint updated successfully',
      data: complaint,
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a consumer affairs complaint' })
  @ApiResponse({ status: 204, description: 'Complaint deleted successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaint',
    description: 'Deleted consumer affairs complaint',
  })
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    await this.complaintService.delete(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Consumer affairs complaint deleted successfully',
    };
  }

  // Staff-only endpoints
  @Roles('administrator', 'staff')
  @Put(':id/status')
  @ApiOperation({ summary: 'Update complaint status (Staff only)' })
  @ApiResponse({ status: 200, description: 'Complaint status updated successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaintStatus',
    description: 'Updated consumer affairs complaint status',
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) statusDto: UpdateConsumerAffairsComplaintStatusDto,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.updateStatus(
      id,
      statusDto,
      req.user.user_id
    );

    return {
      success: true,
      message: 'Complaint status updated successfully',
      data: complaint,
    };
  }

  @Roles('administrator', 'staff')
  @Put(':id/complainee')
  @ApiOperation({ summary: 'Update complaint complainee (Staff only)' })
  @ApiResponse({ status: 200, description: 'Complaint complainee updated successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 400, description: 'Invalid complainee registration number' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplainee',
    description: 'Updated consumer affairs complaint complainee',
  })
  async updateComplainee(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) complaineeDto: UpdateConsumerAffairsComplaineeDto,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.updateComplainee(
      id,
      complaineeDto,
      req.user.user_id
    );

    return {
      success: true,
      message: 'Complaint complainee updated successfully',
      data: complaint,
    };
  }

  @Roles(RoleName.MANAGER)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Put(':id/assign')
  @ApiOperation({ summary: 'Assign complaint to staff member (Manager only)' })
  @ApiResponse({ status: 200, description: 'Complaint assigned successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaintAssignment',
    description: 'Assigned consumer affairs complaint to staff member',
  })
  async assignComplaint(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('assigned_to', ParseUUIDPipe) assignedTo: string,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.update(
      id,
      { assigned_to: assignedTo },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Complaint assigned successfully',
      data: complaint,
    };
  }

  @Get('stats/summary')
  async getStatsSummary(@Request() req: any) {
    // TODO: Implement statistics summary
    // This would return counts by status, category, priority, etc.
    return {
      success: true,
      message: 'Statistics retrieved successfully',
      data: {
        total: 0,
        by_status: {},
        by_category: {},
        by_priority: {},
      },
    };
  }

  @Get('export/csv')
  async exportToCsv(
    @Query(ValidationPipe) filterDto: ConsumerAffairsComplaintFilterDto,
    @Request() req: any,
  ) {
    // TODO: Implement CSV export functionality
    return {
      success: true,
      message: 'Export functionality not yet implemented',
    };
  }

  // File upload endpoint for adding attachments to existing complaints
  @Post(':id/attachments')
  @UseInterceptors(FilesInterceptor('files', 5, {
    fileFilter: (req, file, callback) => {
      // Allow common document and image types
      const allowedMimes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'image/gif',
        'text/plain',
      ];

      if (allowedMimes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Allowed types: PDF, Word, Excel, Images, Text'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiOperation({ summary: 'Upload attachments to a complaint' })
  @ApiParam({ name: 'id', description: 'Complaint UUID' })
  @ApiResponse({ status: 201, description: 'Attachments uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file type or size' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only upload to own complaints' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaintAttachment',
    description: 'Uploaded attachments to complaint',
  })
  async addAttachments(
    @Param('id', ParseUUIDPipe) id: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    const documents = await this.complaintService.uploadAttachments(
      id,
      files,
      req.user.user_id
    );

    return {
      success: true,
      message: `${documents.length} file(s) uploaded successfully`,
      data: documents.map(doc => ({
        document_id: doc.document_id,
        file_name: doc.file_name,
        mime_type: doc.mime_type,
        file_size: doc.file_size,
        created_at: doc.created_at,
      })),
    };
  }

  @Get(':id/attachments')
  @ApiOperation({ summary: 'Get all attachments for a complaint' })
  @ApiParam({ name: 'id', description: 'Complaint UUID' })
  @ApiResponse({ status: 200, description: 'Attachments retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only view own complaint attachments' })
  async getAttachments(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const documents = await this.complaintService.getAttachments(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Attachments retrieved successfully',
      data: documents.map(doc => ({
        attachment_id: doc.document_id,
        file_name: doc.file_name,
        file_type: doc.mime_type,
        file_size: doc.file_size,
        uploaded_at: doc.created_at,
        uploader: doc.creator ? {
          user_id: doc.creator.user_id,
          first_name: doc.creator.first_name,
          last_name: doc.creator.last_name,
        } : undefined,
      })),
    };
  }

  @Delete(':id/attachments/:attachmentId')
  @ApiOperation({ summary: 'Delete an attachment from a complaint' })
  @ApiParam({ name: 'id', description: 'Complaint UUID' })
  @ApiParam({ name: 'attachmentId', description: 'Attachment UUID' })
  @ApiResponse({ status: 200, description: 'Attachment deleted successfully' })
  @ApiResponse({ status: 404, description: 'Attachment not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only delete own attachments' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.CONSUMER_AFFAIRS,
    resourceType: 'ConsumerAffairsComplaintAttachment',
    description: 'Deleted complaint attachment',
  })
  async deleteAttachment(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('attachmentId', ParseUUIDPipe) attachmentId: string,
    @Request() req: any,
  ) {
    await this.complaintService.deleteAttachment(
      attachmentId,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Attachment deleted successfully',
    };
  }

  @Get(':id/attachments/:attachmentId/download')
  @ApiOperation({ summary: 'Download an attachment from a complaint' })
  @ApiParam({ name: 'id', description: 'Complaint UUID' })
  @ApiParam({ name: 'attachmentId', description: 'Attachment UUID' })
  @ApiResponse({ status: 200, description: 'File downloaded successfully' })
  @ApiResponse({ status: 404, description: 'Attachment not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only download attachments from own complaints' })
  async downloadAttachment(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('attachmentId', ParseUUIDPipe) attachmentId: string,
    @Request() req: any,
  ) {
    const { downloadUrl, document } = await this.complaintService.getAttachmentDownloadUrl(
      attachmentId,
      req.user.user_id,
      req.user.isStaff || false,
      3600 // 1 hour
    );

    return {
      success: true,
      message: 'Download URL generated successfully',
      data: {
        download_url: downloadUrl,
        file_name: document.file_name,
        file_type: document.mime_type,
        file_size: document.file_size,
        expires_in: 3600, // seconds
      },
    };
  }
}
