"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const nestjs_paginate_1 = require("nestjs-paginate");
const data_breach_report_entity_1 = require("../entities/data-breach-report.entity");
const data_breach_report_constants_1 = require("./data-breach-report-constants");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const email_template_service_1 = require("../notifications/email-template.service");
const user_entity_1 = require("../entities/user.entity");
const tasks_service_1 = require("../tasks/tasks.service");
const tasks_entity_1 = require("../entities/tasks.entity");
let DataBreachReportService = class DataBreachReportService {
    constructor(reportRepository, userRepository, notificationHelperService, emailTemplateService, tasksService) {
        this.reportRepository = reportRepository;
        this.userRepository = userRepository;
        this.notificationHelperService = notificationHelperService;
        this.emailTemplateService = emailTemplateService;
        this.tasksService = tasksService;
    }
    async create(createDto, reporterId) {
        const report = this.reportRepository.create({
            ...createDto,
            incident_date: new Date(createDto.incident_date),
            reporter_id: reporterId,
            created_by: reporterId,
        });
        const savedReport = await this.reportRepository.save(report);
        try {
            await this.createDataBreachTask(savedReport, reporterId);
        }
        catch (taskError) {
            console.error('❌ Failed to create data breach task:', taskError);
        }
        try {
            const reporter = await this.userRepository.findOne({
                where: { user_id: reporterId },
            });
            if (reporter && reporter.email) {
                const emailTemplate = this.emailTemplateService.generateDataBreachReportSubmittedTemplate({
                    reporterName: `${reporter.first_name} ${reporter.last_name}`,
                    reportNumber: savedReport.report_number || savedReport.report_id,
                    reportTitle: savedReport.title,
                    category: savedReport.category,
                    severity: savedReport.severity,
                    submissionDate: savedReport.created_at.toLocaleDateString(),
                    portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/data-protection`,
                });
                await this.notificationHelperService.sendEmailNotification({
                    recipientId: reporterId,
                    recipientEmail: reporter.email,
                    recipientName: `${reporter.first_name} ${reporter.last_name}`,
                    subject: emailTemplate.subject,
                    message: `Your data breach report ${savedReport.report_number || savedReport.report_id} has been successfully submitted and is being processed.`,
                    htmlContent: emailTemplate.html,
                    entityType: 'data_breach_report',
                    entityId: savedReport.report_id,
                    actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/data-protection`,
                    recipientType: 'CUSTOMER',
                    createdBy: reporterId,
                    sendEmail: true,
                    createInApp: true,
                });
                console.log(`✅ Email notification sent for data breach report ${savedReport.report_id}`);
            }
        }
        catch (error) {
            console.error('❌ Error sending email notification for data breach report:', error);
        }
        return this.findOne(savedReport.report_id, reporterId);
    }
    async findAll(query, userId, isStaff = false) {
        const queryBuilder = this.reportRepository
            .createQueryBuilder('report')
            .leftJoinAndSelect('report.reporter', 'reporter')
            .leftJoinAndSelect('report.assignee', 'assignee')
            .orderBy('report.created_at', 'DESC');
        if (!isStaff) {
            queryBuilder.andWhere('report.reporter_id = :userId', { userId });
        }
        return (0, nestjs_paginate_1.paginate)(query, queryBuilder, {
            sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'severity', 'incident_date'],
            searchableColumns: ['title', 'description', 'organization_involved'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            filterableColumns: {
                status: true,
                severity: true,
                priority: true,
            },
        });
    }
    async findOne(reportId, userId, isStaff = false) {
        const queryBuilder = this.createQueryBuilder()
            .where('report.report_id = :reportId', { reportId });
        if (!isStaff) {
            queryBuilder.andWhere('report.reporter_id = :userId', { userId });
        }
        const report = await queryBuilder.getOne();
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        return this.mapToResponseDto(report);
    }
    async update(reportId, updateDto, userId, isStaff = false) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
            relations: ['reporter'],
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        if (!isStaff && report.reporter_id !== userId) {
            throw new common_1.ForbiddenException('You can only update your own reports');
        }
        if (!isStaff) {
            const allowedFields = ['title', 'description', 'category', 'severity', 'incident_date', 'organization_involved', 'affected_data_types', 'contact_attempts'];
            const updateFields = Object.keys(updateDto);
            const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
            if (invalidFields.length > 0) {
                throw new common_1.BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
            }
        }
        if (updateDto.status === data_breach_report_constants_1.DataBreachStatus.RESOLVED) {
            updateDto.resolved_at = new Date();
        }
        if (updateDto.incident_date) {
            updateDto.incident_date = new Date(updateDto.incident_date);
        }
        Object.assign(report, updateDto);
        report.updated_by = userId;
        await this.reportRepository.save(report);
        return this.findOne(reportId, userId, isStaff);
    }
    async delete(reportId, userId, isStaff = false) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        if (!isStaff && report.reporter_id !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own reports');
        }
        await this.reportRepository.softDelete(reportId);
    }
    async updateStatus(reportId, statusDto, userId) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        report.status = statusDto.status;
        report.updated_by = userId;
        if (statusDto.status === data_breach_report_constants_1.DataBreachStatus.RESOLVED) {
            report.resolved_at = new Date();
        }
        await this.reportRepository.save(report);
        return this.findOne(reportId, userId, true);
    }
    createQueryBuilder() {
        return this.reportRepository
            .createQueryBuilder('report')
            .leftJoinAndSelect('report.reporter', 'reporter')
            .leftJoinAndSelect('report.assignee', 'assignee');
    }
    applyFilters(queryBuilder, filters) {
        if (filters.category) {
            queryBuilder.andWhere('report.category = :category', { category: filters.category });
        }
        if (filters.severity) {
            queryBuilder.andWhere('report.severity = :severity', { severity: filters.severity });
        }
        if (filters.status) {
            queryBuilder.andWhere('report.status = :status', { status: filters.status });
        }
        if (filters.priority) {
            queryBuilder.andWhere('report.priority = :priority', { priority: filters.priority });
        }
        if (filters.assigned_to) {
            queryBuilder.andWhere('report.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
        }
        if (filters.from_date) {
            queryBuilder.andWhere('report.created_at >= :from_date', { from_date: filters.from_date });
        }
        if (filters.to_date) {
            queryBuilder.andWhere('report.created_at <= :to_date', { to_date: filters.to_date });
        }
        if (filters.incident_from_date) {
            queryBuilder.andWhere('report.incident_date >= :incident_from_date', { incident_from_date: filters.incident_from_date });
        }
        if (filters.incident_to_date) {
            queryBuilder.andWhere('report.incident_date <= :incident_to_date', { incident_to_date: filters.incident_to_date });
        }
        if (filters.search) {
            queryBuilder.andWhere('(report.title ILIKE :search OR report.description ILIKE :search OR report.organization_involved ILIKE :search)', { search: `%${filters.search}%` });
        }
    }
    mapToResponseDto(report) {
        return {
            report_id: report.report_id,
            report_number: report.report_number,
            reporter_id: report.reporter_id,
            title: report.title,
            description: report.description,
            category: report.category,
            severity: report.severity,
            status: report.status,
            priority: report.priority,
            incident_date: report.incident_date,
            organization_involved: report.organization_involved,
            respondent_reg_number: report.respondent_reg_number,
            affected_data_types: report.affected_data_types,
            contact_attempts: report.contact_attempts,
            assigned_to: report.assigned_to,
            resolution: report.resolution,
            resolved_at: report.resolved_at,
            created_at: report.created_at,
            updated_at: report.updated_at,
            reporter: report.reporter ? {
                user_id: report.reporter.user_id,
                first_name: report.reporter.first_name,
                last_name: report.reporter.last_name,
                email: report.reporter.email,
            } : undefined,
            assignee: report.assignee ? {
                user_id: report.assignee.user_id,
                first_name: report.assignee.first_name,
                last_name: report.assignee.last_name,
                email: report.assignee.email,
            } : undefined,
        };
    }
    async createDataBreachTask(report, createdBy) {
        try {
            const dataBreachStaff = await this.findUsersWithDataBreachRole();
            const assignedTo = dataBreachStaff.length > 0 ? dataBreachStaff[0].user_id : undefined;
            const taskData = {
                task_type: tasks_entity_1.TaskType.DATA_BREACH,
                title: `Data Breach Report: ${report.title}`,
                description: `Review and investigate data breach report ${report.report_number || report.report_id}.\n\nCategory: ${report.category}\nSeverity: ${report.severity}\nIncident Date: ${report.incident_date.toLocaleDateString()}\nOrganization: ${report.organization_involved}`,
                priority: this.mapSeverityToPriority(report.severity),
                entity_type: 'data-breach',
                entity_id: report.report_id,
                assigned_to: assignedTo,
                metadata: {
                    report_number: report.report_number,
                    category: report.category,
                    severity: report.severity,
                    incident_date: report.incident_date.toISOString(),
                    organization_involved: report.organization_involved,
                },
            };
            const task = await this.tasksService.create(taskData, createdBy);
            console.log(`✅ Data breach task created: ${task.task_id} for report ${report.report_id}`);
            if (assignedTo) {
                console.log(`✅ Task auto-assigned to data breach staff: ${assignedTo}`);
            }
            else {
                console.log(`⚠️ No staff with data_breach role found - task created unassigned`);
            }
        }
        catch (error) {
            console.error('❌ Error creating data breach task:', error);
            throw error;
        }
    }
    async findUsersWithDataBreachRole() {
        try {
            let users = await this.userRepository
                .createQueryBuilder('user')
                .leftJoinAndSelect('user.roles', 'roles')
                .where('roles.name = :roleName', { roleName: 'data_breach' })
                .andWhere('user.status = :status', { status: 'active' })
                .andWhere('user.email IS NOT NULL')
                .getMany();
            if (users.length === 0) {
                console.log('⚠️ No users with data_breach role found, falling back to officer role');
                users = await this.userRepository
                    .createQueryBuilder('user')
                    .leftJoinAndSelect('user.roles', 'roles')
                    .where('roles.name = :roleName', { roleName: 'officer' })
                    .andWhere('user.status = :status', { status: 'active' })
                    .andWhere('user.email IS NOT NULL')
                    .getMany();
            }
            return users;
        }
        catch (error) {
            console.error('❌ Error finding users with data_breach role:', error);
            return [];
        }
    }
    mapSeverityToPriority(severity) {
        switch (severity?.toLowerCase()) {
            case 'critical':
            case 'high':
                return tasks_entity_1.TaskPriority.HIGH;
            case 'medium':
                return tasks_entity_1.TaskPriority.MEDIUM;
            case 'low':
                return tasks_entity_1.TaskPriority.LOW;
            default:
                return tasks_entity_1.TaskPriority.MEDIUM;
        }
    }
};
exports.DataBreachReportService = DataBreachReportService;
exports.DataBreachReportService = DataBreachReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(data_breach_report_entity_1.DataBreachReport)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        notification_helper_service_1.NotificationHelperService,
        email_template_service_1.EmailTemplateService,
        tasks_service_1.TasksService])
], DataBreachReportService);
//# sourceMappingURL=data-breach-report.service.js.map