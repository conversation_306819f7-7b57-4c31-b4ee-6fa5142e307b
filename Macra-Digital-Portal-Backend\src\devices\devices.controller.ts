import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
  ValidationPipe,
  Request,
} from '@nestjs/common';
import { DevicesService } from './devices.service';
import { CreateDeviceDto } from 'src/dto/devices/create-device.dto';
import { UpdateDeviceDto } from 'src/dto/devices/update-device.dto';
import { BatchValidateImeiDto, BatchValidationResult } from 'src/dto/devices/batch-validate-imei.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Audit } from 'src/common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from 'src/entities/audit-trail.entity';

@ApiTags('Devices - Type Approval')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller('devices')
export class DevicesController {
  constructor(private readonly devicesService: DevicesService) {}

  // Device endpoints
  @ApiBearerAuth('JWT-auth')
  @Post()
  @ApiOperation({ summary: 'Create Device' })
  @ApiResponse({ status: 201, description: 'Device created successfully' })
  @ApiResponse({ status: 409, description: 'Device with IMEI or serial number already exists' })
  @ApiBody({ type: CreateDeviceDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Created a device',
  })
  async createDevice(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    dto: CreateDeviceDto,
    @Request() req: any,
  ) {
    return this.devicesService.createDevice(dto, req.user?.userId);
  }

  @ApiBearerAuth('JWT-auth')
  @Get()
  @ApiOperation({ summary: 'List all Devices' })
  @ApiResponse({ status: 200, description: 'List of devices' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Viewed all devices',
  })
  async findAllDevices() {
    return this.devicesService.findAllDevices();
  }

  @ApiBearerAuth('JWT-auth')
  @Get(':id')
  @ApiOperation({ summary: 'Get a Device by ID' })
  @ApiResponse({ status: 200, description: 'Device found' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Viewed a device by ID',
  })
  async findOneDevice(@Param('id') id: string) {
    return this.devicesService.findOneDevice(id);
  }

  @ApiBearerAuth('JWT-auth')
  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get Devices by Application ID' })
  @ApiResponse({ status: 200, description: 'Devices found' })
  @ApiResponse({ status: 404, description: 'No devices found for application' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Viewed devices by application ID',
  })
  async findDevicesByApplication(@Param('applicationId') applicationId: string) {
    return this.devicesService.findDevicesByApplication(applicationId);
  }

  @ApiBearerAuth('JWT-auth')
  // Note: findDeviceByImei endpoint removed as IMEI field is now optional

  @ApiBearerAuth('JWT-auth')
  @Post('imei/batch-validate')
  @ApiOperation({ summary: 'Batch validate multiple IMEIs' })
  @ApiResponse({
    status: 200,
    description: 'IMEIs validated successfully',
    type: [BatchValidationResult]
  })
  @ApiBody({ type: BatchValidateImeiDto })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Batch validated IMEIs',
  })
  async batchValidateImeis(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    dto: BatchValidateImeiDto
  ): Promise<BatchValidationResult[]> {
    return this.devicesService.batchValidateImeis(dto.imeis);
  }

  @ApiBearerAuth('JWT-auth')
  @Put(':id')
  @ApiOperation({ summary: 'Update Device' })
  @ApiResponse({ status: 200, description: 'Device updated successfully' })
  @ApiResponse({ status: 409, description: 'Device with IMEI or serial number already exists' })
  @ApiBody({ type: UpdateDeviceDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Updated a device',
  })
  async updateDevice(
    @Param('id') id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    dto: UpdateDeviceDto,
    @Request() req: any,
  ) {
    return this.devicesService.updateDevice(id, dto, req.user?.userId);
  }

  @ApiBearerAuth('JWT-auth')
  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a Device' })
  @ApiResponse({ status: 200, description: 'Device soft deleted successfully' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'Device',
    description: 'Deleted a device',
  })
  async removeDevice(@Param('id') id: string) {
    await this.devicesService.removeDevice(id);
    return { message: 'Device soft deleted successfully' };
  }
}
