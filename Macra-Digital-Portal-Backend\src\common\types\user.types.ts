/**
 * Enhanced type definitions for user management
 */

import { User, UserStatus } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';

// Base user types
export type SafeUser = Omit<User, 'password' | 'two_factor_code' | 'two_factor_temp'> & {
  generateId: () => void;
};

export type UserWithRoles = User & {
  roles: NonNullable<User['roles']>;
};

export type UserProfile = Pick<User, 
  'user_id' | 'email' | 'first_name' | 'last_name' | 'middle_name' | 
  'phone' | 'profile_image' | 'status' | 'created_at' | 'updated_at'
> & {
  roles?: string[];
};

// User creation and update types
export interface CreateUserData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  department_id?: string;
  organization_id?: string;
  status?: UserStatus;
  role_ids?: string[];
}

export interface UpdateUserData {
  email?: string;
  password?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  department_id?: string;
  organization_id?: string;
  status?: UserStatus;
  role_ids?: string[];
}

export interface UpdateProfileData {
  email?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// Query and pagination types
export interface UserQueryFilters {
  status?: UserStatus;
  department_id?: string;
  role?: string;
  search?: string;
}

export interface UserPaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  filters?: UserQueryFilters;
}

export interface PaginatedUsers {
  data: SafeUser[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Validation types
export interface UserValidationRules {
  email: {
    required: boolean;
    format: RegExp;
    maxLength: number;
  };
  password: {
    required: boolean;
    minLength: number;
    maxLength: number;
    requireSpecialChar: boolean;
    requireNumber: boolean;
    requireUppercase: boolean;
  };
  name: {
    required: boolean;
    minLength: number;
    maxLength: number;
    allowedChars: RegExp;
  };
  phone: {
    required: boolean;
    format: RegExp;
  };
}

export interface UserValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings?: Record<string, string[]>;
}

// Role-related types
export interface UserRole {
  role_id: string;
  name: string;
  description?: string;
}

export interface RoleAssignment {
  user_id: string;
  role_ids: string[];
}

// Avatar and file types
export interface AvatarUpload {
  file: Express.Multer.File;
  userId: string;
}

export interface AvatarResult {
  user: SafeUser;
  avatarUrl?: string;
}

// Service method return types
export type UserCreationResult = Promise<User>;
export type UserUpdateResult = Promise<User>;
export type UserDeletionResult = Promise<void>;
export type UserQueryResult = Promise<PaginatedUsers>;
export type PasswordChangeResult = Promise<{ message: string }>;
export type AvatarUploadResult = Promise<User>;

// Type guards
export function isValidUserStatus(status: string): status is UserStatus {
  return Object.values(UserStatus).includes(status as UserStatus);
}

export function isValidUserId(userId: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(userId);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

export function hasRequiredUserFields(data: any): data is CreateUserData {
  return data &&
    typeof data.email === 'string' &&
    typeof data.password === 'string' &&
    typeof data.first_name === 'string' &&
    typeof data.last_name === 'string' &&
    typeof data.phone === 'string';
}

export function isUserWithRoles(user: User): user is UserWithRoles {
  return user && Array.isArray(user.roles) && user.roles.length > 0;
}

// Utility functions
export class UserTypeUtils {
  static createSafeUser(user: User): SafeUser {
    const { password, two_factor_code, two_factor_temp, ...safeUser } = user;
    return {
      ...safeUser,
      generateId: user.generateId.bind(user)
    };
  }

  static createUserProfile(user: User): UserProfile {
    return {
      user_id: user.user_id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      middle_name: user.middle_name,
      phone: user.phone,
      profile_image: user.profile_image,
      status: user.status,
      created_at: user.created_at,
      updated_at: user.updated_at,
      roles: user.roles?.map(role => role.name),
    };
  }

  static extractRoleNames(user: User): string[] {
    return user.roles?.map(role => role.name) || [];
  }

  static getFullName(user: Pick<User, 'first_name' | 'last_name' | 'middle_name'>): string {
    const parts = [user.first_name];
    if (user.middle_name) {
      parts.push(user.middle_name);
    }
    parts.push(user.last_name);
    return parts.join(' ').trim();
  }
}
