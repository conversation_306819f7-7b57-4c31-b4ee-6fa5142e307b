<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Status Summary Report - MACRA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 3px solid #dc3545;
            margin-bottom: 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .header-text h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header-text p {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card.primary { border-left: 4px solid #007bff; }
        .card.success { border-left: 4px solid #28a745; }
        .card.warning { border-left: 4px solid #ffc107; }
        .card.danger { border-left: 4px solid #dc3545; }
        
        .card-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .card.primary .card-number { color: #007bff; }
        .card.success .card-number { color: #28a745; }
        .card.warning .card-number { color: #ffc107; }
        .card.danger .card-number { color: #dc3545; }
        
        .card-label {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #dc3545;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-review { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }

        .chart-container {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 400px;
            position: relative;
        }

        .chart-placeholder {
            width: 100%;
            height: 350px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo"><img src="../macra-logo.png" alt="MACRA logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%; background: transparent;" /></div>
                <div class="header-text">
                    <h1>Application Status Summary Report</h1>
                    <p>Malawi Communications Regulatory Authority</p>
                </div>
            </div>
            <div class="report-info">
                <div><strong>Report Date:</strong> December 15, 2024</div>
                <div><strong>Period:</strong> November 1-30, 2024</div>
                <div><strong>Generated By:</strong> System Administrator</div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="card primary">
                <div class="card-number">247</div>
                <div class="card-label">Total Applications</div>
            </div>
            <div class="card success">
                <div class="card-number">156</div>
                <div class="card-label">Approved</div>
            </div>
            <div class="card warning">
                <div class="card-number">67</div>
                <div class="card-label">Under Review</div>
            </div>
            <div class="card danger">
                <div class="card-number">24</div>
                <div class="card-label">Rejected</div>
            </div>
        </div>

        <!-- Applications by License Type -->
        <div class="section">
            <h2 class="section-title">Applications by License Type</h2>
            <div class="chart-container">
                <h3 style="color: #333; margin-bottom: 15px; text-align: center;">License Type Distribution</h3>

                <!-- Simple Bar Chart Mockup -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <div style="display: flex; align-items: end; justify-content: space-around; height: 200px; border-bottom: 2px solid #dee2e6;">
                        <!-- Telecommunications Bar -->
                        <div style="display: flex; flex-direction: column; align-items: center;">
                            <div style="background: #007bff; width: 60px; height: 150px; border-radius: 4px 4px 0 0; margin-bottom: 10px; position: relative;">
                                <span style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-weight: bold; color: #333;">90</span>
                            </div>
                            <span style="font-size: 12px; text-align: center; color: #666;">Telecom</span>
                        </div>

                        <!-- Broadcasting Bar -->
                        <div style="display: flex; flex-direction: column; align-items: center;">
                            <div style="background: #28a745; width: 60px; height: 163px; border-radius: 4px 4px 0 0; margin-bottom: 10px; position: relative;">
                                <span style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-weight: bold; color: #333;">98</span>
                            </div>
                            <span style="font-size: 12px; text-align: center; color: #666;">Broadcasting</span>
                        </div>

                        <!-- Postal Services Bar -->
                        <div style="display: flex; flex-direction: column; align-items: center;">
                            <div style="background: #ffc107; width: 60px; height: 80px; border-radius: 4px 4px 0 0; margin-bottom: 10px; position: relative;">
                                <span style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-weight: bold; color: #333;">48</span>
                            </div>
                            <span style="font-size: 12px; text-align: center; color: #666;">Postal</span>
                        </div>

                        <!-- Standards Bar -->
                        <div style="display: flex; flex-direction: column; align-items: center;">
                            <div style="background: #dc3545; width: 60px; height: 32px; border-radius: 4px 4px 0 0; margin-bottom: 10px; position: relative;">
                                <span style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-weight: bold; color: #333;">19</span>
                            </div>
                            <span style="font-size: 12px; text-align: center; color: #666;">Standards</span>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; margin-top: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #007bff; border-radius: 2px;"></div>
                        <span style="font-size: 14px;">Telecommunications (90)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #28a745; border-radius: 2px;"></div>
                        <span style="font-size: 14px;">Broadcasting (98)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #ffc107; border-radius: 2px;"></div>
                        <span style="font-size: 14px;">Postal Services (48)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 16px; height: 16px; background: #dc3545; border-radius: 2px;"></div>
                        <span style="font-size: 14px;">Standards (19)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Status Trends -->
        <div class="section">
            <h2 class="section-title">Application Status Trends</h2>
            <div class="chart-container">
                <h3 style="color: #333; margin-bottom: 15px; text-align: center;">Monthly Application Status Trends</h3>

                <!-- Simple Line Chart Mockup -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <div style="position: relative; height: 200px; border-left: 2px solid #dee2e6; border-bottom: 2px solid #dee2e6;">
                        <!-- Y-axis labels -->
                        <div style="position: absolute; left: -30px; top: 0; font-size: 12px; color: #666;">100</div>
                        <div style="position: absolute; left: -30px; top: 50px; font-size: 12px; color: #666;">75</div>
                        <div style="position: absolute; left: -30px; top: 100px; font-size: 12px; color: #666;">50</div>
                        <div style="position: absolute; left: -30px; top: 150px; font-size: 12px; color: #666;">25</div>
                        <div style="position: absolute; left: -30px; top: 190px; font-size: 12px; color: #666;">0</div>

                        <!-- Grid lines -->
                        <div style="position: absolute; top: 50px; left: 0; right: 0; height: 1px; background: #e9ecef;"></div>
                        <div style="position: absolute; top: 100px; left: 0; right: 0; height: 1px; background: #e9ecef;"></div>
                        <div style="position: absolute; top: 150px; left: 0; right: 0; height: 1px; background: #e9ecef;"></div>

                        <!-- Approved line (trending up) -->
                        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                            <polyline points="50,120 150,100 250,80 350,60"
                                     fill="none" stroke="#28a745" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="50" cy="120" r="4" fill="#28a745"/>
                            <circle cx="150" cy="100" r="4" fill="#28a745"/>
                            <circle cx="250" cy="80" r="4" fill="#28a745"/>
                            <circle cx="350" cy="60" r="4" fill="#28a745"/>
                        </svg>

                        <!-- Under Review line (stable) -->
                        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                            <polyline points="50,140 150,135 250,130 350,125"
                                     fill="none" stroke="#ffc107" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="50" cy="140" r="4" fill="#ffc107"/>
                            <circle cx="150" cy="135" r="4" fill="#ffc107"/>
                            <circle cx="250" cy="130" r="4" fill="#ffc107"/>
                            <circle cx="350" cy="125" r="4" fill="#ffc107"/>
                        </svg>

                        <!-- Rejected line (trending down) -->
                        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                            <polyline points="50,160 150,155 250,150 350,140"
                                     fill="none" stroke="#dc3545" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="50" cy="160" r="4" fill="#dc3545"/>
                            <circle cx="150" cy="155" r="4" fill="#dc3545"/>
                            <circle cx="250" cy="150" r="4" fill="#dc3545"/>
                            <circle cx="350" cy="140" r="4" fill="#dc3545"/>
                        </svg>

                        <!-- X-axis labels -->
                        <div style="position: absolute; bottom: -25px; left: 30px; font-size: 12px; color: #666;">Oct</div>
                        <div style="position: absolute; bottom: -25px; left: 130px; font-size: 12px; color: #666;">Nov</div>
                        <div style="position: absolute; bottom: -25px; left: 230px; font-size: 12px; color: #666;">Dec</div>
                        <div style="position: absolute; bottom: -25px; left: 330px; font-size: 12px; color: #666;">Jan</div>
                    </div>
                </div>

                <!-- Legend -->
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #28a745;"></div>
                        <span style="font-size: 14px;">Approved (↗ Trending Up)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #ffc107;"></div>
                        <span style="font-size: 14px;">Under Review (→ Stable)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 20px; height: 3px; background: #dc3545;"></div>
                        <span style="font-size: 14px;">Rejected (↘ Declining)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 Malawi Communications Regulatory Authority. All rights reserved.</p>
            <p>This report is confidential and intended for authorized personnel only.</p>
        </div>
    </div>
</body>
</html>
