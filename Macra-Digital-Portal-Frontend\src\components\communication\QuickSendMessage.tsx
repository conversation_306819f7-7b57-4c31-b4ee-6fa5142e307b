'use client';

import React, { useState } from 'react';
import { PaperAirplaneIcon, UserIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/contexts/ToastContext';

export interface QuickMessageRecipient {
  id: string;
  name: string;
  email: string;
}

export interface QuickSendMessageProps {
  onSendMessage: (data: {
    recipients: string[];
    subject: string;
    message: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
  }) => Promise<void>;
  availableRecipients: QuickMessageRecipient[];
  defaultRecipients?: QuickMessageRecipient[];
  defaultSubject?: string;
  placeholder?: string;
  showSubject?: boolean;
  showPriority?: boolean;
  allowMultipleRecipients?: boolean;
  className?: string;
  disabled?: boolean;
}

const QuickSendMessage: React.FC<QuickSendMessageProps> = ({
  onSendMessage,
  availableRecipients,
  defaultRecipients = [],
  defaultSubject = '',
  placeholder = 'Type your message here...',
  showSubject = true,
  showPriority = true,
  allowMultipleRecipients = true,
  className = '',
  disabled = false
}) => {
  const { showSuccess, showError } = useToast();
  
  const [selectedRecipients, setSelectedRecipients] = useState<QuickMessageRecipient[]>(defaultRecipients);
  const [subject, setSubject] = useState(defaultSubject);
  const [message, setMessage] = useState('');
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'urgent'>('normal');
  const [isSending, setIsSending] = useState(false);
  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);
  const [recipientSearch, setRecipientSearch] = useState('');

  // Filter recipients based on search
  const filteredRecipients = availableRecipients.filter(recipient =>
    !selectedRecipients.find(selected => selected.id === recipient.id) &&
    (recipient.name.toLowerCase().includes(recipientSearch.toLowerCase()) ||
     recipient.email.toLowerCase().includes(recipientSearch.toLowerCase()))
  );

  const handleAddRecipient = (recipient: QuickMessageRecipient) => {
    if (allowMultipleRecipients) {
      setSelectedRecipients(prev => [...prev, recipient]);
    } else {
      setSelectedRecipients([recipient]);
    }
    setRecipientSearch('');
    setShowRecipientDropdown(false);
  };

  const handleRemoveRecipient = (recipientId: string) => {
    setSelectedRecipients(prev => prev.filter(r => r.id !== recipientId));
  };

  const handleSendMessage = async () => {
    // Validation
    if (selectedRecipients.length === 0) {
      showError('Please select at least one recipient');
      return;
    }

    if (!message.trim()) {
      showError('Please enter a message');
      return;
    }

    if (showSubject && !subject.trim()) {
      showError('Please enter a subject');
      return;
    }

    setIsSending(true);
    try {
      await onSendMessage({
        recipients: selectedRecipients.map(r => r.id),
        subject: subject.trim(),
        message: message.trim(),
        priority
      });
      
      // Reset form after successful send
      setSelectedRecipients([]);
      setSubject('');
      setMessage('');
      setPriority('normal');
      
      showSuccess('Message sent successfully!');
    } catch (error) {
      console.error('Error sending message:', error);
      showError('Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'normal': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      <div className="space-y-4">
        {/* Recipients */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {allowMultipleRecipients ? 'Recipients' : 'Recipient'}
          </label>
          
          {/* Selected Recipients */}
          {selectedRecipients.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedRecipients.map(recipient => (
                <div
                  key={recipient.id}
                  className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                >
                  <UserIcon className="h-4 w-4" />
                  <span>{recipient.name}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveRecipient(recipient.id)}
                    className="text-blue-500 hover:text-blue-700"
                    disabled={disabled}
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Recipient Search */}
          <div className="relative">
            <input
              type="text"
              value={recipientSearch}
              onChange={(e) => {
                setRecipientSearch(e.target.value);
                setShowRecipientDropdown(true);
              }}
              onFocus={() => setShowRecipientDropdown(true)}
              placeholder="Search and select recipients..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              disabled={disabled}
            />
            
            {/* Recipient Dropdown */}
            {showRecipientDropdown && filteredRecipients.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                {filteredRecipients.map(recipient => (
                  <button
                    key={recipient.id}
                    type="button"
                    onClick={() => handleAddRecipient(recipient)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm"
                    disabled={disabled}
                  >
                    <UserIcon className="h-4 w-4 text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900">{recipient.name}</div>
                      <div className="text-xs text-gray-500">{recipient.email}</div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Subject */}
        {showSubject && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject
            </label>
            <input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Enter subject..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              disabled={disabled}
            />
          </div>
        )}

        {/* Priority */}
        {showPriority && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as any)}
              className={`px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${getPriorityColor(priority)}`}
              disabled={disabled}
            >
              <option value="low">Low</option>
              <option value="normal">Normal</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>
        )}

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={placeholder}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical text-sm"
            disabled={disabled}
          />
        </div>

        {/* Send Button */}
        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleSendMessage}
            disabled={disabled || isSending || selectedRecipients.length === 0 || !message.trim()}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
            {isSending ? 'Sending...' : 'Send Message'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickSendMessage;
