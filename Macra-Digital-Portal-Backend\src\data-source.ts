import 'dotenv/config';
import { DataSource } from 'typeorm';
import { join } from 'path';
import * as fs from 'fs';

type PgSsl =
    | boolean
    | {
        rejectUnauthorized?: boolean;
        ca?: string | Buffer;
        cert?: string | Buffer;
        key?: string | Buffer;
    };

function bool(v?: string, def = false) {
    return v == null ? def : /^(1|true|yes)$/i.test(v);
}

function readIfFile(p?: string) {
    try {
        return p ? fs.readFileSync(p) : undefined;
    } catch {
        return undefined;
    }
}

function fromUrl(url?: string) {
    if (!url) return null;
    const u = new URL(url); // ****************************************/db?sslmode=require

    if (!/^postgres(|ql):$/.test(u.protocol)) {
        throw new Error(`Unsupported protocol in DATABASE_URL: ${u.protocol}`);
    }

    // Determine SSL mode
    const isAzure = u.hostname.endsWith('.postgres.database.azure.com');
    const q = u.searchParams;
    const sslMode =
        (q.get('sslmode') ?? q.get('ssl') ?? '').toLowerCase() ||
        (isAzure ? 'require' : '');

    // Optional CA (recommended)
    const ca =
        readIfFile(process.env.DB_SSL_CA_PATH) ||
        readIfFile(process.env.NODE_EXTRA_CA_CERTS);

    let ssl: PgSsl = false;
    if (sslMode === 'require' || sslMode === 'true' || isAzure) {
        // Prefer validating with CA; else allow insecure if explicitly requested
        const rejectUnauthorized = bool(process.env.DB_SSL_REJECT_UNAUTHORIZED, ca ? true : false);
        ssl = ca ? { ca, rejectUnauthorized } : { rejectUnauthorized };
    }

    return {
        type: 'postgres' as const,
        host: u.hostname,
        port: Number(u.port || 5432),
        username: decodeURIComponent(u.username),
        password: decodeURIComponent(u.password),
        database: u.pathname.replace(/^\//, ''),
        ssl,
    };
}

const urlCfg = fromUrl(process.env.DATABASE_URL || process.env.DB_URL);

const envCfg = {
    type: 'postgres' as const,
    host: process.env.DB_HOST || 'localhost',
    port: Number(process.env.DB_PORT || 5432),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'postgres',
    ssl: (() => {
        if (bool(process.env.DB_SSL) || process.env.PGSSLMODE === 'require') {
            const ca =
                readIfFile(process.env.DB_SSL_CA_PATH) ||
                readIfFile(process.env.NODE_EXTRA_CA_CERTS);
            const rejectUnauthorized = bool(process.env.DB_SSL_REJECT_UNAUTHORIZED, ca ? true : false);
            return ca ? { ca, rejectUnauthorized } : { rejectUnauthorized };
        }
        return false as PgSsl;
    })(),
};

const base = urlCfg ?? envCfg;

// Optional pool/driver extras
// const extra = {
//     application_name:
//         process.env.APP_NAME
//             ? `${process.env.APP_NAME}${process.env.APP_ENV ? `-${process.env.APP_ENV}` : ''}`
//             : undefined,
//     statement_timeout: Number(process.env.DB_STATEMENT_TIMEOUT || 0) || undefined,
//     idle_in_transaction_session_timeout:
//         Number(process.env.DB_IDLE_TX_TIMEOUT || 0) || undefined,
//     keepAlive: true,
// };

const AppDataSource = new DataSource({
    ...base,
    // extra,
    entities: [join(__dirname, '**/*.entity{.ts,.js}')],
    migrations: [join(__dirname, 'migrations/*{.ts,.js}')],
    synchronize: false,
    logging: true,
});

export default AppDataSource;
