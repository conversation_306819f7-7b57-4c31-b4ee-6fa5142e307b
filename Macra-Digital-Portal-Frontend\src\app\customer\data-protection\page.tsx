'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import DataBreachModal from '@/components/customer/DataBreachModal';
import ComplaintStatusBar, { COMPLAINT_STAGES, getStageIndexFromStatus } from '@/components/customer/ComplaintStatusBar';
import { dataBreachService } from '@/services/data-breach';
import { DataBreachReport } from '@/types/data-breach';
import DataBreachViewModal from '@/components/data-breach/DataBreachViewModal';

interface CombinedComplaint {
  id: string;
  title: string;
  description: string;
  category: string;
  type: 'consumer_affairs' | 'data_breach';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  submittedAt: string;
  updatedAt: string;
  assignedTo?: string;
  resolution?: string;
  number?: string;
}

const DataProtectionPage = () => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  const [complaints, setComplaints] = useState<CombinedComplaint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'track'>('overview');
  const [showDataBreachModal, setShowDataBreachModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch data function
  const fetchData = useCallback(async () => {
    if (!isAuthenticated) {
      console.log('❌ User not authenticated, skipping data fetch');
      return;
    }

    console.log('✅ User authenticated, fetching data...');

    try {
      setIsLoading(true);
      setError('');

      // Fetch only data breach reports (this is the Data Breach page)
      const dataBreachResponse = await dataBreachService.getReports({ limit: 100 });

        console.log('🔍 Data Breach Response:', dataBreachResponse);
        console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);
        console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);

        // Ensure data is an array (services return data directly)
        const dataBreachData = Array.isArray(dataBreachResponse.data)
          ? dataBreachResponse.data
          : [];

        console.log('🔍 Data Breach Data Array:', dataBreachData);

        // Transform only data breach reports
        const combinedComplaints: CombinedComplaint[] = dataBreachData.map((report: DataBreachReport) => ({
          id: report.report_id,
          title: report.title,
          description: report.description,
          category: report.category,
          type: 'data_breach' as const,
          priority: report.priority,
          status: report.status,
          submittedAt: report.created_at,
          updatedAt: report.updated_at,
          assignedTo: report.assignee?.first_name && report.assignee?.last_name
            ? `${report.assignee.first_name} ${report.assignee.last_name}`
            : undefined,
          resolution: report.resolution,
          number: report.report_number
        }));

        // Sort by creation date (newest first)
        combinedComplaints.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());

        setComplaints(combinedComplaints);

      } catch (err: unknown) {
        console.error('Error fetching complaints:', err);

        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        const isAxiosError = err && typeof err === 'object' && 'response' in err;
        const axiosError = isAxiosError ? err as { response?: { status?: number; data?: unknown } } : null;
        const status = axiosError?.response?.status;

        console.error('Error details:', {
          message: errorMessage,
          response: axiosError?.response?.data,
          status: status
        });

        if (status === 401) {
          setError('Authentication required. Please log in again.');
        } else if (status === 404) {
          setError('API endpoints not found. Please check if the backend is running.');
        } else {
          setError(`Failed to load complaints: ${errorMessage}`);
        }
      } finally {
        setIsLoading(false);
      }
    }, [isAuthenticated]);

  // Fetch data on mount and when authentication changes
  useEffect(() => {
    fetchData();
  }, [isAuthenticated, fetchData]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'investigating': return 'bg-orange-100 text-orange-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleViewDetails = (reportId: string) => {
    setSelectedReportId(reportId);
    setShowViewModal(true);
  };

  // All complaints are now data breach reports only

  // Calculate data breach statistics
  const totalComplaints = complaints.length;
  const pendingComplaints = complaints.filter(c => c.status === 'submitted' || c.status === 'under_review').length;
  const investigatingComplaints = complaints.filter(c => c.status === 'investigating').length;
  const resolvedComplaints = complaints.filter(c => c.status === 'resolved' || c.status === 'closed').length;

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading Data Breach..." />
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Data Breach
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Report and track data breach incidents and privacy violations
            </p>
          </div>
          <button
            type="button"
            onClick={() => setShowDataBreachModal(true)}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300 flex items-center"
          >
            <i className="ri-shield-keyhole-line mr-2"></i>
            Report Data Breach
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: 'Overview', icon: 'ri-dashboard-line' },
              { key: 'track', label: 'Track Complaints', icon: 'ri-search-eye-line', count: complaints.length }
            ].map((tab) => (
              <button
                type="button"
                key={tab.key}
                onClick={() => setActiveTab(tab.key as 'overview' | 'track')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
                  activeTab === tab.key
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-file-list-3-line text-2xl text-blue-600"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Complaints</p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{totalComplaints}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-time-line text-2xl text-yellow-600"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{pendingComplaints}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-search-eye-line text-2xl text-orange-600"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Investigating</p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{investigatingComplaints}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-check-double-line text-2xl text-green-600"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Resolved</p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{resolvedComplaints}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Data Breach Information Card */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <i className="ri-shield-keyhole-line text-3xl text-red-600"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Data Breach Reporting</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Report unauthorized access, misuse, or breach of your personal data
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">What to Report:</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>• Unauthorized data access</p>
                  <p>• Data misuse or sharing</p>
                  <p>• Privacy violations</p>
                  <p>• Identity theft concerns</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Track Complaints Tab */}
        {activeTab === 'track' && (
          <div>
            {complaints.length === 0 ? (
              <div className="text-center py-12">
                <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No complaints found</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  You haven&apos;t submitted any complaints yet.
                </p>
                <button
                  type="button"
                  onClick={() => setActiveTab('overview')}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300"
                >
                  Submit Your First Complaint
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {complaints.map((complaint) => (
                  <div key={complaint.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mr-3">
                            {complaint.title}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(complaint.status)}`}>
                            {complaint.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(complaint.priority)}`}>
                            {complaint.priority.toUpperCase()}
                          </span>
                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                            complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          ID: {complaint.id} | Category: {complaint.category}
                        </p>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                          {complaint.description.length > 150
                            ? `${complaint.description.substring(0, 150)}...`
                            : complaint.description}
                        </p>

                        {/* Complaint Status Bar */}
                        <div className="mb-4">
                          <ComplaintStatusBar
                            currentStage={getStageIndexFromStatus(complaint.status)}
                            stages={complaint.type === 'consumer_affairs' ? COMPLAINT_STAGES.CONSUMER_AFFAIRS : COMPLAINT_STAGES.DATA_BREACH}
                            status={complaint.status as 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'}
                            size="sm"
                            variant="horizontal"
                            showPercentage={false}
                            showStageNames={true}
                            className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"
                          />
                        </div>

                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                          <span>Submitted: {formatDate(complaint.submittedAt)}</span>
                          <span className="mx-2">•</span>
                          <span>Updated: {formatDate(complaint.updatedAt)}</span>
                          {complaint.assignedTo && (
                            <>
                              <span className="mx-2">•</span>
                              <span>Assigned to: {complaint.assignedTo}</span>
                            </>
                          )}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleViewDetails(complaint.id)}
                        className="ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Help Section */}
        <div className="mt-8 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 p-6">
          <h3 className="text-lg font-medium text-red-900 dark:text-red-100 mb-4">
            Need Help with Data Breach Reporting?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Contact Information
              </h4>
              <div className="space-y-2 text-sm text-red-700 dark:text-red-300">
                <div className="flex items-center">
                  <i className="ri-phone-line mr-2"></i>
                  <span>+265 1 770 100</span>
                </div>
                <div className="flex items-center">
                  <i className="ri-mail-line mr-2"></i>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <i className="ri-time-line mr-2"></i>
                  <span>Mon-Fri, 8:00 AM - 5:00 PM</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Data Breach Categories
              </h4>
              <ul className="space-y-1 text-sm text-red-700 dark:text-red-300">
                <li>• Unauthorized Access</li>
                <li>• Data Misuse</li>
                <li>• Privacy Violations</li>
                <li>• Identity Theft</li>
                <li>• Data Sharing Issues</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Modals */}
        {showDataBreachModal && (
          <DataBreachModal
            onClose={() => setShowDataBreachModal(false)}
            onSubmit={(data) => {
              console.log('Data breach report submitted:', data);
              setShowDataBreachModal(false);
              // Refresh complaints list without full page reload
              fetchData();
            }}
          />
        )}

        {showViewModal && selectedReportId && (
          <DataBreachViewModal
            isOpen={showViewModal}
            onClose={() => {
              setShowViewModal(false);
              setSelectedReportId(null);
            }}
            reportId={selectedReportId}
          />
        )}
      </div>
    </CustomerLayout>
  );
};

export default DataProtectionPage;
