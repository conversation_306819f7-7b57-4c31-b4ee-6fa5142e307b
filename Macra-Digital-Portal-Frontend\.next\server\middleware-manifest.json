{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Y8JckVUlWFoRt22g0sJwhPNGOheToeZKXRmo3KpNRec=", "__NEXT_PREVIEW_MODE_ID": "52588122de564c46748374c0cfeec71d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8a890250ad8972c41e84b7d8671993a2fe2caf6b629270d8a084a19902dc50d6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "beb2cdc5c8fefd63716b908f95f0e2f3c0dba238cf6f2ca564c0cf0a77f0bedf"}}}, "functions": {}, "sortedMiddleware": ["/"]}