/**
 * User service constants and utilities
 */

export const UserConstants = {
  // Password hashing rounds (should match auth constants)
  PASSWORD_HASH_ROUNDS: 12,
  
  // Pagination settings
  PAGINATION: {
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
    DEFAULT_SORT_BY: [['created_at', 'DESC']] as const,
  },
  
  // Query relations
  RELATIONS: {
    BASIC: ['roles', 'roles.permissions'],
    FULL: ['roles', 'department','roles.permissions', 'organization', 'creator', 'updater'],
    WITH_DEPARTMENT: ['roles','roles.permissions', 'department'],
  },
  
  // Sortable columns for pagination
  SORTABLE_COLUMNS: ['first_name', 'last_name', 'email', 'created_at', 'status'] as const,
  
  // Searchable columns for pagination
  SEARCHABLE_COLUMNS: ['first_name', 'last_name', 'email'] as const,
  
  // Filterable columns
  FILTERABLE_COLUMNS: {
    status: true,
    department_id: true,
  } as const,
} as const;

export const UserMessages = {
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User with this email already exists',
  EMAIL_ALREADY_TAKEN: 'Email is already taken',
  ROLES_NOT_FOUND: 'One or more roles not found',
  CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
  PASSWORD_MISMATCH: 'New password and confirmation do not match',
  PASSWORD_CHANGED_SUCCESS: 'Password changed successfully',
  USER_NOT_FOUND_AFTER_UPDATE: 'User not found after update',
  NO_FILE_UPLOADED: 'No file uploaded',
  AVATAR_UPLOAD_FAILED: 'Failed to upload avatar',
  EMAIL_SENT_SUCCESS: 'Email sent! Please check inbox',
} as const;

/**
 * User service utility functions
 */
export class UserUtils {
  /**
   * Create base64 image string from file buffer
   */
  static createBase64Image(file: Express.Multer.File): string {
    return `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
  }

  /**
   * Get user's full name
   */
  static getFullName(user: { first_name: string; last_name: string; middle_name?: string }): string {
    const parts = [user.first_name];
    if (user.middle_name) {
      parts.push(user.middle_name);
    }
    parts.push(user.last_name);
    return parts.join(' ').trim();
  }

  /**
   * Sanitize update data by removing sensitive fields
   */
  static sanitizeUpdateData<T extends Record<string, any>>(
    data: T
  ): Omit<T, 'password' | 'two_factor_code' | 'two_factor_next_verification'> {
    const { password, two_factor_code, two_factor_next_verification, ...safeData } = data;
    return safeData;
  }

  /**
   * Check if email is being changed
   */
  static isEmailChanged(currentEmail: string, newEmail?: string): boolean {
    return Boolean(newEmail && newEmail !== currentEmail);
  }

  /**
   * Validate role IDs array
   */
  static validateRoleIds(roleIds: string[] | undefined): boolean {
    return Boolean(roleIds && roleIds.length > 0);
  }

  /**
   * Create pagination config for users
   */
  static createPaginationConfig() {
    return {
      sortableColumns: [...UserConstants.SORTABLE_COLUMNS],
      searchableColumns: [...UserConstants.SEARCHABLE_COLUMNS],
      defaultSortBy: [['created_at', 'DESC'] as ['created_at', 'DESC']],
      defaultLimit: UserConstants.PAGINATION.DEFAULT_LIMIT,
      maxLimit: UserConstants.PAGINATION.MAX_LIMIT,
      filterableColumns: UserConstants.FILTERABLE_COLUMNS,
      relations: [...UserConstants.RELATIONS.WITH_DEPARTMENT],
    };
  }
}
