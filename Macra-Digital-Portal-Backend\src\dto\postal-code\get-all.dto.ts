import { <PERSON><PERSON>ptional, Is<PERSON>umberString, <PERSON>, <PERSON>, IsString, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetAllPostalCodesDTO {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 1)
  @Min(1, { message: 'Page must be at least 1' })
  @Max(1000, { message: 'Page cannot exceed 1000' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of records per page',
    example: 1000,
    minimum: 1,
    maximum: 1000,
    default: 1000
  })
  @IsOptional()
  @Transform(({ value }) => Math.min(1000, Math.max(1, parseInt(value) || 1000)))
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(1000, { message: 'Limit cannot exceed 1000' })
  limit?: number = 1000;
}

