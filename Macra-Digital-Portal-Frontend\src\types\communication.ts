export interface MessageRecipient {
  id: string;
  name: string;
  email: string;
  role?: string;
  avatar?: string;
  department?: string;
  isOnline?: boolean;
}

export interface MessageAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  file?: File;
  uploadedAt?: Date;
}

export interface SendMessageData {
  recipients: string[];
  subject: string;
  message: string;
  attachments: File[];
  priority: MessagePriority;
  messageType: MessageType;
  scheduledAt?: Date;
  expiresAt?: Date;
}

export interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderEmail: string;
  recipients: MessageRecipient[];
  subject: string;
  content: string;
  messageType: MessageType;
  priority: MessagePriority;
  status: MessageStatus;
  attachments: MessageAttachment[];
  sentAt: Date;
  readAt?: Date;
  scheduledAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
  threadId?: string;
  replyToId?: string;
}

export interface MessageThread {
  id: string;
  subject: string;
  participants: MessageRecipient[];
  messages: Message[];
  lastMessageAt: Date;
  unreadCount: number;
  isArchived: boolean;
  tags: string[];
}

export interface MessageTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  messageType: MessageType;
  variables: string[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  digestFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

export interface CommunicationStats {
  totalMessages: number;
  unreadMessages: number;
  sentMessages: number;
  receivedMessages: number;
  messagesByType: Record<MessageType, number>;
  messagesByPriority: Record<MessagePriority, number>;
  responseRate: number;
  averageResponseTime: number;
}

export type MessageType = 
  | 'email' 
  | 'notification' 
  | 'announcement' 
  | 'alert' 
  | 'reminder' 
  | 'system';

export type MessagePriority = 
  | 'low' 
  | 'normal' 
  | 'high' 
  | 'urgent';

export type MessageStatus = 
  | 'draft' 
  | 'scheduled' 
  | 'sent' 
  | 'delivered' 
  | 'read' 
  | 'failed' 
  | 'expired';

export interface MessageFilter {
  messageType?: MessageType;
  priority?: MessagePriority;
  status?: MessageStatus;
  senderId?: string;
  recipientId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  hasAttachments?: boolean;
  isUnread?: boolean;
  search?: string;
  tags?: string[];
}

export interface MessageSearchResult {
  messages: Message[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
}

export interface BulkMessageOperation {
  messageIds: string[];
  operation: 'markRead' | 'markUnread' | 'archive' | 'delete' | 'addTag' | 'removeTag';
  value?: string; // For tag operations
}

export interface MessageDeliveryReport {
  messageId: string;
  totalRecipients: number;
  deliveredCount: number;
  readCount: number;
  failedCount: number;
  deliveryDetails: {
    recipientId: string;
    recipientEmail: string;
    status: MessageStatus;
    deliveredAt?: Date;
    readAt?: Date;
    errorMessage?: string;
  }[];
}

export interface CommunicationChannel {
  id: string;
  name: string;
  description: string;
  type: 'public' | 'private' | 'direct';
  members: MessageRecipient[];
  admins: string[];
  isArchived: boolean;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
  unreadCount: number;
}

export interface MessageDraft {
  id: string;
  recipients: string[];
  subject: string;
  content: string;
  messageType: MessageType;
  priority: MessagePriority;
  attachments: MessageAttachment[];
  scheduledAt?: Date;
  autoSave: boolean;
  lastSavedAt: Date;
}

export interface MessageQuota {
  userId: string;
  dailyLimit: number;
  monthlyLimit: number;
  dailyUsed: number;
  monthlyUsed: number;
  resetDate: Date;
  isUnlimited: boolean;
}

export interface MessageAnalytics {
  period: 'day' | 'week' | 'month' | 'year';
  messagesSent: number;
  messagesReceived: number;
  averageResponseTime: number;
  topRecipients: {
    recipientId: string;
    recipientName: string;
    messageCount: number;
  }[];
  messageTypeDistribution: Record<MessageType, number>;
  priorityDistribution: Record<MessagePriority, number>;
  engagementRate: number;
  deliveryRate: number;
}

// API Response types
export interface SendMessageResponse {
  messageId: string;
  status: 'sent' | 'scheduled' | 'failed';
  deliveryReport?: MessageDeliveryReport;
  error?: string;
}

export interface MessageListResponse {
  messages: Message[];
  totalCount: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface RecipientListResponse {
  recipients: MessageRecipient[];
  totalCount: number;
  page: number;
  limit: number;
}

// Component Props types
export interface SendMessageComponentProps {
  onSendMessage: (messageData: SendMessageData) => Promise<void>;
  availableRecipients: MessageRecipient[];
  defaultRecipients?: MessageRecipient[];
  defaultSubject?: string;
  defaultMessage?: string;
  messageType?: MessageType;
  allowAttachments?: boolean;
  allowPriority?: boolean;
  allowMultipleRecipients?: boolean;
  allowScheduling?: boolean;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxAttachmentSize?: number;
  allowedFileTypes?: string[];
  templates?: MessageTemplate[];
}

export interface MessageListComponentProps {
  messages: Message[];
  loading?: boolean;
  onMessageClick?: (message: Message) => void;
  onMarkAsRead?: (messageId: string) => void;
  onMarkAsUnread?: (messageId: string) => void;
  onArchive?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onBulkOperation?: (operation: BulkMessageOperation) => void;
  showBulkActions?: boolean;
  showFilters?: boolean;
  className?: string;
}

export interface MessageViewComponentProps {
  message: Message;
  onReply?: (replyData: SendMessageData) => void;
  onForward?: (forwardData: SendMessageData) => void;
  onMarkAsRead?: () => void;
  onMarkAsUnread?: () => void;
  onArchive?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  className?: string;
}
