import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinC<PERSON><PERSON>n,BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';

export enum EvaluationType {
  INDIVIDUAL_LICENSE_A = 'individual_license_a',
  CLASS_LICENSE_B = 'class_license_b',
  NETWORK_SERVICE = 'network_service',
  POSTAL_SERVICE = 'postal_service',
  RADIO_COMMUNICATION = 'radio_communication',
  SATELLITE_SERVICE = 'satellite_service',
  TV_BROADCASTING = 'tv_broadcasting',
  UNIVERSITY_RADIO = 'university_radio',
  TYPE_APPROVAL_CERTIFICATE = 'type_approval_certificate',
}

export enum EvaluationStatus {
  DRAFT = 'draft',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export enum EvaluationRecommendation {
  APPROVE = 'approve',
  CONDITIONAL_APPROVE = 'conditional_approve',
  REJECT = 'reject',
}

@Entity('evaluations')
export class Evaluations {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  evaluation_id: string;

  @Column({ type: 'uuid' })
  application_id: string;

  @Column({ type: 'uuid' })
  evaluator_id: string;

  @Column({
    type: 'enum',
    enum: EvaluationType,
  })
  evaluation_type: EvaluationType;

  @Column({
    type: 'enum',
    enum: EvaluationStatus,
    default: EvaluationStatus.DRAFT,
  })
  status: EvaluationStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  total_score: number;

  @Column({
    type: 'enum',
    enum: EvaluationRecommendation,
  })
  recommendation: EvaluationRecommendation;

  @Column({ type: 'text', nullable: true })
  evaluators_notes?: string;

  @Column({ type: 'boolean', nullable: true })
  shareholding_compliance?: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at?: Date;

  // Relations
  @ManyToOne(() => Applications)
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'evaluator_id' })
  evaluator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.evaluation_id) {
      this.evaluation_id = uuidv4();
    }
  }
}
